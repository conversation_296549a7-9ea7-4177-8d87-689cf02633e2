using System.Text;

namespace RxjhVer23;

internal class LocalTextProcessing
{
	public static string GetStringByGb18030(byte[] bytes, int index, int count)
	{
		string text = Encoding.GetEncoding("GB18030").GetString(bytes, index, count);
		int num = text.IndexOf('\0');
		if (num != -1)
		{
			text = text.Remove(num);
		}
		return text;
	}

	public static string RemoveUnderscores(string input)
	{
		int num = input.IndexOf('_');
		if (num != -1)
		{
			return input.Remove(num);
		}
		return string.Empty;
	}

	public static string GetTextAfterUnderscore(string input)
	{
		int num = input.IndexOf('_');
		if (num != -1 && num + 1 < input.Length)
		{
			return input.Substring(num + 1);
		}
		return string.Empty;
	}
}

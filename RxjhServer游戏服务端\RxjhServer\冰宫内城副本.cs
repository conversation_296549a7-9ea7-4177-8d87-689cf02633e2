using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 冰宫内城副本 : IDisposable
{
	private readonly System.Timers.Timer 时间1;

	private System.Timers.Timer 时间2;

	private readonly DateTime kssj;

	private int kssjint = 0;

	public DateTime bossgcsj;

	private int bossgcsjint = 0;

	private readonly ConcurrentDictionary<int, NpcClass> 副本怪物;

	private readonly 组队Class 参加队伍;

	public 冰宫内城副本(组队Class 组队)
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "EventClass-");
			}
			参加队伍 = 组队;
			foreach (Players value in 参加队伍.组队列表.Values)
			{
				value.系统提示("冰宫内城副本申请成功,一分钟后将被传送到冰宫内城");
			}
			副本怪物 = new ConcurrentDictionary<int, NpcClass>();
			副本怪物.Clear();
			World.冰宫内城副怪是否死亡 = 0;
			kssj = DateTime.Now.AddMinutes(1.0);
			时间1 = new System.Timers.Timer(20000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "冰宫内城副本", "初始化", "创建副本定时器");
		}
	}

	public void 时间结束事件1(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "冰宫内城副本出错2");
		}
		try
		{
			int num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			kssjint = num;
			foreach (Players value in 参加队伍.组队列表.Values)
			{
				value.发送其他活动开始倒计时(kssjint);
			}
			if (kssjint > 0)
			{
				return;
			}
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
			foreach (Players value2 in 参加队伍.组队列表.Values)
			{
				if (value2.恢复精力 >= 20)
				{
					value2.恢复精力 -= 20;
					value2.移动(-1f, 181f, 15f, 5201);
					value2.保存人物的数据();
					value2.更新武功和状态();
				}
				else
				{
					value2.系统提示("精力不足20点,武剑刀/冰宫护卫/机关装置/每天可以恢复100点精力");
				}
			}
			try
			{
				foreach (冰宫内城怪物 item in World.冰宫内城怪物列表)
				{
					NpcClass npcClass = World.AddBossEveNpc(item.FLD_PID, item.FLD_X, item.FLD_Y, item.FLD_MID);
					if (npcClass != null && !副本怪物.ContainsKey(npcClass.FLD_INDEX))
					{
						副本怪物.TryAdd(npcClass.FLD_INDEX, npcClass);
					}
				}
			}
			catch (Exception ex)
			{
				RxjhClass.HandleSystemException(ex, "冰宫内城副本", "生成怪物", "创建副本怪物");
			}
			bossgcsj = DateTime.Now.AddMinutes(30.0);
			时间2 = new System.Timers.Timer(30000.0);
			时间2.Elapsed += 时间结束事件2;
			时间2.Enabled = true;
			时间2.AutoReset = true;
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "冰宫内城副本", "时间结束事件1", "队伍解散或传送处理");
			Form1.WriteLine(22, "队伍被解散冰宫内城副本结束-1");
			Dispose();
		}
	}

	public void 时间结束事件2(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "冰宫内城副本出错4");
		}
		try
		{
			int num = (int)bossgcsj.Subtract(DateTime.Now).TotalSeconds;
			bossgcsjint = num;
			foreach (Players value in 参加队伍.组队列表.Values)
			{
				if (value.人物坐标_地图 == 5201)
				{
					value.发送其他活动开始倒计时(bossgcsjint);
				}
			}
			if (bossgcsjint <= 0)
			{
				时间2.Enabled = false;
				时间2.Close();
				时间2.Dispose();
				Dispose();
			}
		}
		catch
		{
			Form1.WriteLine(22, "队伍被解散冰宫内城副本结束-2");
			Dispose();
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "冰宫内城副本出错6");
		}
		List<NpcClass> list = new List<NpcClass>();
		foreach (NpcClass value in 副本怪物.Values)
		{
			list.Add(value);
		}
		if (list != null)
		{
			foreach (NpcClass item in list)
			{
				item.发送怪物一次性死亡数据();
			}
			list.Clear();
		}
		副本怪物.Clear();
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		if (时间2 != null)
		{
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
		}
		foreach (Players value2 in World.allConnectedChars.Values)
		{
			if (value2.人物坐标_地图 == 5201)
			{
				value2.移动(-1979f, 2133f, 15f, 5001);
				value2.发送其他活动开始倒计时(5);
			}
		}
		World.发送特殊公告("冰宫内城副本已结束.其他队伍可以继续参加！", 6, "公告");
		World.冰宫内城副本 = null;
		World.冰宫内城副怪是否死亡 = 0;
		Form1.WriteLine(22, "冰宫内城组队副本活动结束");
	}
}

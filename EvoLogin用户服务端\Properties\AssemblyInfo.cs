using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyProduct("EvoLogin")]

[assembly: AssemblyCopyright("Copyright © Evias 2025")]  

[assembly: AssemblyTitle("EvoLoginServer")]  

[assembly: AssemblyDescription("V23.0")]  

[assembly: AssemblyConfiguration("")]

[assembly: AssemblyCompany("EVias")]  

[assembly: Guid("0f0005ae-4fdb-46b8-bca9-43a14df08271")]  

[assembly: ComVisible(false)]

[assembly: AssemblyTrademark("EVias™")]  

[assembly: AssemblyFileVersion("*******")]  

[assembly: AssemblyVersion("*******")]  
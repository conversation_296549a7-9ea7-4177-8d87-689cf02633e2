using System;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace loginServer;

public class ATPlayerHandler : ATSockClient
{
	private AtServerConnect AtSend;

	public ATPlayerHandler(Socket from, ATRemoveClientDelegate rftsl)
		: base(from, rftsl)
	{
	}

	public void 物理攻击人物包(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 物理攻击怪物包(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 物理攻击怪物包1(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包1(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包2(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包3(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包4(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击怪物包5(object ParObject)
	{
		try
		{
			byte[] databyte = ((ThreadParameter)ParObject).Databyte;
			Thread.Sleep(BitConverter.ToInt16(databyte, 18));
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public void 魔法攻击人物包(object ParObject)
	{
		try
		{
			ThreadParameter obj = (ThreadParameter)ParObject;
			byte[] databyte = obj.Databyte;
			playerS playerS2 = obj.players;
			int num = BitConverter.ToInt16(databyte, 18);
			for (int i = 0; i < num / 6; i++)
			{
				Thread.Sleep(5);
				if (playerS2.是否卡技能)
				{
					return;
				}
			}
			Sendbyte(databyte, 36);
		}
		catch (Exception)
		{
		}
	}

	public override byte[] ProcessDataReceived(byte[] data, int length)
	{
		try
		{
			int num = 0;
			if (170 == data[0] && 102 == data[1])
			{
				byte[] array = new byte[4];
				Buffer.BlockCopy(data, 2, array, 0, 4);
				int num2 = BitConverter.ToInt32(array, 0);
				if (length < num2 + 6)
				{
					return null;
				}
				while (true)
				{
					byte[] array2 = new byte[num2];
					Buffer.BlockCopy(data, num + 6, array2, 0, num2);
					num += num2 + 6;
					DataReceived(array2, num2);
					if (num >= length || data[num] != 170 || data[num + 1] != 102)
					{
						break;
					}
					Buffer.BlockCopy(data, num + 2, array, 0, 4);
					num2 = BitConverter.ToInt16(array, 0);
				}
			}
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "ATServer出错：" + ex.Message);
			return null;
		}
	}

	public byte[] DataReceived(byte[] data, int length)
	{
		try
		{
			if (length != 20 && length != 19)
			{
				int num = data[20];
				playerS playerS2 = players(data, 36);
				if (playerS2 != null)
				{
					switch (num)
					{
					case 0:
						if (playerS2.物理攻击人物 != null)
						{
							if ((playerS2.物理攻击人物.ThreadState.ToString() == "Stopped" || playerS2.物理攻击人物.ThreadState.ToString() == "Unstarted") && !playerS2.物理攻击人物.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter13 = new ThreadParameter(data);
								playerS2.启动物理攻击人物 = 物理攻击人物包;
								playerS2.物理攻击人物 = new Thread(playerS2.启动物理攻击人物);
								playerS2.物理攻击人物.Start(parameter13);
							}
							else
							{
								Task.Factory.StartNew(物理攻击人物包, new ThreadParameter(data));
							}
						}
						else
						{
							ThreadParameter parameter14 = new ThreadParameter(data);
							playerS2.启动物理攻击人物 = 物理攻击人物包;
							playerS2.物理攻击人物 = new Thread(playerS2.启动物理攻击人物);
							playerS2.物理攻击人物.IsBackground = true;
							playerS2.物理攻击人物.Start(parameter14);
						}
						break;
					case 1:
						if (playerS2.物理攻击怪物 != null)
						{
							if ((playerS2.物理攻击怪物.ThreadState.ToString() == "Stopped" || playerS2.物理攻击怪物.ThreadState.ToString() == "Unstarted") && !playerS2.物理攻击怪物.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter15 = new ThreadParameter(data);
								playerS2.启动物理攻击怪物 = 物理攻击怪物包;
								playerS2.物理攻击怪物 = new Thread(playerS2.启动物理攻击怪物);
								playerS2.物理攻击怪物.Start(parameter15);
							}
							else if (playerS2.物理攻击怪物1 == null)
							{
								ThreadParameter parameter16 = new ThreadParameter(data);
								playerS2.启动物理攻击怪物1 = 物理攻击怪物包1;
								playerS2.物理攻击怪物1 = new Thread(playerS2.启动物理攻击怪物1);
								playerS2.物理攻击怪物1.IsBackground = true;
								playerS2.物理攻击怪物1.Start(parameter16);
							}
							else if ((playerS2.物理攻击怪物1.ThreadState.ToString() == "Stopped" || playerS2.物理攻击怪物1.ThreadState.ToString() == "Unstarted") && !playerS2.物理攻击怪物1.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter17 = new ThreadParameter(data);
								playerS2.启动物理攻击怪物1 = 物理攻击怪物包1;
								playerS2.物理攻击怪物1 = new Thread(playerS2.启动物理攻击怪物1);
								playerS2.物理攻击怪物1.Start(parameter17);
							}
							else
							{
								Task.Factory.StartNew(物理攻击怪物包, new ThreadParameter(data));
							}
						}
						else
						{
							ThreadParameter parameter18 = new ThreadParameter(data);
							playerS2.启动物理攻击怪物 = 物理攻击怪物包;
							playerS2.物理攻击怪物 = new Thread(playerS2.启动物理攻击怪物);
							playerS2.物理攻击怪物.IsBackground = true;
							playerS2.物理攻击怪物.Start(parameter18);
						}
						break;
					case 2:
						if (playerS2.魔法攻击人物 != null)
						{
							if ((playerS2.魔法攻击人物.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击人物.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击人物.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter11 = new ThreadParameter(data, playerS2);
								playerS2.启动魔法攻击人物 = 魔法攻击人物包;
								playerS2.魔法攻击人物 = new Thread(playerS2.启动魔法攻击人物);
								playerS2.魔法攻击人物.Start(parameter11);
							}
							else
							{
								Task.Factory.StartNew(魔法攻击人物包, new ThreadParameter(data, playerS2));
							}
						}
						else
						{
							ThreadParameter parameter12 = new ThreadParameter(data, playerS2);
							playerS2.启动魔法攻击人物 = 魔法攻击人物包;
							playerS2.魔法攻击人物 = new Thread(playerS2.启动魔法攻击人物);
							playerS2.魔法攻击人物.IsBackground = true;
							playerS2.魔法攻击人物.Start(parameter12);
						}
						break;
					case 3:
						if (playerS2.魔法攻击怪物 != null)
						{
							if ((playerS2.魔法攻击怪物.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击怪物.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击怪物.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物 = 魔法攻击怪物包;
								playerS2.魔法攻击怪物 = new Thread(playerS2.启动魔法攻击怪物);
								playerS2.魔法攻击怪物.Start(parameter);
							}
							else if (playerS2.魔法攻击怪物1 == null)
							{
								ThreadParameter parameter2 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物1 = 魔法攻击怪物包1;
								playerS2.魔法攻击怪物1 = new Thread(playerS2.启动魔法攻击怪物1);
								playerS2.魔法攻击怪物1.IsBackground = true;
								playerS2.魔法攻击怪物1.Start(parameter2);
							}
							else if ((playerS2.魔法攻击怪物1.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击怪物1.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击怪物1.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter3 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物1 = 魔法攻击怪物包1;
								playerS2.魔法攻击怪物1 = new Thread(playerS2.启动魔法攻击怪物1);
								playerS2.魔法攻击怪物1.Start(parameter3);
							}
							else if (playerS2.魔法攻击怪物2 == null)
							{
								ThreadParameter parameter4 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物2 = 魔法攻击怪物包2;
								playerS2.魔法攻击怪物2 = new Thread(playerS2.启动魔法攻击怪物2);
								playerS2.魔法攻击怪物2.IsBackground = true;
								playerS2.魔法攻击怪物2.Start(parameter4);
							}
							else if ((playerS2.魔法攻击怪物2.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击怪物2.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击怪物2.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter5 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物2 = 魔法攻击怪物包2;
								playerS2.魔法攻击怪物2 = new Thread(playerS2.启动魔法攻击怪物2);
								playerS2.魔法攻击怪物2.Start(parameter5);
							}
							else if (playerS2.魔法攻击怪物3 == null)
							{
								ThreadParameter parameter6 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物3 = 魔法攻击怪物包3;
								playerS2.魔法攻击怪物3 = new Thread(playerS2.启动魔法攻击怪物3);
								playerS2.魔法攻击怪物3.IsBackground = true;
								playerS2.魔法攻击怪物3.Start(parameter6);
							}
							else if ((playerS2.魔法攻击怪物3.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击怪物3.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击怪物3.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter7 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物3 = 魔法攻击怪物包3;
								playerS2.魔法攻击怪物3 = new Thread(playerS2.启动魔法攻击怪物3);
								playerS2.魔法攻击怪物3.Start(parameter7);
							}
							else if (playerS2.魔法攻击怪物4 == null)
							{
								ThreadParameter parameter8 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物4 = 魔法攻击怪物包4;
								playerS2.魔法攻击怪物4 = new Thread(playerS2.启动魔法攻击怪物4);
								playerS2.魔法攻击怪物4.IsBackground = true;
								playerS2.魔法攻击怪物4.Start(parameter8);
							}
							else if ((playerS2.魔法攻击怪物4.ThreadState.ToString() == "Stopped" || playerS2.魔法攻击怪物4.ThreadState.ToString() == "Unstarted") && !playerS2.魔法攻击怪物4.ThreadState.ToString().Contains("StopRequested"))
							{
								ThreadParameter parameter9 = new ThreadParameter(data);
								playerS2.启动魔法攻击怪物4 = 魔法攻击怪物包4;
								playerS2.魔法攻击怪物4 = new Thread(playerS2.启动魔法攻击怪物4);
								playerS2.魔法攻击怪物4.Start(parameter9);
							}
							else
							{
								Task.Factory.StartNew(魔法攻击怪物包5, new ThreadParameter(data));
							}
						}
						else
						{
							ThreadParameter parameter10 = new ThreadParameter(data);
							playerS2.启动魔法攻击怪物 = 魔法攻击怪物包;
							playerS2.魔法攻击怪物 = new Thread(playerS2.启动魔法攻击怪物);
							playerS2.魔法攻击怪物.IsBackground = true;
							playerS2.魔法攻击怪物.Start(parameter10);
						}
						break;
					case 4:
						playerS2.是否卡技能 = true;
						break;
					case 5:
						playerS2.是否卡技能 = false;
						break;
					default:
						return null;
					}
				}
				return null;
			}
			try
			{
				string[] array = Encoding.Default.GetString(data).Split('|');
				AtSend = new AtServerConnect(int.Parse(array[1]));
				AtSend.Sestup(int.Parse(array[1]));
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "ATServer 出错1：" + ex.Message);
			}
			return null;
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "攻击伺服器包出错：" + ex2.Message);
			return null;
		}
	}

	public playerS players(byte[] data, int lenght)
	{
		try
		{
			int num = 12;
			for (int i = 0; i < 12; i++)
			{
				if (data[22 + i] == 0)
				{
					num = i;
					break;
				}
			}
			byte[] array = new byte[num];
			Buffer.BlockCopy(data, 22, array, 0, array.Length);
			string @string = Encoding.Default.GetString(array);
			// 2025-0618 EVIAS 使用新的玩家管理器
			return HelperTools.ConcurrentPlayerManager.GetPlayer(@string);
		}
		catch
		{
			return null;
		}
	}
}

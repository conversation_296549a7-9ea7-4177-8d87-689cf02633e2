using System;
using System.Data;
using System.Data.SqlClient;

namespace loginServer.DbClss;

public class DBA
{
	public static string getstrConnection(string db)
	{
		try
		{
			if (db == null)
			{
				db = "rxjhaccount";
			}
			World.sql = (World.Db.TryGetValue(db, out var value) ? value.SqlConnect : null);
			return World.Db.TryGetValue(db, out value) ? value.SqlConnect : null;
		}
		catch
		{
			return null;
		}
	}

	public static int ExeSqlCommand(string sqlCommand)
	{
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "DBA数据层_错误" + ex.Message);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string sqlCommand, string server)
	{
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "DBA数据层_错误" + ex.Message);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static DataTable GetDBToDataTable(string sqlCommand)
	{
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = new SqlCommand(sqlCommand, sqlConnection));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch
			{
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "DBA数据层_错误" + ex.Message);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string sqlCommand, string server)
	{
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = new SqlCommand(sqlCommand, sqlConnection));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch
			{
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "DBA数据层_错误" + ex.Message);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static int RunProc(SqlConnection conn, string procName, SqlParameter[] prams)
	{
		int result = -1;
		try
		{
			conn.Open();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "SqlDBA数据层_错误3" + ex.Message);
			return result;
		}
		SqlCommand sqlCommand = CreateCommand(conn, procName, prams);
		try
		{
			return sqlCommand.ExecuteNonQuery();
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(100, "SqlDBA数据层_错误4" + ex2.Message);
			sqlCommand.Parameters.Clear();
			return result;
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static SqlCommand CreateCommand(SqlConnection conn, string procName, SqlParameter[] prams)
	{
		SqlCommand sqlCommand = new SqlCommand(procName, conn);
		sqlCommand.CommandType = CommandType.StoredProcedure;
		sqlCommand.CommandTimeout = 180;
		if (prams != null)
		{
			foreach (SqlParameter value in prams)
			{
				sqlCommand.Parameters.Add(value);
			}
		}
		sqlCommand.Parameters.Add(new SqlParameter("ReturnValue", SqlDbType.Int, 4, ParameterDirection.ReturnValue, isNullable: false, 0, 0, string.Empty, DataRowVersion.Default, null));
		return sqlCommand;
	}

	public static SqlParameter MakeInParam(string ParamName, SqlDbType DbType, int Size, object Value)
	{
		return MakeParam(ParamName, DbType, Size, ParameterDirection.Input, Value);
	}

	public static SqlParameter MakeParam(string ParamName, SqlDbType DbType, int Size, ParameterDirection Direction, object Value)
	{
		SqlParameter sqlParameter = ((Size <= 0) ? new SqlParameter(ParamName, DbType) : new SqlParameter(ParamName, DbType, Size));
		sqlParameter.Direction = Direction;
		if (Direction != ParameterDirection.Output || Value != null)
		{
			sqlParameter.Value = Value;
		}
		return sqlParameter;
	}
}

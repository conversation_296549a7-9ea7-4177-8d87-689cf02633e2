using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Timers;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class NpcClass : IDisposable
{
    private static readonly Random MovementRandom = new Random(); //EVIAS

    private static readonly PlayGjClass asfd = new PlayGjClass();

	private readonly object object_0 = new object();

	public Reverser<PlayGjClass> reverser = new Reverser<PlayGjClass>(asfd.GetType(), "攻击数量", ReverserInfo.Direction.DESC);

	public List<PlayGjClass> PlayGjList = new List<PlayGjClass>();

	public ConcurrentDictionary<int, Players> templayer = new ConcurrentDictionary<int, Players>();

	private ConcurrentDictionary<int, Players> PlayList;

	public System.Timers.Timer 自动移动;

	public System.Timers.Timer 自动攻击;

	public System.Timers.Timer 自动复活;

	private static Random Ran;

	public ConcurrentDictionary<int, 异常状态类> 异常状态;

	private 灵兽类 _PlayCw;

	private float _FLD_FACE1;

	private float _FLD_FACE2;

	private int _IsNpc;

	private string _Name;

	private int _FLD_INDEX;

	private int _FLD_PID;

	private double _FLD_AT;

	private double _FLD_JSAT;

	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private float _Rxjh_cs_X;

	private float _Rxjh_cs_Y;

	private float _Rxjh_cs_Z;

	private int _Rxjh_Map;

	private int _Rxjh_Exp;

	private int _Max_Rxjh_HP;

	private int _Rxjh_HP;

	private int _阎王爆累计伤害;

	private int _Level;

	private double _FLD_DF;

	private double _FLD_JSDF;

	private int _FLD_AUTO;

	private int _FLD_BOSS;

	private int _SFYJ;

	private int _FLD_NEWTIME;

	private bool _NPC死亡;

	private bool _一次性怪;

	private bool _怪物阎王爆;

	public int 怪物数字;

	private bool _是否绝命技死亡;

	private double _绝命技死亡爆率加成;

	private double _绝命技死亡经验加成;

	private double _绝命技死亡历练加成;

	private double _绝命技死亡金钱加成;

	private static List<int> 有技能的BOSS列表 = new List<int> { 15419, 15420, 15421, 15422, 15423, 15424, 16403, 16404 };

	public 灵兽类 PlayCw
	{
		get
		{
			return _PlayCw;
		}
		set
		{
			_PlayCw = value;
		}
	}

	public int PlayerWid
	{
		get
		{
			if (PlayGjList.Count <= 0)
			{
				return 0;
			}
			try
			{
				PlayGjList.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "攻击数量", ReverserInfo.Direction.DESC));
				return PlayGjList[0].攻击全服ID;
			}
			catch (Exception ex)
			{
				// 2025-0617 EVIAS 改进异常处理
				RxjhClass.HandleGameException(ex, null, "获取PlayerWid", $"NPC: {Name}[{FLD_INDEX}], 攻击列表数量: {PlayGjList?.Count}");
				return 0;
			}
		}
	}

	public int BossPlayerWid
	{
		get
		{
			if (PlayGjList.Count <= 0)
			{
				return 0;
			}
			try
			{
				int num = 0;
				int index;
				while (true)
				{
					index = RNG.Next(0, PlayGjList.Count - 1);
					if (PlayGjList[index].攻击数量 >= 100000)
					{
						break;
					}
					num++;
					if (num >= PlayGjList.Count)
					{
						PlayGjList.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "攻击数量", ReverserInfo.Direction.DESC));
						return PlayGjList[0].攻击全服ID;
					}
				}
				return PlayGjList[index].攻击全服ID;
			}
			catch (Exception)
			{
				return 0;
			}
		}
	}

	public float FLD_FACE1
	{
		get
		{
			return _FLD_FACE1;
		}
		set
		{
			_FLD_FACE1 = value;
		}
	}

	public float FLD_FACE2
	{
		get
		{
			return _FLD_FACE2;
		}
		set
		{
			_FLD_FACE2 = value;
		}
	}

	public int IsNpc
	{
		get
		{
			return _IsNpc;
		}
		set
		{
			_IsNpc = value;
		}
	}

	public string Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = value;
		}
	}

	public int FLD_INDEX
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_AT
	{
		get
		{
			return _FLD_AT;
		}
		set
		{
			_FLD_AT = value;
		}
	}

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public float Rxjh_cs_X
	{
		get
		{
			return _Rxjh_cs_X;
		}
		set
		{
			_Rxjh_cs_X = value;
		}
	}

	public float Rxjh_cs_Y
	{
		get
		{
			return _Rxjh_cs_Y;
		}
		set
		{
			_Rxjh_cs_Y = value;
		}
	}

	public float Rxjh_cs_Z
	{
		get
		{
			return _Rxjh_cs_Z;
		}
		set
		{
			_Rxjh_cs_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public int Rxjh_Exp
	{
		get
		{
			return _Rxjh_Exp;
		}
		set
		{
			_Rxjh_Exp = value;
		}
	}

	public int Max_Rxjh_HP
	{
		get
		{
			return _Max_Rxjh_HP;
		}
		set
		{
			_Max_Rxjh_HP = value;
		}
	}

	public int Rxjh_HP
	{
		get
		{
			return _Rxjh_HP;
		}
		set
		{
			_Rxjh_HP = value;
		}
	}

	public int 阎王爆累计伤害
	{
		get
		{
			return _阎王爆累计伤害;
		}
		set
		{
			_阎王爆累计伤害 = value;
		}
	}

	public int Level
	{
		get
		{
			return _Level;
		}
		set
		{
			_Level = value;
		}
	}

	public double FLD_DF
	{
		get
		{
			return _FLD_DF;
		}
		set
		{
			_FLD_DF = value;
		}
	}

	public double FLD_JSAT
	{
		get
		{
			return _FLD_JSAT;
		}
		set
		{
			_FLD_JSAT = value;
		}
	}

	public double FLD_JSDF
	{
		get
		{
			return _FLD_JSDF;
		}
		set
		{
			_FLD_JSDF = value;
		}
	}

	public int FLD_AUTO
	{
		get
		{
			return _FLD_AUTO;
		}
		set
		{
			_FLD_AUTO = value;
		}
	}

	public int FLD_BOSS
	{
		get
		{
			return _FLD_BOSS;
		}
		set
		{
			_FLD_BOSS = value;
		}
	}

	public int FLD_SFYJ
	{
		get
		{
			return _SFYJ;
		}
		set
		{
			_SFYJ = value;
		}
	}

	public int FLD_NEWTIME
	{
		get
		{
			return _FLD_NEWTIME;
		}
		set
		{
			_FLD_NEWTIME = value;
		}
	}

	public bool NPC死亡
	{
		get
		{
			return _NPC死亡;
		}
		set
		{
			_NPC死亡 = value;
		}
	}

	public bool 一次性怪
	{
		get
		{
			return _一次性怪;
		}
		set
		{
			_一次性怪 = value;
		}
	}

	public bool 怪物阎王爆
	{
		get
		{
			return _怪物阎王爆;
		}
		set
		{
			_怪物阎王爆 = value;
		}
	}

	public bool 是否绝命技死亡
	{
		get
		{
			return _是否绝命技死亡;
		}
		set
		{
			_是否绝命技死亡 = value;
		}
	}

	public double 绝命技死亡爆率加成
	{
		get
		{
			return _绝命技死亡爆率加成;
		}
		set
		{
			_绝命技死亡爆率加成 = value;
		}
	}

	public double 绝命技死亡经验加成
	{
		get
		{
			return _绝命技死亡经验加成;
		}
		set
		{
			_绝命技死亡经验加成 = value;
		}
	}

	public double 绝命技死亡历练加成
	{
		get
		{
			return _绝命技死亡历练加成;
		}
		set
		{
			_绝命技死亡历练加成 = value;
		}
	}

	public double 绝命技死亡金钱加成
	{
		get
		{
			return _绝命技死亡金钱加成;
		}
		set
		{
			_绝命技死亡金钱加成 = value;
		}
	}

	public void 发送怪物当前血量()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "NpcClass_发送怪物当前血量");
		}
		string hex = "AA5512000F272E210C0012272E4000A86100A02E630055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_PID), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_HP), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(Max_Rxjh_HP), 0, array, 18, 4);
		广播数据(array, array.Length);
	}

	public void 发送阎王爆攻击数据(int 掉落血量)
	{
		byte[] array = Converter.hexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(掉落血量), 0, array, 18, 4);
		广播数据(array, array.Length);
	}

	public void 发送复仇显示伤害血量(int 掉落血量)
	{
		byte[] array = Converter.hexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(掉落血量), 0, array, 18, 4);
		广播数据(array, array.Length);
	}

	public void 触发阎王爆(Players players, int ID)
	{
		try
		{
			if (players == null || !查找范围玩家(400, players))
			{
				return;
			}
			int num = (int)((double)阎王爆累计伤害 * (1.0 + players.神女尸毒爆发)) / World.阎王爆伤害降低百分比;
			if (num >= 1)
			{
				int 阎王爆爆炸距离 = World.阎王爆爆炸距离;
				阎王爆爆炸距离 += (int)players.神女黑花漫开;
				List<NpcClass> list = 阎王爆查找范围Npc(players, 阎王爆爆炸距离);
				foreach (NpcClass item in list)
				{
					if (num <= 0)
					{
						num = 1;
					}
					if (item.Rxjh_HP > num)
					{
						item.Play_Add(players, num);
						item.Rxjh_HP -= num;
					}
					else
					{
						item.Play_Add(players, item.Rxjh_HP);
						item.Rxjh_HP = 0;
					}
					发送阎王爆攻击数据(num);
					if (item.Rxjh_HP <= 0 && !item.NPC死亡)
					{
						item.发送死亡数据(ID);
					}
					item.阎王爆累计伤害 = 0;
				}
			}
			怪物阎王爆 = false;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "触发阎王爆-出错：" + ex);
		}
	}

	public List<NpcClass> 阎王爆查找范围Npc(Players player, int 范围)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value in player.NpcList.Values)
			{
				if (!value.NPC死亡 && value.IsNpc == 0 && 查找范围Npc(范围, value))
				{
					list.Add(value);
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, player, "阎王爆查找范围Npc", $"范围: {范围}");
			return null;
		}
	}

	public void 吸怪清理(Players payer)
	{
		try
		{
			if (payer == null)
			{
				return;
			}
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value2 in payer.怪物攻击列表.Values)
			{
				list.Add(value2);
			}
			for (int i = 0; i < list.Count; i++)
			{
				if (list[i].怪距离检测(payer))
				{
					payer.怪物攻击列表.TryRemove(list[i].FLD_INDEX, out var _);
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, payer, "吸怪清理", "清理怪物攻击列表");
		}
	}

	public void npc_Add(Players payer)
	{
		try
		{
			if (payer != null)
			{
				if (payer.怪物攻击列表 == null)
				{
					payer.怪物攻击列表 = new ConcurrentDictionary<int, NpcClass>();
				}
				if (!payer.怪物攻击列表.ContainsKey(FLD_INDEX))
				{
					payer.怪物攻击列表.TryAdd(FLD_INDEX, this);
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, payer, "引怪", $"NPC: {Name}[{FLD_INDEX}]");
			Form1.WriteLine(1, "引怪 出错：" + ex);
		}
	}

	public bool 怪距离检测(Players npcTemp)
	{
		float num = Rxjh_X - npcTemp.人物坐标_X;
		float num2 = Rxjh_Y - npcTemp.人物坐标_Y;
		float num3 = (float)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2);
		if (num3 >= (float)World.吸怪距离 || Rxjh_Map != npcTemp.人物坐标_地图 || NPC死亡 || Rxjh_HP <= 0)
		{
			return true;
		}
		return false;
	}

	public bool 玩家是否在指定范围内(int 查找范围, Players players_0)
	{
		if (Rxjh_Map != 43001 && (long)players_0.人物_HP <= 0L)
		{
			return false;
		}
		if (players_0.人物坐标_地图 == Rxjh_Map)
		{
			if (players_0.人物坐标_地图 == 7301 || players_0.人物坐标_地图 == 43001)
			{
				查找范围 = 1000;
			}
			float num = players_0.人物坐标_X - Rxjh_X;
			float num2 = players_0.人物坐标_Y - Rxjh_Y;
			float num3 = (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2);
			return num3 <= (float)查找范围;
		}
		return false;
	}

	public NpcClass()
	{
		Ran = new Random(DateTime.Now.Millisecond);
		PlayList = new ConcurrentDictionary<int, Players>();
		double interval = Ran.Next(5000, 15000);
		自动移动 = new System.Timers.Timer(interval);
		自动移动.Elapsed += 自动移动事件;
		自动移动.AutoReset = true;
		自动移动.Enabled = true;
		自动攻击 = new System.Timers.Timer(1000.0);
		自动攻击.Elapsed += 自动攻击事件;
		自动攻击.AutoReset = true;
		异常状态 = new ConcurrentDictionary<int, 异常状态类>();
		是否绝命技死亡 = false;
		绝命技死亡爆率加成 = 0.0;
		绝命技死亡经验加成 = 0.0;
		绝命技死亡历练加成 = 0.0;
		绝命技死亡金钱加成 = 0.0;
		FLD_JSAT = 0.0;
		FLD_JSDF = 0.0;
	}

	public void PlayList_Add(Players Play)
	{
		if (!Play.Client.挂机 && !Contains(Play))
		{
			PlayList.TryAdd(Play.人物全服ID, Play);
		}
	}

	public void PlayList_Remove(Players payer)
	{
		if (Contains(payer))
		{
			PlayList.TryRemove(payer.人物全服ID, out var _);
		}
	}

	public bool Contains(Players payer)
	{
		Players value;
		return PlayList != null && PlayList.Count != 0 && PlayList.TryGetValue(payer.人物全服ID, out value);
	}

	public void Play_Add(Players payer, int 血量)
	{
		try
		{
			if (血量 < 0 || IsNpc == 1)
			{
				return;
			}
			using (new Lock(object_0, "Play_Add"))
			{
				for (int i = 0; i < PlayGjList.Count; i++)
				{
					if (i >= PlayGjList.Count)
					{
						continue;
					}
					PlayGjClass playGjClass = PlayGjList[i];
					if (playGjClass != null && playGjClass.攻击全服ID == payer.人物全服ID)
					{
						playGjClass.攻击数量++;
						playGjClass.攻击血量 += 血量;
						if (playGjClass.攻击血量 >= Max_Rxjh_HP)
						{
							playGjClass.攻击血量 = Max_Rxjh_HP;
						}
						return;
					}
				}
				PlayGjClass playGjClass2 = new PlayGjClass();
				playGjClass2.攻击数量 = 1;
				playGjClass2.攻击血量 = 血量;
				if (playGjClass2.攻击血量 >= Max_Rxjh_HP)
				{
					playGjClass2.攻击血量 = Max_Rxjh_HP;
				}
				playGjClass2.攻击全服ID = payer.人物全服ID;
				PlayGjList.Add(playGjClass2);
				if (PlayGjList[0].攻击全服ID == payer.人物全服ID && playGjClass2.攻击数量 > 0 && 查找NPC范围距离(World.怪物打人距离, payer))
				{
					发送移动数据(payer.人物坐标_X, payer.人物坐标_Y, 10, 2);
				}
			}
		}
		catch
		{
		}
	}

	public void Play_null()
	{
		try
		{
			if (PlayGjList != null && PlayGjList.Count > 0)
			{
				PlayGjList.Clear();
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "Play_null清理", $"NPC: {Name}[{FLD_INDEX}]");
		}
	}

	public void 获取范围玩家发送增加数据包()
	{
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (查找范围玩家(400, value))
				{
					value.获取复查范围Npc();
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "获取范围玩家发送增加数据包", $"NPC: {Name}[{FLD_INDEX}]");
			Form1.WriteLine(1, "获取范围玩家发送地面增加Npc数据包   出错：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			MapClass.delnpc(Rxjh_Map, FLD_INDEX);
			if (自动攻击 != null)
			{
				自动攻击.Enabled = false;
				自动攻击.Close();
				自动攻击.Dispose();
			}
			if (自动移动 != null)
			{
                自动移动.Elapsed -= 自动移动事件; // 解绑 EVIAS 0512
                自动移动.Enabled = false;
				自动移动.Close();
				自动移动.Dispose();
                自动移动 = null; // 置空  EVIAS 0512
            }
			if (自动复活 != null)
			{
				自动复活.Enabled = false;
				自动复活.Close();
				自动复活.Dispose();
			}
			Play_null();
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			获取范围玩家发送消失数据包();
			if (templayer != null)
			{
				templayer.Clear();
			}
			if (PlayList != null)
			{
				PlayList.Clear();
				PlayList = null;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "NPC   关闭数据Dispose()   出错：" + ex);
		}
	}

	public void 获取范围玩家发送消失数据包()
	{
		if (PlayList == null)
		{
			return;
		}
		int num = 0;
		try
		{
			foreach (Players value2 in PlayList.Values)
			{
				num = 1;
				if (value2.Client != null)
				{
					num = 2;
					更新NPC死亡数据(value2);
					更新NPC删除数据(value2);
					if (value2.NpcList.ContainsKey(FLD_INDEX))
					{
						value2.NpcList.TryRemove(FLD_INDEX, out var _);
					}
					num = 3;
				}
			}
			num = 4;
			if (PlayList != null)
			{
				num = 5;
				PlayList.Clear();
				num = 6;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "NPC   获取范围玩家发送消失数据包3   出错：num=" + num + ex);
		}
	}

	public bool ContainsKeyInAbnormalState(int Key)
	{
		异常状态类 value;
		return 异常状态 != null && 异常状态.Count != 0 && 异常状态.TryGetValue(Key, out value);
	}

	public void getbl()
	{
		if (PlayList.Count > 0)
		{
			Form1.WriteLine(2, _Name + "   人物：" + PlayList.Count);
			if (自动移动 != null)
			{
				Form1.WriteLine(2, _Name + "   人物_自动移动：" + 自动移动.Enabled);
			}
			if (自动攻击 != null)
			{
				Form1.WriteLine(2, _Name + "   人物_自动攻击：" + 自动攻击.Enabled);
			}
			if (自动复活 != null)
			{
				Form1.WriteLine(2, _Name + "   人物_自动复活：" + 自动复活.Enabled);
			}
		}
		if (PlayGjList.Count > 0)
		{
			Form1.WriteLine(2, _Name + "   攻击：" + PlayGjList.Count);
		}
		if (PlayCw != null)
		{
			Form1.WriteLine(2, _Name + "   灵兽：" + PlayCw.Name + "主人名：" + PlayCw.ZrName);
		}
	}

    internal void 自动移动事件(object sender, ElapsedEventArgs e) //EVIAS  优化0427
    {
        if (World.jlMsg == 1)
        {
            Form1.WriteLine(0, "自动移动事件");
        }

        try
        {
            if (Rxjh_Map == 43001 && FLD_PID != 16555)
            {
                return;
            }

            int interval = MovementRandom.Next(5000, 15000);

            if (自动移动 == null)
            {
                自动移动 = new System.Timers.Timer(interval)
                {
                    AutoReset = true,
                    Enabled = true
                };
                自动移动.Elapsed += 自动移动事件;
            }
            else
            {
                自动移动.Interval = interval;
            }

            if (FLD_PID == 5 || FLD_PID == 3)
            {
                发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 20, 0);
                return;
            }

            if (IsNpc != 0)
            {
                自动移动.Enabled = false;
                return;
            }

            if (!NPC死亡 && FLD_AT > 0.0 && PlayList.Count >= 1)
            {
                if (FLD_AUTO == 1 && 获取范围玩家())
                {
                    自动移动.Enabled = false;
                    自动攻击.Enabled = true;
                }
                else if (FLD_PID != 16431 && FLD_PID != 16430 && FLD_PID != 16435)
                {
                    int speed = (FLD_PID != 5) ? 50 : 20;
                    发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, speed, 0);
                }
                else
                {
                    自动移动.Enabled = false;
                    自动攻击.Enabled = false;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, $"自动移动事件出错：{ex}");
        }
    }

    private void 自动复活事件(object source, ElapsedEventArgs e)
	{
		try
		{
			if (IsNpc == 1)
			{
				自动复活.Enabled = false;
				return;
			}
			自动移动.Enabled = true;
			if (!NPC死亡)
			{
				return;
			}
			if (Rxjh_Map == 43001)
			{
				if (讨伐战系统.讨伐副本占领者 == 0)
				{
					更新NPC复活数据();
				}
				else if (FLD_PID != 16555)
				{
					地狱火龙副本状态效果消失(是否玩家击杀: false);
					if (自动复活 != null)
					{
						自动复活.Enabled = false;
						自动复活.Close();
						自动复活.Dispose();
						自动复活 = null;
					}
				}
			}
			else
			{
				更新NPC复活数据();
			}
		}
		catch (Exception ex)
		{
			if (自动复活 != null)
			{
				自动复活.Enabled = false;
				自动复活.Close();
				自动复活.Dispose();
				自动复活 = null;
			}
			Form1.WriteLine(1, "自动复活事件1   出错：" + ex);
		}
		finally
		{
			if (自动复活 != null)
			{
				自动复活.Enabled = false;
				自动复活.Close();
				自动复活.Dispose();
				自动复活 = null;
			}
		}
	}

    internal void 自动攻击事件(object source, ElapsedEventArgs e) //EVIAS internal
    {
		int num = 0;
		try
		{
			num = 1;
			if (Rxjh_Map == 43001 && FLD_PID != 16555 && FLD_PID != 16556)
			{
				return;
			}
			num = 2;
			if (IsNpc != 0)
			{
				自动攻击.Enabled = false;
				return;
			}
			if (Rxjh_HP < 0)
			{
				自动攻击.Enabled = false;
				return;
			}
			num = 3;
			if (FLD_AT <= 0.0)
			{
				return;
			}
			int num2 = (int)(FLD_AT * (World.怪物攻击百分比 - FLD_JSAT));
			Random random = new Random(DateTime.Now.Millisecond);
			num = 5;
			int num3 = random.Next(num2 - 8, num2 + 8);
			if (PlayList.TryGetValue(PlayerWid, out var value))
			{
				switch (value.Player_Zx)
				{
				case 1:
					if (FLD_PID == 15450 || FLD_PID == 15451 || FLD_PID == 15491 || FLD_PID == 15492)
					{
						return;
					}
					break;
				case 2:
					if (FLD_PID == 15452 || FLD_PID == 15453 || FLD_PID == 15493 || FLD_PID == 15494)
					{
						return;
					}
					break;
				}
				if (value.人物_HP > 0 && !value.Player死亡 && value.GM模式 != 19 && 查找NPC范围距离(World.怪物打人距离, value))
				{
					int num4 = 28;
					double num5 = value.FLD_人物基本_防御 + value.总对怪防御力;
					num = 12;
					if (!value.检查毒蛇出洞状态() && value.Player_Job == 12 && value.牢不可破 > 0.0)
					{
						value.衣服属性提升 = 0;
						if (value.牢不可破 >= (double)RNG.Next(1, 100))
						{
							num5 += num5 * ((double)value.装备栏已穿装备[0].物品属性阶段数 * 0.005 * 2.0);
							value.衣服属性提升 = 2;
							value.显示大字(value.人物全服ID, 1010);
						}
					}
					num = 13;
					bool flag = false;
					if (value.FLD_装备_追加_降低百分比攻击 > 0.0)
					{
						num3 = (int)((double)num3 * (1.0 - value.FLD_装备_追加_降低百分比攻击));
					}
					if (Level >= 60)
					{
						switch (RNG.Next(0, 10))
						{
						case 1:
							num4 = 28;
							break;
						case 2:
							num4 = 29;
							break;
						case 4:
							num4 = 29;
							break;
						}
						if (num4 == 29)
						{
							num3 = (int)((double)num3 * 1.2);
						}
					}
					num = 14;
					if (value.中级附魂_移星 != 0 && (double)RNG.Next(1, 100) <= (double)value.中级附魂_移星)
					{
						num3 = 0;
					}
					if (value.Player_Job == 3 && (double)RNG.Next(1, 100) <= value.枪_转攻为守)
					{
						value.显示大字(value.人物全服ID, 130);
						num5 += (double)value.FLD_攻击 / 2.0;
					}
					if (value.Player_Job == 12 && (double)RNG.Next(1, 100) <= value.卢_转攻为守)
					{
						value.显示大字(value.人物全服ID, 130);
						num5 += (double)value.FLD_攻击 / 2.0;
					}
					if (value.Player_Job == 10)
					{
						if ((double)RNG.Next(1, 100) <= value.拳师_转攻为守)
						{
							value.显示大字(value.人物全服ID, 130);
							num5 += (double)value.FLD_攻击 / 2.0;
						}
						if (num4 == 29 && (double)RNG.Next(1, 100) <= value.拳师_金刚不坏)
						{
							value.显示大字(value.人物全服ID, 554);
							num3 = (int)((double)num3 * 0.1);
						}
						if ((double)RNG.Next(1, 100) <= value.升天五式_不死之躯)
						{
							value.显示大字(value.人物全服ID, 1021);
							num3 = 0;
						}
					}
					if (value.中级附魂_护体 != 0 && RNG.Next(1, 100) <= value.中级附魂_护体)
					{
						value.显示大字(value.人物全服ID, 406);
						value.人物_MP += num3;
						value.更新HP_MP_SP();
						num3 = 0;
						num = 15;
					}
					int num6 = ((!((double)num3 > num5)) ? 1 : (num3 - (int)num5));
					if (查找范围玩家(20, value))
					{
						if (FLD_PID == World.世界BOSS怪物ID && World.世界boss != null && (double)RNG.Next(1, 100) <= 10.0)
						{
							if (value.异常状态 != null)
							{
								if (!value.GetAbnormalState(17))
								{
									异常状态类 value2 = new 异常状态类(value, 2000, 17, 0.0);
									value.异常状态.TryAdd(17, value2);
									value.人物锁定 = true;
								}
							}
							else
							{
								value.异常状态 = new ConcurrentDictionary<int, 异常状态类>();
								异常状态类 value3 = new 异常状态类(value, 2000, 17, 0.0);
								value.异常状态.TryAdd(17, value3);
								value.人物锁定 = true;
							}
							num = 16;
						}
						if (value.中级附魂_混元 != 0 && RNG.Next(1, 100) <= value.中级附魂_混元)
						{
							num6 = (int)((double)num6 * 0.5);
						}
						if (value.FLD_装备_降低_伤害值 > 0.0)
						{
							num6 -= (int)value.FLD_装备_降低_伤害值;
						}
						if (value.Player_Job == 2)
						{
							double num7 = num6;
							if ((double)RNG.Next(1, 110) <= value.剑_升天一气功_护身罡气)
							{
								value.显示大字(value.人物全服ID, 25);
								num6 = (int)(num7 * 0.5);
							}
						}
						else if (value.Player_Job == 5)
						{
							if ((double)RNG.Next(1, 100) <= value.升天一气功_狂风天意 && !value.怒)
							{
								value.人物_SP = value.人物最大_SP + 5;
							}
						}
						else if (value.Player_Job == 6)
						{
							if ((double)RNG.Next(1, 110) <= value.刺_三花聚顶)
							{
								value.刺_连消带打数量 = (double)num6 * value.刺_连消带打;
								num6 = 0;
							}
							if ((double)RNG.Next(1, 110) <= value.刺_升天一气功_夜魔缠身)
							{
								num6 = (int)((double)num6 * 0.7);
								value.显示大字(value.人物全服ID, 370);
							}
							if ((double)RNG.Next(1, 100) <= value.刺_升天二气功_顺水推舟)
							{
								value.加血((int)((double)num6 * 0.2));
								value.显示大字(value.人物全服ID, 371);
							}
							num = 17;
						}
						else if (value.Player_Job == 9)
						{
							double num8 = num6;
							if ((double)RNG.Next(1, 100) <= value.谭_护身罡气)
							{
								value.显示大字(value.人物全服ID, 25);
								num6 = (int)(num8 * 0.5);
							}
							if ((double)RNG.Next(1, 110) <= value.谭_升天三气功_以柔克刚 + value.升天五式_惊涛骇浪)
							{
								value.显示大字(value.人物全服ID, 700);
								num6 = (int)(num8 * (1.0 - value.谭_升天三气功_以柔克刚 * 0.01));
								if (RNG.Next(1, 6) >= 2 && !value.GetAddState(1008001198))
								{
									if (value.追加状态列表 != null && value.GetAddState(1008001198))
									{
										value.追加状态列表[1008001198].时间结束事件();
									}
									追加状态类 追加状态类2 = new 追加状态类(value, 3000, 1008001198, 0);
									value.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
									value.FLD_人物_追加百分比_回避 += 0.1;
									value.状态效果(BitConverter.GetBytes(1008001198), 1, 3000);
									value.更新武功和状态();
								}
							}
						}
						if (num6 <= 10)
						{
							num6 = ((RNG.Next(1, 110) > 70) ? RNG.Next(3, 10) : 0);
						}
						int num9 = 0;
						if (value.Player_Job == 11 && value.梅_障力激活 > 0.0)
						{
							num9 = (int)((double)num6 * (value.梅_障力激活 * 2.0 * 0.01));
							if (num9 > value.人物_AP)
							{
								num9 = value.人物_AP;
							}
							value.人物_AP -= num9;
						}
						int num10 = num6 - num9;
						num = 18;
						发送攻击数据(num10, num4, value.人物全服ID, num9);
						value.人物_HP -= num10;
						if (value.Player_Job == 1 || value.Player_Job == 7)
						{
							if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
							try
							{
								if ((double)RNG.Next(1, 100) <= ((value.Player_Job != 1) ? value.琴师_升天二气功_三潭映月 : value.怪反伤几率) && num10 > 0)
								{
									if (value.Player_Job == 7)
									{
										value.显示大字(value.人物全服ID, 391);
									}
									else
									{
										num = 19;
										发送琴反伤攻击数据(num10, value.人物全服ID);
									}
									if (num10 <= 0)
									{
										num10 = 1;
									}
									if (Rxjh_HP > num10)
									{
										Play_Add(value, num10);
										Rxjh_HP -= num10;
									}
									else
									{
										Play_Add(value, Rxjh_HP);
										Rxjh_HP = 0;
									}
									if (Rxjh_HP <= 0 && !NPC死亡)
									{
										flag = true;
									}
								}
							}
							catch (Exception ex)
							{
								Form1.WriteLine(1, "自动攻击事件[" + num + "]琴师/反伤出错：" + ex);
							}
						}
						else if (value.Player_Job == 2)
						{
							if ((double)RNG.Next(1, 100) <= value.剑_升天三气功_火凤临朝 && value.人物_HP <= 0)
							{
								value.人物_HP = 10;
								value.显示大字(value.人物全服ID, 322);
							}
							if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
						}
						else if (value.Player_Job == 3)
						{
							if (value.枪_狂神降世 != 0.0)
							{
								if (!value.怒)
								{
									value.人物_SP += (int)(3.0 + (double)(value.Player_Level * 2) * value.枪_狂神降世);
								}
							}
							else if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
						}
						else if (value.Player_Job == 6)
						{
							if (value.刺_荆轲之怒 != 0.0)
							{
								value.人物_SP += (int)(3.0 + (double)value.Player_Level * 0.5 * 0.01 * value.刺_荆轲之怒);
							}
							else if ((double)num10 <= num5)
							{
								value.人物_SP++;
							}
							else
							{
								value.人物_SP += 2;
							}
						}
						else if (value.Player_Job == 8)
						{
							if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
							try
							{
								if ((double)RNG.Next(1, 100) <= value.韩_追骨吸元)
								{
									double num11 = (double)num10 * value.韩_追骨吸元 * 0.01;
									if (num11 <= 0.0)
									{
										num11 = 1.0;
									}
									num = 22;
									value.加血((int)num11);
									if (Rxjh_HP > (int)num11)
									{
										Play_Add(value, (int)num11);
										Rxjh_HP -= (int)num11;
									}
									else
									{
										Play_Add(value, Rxjh_HP);
										Rxjh_HP = 0;
									}
									if (Rxjh_HP <= 0)
									{
										flag = true;
									}
								}
							}
							catch (Exception ex2)
							{
								Form1.WriteLine(1, "自动攻击事件[" + num + "]韩飞官追骨吸元反伤出错：" + ex2);
							}
						}
						else if (value.Player_Job == 9)
						{
							if ((double)RNG.Next(1, 100) <= value.谭_升天三气功_火凤临朝 && value.人物_HP <= 0)
							{
								value.人物_HP = 10;
								value.显示大字(value.人物全服ID, 322);
							}
							if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
						}
						else if (value.Player_Job == 10)
						{
							if (value.拳师_狂神降世 != 0.0)
							{
								if (!value.怒)
								{
									value.人物_SP += (int)(3.0 + (double)(value.Player_Level * 2) * value.拳师_狂神降世);
								}
							}
							else if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
						}
						else if (value.Player_Job == 11)
						{
							if ((double)num10 <= num5)
							{
								if (!value.怒)
								{
									value.人物_SP++;
								}
							}
							else if (!value.怒)
							{
								value.人物_SP += 2;
							}
							if (value.梅_障力恢复 > 0.0 && value.人物_AP * 2 < value.人物最大_AP && (double)RNG.Next(1, 100) <= value.梅_障力恢复)
							{
								value.人物_AP = value.人物最大_AP;
								value.显示大字(value.人物全服ID, 801);
							}
							if (value.梅_愤怒爆发 > 0.0 && RNG.Next(1, 100) <= 40 && value.怒点 < 3)
							{
								value.怒点++;
							}
						}
						else if ((double)num10 <= num5)
						{
							if (!value.怒)
							{
								value.人物_SP++;
							}
						}
						else if (!value.怒)
						{
							value.人物_SP += 2;
						}
						if (value.FLD_装备_追加_愤怒 > 0 && !value.怒)
						{
							value.人物_SP += value.FLD_装备_追加_愤怒;
						}
						if (value.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= value.FLD_装备_追加_中毒概率百分比 && !ContainsKeyInAbnormalState(3))
						{
							num = 25;
							异常状态.TryAdd(3, new 异常状态类(this, PlayerWid, 60000, 3, 0.0));
						}
						if (value.人物_HP <= 0)
						{
							if (World.是否开启死亡掉经验 == 1 && value.人物坐标_地图 != 41001 && value.Player_Level >= 10)
							{
								long num12 = ((long)World.lever[value.Player_Level + 1] - (long)World.lever[value.Player_Level]) / 1000;
								int num13 = RNG.Next(1, 100);
								num12 = ((num13 >= 1 && num13 <= 10) ? num12 : ((num13 >= 11 && num13 <= 20) ? (num12 * 4) : ((num13 >= 21 && num13 <= 30) ? (num12 * 6) : ((num13 >= 31 && num13 <= 40) ? (num12 * 8) : ((num13 >= 41 && num13 <= 50) ? (num12 * 10) : ((num13 >= 51 && num13 <= 60) ? (num12 * 12) : ((num13 >= 61 && num13 <= 70) ? (num12 * 14) : ((num13 >= 71 && num13 <= 80) ? (num12 * 16) : ((num13 < 81 || num13 > 90) ? (num12 * 20) : (num12 * 18))))))))));
								if (value.FLD_装备_追加_死亡损失经验减少 > 0.0)
								{
									num12 = (long)((double)num12 * (1.0 - value.FLD_装备_追加_死亡损失经验减少));
									if (num12 < 0)
									{
										num12 = 0L;
									}
								}
								for (int i = 0; i < 15; i++)
								{
									if (BitConverter.ToInt32(value.装备栏已穿装备[i].物品ID, 0) == 700004)
									{
										num = 26;
										num12 = 0L;
										value.装备栏已穿装备[i].物品_byte = new byte[World.数据库单个物品大小];
										value.初始化已装备物品();
										break;
									}
								}
								if (value.GetAddState(1008000160) || value.GetAddState(1008000159))
								{
									num12 = 0L;
								}
								else if (value.时间药品.ContainsKey(1008000142))
								{
									value.时间药品.TryRemove(1008000142, out var _);
									value.药品新效果(1008000142, 0, 0u, 0u);
									num12 = 0L;
								}
								else
								{
									num = 27;
									if (value.Player_Job == 8 || value.Player_Job == 9 || value.Player_Job == 11 || value.Player_Job == 12 || value.Player_Job == 13)
									{
										if (value.经验惩罚剩余 + num12 < 0)
										{
											value.经验惩罚剩余 = long.MaxValue;
										}
										else
										{
											value.经验惩罚剩余 += num12;
										}
										value.更新经验和历练();
									}
									else
									{
										value.死亡经验掉落数 = num12;
										value.人物经验 -= num12;
									}
									value.死亡掉经验 = true;
									value.计算人物基本数据3();
									value.更新经验和历练();
								}
							}
							num = 28;
							自动攻击.Enabled = false;
							自动移动.Enabled = true;
							Rxjh_X = Rxjh_cs_X;
							Rxjh_Y = Rxjh_cs_Y;
							Rxjh_Z = Rxjh_cs_Z;
							num = 29;
							发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
							value.人物_HP = 0;
							num = 30;
							value.人物死亡(FLD_INDEX);
							PlayCw = null;
							num = 31;
							Play_null();
						}
						if (flag)
						{
							num = 32;
							发送死亡数据(value.人物全服ID);
						}
						num = 33;
						value.更新HP_MP_SP();
					}
					else if (查找范围玩家(80, value))
					{
						num = 34;
						发送移动数据(value.人物坐标_X, value.人物坐标_Y, 10, 2);
					}
					else
					{
						num = 35;
						PlayCw = null;
						Play_null();
						自动攻击.Enabled = false;
						自动移动.Enabled = true;
						Rxjh_X = Rxjh_cs_X;
						Rxjh_Y = Rxjh_cs_Y;
						Rxjh_Z = Rxjh_cs_Z;
						发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
					}
				}
				else
				{
					value.自动攻击Stop();
					num = 6;
					if (value.无敌时间计数器 != null)
					{
						value.无敌时间计数器.Enabled = false;
						value.无敌时间计数器.Close();
						value.无敌时间计数器.Dispose();
						num = 8;
					}
					num = 9;
					PlayCw = null;
					Play_null();
					num = 10;
					自动攻击.Enabled = false;
					自动移动.Enabled = true;
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
					Rxjh_Z = Rxjh_cs_Z;
					num = 11;
					发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
				}
			}
			else
			{
				num = 36;
				PlayCw = null;
				Play_null();
				自动攻击.Enabled = false;
				自动移动.Enabled = true;
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
				Rxjh_Z = Rxjh_cs_Z;
				发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
			}
		}
		catch (Exception ex3)
		{
			num = 37;
			PlayCw = null;
			Play_null();
			自动攻击.Enabled = false;
			自动移动.Enabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			发送移动数据(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "怪物攻击人物出错" + num + "|" + Name + "丨" + ex3.Message);
			}
		}
	}

	public List<NpcClass> 群攻查找范围Npc2(Players player, int 数量)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			int num = 0;
			foreach (NpcClass value in player.NpcList.Values)
			{
				if (!value.NPC死亡 && value.IsNpc == 0 && 查找范围Npc(25, value) && value._FLD_INDEX != _FLD_INDEX)
				{
					list.Add(value);
					if (num >= 数量)
					{
						break;
					}
					num++;
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "群攻查找范围Npc出错：" + ex);
			return null;
		}
	}

	public List<NpcClass> 传染怪物范围(Players player, int 数量)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			int num = 0;
			foreach (NpcClass value in player.NpcList.Values)
			{
				if (!value.NPC死亡 && value.IsNpc == 0 && 查找范围Npc(70, value) && value._FLD_INDEX != _FLD_INDEX)
				{
					list.Add(value);
					if (num >= 数量)
					{
						break;
					}
					num++;
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "群攻查找范围Npc出错：" + ex);
			return null;
		}
	}

	public static void 更新NPC数据(ConcurrentDictionary<int, NpcClass> NpcList, Players Playe)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		using 发包类 发包类 = new 发包类();
		发包类.Write4(NpcList.Count);
		foreach (NpcClass value in NpcList.Values)
		{
			// 2025-0619 EVIAS 清理调试代码
			发包类.Write4(value.FLD_INDEX);
			发包类.Write4(value.FLD_INDEX);
			发包类.Write2(value.FLD_PID);
			if (value.NPC死亡)
			{
				发包类.Write2(0);
			}
			else
			{
				发包类.Write2(1);
			}
			发包类.Write4(value.Rxjh_HP);
			发包类.Write4(value.Max_Rxjh_HP);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write(4f);
			发包类.Write(value.FLD_FACE1);
			发包类.Write(value.FLD_FACE2);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write4(0);
			发包类.Write4((value.FLD_BOSS == 1) ? 1 : 0);
			发包类.Write4(11);
			发包类.Write4(0);
			发包类.Write4(0);
			发包类.Write4(uint.MaxValue);
		}
		if (Playe.Client != null)
		{
			Playe.Client.SendPak(发包类, 26368, Playe.人物全服ID);
		}
	}

	public static void 更新NPC删除数据(ConcurrentDictionary<int, NpcClass> NpcList, Players Playe)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		using 发包类 发包类 = new 发包类();
		发包类.Write4(NpcList.Count);
		foreach (NpcClass value in NpcList.Values)
		{
			发包类.Write4(value.FLD_INDEX);
			发包类.Write4(value.FLD_INDEX);
			发包类.Write2(value.FLD_PID);
			发包类.Write4(1);
			发包类.Write4(value.Rxjh_HP);
			发包类.Write4(value.Max_Rxjh_HP);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write4(1082130432);
			发包类.Write(value.FLD_FACE1);
			发包类.Write(value.FLD_FACE2);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write4(0);
			发包类.Write4(0);
			发包类.Write4(12);
			发包类.Write4(0);
			发包类.Write4(0);
			发包类.Write4(uint.MaxValue);
		}
		if (Playe.Client != null)
		{
			Playe.Client.SendPak(发包类, 26624, Playe.人物全服ID);
		}
	}

	public void 更新NPC删除数据(Players Playe)
	{
		using 发包类 发包类 = new 发包类();
		发包类.Write4(1);
		发包类.Write4(FLD_INDEX);
		发包类.Write4(FLD_INDEX);
		发包类.Write2(FLD_PID);
		发包类.Write4(1);
		发包类.Write4(Rxjh_HP);
		发包类.Write4(Max_Rxjh_HP);
		发包类.Write(Rxjh_X);
		发包类.Write(Rxjh_Z);
		发包类.Write(Rxjh_Y);
		发包类.Write4(1082130432);
		发包类.Write(FLD_FACE1);
		发包类.Write(FLD_FACE2);
		发包类.Write(Rxjh_X);
		发包类.Write(Rxjh_Z);
		发包类.Write(Rxjh_Y);
		发包类.Write4(0);
		发包类.Write4(0);
		发包类.Write4(12);
		发包类.Write4(0);
		发包类.Write4(0);
		发包类.Write4(uint.MaxValue);
		if (Playe.Client != null)
		{
			Playe.Client.SendPak(发包类, 26624, Playe.人物全服ID);
		}
	}

	public void 更新NPC复活数据()
	{
		try
		{
			NPC死亡 = false;
			Rxjh_HP = Max_Rxjh_HP;
			if (FLD_PID != 15349 && FLD_PID != 15350)
			{
				if (FLD_PID != 15450 && FLD_PID != 15451 && FLD_PID != 15452 && FLD_PID != 15453 && FLD_PID != 15491 && FLD_PID != 15492 && FLD_PID != 15493 && FLD_PID != 15494)
				{
					Random random = new Random();
					int num = random.Next(0, 2);
					double num2 = random.NextDouble() * 50.0;
					double num3 = random.NextDouble() * 50.0;
					if (num == 0)
					{
						Rxjh_X = Rxjh_cs_X + (float)num2;
						Rxjh_Y = Rxjh_cs_Y + (float)num3;
					}
					else
					{
						Rxjh_X = Rxjh_cs_X - (float)num2;
						Rxjh_Y = Rxjh_cs_Y - (float)num3;
					}
				}
				else
				{
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
				}
			}
			else
			{
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
			}
			Rxjh_Z = Rxjh_cs_Z;
			using (发包类 发包类 = new 发包类())
			{
				发包类.Write4(1);
				发包类.Write4(FLD_INDEX);
				发包类.Write4(FLD_INDEX);
				发包类.Write2(FLD_PID);
				发包类.Write2(1);
				发包类.Write4(Rxjh_HP);
				发包类.Write4(Max_Rxjh_HP);
				发包类.Write(Rxjh_X);
				发包类.Write(Rxjh_Z);
				发包类.Write(Rxjh_Y);
				发包类.Write(4f);
				发包类.Write(FLD_FACE1);
				发包类.Write(FLD_FACE2);
				发包类.Write(Rxjh_X);
				发包类.Write(Rxjh_Z);
				发包类.Write(Rxjh_Y);
				发包类.Write4(0);
				发包类.Write4(128369664);
				发包类.Write4(0);
				发包类.Write4(215040);
				发包类.Write4(0);
				发包类.Write4(786432);
				发包类.Write4(uint.MaxValue);
				发送当前范围广播数据(发包类, 31488, FLD_INDEX);
			}
			if (自动复活 != null)
			{
				自动复活.Enabled = false;
				自动复活.Close();
				自动复活.Dispose();
				自动复活 = null;
			}
		}
		catch (Exception ex)
		{
			if (自动复活 != null)
			{
				自动复活.Enabled = false;
				自动复活.Close();
				自动复活.Dispose();
				自动复活 = null;
			}
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "更新NPC复活数据出错" + FLD_PID + "|" + Name + ex.Message);
			}
		}
		finally
		{
			if (自动复活 != null)
			{
				自动复活.Close();
				自动复活.Dispose();
				自动复活 = null;
			}
		}
	}

	private void 更新NPC死亡数据(Players Playe)
	{
		using 发包类 发包类 = new 发包类();
		if (Playe.Client != null)
		{
			for (int i = 0; i < 7; i++)
			{
				发包类.Write4(0);
			}
			Playe.Client.SendPak(发包类, 34816, FLD_INDEX);
		}
	}

	private void 广播NPC死亡数据()
	{
		using 发包类 发包类 = new 发包类();
		for (int i = 0; i < 7; i++)
		{
			发包类.Write4(0);
		}
		发送当前范围广播数据(发包类, 34816, FLD_INDEX);
	}

	public void 发送移动数据(float X, float Y, int sl, int 移动方式)
	{
		if (FLD_PID == 16556)
		{
			return;
		}
		try
		{
			using 发包类 发包类 = new 发包类();
			Random random = new Random(DateTime.Now.Millisecond);
			int num = RNG.Next(0, 4);
			double num2 = random.NextDouble() * (double)sl;
			double num3 = random.NextDouble() * (double)sl;
			switch (num)
			{
			case 0:
				Rxjh_X = X + (float)num2;
				Rxjh_Y = Y + (float)num3;
				break;
			case 1:
				Rxjh_X = X + (float)num2;
				Rxjh_Y = Y - (float)num3;
				break;
			case 2:
				Rxjh_X = X - (float)num2;
				Rxjh_Y = Y - (float)num3;
				break;
			case 3:
				Rxjh_X = X - (float)num2;
				Rxjh_Y = Y + (float)num3;
				break;
			}
			发包类.Write(Rxjh_X);
			发包类.Write(Rxjh_Y);
			发包类.Write(Rxjh_Z);
			发包类.Write4(-1);
			if (FLD_PID == 5)
			{
				发包类.Write4(0);
			}
			else if (移动方式 != 2)
			{
				发包类.Write4(random.Next(0, 2));
			}
			else
			{
				发包类.Write4(移动方式);
			}
			double num4 = Math.Sqrt(X * Rxjh_X + Y * Rxjh_Y);
			发包类.Write((float)num4);
			发包类.Write4(Rxjh_HP);
			发包类.Write(Rxjh_X);
			发包类.Write(Rxjh_Z);
			发包类.Write(Rxjh_Y);
			发送当前范围广播数据(发包类, 29696, FLD_INDEX);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送移动数据 出错" + FLD_PID + "|" + Name + "   " + ex.Message);
		}
	}

	public void 发送攻击数据(int 攻击力, int 攻击类型, int 人物全服ID, int 恢复障力)
	{
		try
		{
			if (攻击类型 == 29 && 有技能的BOSS列表.Contains(FLD_PID))
			{
				using 发包类 发包类 = new 发包类();
				发包类.Write4(人物全服ID);
				发包类.Write2(13);
				if (FLD_PID == 15419)
				{
					发包类.Write2(1);
				}
				if (FLD_PID == 15420)
				{
					发包类.Write2(1);
				}
				if (FLD_PID == 15421)
				{
					发包类.Write2(1);
				}
				if (FLD_PID == 15422)
				{
					发包类.Write2(1);
				}
				if (FLD_PID == 15423)
				{
					发包类.Write2(RNG.Next(0, 1));
				}
				if (FLD_PID == 15424)
				{
					发包类.Write2(RNG.Next(0, 1));
				}
				if (FLD_PID == 16403)
				{
					发包类.Write2(1);
				}
				if (FLD_PID == 16404)
				{
					发包类.Write2(1);
				}
				发包类.Write4(3);
				发包类.Write4(1);
				for (int i = 0; i < 8; i++)
				{
					发包类.Write4(0);
				}
				发送当前范围广播数据(发包类, 290, FLD_INDEX);
			}
			using 发包类 发包类2 = new 发包类();
			发包类2.Write4(人物全服ID);
			发包类2.Write2(1);
			发包类2.Write2(0);
			发包类2.Write4(攻击力);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(恢复障力);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(攻击类型);
			发包类2.Write(Rxjh_X);
			发包类2.Write(15f);
			发包类2.Write(Rxjh_Y);
			发包类2.Write(0);
			发包类2.Write(1);
			发包类2.Write2(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(-1);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发包类2.Write4(0);
			发送当前范围广播数据(发包类2, 3072, FLD_INDEX);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送攻击数据出错" + FLD_PID + "|" + Name + "丨" + ex);
		}
	}

	public void 发送当前范围广播数据(发包类 pak, int id, int wordid)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "Players_发送当前范围广播数据");
		}
		try
		{
			if (PlayList == null)
			{
				return;
			}
			foreach (Players value2 in PlayList.Values)
			{
				if (value2.Client != null && value2.Client.Running)
				{
					if (!value2.Client.挂机)
					{
						value2.Client.SendPak(pak, id, wordid);
					}
					continue;
				}
				Players value;
				if (Contains(value2))
				{
					PlayList.TryRemove(value2.人物全服ID, out value);
				}
				if (value2.NpcList != null && value2.NpcList.Count > 0)
				{
					foreach (NpcClass value3 in value2.NpcList.Values)
					{
						if (value3.Contains(value2))
						{
							value3.PlayList.TryRemove(value2.人物全服ID, out value);
						}
					}
				}
				if (value2.Client != null)
				{
					Form1.WriteLine(2, "NPC发送当前范围广播数据删除卡号人物：[" + value2.Userid + "][" + value2.UserName + "]");
					value2.Client.Dispose();
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "NPC广播数据出错3：" + ex);
		}
	}

	public void 更新死亡数据()
	{
		int num = 0;
		try
		{
			num = 1;
			if (NPC死亡)
			{
				return;
			}
			num = 2;
			NPC死亡 = true;
			if (一次性怪)
			{
				num = 3;
				if (PlayCw != null)
				{
					num = 4;
					PlayCw = null;
				}
				num = 5;
				Play_null();
				num = 6;
				广播NPC死亡数据();
				num = 7;
				Dispose();
				num = 8;
				return;
			}
			num = 9;
			if (自动攻击 != null)
			{
				自动攻击.Enabled = false;
			}
			num = 10;
			if (自动移动 != null)
			{
				自动移动.Enabled = false;
			}
			num = 11;
			if (自动复活 != null)
			{
				自动复活.Interval = FLD_NEWTIME * 1000;
				自动复活.Enabled = true;
			}
			else
			{
				num = 12;
				自动复活 = new System.Timers.Timer(FLD_NEWTIME * 1000);
				自动复活.Elapsed += 自动复活事件;
				自动复活.Enabled = true;
			}
			num = 13;
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			num = 14;
			Play_null();
			num = 15;
			广播NPC死亡数据();
			num = 16;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "更新死亡数据   出错" + FLD_PID + "|" + Name + "  num:" + num + "/ex:" + ex.Message);
		}
	}

	public void 发送采药数据()
	{
		if (IsNpc == 2)
		{
			if (自动复活 != null)
			{
				自动复活.Interval = FLD_NEWTIME * 1000;
				自动复活.Enabled = true;
			}
			else
			{
				自动复活 = new System.Timers.Timer(FLD_NEWTIME * 1000);
				自动复活.Elapsed += 自动复活事件;
				自动复活.Enabled = true;
			}
			PlayCw = null;
			Play_null();
			广播NPC死亡数据();
		}
	}

	public void 发送怪物一次性死亡数据()
	{
		try
		{
			异常状态列表();
			if (IsNpc != 1 && !NPC死亡)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				Play_null();
				广播NPC死亡数据();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送死亡数据1   出错" + FLD_PID + "|" + Name + "   " + ex.Message);
		}
	}

	public void 势力战打怪得分(Players player, int 攻击血量)
	{
		try
		{
			if (!player.查找范围Npc(700, this) || 攻击血量 <= 0)
			{
				return;
			}
			if (攻击血量 > Max_Rxjh_HP)
			{
				攻击血量 = Max_Rxjh_HP;
			}
			double num = (double)攻击血量 / (double)Max_Rxjh_HP;
			if (Rxjh_Map == 801 && World.势力战进程 == 3 && World.EventTop.TryGetValue(player.UserName, out var value))
			{
				switch (FLD_PID)
				{
				case 15491:
				{
					int num7 = (int)(num * (double)World.势力战打死大怪得分);
					value.玩家杀怪分数 += num7;
					World.势力战邪分数 += num7;
					return;
				}
				case 15492:
				{
					int num4 = (int)(num * (double)World.势力战打死小怪得分);
					value.玩家杀怪分数 += num4;
					World.势力战邪分数 += num4;
					return;
				}
				case 15493:
				{
					int num9 = (int)(num * (double)World.势力战打死大怪得分);
					value.玩家杀怪分数 += num9;
					World.势力战正分数 += num9;
					return;
				}
				case 15494:
				{
					int num8 = (int)(num * (double)World.势力战打死小怪得分);
					value.玩家杀怪分数 += num8;
					World.势力战正分数 += num8;
					return;
				}
				case 15450:
				{
					int num6 = (int)(num * (double)World.势力战打死大怪得分);
					value.玩家杀怪分数 += num6;
					World.势力战邪分数 += num6;
					if (World.势力战类型 == 1)
					{
						World.eve.势力战胜利状态 = 2;
						World.eve.势力战进行中结束时间 = DateTime.Now;
					}
					return;
				}
				case 15451:
				{
					int num5 = (int)(num * (double)World.势力战打死小怪得分);
					value.玩家杀怪分数 += num5;
					World.势力战邪分数 += num5;
					return;
				}
				case 15452:
				{
					int num3 = (int)(num * (double)World.势力战打死大怪得分);
					value.玩家杀怪分数 += num3;
					World.势力战正分数 += num3;
					if (World.势力战类型 == 1)
					{
						World.eve.势力战胜利状态 = 1;
						World.eve.势力战进行中结束时间 = DateTime.Now.AddMinutes(1.0);
					}
					return;
				}
				case 15453:
				{
					int num2 = (int)(num * (double)World.势力战打死小怪得分);
					value.玩家杀怪分数 += num2;
					World.势力战正分数 += num2;
					return;
				}
				}
			}
			if (Rxjh_Map == 伏魔洞地图ID(1) && FLD_PID == 伏魔洞怪物ID(1) && World.伏魔洞副本 != null)
			{
				World.伏魔洞副本.bossgcsj = DateTime.Now.AddMinutes(1.0);
			}
			if (Rxjh_Map == 冰宫内城地图ID(1) && FLD_PID == 冰宫内城怪物ID(1) && World.冰宫内城副本 != null)
			{
				World.冰宫内城副本.bossgcsj = DateTime.Now.AddMinutes(1.0);
			}
			if (Rxjh_Map == 活动副本地图ID(1) && FLD_PID == 活动副本怪物ID(1) && player.人物坐标_地图 == 活动副本地图ID(1))
			{
				player.副本活动时间 = DateTime.Now.AddMinutes(1.0);
				player.发送其他活动开始倒计时((int)player.副本活动时间.Subtract(DateTime.Now).TotalSeconds);
			}
			if (Rxjh_Map == 活动副本地图ID(2) && FLD_PID == 活动副本怪物ID(2) && player.人物坐标_地图 == 活动副本地图ID(2))
			{
				player.副本活动时间 = DateTime.Now.AddMinutes(1.0);
				player.发送其他活动开始倒计时((int)player.副本活动时间.Subtract(DateTime.Now).TotalSeconds);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "势力战打死大怪:" + ex.Message + "/" + ex.StackTrace);
		}
	}

	private int 活动副本地图ID(int 档次)
	{
		int result = 0;
		foreach (活动副本怪物 item in World.活动副本怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_MID;
			}
		}
		return result;
	}

	private int 活动副本怪物ID(int 档次)
	{
		int result = 0;
		foreach (活动副本怪物 item in World.活动副本怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_PID;
			}
		}
		return result;
	}

	private int 伏魔洞地图ID(int 档次)
	{
		int result = 0;
		foreach (伏魔洞怪物 item in World.伏魔洞怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_MID;
			}
		}
		return result;
	}

	private int 伏魔洞怪物ID(int 档次)
	{
		int result = 0;
		foreach (伏魔洞怪物 item in World.伏魔洞怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_PID;
			}
		}
		return result;
	}

	private int 冰宫内城地图ID(int 档次)
	{
		int result = 0;
		foreach (冰宫内城怪物 item in World.冰宫内城怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_MID;
			}
		}
		return result;
	}

	private int 冰宫内城怪物ID(int 档次)
	{
		int result = 0;
		foreach (冰宫内城怪物 item in World.冰宫内城怪物列表)
		{
			if (item.ID == 档次)
			{
				result = item.FLD_PID;
			}
		}
		return result;
	}

	public void 怪物死亡掉落任务物品(Players yxqplayer)
	{
		try
		{
			if (yxqplayer.组队id == 0 || yxqplayer.组队阶段 != 2 || !World.W组队.TryGetValue(yxqplayer.组队id, out var value))
			{
				怪物死亡掉落物品实现(yxqplayer, 组队: false);
				return;
			}
			foreach (Players value2 in value.组队列表.Values)
			{
				if (查找范围玩家(1000, value2) && value2.人物_HP > 0 && !value2.Player死亡)
				{
					怪物死亡掉落物品实现(value2, (value2.人物全服ID != yxqplayer.人物全服ID) ? true : false);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "掉落任务物品出错   怪物ID-" + FLD_PID + ex.Message);
		}
	}

	public void 怪物死亡掉落物品实现(Players play, bool 组队)
	{
		try
		{
			foreach (任务类 value in play.任务.Values)
			{
				if (value.任务阶段ID == 0 || (组队 && !value.是否组队获得))
				{
					continue;
				}
				任务类 rW = 任务类.GetRW(value.任务ID);
				if (rW == null)
				{
					continue;
				}
				if (value.任务ID == 45 && 15062 == FLD_PID)
				{
					play.设置任务物品(900000099, 1);
				}
				if (value.任务ID == 46 && 15072 == FLD_PID)
				{
					play.设置任务物品(900000101, 1);
				}
				任务阶段类 rWJD = rW.GetRWJD(value.任务阶段ID);
				if (rWJD == null)
				{
					continue;
				}
				int num = play.Player_Level - Level;
				int num2 = Level - play.Player_Level;
				if (num >= World.人物越级怪物掉落差 || num2 >= World.怪物越级人物掉落差)
				{
					continue;
				}
				foreach (任务需要物品类 item in rWJD.任务需要物品)
				{
					if (item.NPCPID != 0 || item.NPCPID == 0 || item.NPCPID == FLD_PID)
					{
						int num3 = RNG.Next(1, 100);
						if (!play.CheckItem(item.物品ID, item.物品数量) && num3 >= item.难度)
						{
							play.设置任务物品(item.物品ID, 1);
							break;
						}
					}
				}
			}
		}
		catch
		{
		}
	}

	public void 发送死亡数据(int UserWorldId)
	{
		if (IsNpc == 1 || NPC死亡)
		{
			return;
		}
		NPC死亡 = true;
		int num = 0;
		if (!PlayList.TryGetValue(UserWorldId, out var value))
		{
			return;
		}
		try
		{
			Players players = 取最大攻击伤害者();
			if (players != null)
			{
				value = players;
			}
			num = 1;
			if (value != null)
			{
				if (!value.Client.挂机 && !value.Client.云挂机 && !value.Client.假人)
				{
					怪物死亡掉落任务物品(value);
				}
				num = 2;
				异常状态列表();
				num = 3;
				怪物死亡分配经验历练金钱();
				num = 4;
				暴物品(value);
				num = 5;
				if (FLD_PID == 15261)
				{
					value.增加物品带属性(1000000262, value.得到包裹空位位置(), 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					World.发送特殊公告("恭喜玩家" + value.UserName + "，在九泉之下击杀王龙获得3根王龙的金条", 6, "公告");
				}
				num = 7;
				switch (Rxjh_Map)
				{
				case 5201:
					if (FLD_PID == 冰宫内城怪物ID(2))
					{
						World.冰宫内城副怪是否死亡++;
					}
					break;
				case 6001:
					if (FLD_PID == 16314)
					{
						value.增加物品带属性(1000001160, value.得到包裹空位位置(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
						World.发送特殊公告("偷走灵宠（蛋）的" + Name + "被抓了", 6, "公告");
					}
					break;
				case 21001:
					if (FLD_PID == 伏魔洞怪物ID(2))
					{
						World.伏魔洞副怪是否死亡++;
					}
					break;
				case 42001:
					if (World.攻城战进程 != 3 || Rxjh_Map != 42001)
					{
						break;
					}
					if (FLD_PID == 16435)
					{
						if (value != null)
						{
							World.天魔神宫占领者 = value.门派联盟盟主;
							World.攻城战进程 = 2;
							World.攻城.火龙之力释放 = false;
						}
					}
					else
					{
						if (FLD_PID != 16430 && FLD_PID != 16431)
						{
							break;
						}
						if (FLD_PID == 16430)
						{
							World.天魔神宫大门是否死亡 = 1;
						}
						if (FLD_PID == 16431)
						{
							World.天魔神宫东门是否死亡 = 1;
						}
						foreach (Players value2 in World.allConnectedChars.Values)
						{
							if (value2.人物坐标_地图 == 42001)
							{
								if (FLD_PID == 16430)
								{
									天魔神宫.天魔神宫正城门已开启(value2);
								}
								if (FLD_PID == 16431)
								{
									天魔神宫.天魔神宫东城门已开启(value2);
								}
							}
						}
					}
					break;
				}
			}
			num = 8;
			if (一次性怪)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				Play_null();
				广播NPC死亡数据();
				Dispose();
				return;
			}
			num = 9;
			if (自动攻击 != null)
			{
				自动攻击.Enabled = false;
			}
			if (自动移动 != null)
			{
				自动移动.Enabled = true;
			}
			num = 10;
			if (自动复活 != null)
			{
				自动复活.Interval = FLD_NEWTIME * 1000;
				自动复活.Enabled = true;
			}
			else
			{
				自动复活 = new System.Timers.Timer(FLD_NEWTIME * 1000);
				自动复活.Elapsed += 自动复活事件;
				自动复活.Enabled = true;
			}
			num = 11;
			Play_null();
			广播NPC死亡数据();
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字 == 1)
			{
				Form1.WriteLine(1, "发送死亡数据2出错" + FLD_PID + "|" + Name + "丨" + num + "丨" + ex.Message);
			}
		}
	}

	public Players 取最大攻击伤害者()
	{
		int num = 0;
		Players result = null;
		try
		{
			for (int i = 0; i < PlayGjList.Count; i++)
			{
				if (i >= PlayGjList.Count)
				{
					continue;
				}
				PlayGjClass playGjClass = PlayGjList[i];
				if (playGjClass == null)
				{
					continue;
				}
				int 攻击全服ID = playGjClass.攻击全服ID;
				Players players = World.检查玩家世界ID(攻击全服ID);
				if (players == null)
				{
					continue;
				}
				if (players.组队id == 0)
				{
					if (num < playGjClass.攻击血量)
					{
						num = playGjClass.攻击血量;
						result = players;
					}
					continue;
				}
				int num2 = players.计算队伍总攻击血量(this, players.组队id);
				if (num < num2)
				{
					num = num2;
					result = players;
				}
			}
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "取最大攻击伤害者出错:" + ex.Message);
			}
		}
		return result;
	}

	public int 获得经验()
	{
		try
		{
			int num = (int)((double)Rxjh_Exp * World.经验倍数);
			if (World.开启全服经验 != null)
			{
				num *= (int)World.双倍奖励经验倍数;
			}
			Random random = new Random(DateTime.Now.Millisecond);
			num += num * random.Next(1, 6) / 100;
			return num / 3;
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "获得经验错误" + ex.Message);
			}
			return 0;
		}
	}

	public int 获得钱()
	{
		try
		{
			int num = (int)((double)Rxjh_Exp * World.钱倍数);
			if (World.开启全服经验 != null)
			{
				num *= (int)World.双倍奖励经验倍数;
			}
			int num2 = num / 3;
			return new Random(DateTime.Now.Millisecond).Next(num - num2, num + num2);
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "获得金钱错误" + ex.Message);
			}
			return 0;
		}
	}

	public int 获得历练()
	{
		try
		{
			int num = Rxjh_Exp * World.历练倍数 / Level / 2;
			if (World.开启全服经验 != null)
			{
				num *= (int)World.双倍奖励经验倍数;
			}
			int num2 = num / 3;
			return new Random().Next(num - num2, num + num2);
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "获得历练错误" + ex.Message);
			}
			return 0;
		}
	}

	public int 获得升天历练()
	{
		try
		{
			int num = Rxjh_Exp * World.历练倍数;
			int num2 = num / 3;
			return new Random().Next(num - num2, num + num2);
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "获得升天历练错误" + ex.Message);
			}
			return 0;
		}
	}

	public void 怪物死亡分配经验历练金钱()
	{
		int num = 0;
		double num2 = (uint)获得钱();
		double num3 = 获得经验();
		double num4 = 获得历练();
		double num5 = 获得升天历练();
		try
		{
			if (是否绝命技死亡)
			{
				num2 += (double)(int)(num2 * 绝命技死亡金钱加成);
				num3 += (double)(int)(num3 * 绝命技死亡经验加成);
				num4 += (double)(int)(num4 * 绝命技死亡历练加成);
				num5 += (double)(int)(num5 * 绝命技死亡历练加成);
			}
			if (FLD_PID == 16556 && World.讨伐战副本 != null)
			{
				讨伐战系统.讨伐副本进程 = 2;
				World.讨伐战副本.时间结束事件1(null, null);
				World.讨伐战副本.主怪死亡状态 = true;
				World.讨伐战副本.讨伐奖励buff();
				World.讨伐发奖励();
				World.发送特殊公告("[" + World.讨伐队伍名 + "]的队伍讨伐副本结束，其他队伍1分钟后.请各位大侠做好进入准备", 6, "公告");
			}
			SortedDictionary<int, int> sortedDictionary = new SortedDictionary<int, int>();
			num = 1;
			for (int i = 0; i < PlayGjList.Count; i++)
			{
				PlayGjClass playGjClass = PlayGjList[i];
				if (playGjClass == null)
				{
					continue;
				}
				Players players = World.检查玩家世界ID(playGjClass.攻击全服ID);
				num = 2;
				int num6 = 0;
				if (players == null)
				{
					continue;
				}
				num = 4;
				if (players.组队id != 0)
				{
					num = 5;
					if (!sortedDictionary.ContainsKey(players.组队id))
					{
						num = 6;
						num6 = players.计算队伍总攻击血量(this, players.组队id);
						num = 7;
						sortedDictionary.Add(players.组队id, num6);
						num = 8;
						分配经验历练金钱(players, num6, num3, num4, num2, num5);
						num = 9;
					}
				}
				else
				{
					num = 10;
					if (!sortedDictionary.ContainsKey(players.人物全服ID))
					{
						num = 11;
						num6 = players.计算个人攻击血量(this);
						num = 12;
						sortedDictionary.Add(players.人物全服ID, num6);
						num = 13;
						分配经验历练金钱(players, num6, num3, num4, num2, num5);
						num = 14;
					}
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "怪物死亡分配经验", $"步骤: {num}, NPC: {Name}[{FLD_PID}]");
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "怪物死亡分配经验出错:" + num + "/" + FLD_PID + "/" + ex.Message + "/" + ex.StackTrace);
			}
		}
	}

	public void 分配经验历练金钱(Players player, int 攻击血量, double 经验, double 历练, double 金钱, double 升天历练)
	{
		try
		{
			if (经验 <= 0.0 || !player.查找范围Npc(700, this) || 攻击血量 <= 0)
			{
				return;
			}
			int num = player.Player_Level - Level;
			int num2 = Level - player.Player_Level;
            if ((num > World.人物越级怪物经验差 || num2 > World.怪物越级人物经验差) && FLD_BOSS == 0) //EVIAS
            {
				if ((player.云挂机打怪模式 == 1 || player.离线挂机打怪模式 == 1) && player.人物坐标_地图 != 26200)
				{
					player.内挂地图读取(player, player.Player_Job_leve);
				}
				return;
			}
			if (攻击血量 > Max_Rxjh_HP)
			{
				攻击血量 = Max_Rxjh_HP;
			}
			if (player.组队id != 0)
			{
				double num3 = 经验 * (double)攻击血量 / (double)Max_Rxjh_HP;
				double num4 = 历练 * (double)攻击血量 / (double)Max_Rxjh_HP;
				double num5 = 金钱 * (double)攻击血量 / (double)Max_Rxjh_HP;
				double num6 = 升天历练 * (double)攻击血量 / (double)Max_Rxjh_HP;
				if (!World.W组队.TryGetValue(player.组队id, out var value))
				{
					return;
				}
				ConcurrentDictionary<int, Players> concurrentDictionary = new ConcurrentDictionary<int, Players>();
				foreach (Players value2 in value.组队列表.Values)
				{
					if (查找范围玩家(700, value2))
					{
						concurrentDictionary.TryAdd(value2.人物全服ID, value2);
					}
				}
				if (concurrentDictionary.Count < 1)
				{
					return;
				}
				double num7 = num3 * (1.0 + World.获得队伍加成(value)) / (double)concurrentDictionary.Count;
				double num8 = num4 / (double)concurrentDictionary.Count;
				double num9 = num5 / (double)concurrentDictionary.Count;
				double num10 = num6 / (double)concurrentDictionary.Count;
				{
					foreach (Players value3 in concurrentDictionary.Values)
					{
						double num11 = num7;
						double num12 = num8;
						double num13 = num9;
						double num14 = value3.FLD_人物_总加经验;
						double num15 = value3.FLD_人物_总加金钱;
						double fLD_人物_总加历练 = value3.FLD_人物_总加历练;
						double num16 = 1.0 - 队伍限制职业(value);
						double num17 = 1.0 - value3.LS_降低_经验百分比;
						double num18 = 1.0 - value3.LS_降低_金钱百分比;
						double num19 = 1.0 - value3.LS_降低_历练百分比;
						if (value.红包)
						{
							num14 += World.队伍红包增加经验百分比;
							num15 += World.队伍红包增加金钱百分比;
						}
						if (num14 > 0.0)
						{
							num11 *= num14;
						}
						if (num15 > 0.0)
						{
							num13 *= num15;
						}
						if (fLD_人物_总加历练 > 0.0)
						{
							num12 *= fLD_人物_总加历练;
						}
						if (num16 < 1.0)
						{
							num11 *= num16;
						}
						if (num17 < 1.0)
						{
							num11 *= num17;
						}
						if (num18 < 1.0)
						{
							num13 *= num18;
						}
						if (num19 < 1.0)
						{
							num12 *= num19;
						}
						if (value3.中级附魂_奇缘 != 0 && (double)RNG.Next(1, 100) <= (double)value3.中级附魂_奇缘)
						{
							num11 *= 2.0;
							value3.显示大字(value3.人物全服ID, 403);
						}
						if (value3.Player_Level < World.自定义经验等级)
						{
							num11 *= World.自定义等级经验倍数;
						}
						else if (value3.Player_Level < 35)
						{
							num11 *= World.三十五级以下经验倍数;
						}
						else if (value3.Player_Level < 60)
						{
							num11 *= World.六十级以下经验倍数;
						}
						else if (value3.Player_Level < 80)
						{
							num11 *= World.八十级以下经验倍数;
						}
						else if (value3.Player_Level < 100)
						{
							num11 *= World.一百级以下经验倍数;
						}
						else if (value3.Player_Level < 115)
						{
							num11 *= World.一百一以下经验倍数;
						}
						else if (value3.Player_Level < 120)
						{
							num11 *= World.一百二以下经验倍数;
						}
						else if (value3.Player_Level < 130)
						{
							num11 *= World.一百三以下经验倍数;
						}
						else if (value3.Player_Level < 140)
						{
							num11 *= World.一百四以下经验倍数;
						}
						else if (value3.Player_Level < 150)
						{
							num11 *= World.一百五以下经验倍数;
						}
						else if (value3.Player_Level < 160)
						{
							num11 *= World.一百六以下经验倍数;
						}
						if (value3.FLD_SPREADER_LEVEL == 1)
						{
							num11 *= World.一级推广经验增加百分比;
						}
						else if (value3.FLD_SPREADER_LEVEL == 2)
						{
							num11 *= World.二级推广经验增加百分比;
						}
						else if (value3.FLD_SPREADER_LEVEL == 3)
						{
							num11 *= World.三级推广经验增加百分比;
						}
						if (value3.FLD_VIP == 1)
						{
							num11 *= World.VIP经验增加百分比;
							num13 *= World.VIP金钱增加百分比;
							num12 *= World.VIP历练增加百分比;
						}
						if (value3.帮派Id != 0)
						{
							if (value3.帮派等级 == 4)
							{
								num11 *= World.四级门派增加百分比;
							}
							else if (value3.帮派等级 == 5)
							{
								num11 *= World.五级门派增加百分比;
							}
							else if (value3.帮派等级 == 6)
							{
								num11 *= World.六级门派增加百分比;
							}
							else if (value3.帮派等级 == 7)
							{
								num11 *= World.七级门派增加百分比;
							}
						}
						if (value3.医生群疗_追加_经验百分比 > 0.0)
						{
							num11 *= 1.0 + value3.医生群疗_追加_经验百分比;
						}
						if (value3.查询天关地图(value3.人物坐标_地图))
						{
							num11 *= 1.0 + value3.得到天关福利加成(0, value3.人物坐标_地图);
						}
						if (value3.人物灵兽 != null && value3.Config.宠物经验 > 0 && !value3.人物灵兽.死亡 && value3.人物灵兽.FLD_LEVEL < 100)
						{
							double num20 = (double)value3.Config.宠物经验 * 0.01;
							int num21 = (int)(num11 * num20 * (1.0 + value3.人物灵兽.FLD_灵兽_获得经验增加百分比));
							if (num21 <= 0 || num21 > 2000000000)
							{
								num21 = 0;
							}
							value3.人物灵兽.FLD_EXP += num21;
							value3.人物灵兽.计算基本数据();
							value3.更新灵兽经验和历练();
							double num22 = num11 * (1.0 - num20);
							if (num22 <= 0.0 || num22 > 2000000000.0)
							{
								num22 = 0.0;
							}
							if (value3.Player_Level < World.限制最高级别)
							{
								value3.人物经验 += (long)num22;
								value3.精神宝珠包(3, (int)num22);
							}
						}
						else
						{
							if (num11 <= 0.0 || num11 > 2000000000.0)
							{
								num11 = 0.0;
							}
							if (value3.Player_Level < World.限制最高级别)
							{
								if (value3.经验惩罚剩余 > 0)
								{
									double num23 = num11 / 2.0;
									double num24 = value3.经验惩罚剩余;
									if (num23 > num24)
									{
										num23 = num24;
									}
									num11 -= num23;
									value3.经验惩罚剩余 -= (long)num23;
									if (value3.经验惩罚剩余 < 0)
									{
										value3.经验惩罚剩余 = 0L;
									}
								}
								value3.人物经验 += (long)num11;
								value3.精神宝珠包(3, (int)num11);
							}
						}
						value3.Player_ExpErience += (int)num12;
						value3.升天历练当前获得数 += (int)num10;
						if (World.是否开启王龙 == 1)
						{
							if (value3.人物坐标_地图 >= 23001 && value3.人物坐标_地图 <= 24000)
							{
								World.王龙的金币 += (uint)(num13 * World.九泉金币比率);
							}
							else
							{
								value3.Player_Money += (uint)num13;
								value3.得到钱的提示((uint)num13);
							}
						}
						else
						{
							value3.Player_Money += (uint)num13;
							value3.得到钱的提示((uint)num13);
						}
						value3.计算人物基本数据3();
						value3.更新经验和历练();
						value3.更新金钱和负重();
					}
					return;
				}
			}
			double num25 = 经验 * (double)攻击血量 / (double)Max_Rxjh_HP;
			double num26 = 历练 * (double)攻击血量 / (double)Max_Rxjh_HP;
			double num27 = 金钱 * (double)攻击血量 / (double)Max_Rxjh_HP;
			double num28 = 升天历练 * (double)攻击血量 / (double)Max_Rxjh_HP;
			double fLD_人物_总加经验 = player.FLD_人物_总加经验;
			double fLD_人物_总加金钱 = player.FLD_人物_总加金钱;
			double fLD_人物_总加历练2 = player.FLD_人物_总加历练;
			double num29 = 1.0 - player.LS_降低_经验百分比;
			double num30 = 1.0 - player.LS_降低_金钱百分比;
			double num31 = 1.0 - player.LS_降低_历练百分比;
			if (fLD_人物_总加经验 > 0.0)
			{
				num25 *= fLD_人物_总加经验;
			}
			if (fLD_人物_总加金钱 > 0.0)
			{
				num27 *= fLD_人物_总加金钱;
			}
			if (fLD_人物_总加历练2 > 0.0)
			{
				num26 *= fLD_人物_总加历练2;
			}
			if (num29 < 1.0)
			{
				num25 *= num29;
			}
			if (num30 < 1.0)
			{
				num27 *= num30;
			}
			if (num31 < 1.0)
			{
				num26 *= num31;
			}
			if (player.中级附魂_奇缘 != 0 && (double)RNG.Next(1, 100) <= (double)player.中级附魂_奇缘)
			{
				num25 *= 2.0;
				player.显示大字(player.人物全服ID, 403);
			}
			if (player.Player_Level < World.自定义经验等级)
			{
				num25 *= World.自定义等级经验倍数;
			}
			else if (player.Player_Level < 35)
			{
				num25 *= World.三十五级以下经验倍数;
			}
			else if (player.Player_Level < 60)
			{
				num25 *= World.六十级以下经验倍数;
			}
			else if (player.Player_Level < 80)
			{
				num25 *= World.八十级以下经验倍数;
			}
			else if (player.Player_Level < 100)
			{
				num25 *= World.一百级以下经验倍数;
			}
			else if (player.Player_Level < 115)
			{
				num25 *= World.一百一以下经验倍数;
			}
			else if (player.Player_Level < 120)
			{
				num25 *= World.一百二以下经验倍数;
			}
			else if (player.Player_Level < 130)
			{
				num25 *= World.一百三以下经验倍数;
			}
			else if (player.Player_Level < 140)
			{
				num25 *= World.一百四以下经验倍数;
			}
			else if (player.Player_Level < 150)
			{
				num25 *= World.一百五以下经验倍数;
			}
			else if (player.Player_Level < 160)
			{
				num25 *= World.一百六以下经验倍数;
			}
			if (player.FLD_SPREADER_LEVEL == 1)
			{
				num25 *= World.一级推广经验增加百分比;
			}
			else if (player.FLD_SPREADER_LEVEL == 2)
			{
				num25 *= World.二级推广经验增加百分比;
			}
			else if (player.FLD_SPREADER_LEVEL == 3)
			{
				num25 *= World.一级推广经验增加百分比;
			}
			if (player.查询天关地图(player.人物坐标_地图))
			{
				num25 *= 1.0 + player.得到天关福利加成(0, player.人物坐标_地图);
			}
			if (player.FLD_VIP == 1)
			{
				num25 *= World.VIP经验增加百分比;
				num27 *= World.VIP金钱增加百分比;
				num26 *= World.VIP历练增加百分比;
			}
			if (player.帮派Id != 0)
			{
				if (player.帮派等级 == 4)
				{
					num25 *= World.四级门派增加百分比;
				}
				else if (player.帮派等级 == 5)
				{
					num25 *= World.五级门派增加百分比;
				}
				else if (player.帮派等级 == 6)
				{
					num25 *= World.六级门派增加百分比;
				}
				else if (player.帮派等级 == 7)
				{
					num25 *= World.七级门派增加百分比;
				}
			}
			if (player.人物灵兽 != null && player.Config.宠物经验 > 0 && !player.人物灵兽.死亡 && player.人物灵兽.FLD_LEVEL < 100)
			{
				double num32 = (double)player.Config.宠物经验 * 0.01;
				int num33 = (int)(num25 * num32 * (1.0 + player.人物灵兽.FLD_灵兽_获得经验增加百分比));
				if (num33 <= 0 || num33 > 2000000000)
				{
					num33 = 0;
				}
				player.人物灵兽.FLD_EXP += num33;
				player.人物灵兽.计算基本数据();
				player.更新灵兽经验和历练();
				double num34 = num25 * (1.0 - num32);
				if (num34 <= 0.0 || num34 > 2000000000000.0)
				{
					num34 = 0.0;
				}
				if (player.Player_Level < World.限制最高级别)
				{
					player.人物经验 += (long)num34;
					player.精神宝珠包(3, (int)num34);
				}
			}
			else
			{
				double num35 = num25;
				if (num35 <= 0.0 || num35 > 2000000000000.0)
				{
					num35 = 0.0;
				}
				if (player.Player_Level < World.限制最高级别)
				{
					if (player.经验惩罚剩余 > 0)
					{
						double num36 = num35 / 2.0;
						double num37 = player.经验惩罚剩余;
						if (num36 > num37)
						{
							num36 = num37;
						}
						num35 -= num36;
						player.经验惩罚剩余 -= (long)num36;
						if (player.经验惩罚剩余 < 0)
						{
							player.经验惩罚剩余 = 0L;
						}
					}
					player.人物经验 += (long)num35;
					player.精神宝珠包(3, (int)num35);
				}
			}
			player.Player_ExpErience += (int)num26;
			player.升天历练当前获得数 += (int)num28;
			if (World.是否开启王龙 == 1)
			{
				if (player.人物坐标_地图 >= 23001 && player.人物坐标_地图 <= 24000)
				{
					World.王龙的金币 += (uint)(num27 * World.九泉金币比率);
				}
				else
				{
					player.Player_Money += (uint)num27;
					player.得到钱的提示((uint)num27);
				}
			}
			else
			{
				player.Player_Money += (uint)num27;
				player.得到钱的提示((uint)num27);
			}
			player.计算人物基本数据3();
			player.更新经验和历练();
			player.更新金钱和负重();
		}
		catch (Exception ex)
		{
			if (World.是否开启票红字2 == 1)
			{
				Form1.WriteLine(1, "分配经验历练游戏币出错:" + ex.Message + "/" + ex.StackTrace);
			}
		}
	}

	public static double 队伍限制职业(组队Class 组队a)
	{
		double result = 0.0;
		if (组队a.限制职业一 >= World.限制职业一数量)
		{
			result = World.限制职业一降低百分比;
		}
		else if (组队a.限制职业二 >= World.限制职业二数量)
		{
			result = World.限制职业二降低百分比;
		}
		return result;
	}

	public void 发送琴反伤攻击数据(int 攻击力, int 人物ID)
	{
		byte[] array = Converter.hexStringToByte("AA551B00A42789000C002C0100000F0000000100000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(人物ID), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(攻击力), 0, array, 18, 4);
		广播数据(array, array.Length);
	}

	public byte[] 掉出物品(DropClass drop, Players yxqname, int num2)
	{
		byte[] result;
		try
		{
			long dBItmeId = RxjhClass.GetDBItmeId();
			byte[] array = new byte[World.数据库单个物品大小];
			byte[] bytes = BitConverter.GetBytes(dBItmeId);
			byte[] array2 = new byte[20];
			if (!World.Itme.TryGetValue(drop.FLD_PID, out var value))
			{
				result = null;
			}
			else if (value.FLD_QUESTITEM != 1)
			{
				try
				{
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC0), 0, array2, 0, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC1), 0, array2, 4, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC2), 0, array2, 8, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC3), 0, array2, 12, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC4), 0, array2, 16, 4);
					Buffer.BlockCopy(bytes, 0, array, 0, 4);
					Buffer.BlockCopy(array2, 0, array, 16, 20);
					Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_PID), 0, array, 8, 4);
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 12, 4);
					if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
					{
						Buffer.BlockCopy(BitConverter.GetBytes(1000), 0, array, 60, 2);
					}
				}
				catch (Exception ex)
				{
					Exception ex2 = ex;
					object[] array3 = new object[6] { "掉出物品1 出错 ", FLD_PID, "|", Name, " ", ex2.Message };
					Form1.WriteLine(1, string.Concat(array3));
					result = null;
					return result;
				}
				地面物品类 地面物品类2;
				地面物品类 value2;
				try
				{
					if (FLD_BOSS == 0)
					{
						地面物品类2 = new 地面物品类(array, Rxjh_X, Rxjh_Y, Rxjh_Z, Rxjh_Map, yxqname, 0, FLD_PID, drop.是否开启公告, Name, drop.FLD_NAME, FLD_BOSS);
					}
					else
					{
						double num3 = Rxjh_X;
						double num4 = Rxjh_Y;
						switch (num2)
						{
						case 0:
							num3 = Rxjh_X + (float)RNG.Next(-5, 5);
							num4 = Rxjh_Y + (float)RNG.Next(5, -5);
							break;
						case 1:
							num3 = Rxjh_X + (float)RNG.Next(-8, 8);
							num4 = Rxjh_Y - (float)RNG.Next(8, -8);
							break;
						case 2:
							num3 = Rxjh_X - (float)RNG.Next(-11, 11);
							num4 = Rxjh_Y - (float)RNG.Next(11, -11);
							break;
						case 3:
							num3 = Rxjh_X - (float)RNG.Next(-14, 14);
							num4 = Rxjh_Y + (float)RNG.Next(14, -14);
							break;
						case 4:
							num3 = Rxjh_X + (float)RNG.Next(-17, 17);
							num4 = Rxjh_Y + (float)RNG.Next(17, -17);
							break;
						case 5:
							num3 = Rxjh_X + (float)RNG.Next(-20, 20);
							num4 = Rxjh_Y - (float)RNG.Next(20, -20);
							break;
						case 6:
							num3 = Rxjh_X - (float)RNG.Next(-23, 23);
							num4 = Rxjh_Y - (float)RNG.Next(23, -23);
							break;
						case 7:
							num3 = Rxjh_X - (float)RNG.Next(-26, 26);
							num4 = Rxjh_Y + (float)RNG.Next(26, -26);
							break;
						case 8:
							num3 = Rxjh_X + (float)RNG.Next(-29, 29);
							num4 = Rxjh_Y + (float)RNG.Next(29, -29);
							break;
						case 9:
							num3 = Rxjh_X + (float)RNG.Next(-32, 32);
							num4 = Rxjh_Y - (float)RNG.Next(32, -32);
							break;
						case 10:
							num3 = Rxjh_X - (float)RNG.Next(-35, 35);
							num4 = Rxjh_Y - (float)RNG.Next(35, -35);
							break;
						case 11:
							num3 = Rxjh_X - (float)RNG.Next(-38, 38);
							num4 = Rxjh_Y + (float)RNG.Next(38, -38);
							break;
						case 12:
							num3 = Rxjh_X - (float)RNG.Next(-35, 35);
							num4 = Rxjh_Y - (float)RNG.Next(35, -35);
							break;
						case 13:
							num3 = Rxjh_X + (float)RNG.Next(-32, 32);
							num4 = Rxjh_Y - (float)RNG.Next(32, -32);
							break;
						case 14:
							num3 = Rxjh_X + (float)RNG.Next(-29, 29);
							num4 = Rxjh_Y + (float)RNG.Next(29, -29);
							break;
						case 15:
							num3 = Rxjh_X - (float)RNG.Next(-26, 26);
							num4 = Rxjh_Y + (float)RNG.Next(26, -26);
							break;
						case 16:
							num3 = Rxjh_X - (float)RNG.Next(-23, 23);
							num4 = Rxjh_Y - (float)RNG.Next(23, -23);
							break;
						case 17:
							num3 = Rxjh_X + (float)RNG.Next(-20, 20);
							num4 = Rxjh_Y - (float)RNG.Next(20, -20);
							break;
						case 18:
							num3 = Rxjh_X + (float)RNG.Next(-17, 17);
							num4 = Rxjh_Y + (float)RNG.Next(17, -17);
							break;
						case 19:
							num3 = Rxjh_X - (float)RNG.Next(-14, 14);
							num4 = Rxjh_Y - (float)RNG.Next(14, -14);
							break;
						case 20:
							num3 = Rxjh_X - (float)RNG.Next(-11, 11);
							num4 = Rxjh_Y - (float)RNG.Next(11, -11);
							break;
						case 21:
							num3 = Rxjh_X + (float)RNG.Next(-8, 8);
							num4 = Rxjh_Y - (float)RNG.Next(8, -8);
							break;
						case 22:
							num3 = Rxjh_X - (float)RNG.Next(-10, 10);
							num4 = Rxjh_Y + (float)RNG.Next(-10, 10);
							break;
						}
						地面物品类2 = new 地面物品类(array, (float)num3, (float)num4, Rxjh_Z, Rxjh_Map, yxqname, 0, FLD_PID, drop.是否开启公告, Name, drop.FLD_NAME, FLD_BOSS);
					}
					if (地面物品类2 == null)
					{
						object[] array4 = new object[4] { "掉出物品2 出错 ", FLD_PID, "|", Name };
						Form1.WriteLine(1, string.Concat(array4));
						result = null;
						return result;
					}
					if (!World.ItmeTeM.TryGetValue(dBItmeId, out value2))
					{
						World.ItmeTeM.TryAdd(dBItmeId, 地面物品类2);
					}
				}
				catch (Exception ex3)
				{
					object[] array5 = new object[6] { "掉出物品3 出错 ", FLD_PID, "|", Name, " ", ex3.Message };
					Form1.WriteLine(1, string.Concat(array5));
					result = null;
					return result;
				}
				try
				{
					if (World.ItmeTeM.TryGetValue(dBItmeId, out value2))
					{
						地面物品类2.获取范围玩家发送地面增加物品数据包();
					}
					result = array;
				}
				catch (Exception ex4)
				{
					object[] array6 = new object[6] { "掉出物品4 出错 ", FLD_PID, "|", Name, " ", ex4.Message };
					Form1.WriteLine(1, string.Concat(array6));
					result = null;
				}
			}
			else
			{
				if (yxqname != null)
				{
					int num5 = yxqname.得到包裹空位(yxqname);
					if (num5 != -1)
					{
						yxqname.增加物品(bytes, BitConverter.GetBytes(drop.FLD_PID), num5, BitConverter.GetBytes(1), new byte[60]);
					}
				}
				result = null;
			}
		}
		catch (Exception ex5)
		{
			Exception ex6 = ex5;
			object[] array7 = new object[6] { "掉出物品5 出错 ", FLD_PID, "|", Name, " ", ex6.Message };
			Form1.WriteLine(1, string.Concat(array7));
			result = null;
		}
		finally
		{
			drop.FLD_PID = drop.FLD_PIDNew;
			drop.FLD_MAGIC0 = drop.FLD_MAGICNew0;
			drop.FLD_MAGIC1 = drop.FLD_MAGICNew1;
			drop.FLD_MAGIC2 = drop.FLD_MAGICNew2;
			drop.FLD_MAGIC3 = drop.FLD_MAGICNew3;
			drop.FLD_MAGIC4 = drop.FLD_MAGICNew4;
		}
		return result;
	}

	public void 暴物品(Players yxqname)
	{
		try
		{
			if (Rxjh_Map == 41001)
			{
				yxqname = null;
			}
			if (yxqname == null)
			{
				return;
			}
			switch (FLD_BOSS)
			{
			case 0:
			{
				int num = yxqname.Player_Level - Level;
				int num2 = Level - yxqname.Player_Level;
				if (num < World.人物越级怪物掉落差 && num2 < World.怪物越级人物掉落差)
				{
					普通怪掉落(yxqname);
				}
				break;
			}
			case 1:
				if (World.allConnectedChars.TryGetValue(BossPlayerWid, out yxqname) && 查找范围玩家(1000, yxqname))
				{
					BOSS怪掉落(yxqname);
				}
				break;
			case 2:
				if (查找范围玩家(1000, yxqname))
				{
					高手怪掉落(yxqname);
				}
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "暴物品出错：" + ex);
		}
	}

	public void 高手怪掉落(Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			if (DropClass.高手必掉(Rxjh_Map, FLD_PID))
			{
				高手PlayerDrop(yxqname);
			}
			int num = RNG.Next(1, 8000);
			int num2 = World.暴率 + 坐标Class.地图增加爆率(yxqname.人物坐标_地图) + yxqname.角色暴率;
			if (World.开启全服经验 != null)
			{
				num2 *= World.双倍奖励爆率倍数;
			}
			if (yxqname.组队id != 0 && World.W组队.TryGetValue(yxqname.组队id, out var value))
			{
				num2 += (int)((double)num2 * World.获得队伍暴率加成(value));
			}
			if (yxqname != null)
			{
				if (yxqname.FLD_人物_追加_物品掉落概率百分比 > 0.0)
				{
					num2 += (int)((double)num2 * yxqname.FLD_人物_追加_物品掉落概率百分比);
				}
				if (yxqname.查询天关地图(yxqname.人物坐标_地图))
				{
					num2 += (int)yxqname.得到天关福利加成(1, yxqname.人物坐标_地图);
				}
				if (yxqname.医生群疗_追加_爆率 > 0)
				{
					num2 += yxqname.医生群疗_追加_爆率;
				}
				if (yxqname.FLD_VIP == 1)
				{
					num2 += World.VIP爆率增加;
				}
				if (yxqname.FLD_SPREADER_LEVEL == 1)
				{
					num2 += 20;
				}
				else if (yxqname.FLD_SPREADER_LEVEL == 2)
				{
					num2 += 30;
				}
				else if (yxqname.FLD_SPREADER_LEVEL == 3)
				{
					num2 += 50;
				}
				if (yxqname.LS_降低_暴率百分比 != 0.0)
				{
					num2 = (int)((double)num2 * (1.0 - yxqname.LS_降低_暴率百分比));
				}
			}
			if (num > num2)
			{
				return;
			}
			List<DropClass> gSDrop = DropClass.GetGSDrop(yxqname, Level, Rxjh_Map, FLD_PID);
			if (gSDrop == null)
			{
				return;
			}
			foreach (DropClass item in gSDrop)
			{
				if (item == null)
				{
					continue;
				}
				int num3 = RNG.Next(item.FLD_SUNX, item.FLD_SUND);
				ItmeClass itmeClass = World.Itme[item.FLD_PID];
				switch (item.FLD_PID)
				{
				case 800000001:
				case 800000002:
				case 800000023:
				case 800000024:
				case 800000025:
				case 800000026:
				case 800000028:
				case 800000030:
				case 800000031:
				case 800000032:
				case 800000033:
				case 800000061:
				case 800000062:
				case 800000071:
					if (item.FLD_MAGIC0 == 10)
					{
						switch (itmeClass.FLD_RESIDE2)
						{
						case 1:
						case 2:
						case 4:
						case 5:
						case 7:
						case 8:
						case 10:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num3;
							item.FLD_MAGIC2 = num3;
							item.FLD_MAGIC3 = num3;
							item.FLD_MAGIC4 = num3;
							break;
						case 6:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num3;
							item.FLD_MAGIC2 = num3;
							item.FLD_MAGIC3 = 0;
							item.FLD_MAGIC4 = 0;
							break;
						default:
							item.FLD_MAGIC0 = num3;
							break;
						}
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
					}
					break;
				case 800000046:
					if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(1, 22);
					}
					break;
				case 800000047:
					if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(23, 51);
					}
					break;
				case 1000000321:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(10, 50);
					break;
				case 1000000323:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(100, 150);
					break;
				case 1000000325:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(400, 699);
					break;
				case 1000000327:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(2000, 2499);
					break;
				default:
					if (item.FLD_MAGIC0 == 10)
					{
						switch (itmeClass.FLD_RESIDE2)
						{
						case 1:
						case 2:
						case 4:
						case 5:
						case 7:
						case 8:
						case 10:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num3;
							item.FLD_MAGIC2 = num3;
							item.FLD_MAGIC3 = num3;
							item.FLD_MAGIC4 = num3;
							break;
						case 6:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num3;
							item.FLD_MAGIC2 = num3;
							item.FLD_MAGIC3 = 0;
							item.FLD_MAGIC4 = 0;
							break;
						default:
							item.FLD_MAGIC0 = num3;
							break;
						}
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
					}
					break;
				}
				switch (item.FLD_PID)
				{
				case 300204007:
				case 300204008:
				case 300204009:
				case 300204010:
				case 300204026:
				case 300204027:
				case 300204033:
				case 300204034:
				case 300204037:
				case 300204038:
					暴物品到背包(yxqname, item);
					break;
				default:
					掉出物品(item, yxqname, RNG.Next(0, 22));
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "暴物品   出错：" + ex);
		}
	}

	public void 高手PlayerDrop(Players yxqname)
	{
		try
		{
			DropClass dropClass = DropClass.获得高手怪持有物品(Rxjh_Map, FLD_PID);
			if (dropClass != null)
			{
				掉出物品(dropClass, yxqname, 0);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(0, "BossPlayerDrop error " + ex);
		}
	}

	public void BossPlayerDrop(Players yxqname)
	{
		try
		{
			DropClass dropClass = DropClass.获得BOSS持有物品(Rxjh_Map, FLD_PID);
			if (dropClass != null)
			{
				掉出物品(dropClass, yxqname, 0);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(0, "BossPlayerDrop error " + ex);
		}
	}

	public void BOSS怪掉落(Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			if (DropClass.boss必掉(Rxjh_Map, FLD_PID))
			{
				BossPlayerDrop(yxqname);
			}
			List<DropClass> bossDrop = DropClass.GetBossDrop(yxqname, Level, Rxjh_Map, FLD_PID);
			if (bossDrop == null)
			{
				return;
			}
			foreach (DropClass item in bossDrop)
			{
				if (item == null)
				{
					continue;
				}
				int num = RNG.Next(item.FLD_SUNX, item.FLD_SUND);
				ItmeClass itmeClass = World.Itme[item.FLD_PID];
				switch (item.FLD_PID)
				{
				case 800000001:
				case 800000002:
				case 800000023:
				case 800000024:
				case 800000025:
				case 800000026:
				case 800000028:
				case 800000030:
				case 800000031:
				case 800000032:
				case 800000033:
				case 800000061:
				case 800000062:
				case 800000071:
					if (item.FLD_MAGIC0 == 10)
					{
						switch (itmeClass.FLD_RESIDE2)
						{
						case 1:
						case 2:
						case 4:
						case 5:
						case 7:
						case 8:
						case 10:
							item.FLD_MAGIC1 = num;
							item.FLD_MAGIC2 = num;
							item.FLD_MAGIC3 = num;
							item.FLD_MAGIC4 = num;
							break;
						case 6:
							item.FLD_MAGIC1 = num;
							item.FLD_MAGIC2 = num;
							break;
						default:
							item.FLD_MAGIC0 = num;
							break;
						}
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
					}
					break;
				case 800000046:
					if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(1, 22);
					}
					break;
				case 800000047:
					if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(23, 51);
					}
					break;
				case 1000000321:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(10, 50);
					break;
				case 1000000323:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(100, 150);
					break;
				case 1000000325:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(400, 699);
					break;
				case 1000000327:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(2000, 2499);
					break;
				default:
					if (item.FLD_MAGIC0 == 10)
					{
						switch (itmeClass.FLD_RESIDE2)
						{
						case 1:
						case 2:
						case 4:
						case 5:
						case 7:
						case 8:
						case 10:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num;
							item.FLD_MAGIC2 = num;
							item.FLD_MAGIC3 = num;
							item.FLD_MAGIC4 = num;
							break;
						case 6:
							item.FLD_MAGIC0 = 0;
							item.FLD_MAGIC1 = num;
							item.FLD_MAGIC2 = num;
							item.FLD_MAGIC3 = 0;
							item.FLD_MAGIC4 = 0;
							break;
						default:
							item.FLD_MAGIC0 = num;
							break;
						}
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
					}
					break;
				}
				switch (item.FLD_PID)
				{
				case 300204007:
				case 300204008:
				case 300204009:
				case 300204010:
				case 300204026:
				case 300204027:
				case 300204033:
				case 300204034:
				case 300204037:
				case 300204038:
					暴物品到背包(yxqname, item);
					break;
				default:
					掉出物品(item, yxqname, RNG.Next(0, 22));
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "暴物品   出错：" + ex);
		}
	}

	public void 普通怪掉落(Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			int num = RNG.Next(1, 8000);
			int num2 = World.暴率 + 坐标Class.地图增加爆率(yxqname.人物坐标_地图) + yxqname.角色暴率;
			if (World.开启全服经验 != null)
			{
				num2 *= World.双倍奖励爆率倍数;
			}
			if (yxqname.组队id != 0 && World.W组队.TryGetValue(yxqname.组队id, out var value))
			{
				num2 += (int)((double)num2 * World.获得队伍暴率加成(value));
			}
			if (yxqname != null)
			{
				if (yxqname.FLD_人物_追加_物品掉落概率百分比 > 0.0)
				{
					num2 += (int)((double)num2 * yxqname.FLD_人物_追加_物品掉落概率百分比);
				}
				if (yxqname.查询天关地图(yxqname.人物坐标_地图))
				{
					num2 += (int)yxqname.得到天关福利加成(1, yxqname.人物坐标_地图);
				}
				if (yxqname.医生群疗_追加_爆率 > 0)
				{
					num2 += yxqname.医生群疗_追加_爆率;
				}
				if (yxqname.FLD_VIP == 1)
				{
					num2 += World.VIP爆率增加;
				}
				if (yxqname.FLD_SPREADER_LEVEL == 1)
				{
					num2 += 20;
				}
				else if (yxqname.FLD_SPREADER_LEVEL == 2)
				{
					num2 += 30;
				}
				else if (yxqname.FLD_SPREADER_LEVEL == 3)
				{
					num2 += 50;
				}
				if (yxqname.帮派Id != 0)
				{
					if (yxqname.帮派等级 == 4)
					{
						num2 = (int)((double)num2 * 1.02);
					}
					else if (yxqname.帮派等级 == 5)
					{
						num2 = (int)((double)num2 * 1.05);
					}
					else if (yxqname.帮派等级 == 6)
					{
						num2 = (int)((double)num2 * 1.08);
					}
					else if (yxqname.帮派等级 == 7)
					{
						num2 = (int)((double)num2 * 1.12);
					}
				}
				if (yxqname.LS_降低_暴率百分比 != 0.0)
				{
					num2 = (int)((double)num2 * (1.0 - yxqname.LS_降低_暴率百分比));
				}
				if (是否绝命技死亡)
				{
					num2 += (int)((double)num2 * 绝命技死亡爆率加成);
				}
			}
			if (num > num2)
			{
				return;
			}
			DropClass drop = DropClass.GetDrop(yxqname, Level, Rxjh_Map, FLD_PID);
			if (drop == null)
			{
				return;
			}
			int num3 = RNG.Next(drop.FLD_SUNX, drop.FLD_SUND);
			ItmeClass itmeClass = World.Itme[drop.FLD_PID];
			switch (drop.FLD_PID)
			{
			case 800000001:
			case 800000002:
			case 800000023:
			case 800000024:
			case 800000025:
			case 800000026:
			case 800000028:
			case 800000030:
			case 800000031:
			case 800000032:
			case 800000033:
			case 800000061:
			case 800000062:
			case 800000071:
				if (drop.FLD_MAGIC0 == 10)
				{
					switch (itmeClass.FLD_RESIDE2)
					{
					case 1:
					case 2:
					case 4:
					case 5:
					case 7:
					case 8:
					case 10:
						drop.FLD_MAGIC0 = 0;
						drop.FLD_MAGIC1 = num3;
						drop.FLD_MAGIC2 = num3;
						drop.FLD_MAGIC3 = num3;
						drop.FLD_MAGIC4 = num3;
						break;
					case 6:
						drop.FLD_MAGIC0 = 0;
						drop.FLD_MAGIC1 = num3;
						drop.FLD_MAGIC2 = num3;
						drop.FLD_MAGIC3 = 0;
						drop.FLD_MAGIC4 = 0;
						break;
					default:
						drop.FLD_MAGIC0 = num3;
						break;
					}
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000046:
				if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = RNG.Next(1, 22);
				}
				break;
			case 800000047:
				if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = RNG.Next(23, 51);
				}
				break;
			case 1000000321:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(10, 50);
				break;
			case 1000000323:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(100, 150);
				break;
			case 1000000325:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(400, 699);
				break;
			case 1000000327:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(2000, 2499);
				break;
			default:
				if (drop.FLD_MAGIC0 == 10)
				{
					switch (itmeClass.FLD_RESIDE2)
					{
					case 1:
					case 2:
					case 4:
					case 5:
					case 7:
					case 8:
					case 10:
						drop.FLD_MAGIC0 = 0;
						drop.FLD_MAGIC1 = num3;
						drop.FLD_MAGIC2 = num3;
						drop.FLD_MAGIC3 = num3;
						drop.FLD_MAGIC4 = num3;
						break;
					case 6:
						drop.FLD_MAGIC0 = 0;
						drop.FLD_MAGIC1 = num3;
						drop.FLD_MAGIC2 = num3;
						drop.FLD_MAGIC3 = 0;
						drop.FLD_MAGIC4 = 0;
						break;
					default:
						drop.FLD_MAGIC0 = num3;
						break;
					}
				}
				break;
			}
			switch (drop.FLD_PID)
			{
			case 300204007:
			case 300204008:
			case 300204009:
			case 300204010:
			case 300204026:
			case 300204027:
			case 300204033:
			case 300204034:
			case 300204037:
			case 300204038:
				暴物品到背包(yxqname, drop);
				break;
			default:
				掉出物品(drop, yxqname, 0);
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "暴物品   出错：" + ex);
		}
	}

	public void 暴物品到背包(Players yxqname, DropClass item)
	{
		try
		{
			if (yxqname == null)
			{
				return;
			}
			Players players = yxqname;
			if (yxqname.组队id != 0)
			{
				players = yxqname.组队分配(yxqname);
			}
			int num = players.得到包裹空位(players);
			if (num != -1)
			{
				players.增加物品带属性(item.FLD_PID, num, 1, item.FLD_MAGIC0, item.FLD_MAGIC1, item.FLD_MAGIC2, item.FLD_MAGIC3, item.FLD_MAGIC4, item.FLD_初级附魂, item.FLD_中级附魂, item.FLD_进化, item.FLD_绑定, 0);
				if (World.是否开启公告掉落提示 == 1 && item.是否开启公告 == 1)
				{
					players.玩家从怪物身上获得物品(players.UserName, players.人物坐标_地图, FLD_PID, item.FLD_PID);
				}
				else if (item.是否开启公告 == 1)
				{
					string text = yxqname.UserName + "玩家从" + 坐标Class.getmapname(yxqname.人物坐标_地图) + "的[" + Name + "]身上获得了[" + item.FLD_NAME + "]。";
					World.conn.发送("刷怪掉宝|" + 6 + "|" + text + "|" + World.服务器ID);
				}
			}
			else
			{
				yxqname.系统提示("背包没空位了。");
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, yxqname, "暴物品到背包", $"物品ID: {item.FLD_PID}, 数量: {1}");
		}
	}

	public void 异常状态列表()
	{
		if (异常状态 == null || 异常状态.Count == 0)
		{
			return;
		}
		Queue queue = Queue.Synchronized(new Queue());
		try
		{
			foreach (异常状态类 value2 in 异常状态.Values)
			{
				queue.Enqueue(value2);
			}
			while (queue.Count > 0)
			{
				异常状态类 异常状态类2 = (异常状态类)queue.Dequeue();
				异常状态类2.时间结束事件();
				if (异常状态 != null)
				{
					异常状态.TryRemove(异常状态类2.FLD_PID, out var _);
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "NPC异常状态列表", $"NPC: {Name}[{FLD_INDEX}]");
		}
	}

	public bool 查找范围Npc(int far_, NpcClass Npc)
	{
		if (Npc.Rxjh_Map != Rxjh_Map)
		{
			return false;
		}
		float num = Npc.Rxjh_X - Rxjh_X;
		float num2 = Npc.Rxjh_Y - Rxjh_Y;
		return (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= far_;
	}

	public bool 查找范围玩家(int far_, Players Playe)
	{
		if (Playe.人物坐标_地图 != Rxjh_Map)
		{
			return false;
		}
		if (Playe.人物坐标_地图 == 7101)
		{
			far_ = 1000;
		}
		float num = Playe.人物坐标_X - Rxjh_X;
		float num2 = Playe.人物坐标_Y - Rxjh_Y;
		return (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= far_;
	}

	public bool 查找NPC范围距离(int 范围, Players Playe)
	{
		if (Playe.人物坐标_地图 != Rxjh_Map)
		{
			return false;
		}
		if (Playe.人物坐标_地图 == 7101)
		{
			范围 = 1000;
		}
		float num = Playe.人物坐标_X - Rxjh_cs_X;
		float num2 = Playe.人物坐标_Y - Rxjh_cs_Y;
		return (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= 范围;
	}

	public void 发送怪物头上图标(int 是否消失)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "NpcClass_发送怪物头上图标");
		}
		string hex = "AA551200E9478B100C0046050000E94700000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(是否消失), 0, array, 18, 4);
		广播数据(array, array.Length);
	}

	public bool 获取范围玩家()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "NpcClass_获取范围玩家");
		}
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物_HP > 0 && 查找范围玩家(World.引怪距离, value))
				{
					吸怪清理(value);
					if (value.怪物攻击列表.Count < World.吸怪数量)
					{
						npc_Add(value);
						Play_Add(value, 0);
						return true;
					}
				}
			}
		}
		catch (Exception)
		{
			return false;
		}
		return false;
	}

	public void 广播数据(byte[] data, int length)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "Players_发送当前范围广播数据");
		}
		try
		{
			foreach (Players value2 in PlayList.Values)
			{
				Players value;
				if (value2.Client != null)
				{
					if (value2.Client.Running)
					{
						value2.Client.Send(data, length);
					}
				}
				else
				{
					value2.Client.Dispose();
					PlayList.TryRemove(value2.人物全服ID, out value);
				}
				if (!World.allConnectedChars.ContainsKey(value2.Client.WorldId))
				{
					if (value2.Client != null)
					{
						value2.Client.Dispose();
					}
					PlayList.TryRemove(value2.人物全服ID, out value);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "NPC广播数据2出错2：" + ex);
		}
	}

	public static void 发送当前显示的讨伐副本怪物(ConcurrentDictionary<int, NpcClass> NpcList, Players player)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (NpcClass value in NpcList.Values)
		{
			if (value.FLD_PID == 16555 || value.FLD_PID == 16556)
			{
				continue;
			}
			if (value.NPC死亡)
			{
				byte[] array = Converter.hexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_INDEX), 0, array, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_INDEX), 0, array, 10, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array, 26, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array, 34, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.怪物数字), 0, array, 46, 4);
				if (player.Client != null)
				{
					player.Client.Send(array, array.Length);
				}
			}
			else
			{
				byte[] array2 = Converter.hexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_INDEX), 0, array2, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.FLD_INDEX), 0, array2, 10, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array2, 18, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array2, 26, 4);
				if (player.Client != null)
				{
					player.Client.Send(array2, array2.Length);
				}
			}
		}
	}

	public void 地狱火龙副本状态效果()
	{
		Random random = new Random(DateTime.Now.Millisecond);
		int num = random.Next(1, 400);
		Rxjh_X = num - 200;
		num = random.Next(1, 400);
		Rxjh_Y = num - 400;
		byte[] array = Converter.hexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 26, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 34, 4);
		if (FLD_PID == 16607 || FLD_PID == 16557)
		{
			num = random.Next(2, 5);
			Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 46, 4);
			怪物数字 = num;
		}
		广播数据(array, array.Length);
		templayer.Clear();
		NPC死亡 = true;
		if (自动攻击 != null)
		{
			自动攻击.Enabled = false;
		}
		if (自动移动 != null)
		{
			自动移动.Enabled = true;
		}
		if (自动复活 != null)
		{
			自动复活.Interval = FLD_NEWTIME * 1000;
			自动复活.Enabled = true;
		}
		else
		{
			自动复活 = new System.Timers.Timer(FLD_NEWTIME * 1000);
			自动复活.Elapsed += 自动复活事件;
			自动复活.Enabled = true;
		}
	}

	public void 地狱火龙副本状态效果消失(bool 是否玩家击杀)
	{
		NPC死亡 = false;
		byte[] array = Converter.hexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 26, 4);
		广播数据(array, array.Length);
		if (自动复活 != null)
		{
			自动复活.Enabled = false;
			自动复活.Close();
			自动复活.Dispose();
			自动复活 = null;
		}
		if (!是否玩家击杀)
		{
			讨伐副本怪物秒人();
		}
	}

	public void 讨伐副本怪物秒人()
	{
		switch (FLD_PID)
		{
		case 16557:
		{
			List<Players> list = new List<Players>();
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 43001 && World.是否讨伐副本危险区域(value) && !value.Player死亡 && value.副本复活剩余次数 > 0)
				{
					list.Add(value);
				}
			}
			if (list.Count <= 0)
			{
				break;
			}
			for (int i = 0; i < 怪物数字; i++)
			{
				Random random = new Random(DateTime.Now.Millisecond);
				int index = random.Next(0, list.Count - 1);
				Players players = list[index];
				players.人物_HP = 0;
				players.人物死亡(FLD_INDEX);
				PlayCw = null;
				Play_null();
				players.更新HP_MP_SP();
				list.Remove(players);
				if (list.Count == 0)
				{
					break;
				}
			}
			break;
		}
		case 16600:
		{
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (!查找范围玩家(100, value2) && World.是否讨伐副本危险区域(value2) && value2.副本复活剩余次数 > 0)
				{
					value2.人物_HP = 0;
					value2.人物死亡(FLD_INDEX);
					PlayCw = null;
					Play_null();
					value2.更新HP_MP_SP();
				}
			}
			break;
		}
		case 16602:
		{
			foreach (Players value3 in World.allConnectedChars.Values)
			{
				if (查找范围玩家(100, value3) && value3.副本复活剩余次数 > 0)
				{
					value3.人物_HP = 0;
					value3.人物死亡(FLD_INDEX);
					PlayCw = null;
					Play_null();
					value3.更新HP_MP_SP();
				}
			}
			break;
		}
		case 16604:
		{
			foreach (Players value4 in World.allConnectedChars.Values)
			{
				if (查找范围玩家(80, value4) && value4.副本复活剩余次数 > 0)
				{
					value4.人物_HP = 0;
					value4.人物死亡(FLD_INDEX);
					PlayCw = null;
					Play_null();
					value4.更新HP_MP_SP();
				}
			}
			break;
		}
		case 16607:
			if (NPC死亡)
			{
				break;
			}
			{
				foreach (Players value5 in World.allConnectedChars.Values)
				{
					if (World.是否讨伐副本危险区域(value5) && !value5.Player死亡 && value5.副本复活剩余次数 > 0)
					{
						value5.人物_HP = 0;
						value5.人物死亡(FLD_INDEX);
						PlayCw = null;
						Play_null();
						value5.更新HP_MP_SP();
					}
				}
				break;
			}
		}
	}

	public static void 更新NPC复活数据(ConcurrentDictionary<int, NpcClass> NpcList, Players Playe)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (NpcClass value in NpcList.Values)
		{
			using 发包类 发包类 = new 发包类();
			发包类.Write4(1);
			发包类.Write4(value.FLD_INDEX);
			发包类.Write4(value.FLD_INDEX);
			发包类.Write2(value.FLD_PID);
			发包类.Write2(1);
			发包类.Write4(value.Rxjh_HP);
			发包类.Write4(value.Max_Rxjh_HP);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write(4f);
			发包类.Write(value.FLD_FACE1);
			发包类.Write(value.FLD_FACE2);
			发包类.Write(value.Rxjh_X);
			发包类.Write(value.Rxjh_Z);
			发包类.Write(value.Rxjh_Y);
			发包类.Write4(0);
			发包类.Write4(128369664);
			发包类.Write4(0);
			发包类.Write4(215040);
			发包类.Write4(0);
			发包类.Write4(786432);
			发包类.Write4(uint.MaxValue);
			if (Playe.Client != null)
			{
				Playe.Client.SendPak(发包类, 31488, value.FLD_INDEX);
			}
		}
	}
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RxjhServer;

public class FormCdkeyShow : Form
{
	private Dictionary<int, List<string>> _showDic;

	private IContainer components = null;

	private RichTextBox richTextBox1;

	private Button button1;

	public FormCdkeyShow(Dictionary<int, List<string>> showDic)
	{
		InitializeComponent();
		_showDic = showDic;
	}

	private void FormCdkeyShow_Load(object sender, EventArgs e)
	{
		foreach (KeyValuePair<int, List<string>> item in _showDic)
		{
			foreach (string item2 in item.Value)
			{
				richTextBox1.AppendText(item2);
				richTextBox1.AppendText(Environment.NewLine);
			}
			richTextBox1.AppendText(Environment.NewLine);
			richTextBox1.AppendText(Environment.NewLine);
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		Clipboard.SetText(richTextBox1.Text);
		MessageBox.Show("复制Cdkey内容成功");
	}

	private void richTextBox1_TextChanged(object sender, EventArgs e)
	{
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormCdkeyShow));
            this.richTextBox1 = new System.Windows.Forms.RichTextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // richTextBox1
            // 
            this.richTextBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.richTextBox1.BackColor = System.Drawing.SystemColors.InfoText;
            this.richTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richTextBox1.ForeColor = System.Drawing.SystemColors.Control;
            this.richTextBox1.Location = new System.Drawing.Point(12, 41);
            this.richTextBox1.Name = "richTextBox1";
            this.richTextBox1.Size = new System.Drawing.Size(677, 397);
            this.richTextBox1.TabIndex = 1;
            this.richTextBox1.Text = "";
            this.richTextBox1.TextChanged += new System.EventHandler(this.richTextBox1_TextChanged);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(562, 12);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(111, 23);
            this.button1.TabIndex = 2;
            this.button1.Text = "复制内容";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // FormCdkeyShow
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(701, 450);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.richTextBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FormCdkeyShow";
            this.Text = "未使用CDK展示";
            this.Load += new System.EventHandler(this.FormCdkeyShow_Load);
            this.ResumeLayout(false);

	}
}

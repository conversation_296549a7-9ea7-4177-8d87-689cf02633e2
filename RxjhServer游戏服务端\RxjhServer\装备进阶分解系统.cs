﻿using System;
using Newtonsoft.Json.Linq;
using RxjhServer;
using RxjhServer.DbClss;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

public class 装备进阶分解系统
{
    private readonly Players player;

    public 装备进阶分解系统(Players player)
    {
        this.player = player;
    }

    // 统一进行装备进阶的方法
    public void 进行装备进阶(int 材料ID, string 材料类型)
    {
        if (player.Player死亡 || player.人物锁定)
        {
            return;
        }

        player.查百宝阁元宝数();

        if (!World.装备进阶.TryGetValue(BitConverter.ToInt32(player.装备栏包裹[0].物品ID, 0), out var value))
        {
            player.系统提示($"请将要进阶的低阶 {材料类型} 放入背包栏第一格", 22, $"{材料类型}进阶");
            return;
        }

        if (!检查合成属性(value) || !装备强化等级要求(value) || !装备是否绑定())
        {
            return;
        }

        bool sufficient = false;

        for (int j = 0; j < 96; j++)
        {
            int 当前物品ID = BitConverter.ToInt32(player.装备栏包裹[j].物品ID, 0);
            int 当前物品数量 = BitConverter.ToInt32(player.装备栏包裹[j].物品数量, 0);

            if (当前物品ID == 材料ID && 当前物品数量 >= value.需要材料1数量)
            {
                for (int s = 0; s < 96; s++)
                {
                    int 第二个物品ID = BitConverter.ToInt32(player.装备栏包裹[s].物品ID, 0);
                    int 第二个物品数量 = BitConverter.ToInt32(player.装备栏包裹[s].物品数量, 0);
                    if (第二个物品ID == value.需要材料2PID && 第二个物品数量 >= value.需要材料2数量 && player.FLD_RXPIONT >= value.需要元宝数量)
                    {
                        player.减去物品(j, value.需要材料1数量);
                        player.减去物品(s, value.需要材料2数量);
                        player.检察元宝数据(value.需要元宝数量, 0, "装备进阶");
                        player.保存元宝数据();
                        sufficient = true;
                        break;
                    }
                }
            }

            if (sufficient)
            {
                break;
            }
        }

        if (!sufficient)
        {
            player.系统提示($"进阶材料不足「{value.需要装备名称}」进阶为「{value.进阶装备名称}」需要「{value.需要材料1数量}」个「{value.需要材料1名称}」「{value.需要材料2数量}」个「{value.需要材料2名称}」「{value.需要元宝数量}」个元宝！", 22, "进阶提示");
        }
        else
        {
            int 进阶装备属性1 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_MAGIC1 : value.进阶装备属性1;
            int 进阶装备属性2 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_MAGIC2 : value.进阶装备属性2;
            int 进阶装备属性3 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_MAGIC3 : value.进阶装备属性3;
            int 进阶装备属性4 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_MAGIC4 : value.进阶装备属性4;
            int UP_进化 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_FJ_进化 : 0;
            int UP_觉醒 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_FJ_觉醒 : 0;
            int UP_中魂 = value.是否继承属性 == 1 ? player.装备栏包裹[0].FLD_FJ_中级附魂 : 0;

            player.减去物品(0, 1);

            player.增加物品带属性(value.进阶装备PID, player.得到包裹空位位置(), 1, 0, 进阶装备属性1, 进阶装备属性2, 进阶装备属性3, 进阶装备属性4, UP_觉醒, UP_中魂, UP_进化, 0, value.使用天数);

            player.系统提示($"进阶成功 消耗 {材料类型}碎片 {value.需要材料1数量} 个 {value.需要材料2名称} {value.需要材料2数量} 个", 22, "消耗提示");

            if (value.是否提示 == 1)
            {
                World.全局提示($"{材料类型}进阶", 10, $"恭喜玩家「{player.UserName}」成功将「{value.需要装备名称}」进阶为「{value.进阶装备名称}」实力大增");
            }
            else
            {
                player.系统提示($"恭喜您将「{value.需要装备名称}」成功进阶为「{value.进阶装备名称}」", 10, $"{材料类型}进阶");
            }
        }
    }

    // 一键分解接口
    public void 一键分解(int 道具ID, int num5)
    {
        if (player.GetAddState(900000169))
        {
            player.系统提示("请谨慎操作，稍后再试！", 22, "系统提示");
            return;
        }

        bool foundItemToDecompose = false;
        int sls = 0;
        追加状态类 追加状态类423 = new 追加状态类(player, 30000, num5, 1);
        player.追加状态列表.TryAdd(追加状态类423.FLD_PID, 追加状态类423);
        player.状态效果(BitConverter.GetBytes(道具ID), 1, 30000);
        
        try
        {
            for (int p = 0; p < 36; p++)
            {
                if (player.装备栏包裹[p] == null || player.装备栏包裹[p].Get物品数量 <= 0)
                    continue;

                int currentItemId = BitConverter.ToInt32(player.装备栏包裹[p].物品ID, 0);
                sls = player.装备栏包裹[p].Get物品数量;

                // 石头分解
                switch (currentItemId)
                {
                    case 800000013:  // 热血石
                    case 1000001650: // 天方
                    case 1000001651: // 地方
                    case 800000061:  // 乾坤金刚
                    case 800000062:  // 凝霜寒玉
                    case 800000046:  // 初级奇玉石
                    case 1000000365: // 天然初级集魂石
                        RxjhClass.添加清理记录(player.UserName, player.Userid, p, player.装备栏包裹, "石头分解");
                        player.减去物品(p, sls);
                        player.增加物品带属性(900000171, player.得到包裹空位位置(), sls, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0); // 神石碎片
                        player.系统提示($"分解成功 神石碎片 {sls}个", 22, "系统提示");
                        foundItemToDecompose = true;
                        continue; 
                }

                // 材料表中定义的装备分解 
                if (World.装备分解.TryGetValue(currentItemId, out 装备分解材料 value790) &&
                    currentItemId == value790.分解物品PID)
                {
                    if (value790.分解模式 == 0 || value790.分解模式 == 1)
                    {
                        RxjhClass.添加清理记录(player.UserName, player.Userid, p, player.装备栏包裹, "材料表分解");
                        player.减去物品(p, sls);
                        if (value790.分解模式 == 0)
                        {
                            player.增加物品带属性(value790.分解材料1PID, player.得到包裹空位位置(), value790.分解材料1数量, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                            player.系统提示($"分解成功 {value790.分解材料1名称} {value790.分解材料1数量}个", 22, "系统提示");
                        }
                        else if (value790.分解模式 == 1)
                        {
                            player.增加物品带属性(value790.分解材料1PID, player.得到包裹空位位置(), value790.分解材料1数量, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                            player.增加物品带属性(value790.分解材料2PID, player.得到包裹空位位置(), value790.分解材料2数量, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                            player.系统提示($"分解成功 {value790.分解材料1名称} {value790.分解材料1数量}个 {value790.分解材料2名称} {value790.分解材料2数量}个", 22, "系统提示");
                        }
                        foundItemToDecompose = true;
                        continue;
                    }
                }

                // 不在材料表中的物品 
                else if (World.Itme.TryGetValue(currentItemId, out ItmeClass value78))
                {
                    int 碎片NUM = GetFragmentCountForLevel(value78.FLD_LEVEL);
                    if (碎片NUM > 0 && IsValidResidue(value78.FLD_RESIDE2))
                    {
                        int 碎片PID = 0;
                        switch (value78.FLD_RESIDE2)
                        {
                            case 1: 碎片PID = 999000221; break;
                            case 2: 碎片PID = 999000223; break;
                            case 4: 碎片PID = 999000220; break;
                            case 5: 碎片PID = 999000219; break;
                            case 6: 碎片PID = 999000222; break;
                        }
                        if (碎片PID != 0)
                        {
                            RxjhClass.添加清理记录(player.UserName, player.Userid, p, player.装备栏包裹, "其它分解");
                            player.减去物品(p, 1);
                            player.增加物品带属性(碎片PID, player.得到包裹空位位置(), 碎片NUM, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                            foundItemToDecompose = true;
                            continue;
                        }
                    }
                }
            }

            if (foundItemToDecompose)
            {
                player.系统提示("高阶装备可放置背包第1格，输入命令「!分解神器」单件分解", 10, "友情提示");
            }
            else
            {
                player.系统提示("您背包中无可一键分解物品，高阶装备可放背包第1格，输入命令「!分解神器」单件分解", 10, "分解提示");
            }
        }
        catch (Exception)
        {
            player.系统提示("分解过程中发生错误，不支持分解的物品", 10, "错误提示");
        }
    }

    // 高阶分解接口OK
    public bool 高阶分解命令(string 命令)
    {
        // 检查命令是否为高阶分解命令
        if (命令 != "!分解神器" && 命令 != "!分解花篮" && 命令 != "!分解花珠" && 
            命令 != "!分解灵宠" && 命令 != "!分解灵兽" && 命令 != "!分解首饰" && 
            命令 != "!分解装备" && 命令 != "!分解门甲" && 命令 != "!分解物品")
        {
            return false;
        }

        int 物品ID = BitConverter.ToInt32(player.装备栏包裹[0].物品ID, 0);

        if (World.装备分解.TryGetValue(物品ID, out 装备分解材料 value))
        {
            if (物品ID == value.分解物品PID && value.分解模式 == 2)
            {
                RxjhClass.添加清理记录(player.UserName, player.Userid, 0, player.装备栏包裹, "高阶分解");
                player.减去物品(0, 1);
                player.增加物品带属性(value.分解材料1PID, player.得到包裹空位位置(), value.分解材料1数量, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                player.增加物品带属性(value.分解材料2PID, player.得到包裹空位位置(), value.分解材料2数量, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                player.系统提示("恭喜已将 " + value.分解物品名称 + " 分解成功，获得" + value.分解材料1数量 + "个" + value.分解材料1名称 + " " + value.分解材料2数量 + "个" + value.分解材料2名称 + " ", 22, "高阶分解");
                return true;
            }
            else
            {
                player.系统提示("该物品不能使用高阶分解，低阶装备支持批量一键分解！", 10, "分解提示");
                return false;
            }
        }
        else
        {
            player.系统提示("请将需分解的高阶「装备 花篮 花珠 灵兽 灵宠 门甲 首饰」放置背包第1格，并确保装备符合分解", 22, "分解提示");
            player.系统提示("低阶装备可批量一键分解，请在平十指处购买分解道具", 10, "友情提示");
            return false;
        }
    }

    // 其他辅助方法
    public string 合成属性提示(int 属性) //EVIAS
    {
        string text = "";
        int num = 属性 / 10000000;
        int num2 = 属性 % 10000000;
        switch (num)
        {
            case 1:
                text = text + "攻击" + num2;
                break;
            case 2:
                text = text + "防御" + num2;
                break;
            case 3:
                text = text + "生命" + num2;
                break;
            case 4:
                text = text + "内功" + num2;
                break;
            case 5:
                text = text + "命中" + num2;
                break;
            case 6:
                text = text + "回避" + num2;
                break;
            case 7:
                text = text + "武功" + num2 + "%";
                break;
            case 8:
                text = text + "气功" + num2;
                break;
            case 9:
                text = text + "升级合成率" + num2 + "%";
                break;
            case 10:
                text = text + "追伤" + num2;
                break;
            case 11:
                text = text + "武防" + num2;
                break;
            case 12:
                text = text + "获得金钱" + num2 + "%";
                break;
            case 13:
                text = text + "死亡损失经验减少" + num2 + "%";
                break;
            case 15:
                text = text + "经验值增加" + num2 + "%";
                break;
        }
        return text;
    }

    private bool 检查合成属性(材料进阶装备 value)
    {
        if ((value.需要装备属性1 != 0 && player.装备栏包裹[0].FLD_MAGIC1 != value.需要装备属性1) ||
            (value.需要装备属性2 != 0 && player.装备栏包裹[0].FLD_MAGIC2 != value.需要装备属性2) ||
            (value.需要装备属性3 != 0 && player.装备栏包裹[0].FLD_MAGIC3 != value.需要装备属性3) ||
            (value.需要装备属性4 != 0 && player.装备栏包裹[0].FLD_MAGIC4 != value.需要装备属性4))
        {
            player.系统提示("合成属性不符 装备合成属性必须为「" + 合成属性提示(value.需要装备属性1) + "」才能进阶", 22, "装备进阶");
            return false;
        }
        return true;
    }

    private bool 装备强化等级要求(材料进阶装备 value)
    {
        if (player.装备栏包裹[0].FLD_强化数量 < value.需要装备强化等级)
        {
            player.系统提示($"强化等级不符 装备强化等级低于「{value.需要装备强化等级}」无法进阶", 22, "装备进阶");
            return false;
        }
        return true;
    }

    private bool 装备是否绑定()
    {
        if (player.装备栏包裹[0].物品绑定 == true)
        {
            player.系统提示($"绑定物品不可进阶", 22, "装备进阶");
            return false;
        }
        return true;
    }

    private int GetFragmentCountForLevel(int level)
    {
        if (level >= 100 && level <= 170) //24.0 EVIAS +支持170级装备分解
        {
            int index = (level - 100) / 10; // 100级=0, 110级=1,...160级=6, 170级=7 EVIAS
            if (index >= World.碎片数量.Length) //24.0 EVIAS +170级使用160级的碎片数量
            {
                index = World.碎片数量.Length - 1; 
            }
            return int.Parse(World.碎片数量[index]);
        }
        return 0;
    }

    private bool IsValidResidue(int residueType)  // 判断物品是否符合分解条件 EVIAS
    {
        return residueType == 1 || residueType == 2 || residueType == 4 || residueType == 5 || residueType == 6;
    }
}
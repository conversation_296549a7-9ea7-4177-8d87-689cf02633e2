-- EVIAS 24.0 为兑换记录表添加硬件指纹字段
-- 执行日期：2025-07-01

-- 检查字段是否已存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[兑换记录]') AND name = '硬件指纹')
BEGIN
    ALTER TABLE [dbo].[兑换记录] ADD [硬件指纹] varchar(15) COLLATE Chinese_PRC_CI_AS DEFAULT ('0') NULL
    PRINT '硬件指纹字段添加成功'
END
ELSE
BEGIN
    PRINT '硬件指纹字段已存在，无需添加'
END

-- 为现有记录设置默认值（如果需要）
UPDATE [dbo].[兑换记录] SET [硬件指纹] = '0' WHERE [硬件指纹] IS NULL

PRINT '兑换记录表硬件指纹字段配置完成'

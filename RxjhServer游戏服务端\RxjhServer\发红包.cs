using System;

namespace RxjhServer;

public class 发红包
{
	private double double_0;

	private double double_1 = 0.01;

	private int int_0;

	private string string_0;

	public int RemainCount
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public double RemainMonry
	{
		get
		{
			return double_0;
		}
		set
		{
			double_0 = value;
		}
	}

	public string zjname
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	private void method_0(string string_1, double double_2)
	{
		if (double_2 > double_1)
		{
			double_1 = double_2;
			string_0 = string_1;
		}
	}

	public int GetRandomMoney(string string_1)
	{
		if (int_0 == 1)
		{
			int_0--;
			double num = Math.Round(double_0 * 100.0) / 100.0;
			method_0(string_1, num);
			World.参加红包列表.Clear();
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.系统提示("本次抢红包幸运玩家[" + zjname + "]大家恭喜他!!", 3, "抢红包系统");
			}
			return (int)num;
		}
		Random random = new Random();
		double num2 = 0.01;
		double num3 = double_0 / (double)int_0 * 2.0;
		double num4 = random.NextDouble() * num3;
		num4 = ((num4 <= num2) ? 0.01 : num4);
		num4 = Math.Floor(num4 * 100.0) / 100.0;
		method_0(string_1, num4);
		int_0--;
		double_0 -= num4;
		return (int)num4;
	}
}

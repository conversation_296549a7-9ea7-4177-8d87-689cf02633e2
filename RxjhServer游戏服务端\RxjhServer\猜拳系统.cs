using System;
using System.Timers;

namespace RxjhServer;

public class 猜拳系统 : IDisposable
{
	private System.Timers.Timer 时间1;

	private DateTime kssj;

	private int kssjint = 0;

	public 猜拳系统()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "猜拳系统错误1");
			}
			kssj = DateTime.Now.AddMinutes(World.猜拳倒计时);
			时间1 = new System.Timers.Timer(10000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch
		{
			Form1.WriteLine(0, "猜拳系统错误2");
		}
	}

	public void 时间结束事件1(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "猜拳系统错误3");
		}
		try
		{
			int num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			kssjint = num;
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.发送其他活动开始倒计时(kssjint);
				value.系统提示("距离猜拳开奖还剩下[" + kssjint + "]秒，亲们准备好了吗？", 10, "猜拳");
				value.系统提示("购买命令 !猜拳 石头 元宝数", 7, "猜拳");
				value.系统提示("买1赔" + World.剪刀石头布元宝倍数 + ",赌一赌吉普变路虎,搏一搏单车变摩托.", 3, "猜拳");
			}
			if (kssjint > 0)
			{
				return;
			}
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
			int 石头总元宝数 = World.石头总元宝数;
			int 剪刀总元宝数 = World.剪刀总元宝数;
			int 布总元宝数 = World.布总元宝数;
			int num2 = new Random().Next(1, World.剪刀石头布返还几率);
			int num3 = 0;
			num3 = ((num2 <= 0 || num2 >= 20) ? Math.Min(Math.Min(石头总元宝数, 剪刀总元宝数), 布总元宝数) : Math.Max(Math.Max(石头总元宝数, 剪刀总元宝数), 布总元宝数));
			if (World.剪刀石头布元开奖 != 0)
			{
				num3 = World.剪刀石头布元开奖;
			}
			if (num3 == World.石头总元宝数)
			{
				World.发送特殊公告("本期猜拳开奖为[石头]", 10, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[石头]", 3, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[石头]", 7, "猜拳");
			}
			else if (num3 == World.剪刀总元宝数)
			{
				World.发送特殊公告("本期猜拳开奖为[剪刀]", 10, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[剪刀]", 3, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[剪刀]", 7, "猜拳");
			}
			else if (num3 == World.布总元宝数)
			{
				World.发送特殊公告("本期猜拳开奖为[布]", 10, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[布]", 3, "猜拳");
				World.发送特殊公告("本期猜拳开奖为[布]", 7, "猜拳");
			}
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (num3 == World.石头总元宝数)
				{
					if (value2.猜拳 == "石头")
					{
						value2.检察元宝数据(value2.猜拳元宝 * World.剪刀石头布元宝倍数, 1, "猜拳");
						value2.系统提示("恭喜你猜拳赢了,获得" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "元宝", 3, "猜拳");
						World.发送特殊公告("恭喜玩家≮" + value2.UserName + "≯石头剪刀布赢了,获得[" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "]元宝", 10, "猜拳");
						value2.猜拳 = string.Empty;
					}
				}
				else if (num3 == World.剪刀总元宝数)
				{
					if (value2.猜拳 == "剪刀")
					{
						value2.检察元宝数据(value2.猜拳元宝 * World.剪刀石头布元宝倍数, 1, "猜拳");
						value2.系统提示("恭喜你猜拳赢了,获得" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "元宝", 3, "猜拳");
						World.发送特殊公告("恭喜玩家≮" + value2.UserName + "≯石头剪刀布赢了,获得[" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "]元宝", 10, "猜拳");
						value2.猜拳 = string.Empty;
					}
				}
				else if (num3 == World.布总元宝数 && value2.猜拳 == "布")
				{
					value2.检察元宝数据(value2.猜拳元宝 * World.剪刀石头布元宝倍数, 1, "猜拳");
					value2.系统提示("恭喜你猜拳赢了,获得" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "元宝", 3, "猜拳");
					World.发送特殊公告("恭喜玩家≮" + value2.UserName + "≯石头剪刀布赢了,获得[" + value2.猜拳元宝 * World.剪刀石头布元宝倍数 + "]元宝", 10, "猜拳");
					value2.猜拳 = string.Empty;
				}
				if (value2.猜拳 != string.Empty)
				{
					value2.系统提示("很抱歉本局你猜拳输了，不要灰心总是会赢得！", 3, "猜拳");
					value2.猜拳 = string.Empty;
				}
			}
			World.猜拳.Dispose();
		}
		catch
		{
			Form1.WriteLine(0, "猜拳系统错误4");
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "猜拳系统错误5");
		}
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		World.剪刀总元宝数 = 0;
		World.石头总元宝数 = 0;
		World.布总元宝数 = 0;
		World.剪刀石头布元开奖 = 0;
		World.猜拳 = null;
	}
}

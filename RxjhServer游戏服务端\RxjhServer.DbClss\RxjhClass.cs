using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using RxjhServer.HelperTools;
using RxjhServer.Model.Log;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace RxjhServer.DbClss;

public class RxjhClass
{
	private static readonly ItmesIDClass ItmeId = new ItmesIDClass();

	public static string md5(string strmm)
	{
		return BitConverter.ToString(new MD5CryptoServiceProvider().ComputeHash(Encoding.ASCII.GetBytes(strmm))).Replace("-", string.Empty).ToLower();
	}

	public static long GetDBItmeId()
	{
		return ItmeId.AcquireBuffer();
	}

	public static string GetUserIpadds(string ip)
	{
		try
		{
			return new IPScaner().IPLocation(Application.StartupPath + "\\QQWry.Dat", ip);
		}
		catch
		{
			return "";
		}
	}



    public static void AddBinIp(string Binip) //Evias
    {
        string sql = "INSERT INTO 封IP表 (FLD_BANEDIP) VALUES (@ip)";
        SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@ip", SqlDbType.VarChar, 50, Binip) };
        DBA.ExeSqlCommand(sql, prams, "rxjhaccount");
    }

    public static int GetUserName1(string name)
	{
		string sqlCommand = "SELECT FLD_NAME FROM TBL_XWWL_Char WHERE FLD_NAME=@name";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return 1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static string 得到账号下第一个人物名字(string userID)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Char  WHERE  FLD_ID  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 500, userID) });
		if (dBToDataTable == null)
		{
			return "";
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable.Rows[0]["FLD_NAME"].ToString();
		}
		return "";
	}

    public static void 上线删除传书已读(string sname, int 是否已读) //EVIAS
    {
        DBA.LogSql.Delete<传书记录>()
            .Where(w => w.接收人物名 == sname && w.阅读标识 == 是否已读)
            .ExecuteAffrows();
    }

    public static void 变更门服(int 帮派id, int 门服字, int 门服颜色)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  门服字={1},门服颜色={2}  WHERE  ID='{0}'", 帮派id, 门服字, 门服颜色));
	}

	public static DataTable 得到天魔神宫占领信息()
	{
		string sqlCommand = "SELECT * FROM 天魔神宫信息  where 分区= '" + World.分区编号 + "' ";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static int 更新天魔神宫信息(string 占领门派, string 占领日期, int 城门强化等级, string 分区编号)
	{
		DataTable dataTable = 得到天魔神宫占领信息();
		if (dataTable != null)
		{
			return DBA.ExeSqlCommand($"UPDATE 天魔神宫信息 SET 占领者='{占领门派}',占领日期='{占领日期}',城门强化等级={城门强化等级},更新时间='{DateTime.Now.AddHours(World.攻城战胜利占领时间)}' WHERE 分区='{分区编号}'");
		}
		return DBA.ExeSqlCommand($"INSERT INTO 天魔神宫信息 (占领日期,占领者,城门强化等级,更新时间,分区) VALUES ({占领日期},'{占领门派}',{城门强化等级},'{DateTime.Now.AddHours(World.攻城战胜利占领时间)}','{分区编号}')");
	}

	public static void 更新登陆IP(string Userid, string ip, string 名字)
	{
		SqlDBA.RunProc(new SqlConnection(DBA.getstrConnection("rxjhaccount")), "XWWL_UPDATE_ACCOUNT_ZOGINIP", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 30, Userid),
			SqlDBA.MakeInParam("@ip", SqlDbType.VarChar, 30, ip)
		}, 名字);
	}

    public static int CheckUserName(string name)
    {
        DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT FLD_NAME FROM TBL_XWWL_Char WHERE FLD_NAME=@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
        if (dBToDataTable == null)
        {
            return -1;
        }
        if (dBToDataTable.Rows.Count == 1)
        {
            dBToDataTable.Dispose();
            return 1;
        }
        dBToDataTable.Dispose();
        return -1;
    }

    public static int GetUserIP(string ip)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 封IP表 WHERE FLD_BANEDIP='" + ip + "'", "rxjhaccount");
		if (dBToDataTable != null && dBToDataTable.Rows.Count != 0)
		{
			return -1;
		}
		return 0;
	}

	public static void 保存普通暴率(DropClass drop)
	{
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@id", SqlDbType.Int, 30, drop.ID),
			SqlDBA.MakeInParam("@dqsl", SqlDbType.Int, 30, drop.当前数量)
		};
		DbPoolClass obj = new DbPoolClass
		{
			Conn = DBA.getstrConnection("PublicDb"),
			Prams = prams,
			Sql = "XWWL_UPDATE_drop"
		};
		World.SqlPool.Enqueue(obj);
	}

    public static void 保存安全码数据(string id, string nip)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "PlayersBes_保存密码数据()");
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat("UPDATE [TBL_ACCOUNT] SET FLD_SAFEWORD = '{1}' WHERE FLD_ID = '{0}'", id, nip);
		if (DBA.ExeSqlCommand(stringBuilder.ToString(), "rxjhaccount") == -1)
		{
			Form1.WriteLine(1, "保存密码数据 数据出错[" + id + "]");
		}
	}

	public static void 保存元宝数据(string Userid, int rxpiont, int rxpiontx, int zjf, int SPREADER_LEVEL, string 名字)
	{
		try
		{
			using (SqlConnection connection = new SqlConnection(DBA.getstrConnection("rxjhaccount")))
			{
				SqlDBA.RunProc(connection, "XWWL_UPDATE_RXPIONTX", new SqlParameter[5]
				{
					SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 20, Userid),
					SqlDBA.MakeInParam("@rxpiont", SqlDbType.Int, 4, rxpiont),
					SqlDBA.MakeInParam("@rxpiontx", SqlDbType.Int, 4, rxpiontx),
					SqlDBA.MakeInParam("@zjf", SqlDbType.Int, 4, zjf),
					SqlDBA.MakeInParam("@spreaderlv", SqlDbType.Int, 4, SPREADER_LEVEL)
				}, 名字);
			}
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "保存元宝数据", $"用户ID: {Userid}, 玩家: {名字}");
		}
	}

	// 保存开店奖励时间到账号表 EVIAS
	public static void 保存开店奖励时间(string Userid, DateTime 开店奖励时间, string 名字)
	{
		try
		{
			string sql = "UPDATE TBL_ACCOUNT SET 开店奖励时间 = @开店奖励时间 WHERE FLD_ID = @Userid";
			var parameters = new[] {
				SqlDBA.MakeInParam("@开店奖励时间", SqlDbType.DateTime, 8, 开店奖励时间),
				SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 50, Userid)
			};

			DBA.ExeSqlCommand(sql, parameters, "rxjhaccount");
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "保存开店奖励时间", $"用户ID: {Userid}, 玩家: {名字}");
		}
	}

	public static DataTable 得到下线账号(string strSpId)
	{
		try
		{
			using (SqlConnection connection = new SqlConnection(DBA.getstrConnection("rxjhaccount")))
			{
				DataTable dataTable = SqlDBA.RunProcc(connection, "XWWL_WEB_LOAD_ACCOUNT_SPREADERID", new SqlParameter[1] { SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 30, strSpId) });
				if (dataTable != null)
				{
					if (dataTable.Rows.Count > 0)
					{
						return dataTable;
					}
					dataTable.Dispose();
				}
				return null;
			}
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "获取下线账号", $"推广ID: {strSpId}");
			return null;
		}
	}

	public static DataTable 得到上线账号(string strSpId)
	{
		try
		{
			using (SqlConnection connection = new SqlConnection(DBA.getstrConnection("rxjhaccount")))
			{
				DataTable dataTable = SqlDBA.RunProcc(connection, "XWWL_WEB_LOAD_ACCOUNT_USERID", new SqlParameter[1] { SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 30, strSpId) });
				if (dataTable != null)
				{
					if (dataTable.Rows.Count > 0)
					{
						return dataTable;
					}
					dataTable.Dispose();
				}
				return null;
			}
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "获取上线账号", $"用户ID: {strSpId}");
			return null;
		}
	}

	public static DataTable 得到人物数据ID(string Userid)
	{
		try
		{
			using (SqlConnection connection = new SqlConnection(DBA.getstrConnection(null)))
			{
				DataTable dataTable = SqlDBA.RunProcc(connection, "XWWL_LOAD_CHARS", new SqlParameter[1] { SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 30, Userid) });
				if (dataTable != null)
				{
					if (dataTable.Rows.Count > 0)
					{
						return dataTable;
					}
					dataTable.Dispose();
				}
				return null;
			}
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "获取人物数据", $"用户ID: {Userid}");
			return null;
		}
	}

    public static DataTable 读取首爆记录表(int ID, string 分区, int SX)
    {
        DataTable dBToDataTable = DBA.GetDBToDataTable($"select * from 首爆记录 WHERE 分区=@fq and FLD_PID=@id and FLD_MAGIC0=@sx", new SqlParameter[3]
        {
            SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区),
            SqlDBA.MakeInParam("@id", SqlDbType.Int, 0, ID),
            SqlDBA.MakeInParam("@sx", SqlDbType.Int, 0, SX)
        }, "GameLog");
        if (dBToDataTable == null)
        {
            return null;
        }
        if (dBToDataTable.Rows.Count == 0)
        {
            return null;
        }
        return dBToDataTable;
    }

    public static void 首爆记录(string UserId, string UserName, long 全局ID, int 物品ID, string 物品名, int FLD_MAGIC0, int FLD_MAGIC1, int FLD_MAGIC2, int FLD_MAGIC3, int FLD_MAGIC4, int FLD_MAP, int x, int y, string type, string 分区)
    {
        if (World.首爆记录 == 1)
        {
            首爆记录 record = new 首爆记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_QJID = (int)全局ID,
                FLD_PID = 物品ID,
                FLD_INAME = 物品名,
                FLD_MAGIC0 = FLD_MAGIC0,
                FLD_MAGIC1 = FLD_MAGIC1,
                FLD_MAGIC2 = FLD_MAGIC2,
                FLD_MAGIC3 = FLD_MAGIC3,
                FLD_MAGIC4 = FLD_MAGIC4,
                FLD_MAP = FLD_MAP,
                FLD_X = x,
                FLD_Y = y,
                FLD_TYPE = type,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 开盒记录(string UserId, string UserName, long 全局ID, int 物品ID, string 物品名, int FLD_MAGIC0, int FLD_MAGIC1, int FLD_MAGIC2, int FLD_MAGIC3, int FLD_MAGIC4, int FLD_MAP, int x, int y, string type, string 分区)
    {
        if (World.开盒记录 == 1)
        {
            开盒记录 record = new 开盒记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_QJID = (int)全局ID,
                FLD_PID = 物品ID,
                FLD_INAME = 物品名,
                FLD_MAGIC0 = FLD_MAGIC0,
                FLD_MAGIC1 = FLD_MAGIC1,
                FLD_MAGIC2 = FLD_MAGIC2,
                FLD_MAGIC3 = FLD_MAGIC3,
                FLD_MAGIC4 = FLD_MAGIC4,
                FLD_MAP = FLD_MAP,
                FLD_X = x,
                FLD_Y = y,
                FLD_TYPE = type,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 掉落记录(string UserId, string UserName, long 全局ID, int 物品ID, string 物品名, int FLD_MAGIC0, int FLD_MAGIC1, int FLD_MAGIC2, int FLD_MAGIC3, int FLD_MAGIC4, int FLD_MAP, int x, int y, string type, string 分区)
    {
        if (World.掉落记录 == 1)
        {
            var record = new 掉落记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_QJID = (int)全局ID,
                FLD_PID = 物品ID,
                FLD_INAME = string.IsNullOrEmpty(物品名) ? "自己扔出去" : 物品名,
                FLD_MAGIC0 = FLD_MAGIC0,
                FLD_MAGIC1 = FLD_MAGIC1,
                FLD_MAGIC2 = FLD_MAGIC2,
                FLD_MAGIC3 = FLD_MAGIC3,
                FLD_MAGIC4 = FLD_MAGIC4,
                FLD_MAP = FLD_MAP,
                FLD_X = x,
                FLD_Y = y,
                FLD_TYPE = type,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 商店记录(string UserId, string UserName, int 物品ID, string 物品名, string 类型, int 数量, long 单价, int MAGIC0, int MAGIC1, int MAGIC2, int MAGIC3, int MAGIC4, long 全局ID, string 分区)
    {
        if (World.商店记录 == 1)
        {
            var record = new 商店记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_PID = 物品ID,
                FLD_INAME = 物品名,
                FLD_TYPE = 类型,
                FLD_NUM = 数量,
                FLD_PRICE = 单价.ToString(),
                FLD_MAGIC0 = MAGIC0,
                FLD_MAGIC1 = MAGIC1,
                FLD_MAGIC2 = MAGIC2,
                FLD_MAGIC3 = MAGIC3,
                FLD_MAGIC4 = MAGIC4,
                FLD_QJID = (int)全局ID,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 仓库记录(string UserId, string UserName, int 物品ID, string 物品名, string 类型, int 数量, long 单价, int MAGIC0, int MAGIC1, int MAGIC2, int MAGIC3, int MAGIC4, long 全局ID, string 分区)
    {
        if (World.仓库记录 == 1)
        {
            var record = new 仓库记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_PID = 物品ID,
                FLD_INAME = 物品名,
                FLD_TYPE = 类型,
                FLD_NUM = 数量,
                FLD_PRICE = 单价.ToString(),
                FLD_MAGIC0 = MAGIC0,
                FLD_MAGIC1 = MAGIC1,
                FLD_MAGIC2 = MAGIC2,
                FLD_MAGIC3 = MAGIC3,
                FLD_MAGIC4 = MAGIC4,
                FLD_QJID = (int)全局ID,    
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 药品记录(string UserId, string UserName, int 物品ID, string 物品名, int 数量, string 分区)
    {
        if (World.药品记录 == 1)
        {
            var record = new 药品记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_PID = 物品ID,
                FLD_INAME = 物品名,
                FLD_NUM = 数量,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 合成记录(string UserId, string UserName, string 物品名, int 操作ID, string 类型, string 成败, 物品类 物品, string 分区)
    {
        if (World.合成记录 == 1)
        {
            var record = new 合成记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_QJID = (int)物品.Get物品全局ID,
                FLD_PID = (int)物品.Get物品ID,
                FLD_INAME = 物品.得到物品名称(),
                FLD_MAGIC0 = 物品.FLD_MAGIC0,
                FLD_MAGIC1 = 物品.FLD_MAGIC1,
                FLD_MAGIC2 = 物品.FLD_MAGIC2,
                FLD_MAGIC3 = 物品.FLD_MAGIC3,
                FLD_MAGIC4 = 物品.FLD_MAGIC4,
                FLD_TYPE = 类型,
                FLD_CZID = 操作ID,
                FLD_SUCCESS = 成败,
                FLD_QHJD = 物品.FLD_强化数量,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 进化记录(string UserId, string UserName, int 物品ID, string 物品名, int 数量, int 全局, string 名称, string 分区)
    {
        if (World.进化记录 == 1)
        {
            var record = new 进化记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_PID = 物品ID,
                FLD_ML = 物品名,
                FLD_HCPID = 数量,
                FLD_QJID = 全局,
                物品名称 = 名称,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 复制记录(string UserId, string UserName, int 物品ID, string 物品名, int 数量, int 全局, string 名称, string 物品属性, string 分区)
    {
        var record = new 复制记录
        {
            FLD_ID = UserId,
            FLD_NAME = UserName,
            FLD_PID = 物品ID,
            FLD_ML = 物品名,
            FLD_HCPID = 数量,
            FLD_QJID = 全局,
            物品名称 = 名称,
            物品属性 = 物品属性,
            分区 = 分区
        };

        Task.Run(async () =>
        {
            await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
        });
    }

    public static void 卡号记录(string UserId, string UserName, string 物品名, int 职业, string 分区)
    {
        if (World.卡号记录 == 1)
        {
            var record = new 卡号记录
            {
                FLD_ID = UserId,
                FLD_NAME = UserName,
                FLD_ML = 物品名,
                FLD_职业 = 职业,
                分区 = 分区
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 百宝记录(string UserId, string UserName, double 物品ID, string 类型, int 物品数量, int 元宝数, string 分区)
    {
        if (World.百宝记录 == 1)
        {
            百宝记录 record = new 百宝记录
            {
                UserId = UserId,
                UserName = UserName,
                物品ID = 物品ID.ToString(),
                类型 = 类型,
                物品数量 = 物品数量,
                元宝数 = 元宝数,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 元宝记录(string 账号, string 名字, string 类型, string 来源, string 币种, int 数量, string 分区)
    {
        if (World.元宝记录 == 1)
        {
            元宝记录 record = new 元宝记录
            {
                账号 = 账号,
                名字 = 名字,
                类型 = 类型,
                来源 = 来源,
                币种 = 币种,
                数量 = 数量,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 武勋记录(string 账号, string 名字, string 类型, string 来源, string 币种, int 数量, string 分区)
    {
        if (World.武勋记录 == 1)
        {
            武勋记录 record = new 武勋记录
            {
                账号 = 账号,
                名字 = 名字,
                类型 = 类型,
                来源 = 来源,
                币种 = 币种,
                数量 = 数量,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 登陆记录(string UserId, string UserName, string UserIp, string 类型, string 分区)
    {
        if (World.登陆记录 == 1)
        {
            登陆记录 record = new 登陆记录
            {
                UserId = UserId,
                UserName = UserName,
                UserIp = UserIp,
                类型 = 类型,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 物品记录(string UserId, string UserName, string ToUserId, string ToUserName, double 全局ID, int 物品ID, string 物品名, int 物品数量, string 物品属性, double 钱数, string 类型, string 分区)
    {
        if (World.物品记录 == 1)
        {
            物品记录 record = new 物品记录
            {
                UserId = UserId,
                UserName = UserName,
                ToUserId = ToUserId,
                ToUserName = ToUserName,
                全局ID = 全局ID.ToString(),
                物品ID = 物品ID.ToString(),
                物品名 = 物品名,
                物品数量 = 物品数量,
                物品属性 = 物品属性,
                钱数 = 钱数.ToString(),
                类型 = 类型,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

    public static void 锁定记录(string UserId, string UserName, string ToUserId, string ToUserName, double 全局ID, int 物品ID, string 物品名, int 物品数量, string 物品属性, double 秘钥, string 类型, string 分区)
    {
        if (World.锁定记录 == 1)
        {
            锁定记录 record = new 锁定记录
            {
                UserId = UserId,
                UserName = UserName,
                ToUserId = ToUserId,
                ToUserName = ToUserName,
                全局ID = 全局ID.ToString(),
                物品ID = 物品ID.ToString(),
                物品名 = 物品名,
                物品数量 = 物品数量,
                物品属性 = 物品属性,
                秘钥 = 秘钥.ToString(),
                类型 = 类型,
                分区 = 分区,
                时间 = DateTime.Now
            };

            Task.Run(async () =>
            {
                await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
            });
        }
    }

	public static void 添加清理记录(string userId, string userName, int i, 物品类[] 装备栏, string type) //EVIAS
	{
		清理记录 vData = new 清理记录
		{
            USERID = userId,
            USERNAME = userName,
            全局ID = 装备栏[i].Get物品全局ID.ToString(),
			物品ID = 装备栏[i].Get物品ID.ToString(),
			物品名 = 装备栏[i].得到物品名称(),
			数量 = 装备栏[i].Get物品数量,
			类型 = type,
			MAGIC0 = 装备栏[i].FLD_MAGIC0,
			MAGIC1 = 装备栏[i].FLD_FJ_MAGIC1,
			MAGIC2 = 装备栏[i].FLD_FJ_MAGIC2,
			MAGIC3 = 装备栏[i].FLD_MAGIC3,
			MAGIC4 = 装备栏[i].FLD_MAGIC4,
			时间 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
			分区 = World.分区编号
		};
		Task.Run(async () =>
		{
			await DBA.LogSql.Insert(vData).ExecuteIdentityAsync();
		});
	}

	public static void 写入充值排名(int id, string 账号, string 名称, string 排名, int 地图, int 坐标X, int 坐标Y, int 额度, string 分区)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  充值排名  (全局id,账号,排名,名称,地图,坐标X,坐标Y,额度,分区信息)  VALUES  ({id},'{账号}','{排名}','{名称}',{地图},{坐标X},{坐标Y},{额度},'{分区}')");
	}

	public static void 修改充值排名(int id, string 账号, string 名称, int 额度, string 分区)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE [充值排名] SET 账号 = '{1}',名称 = '{2}',额度 = {3} ,分区信息 = '{4}' WHERE 全局id = {0}", id, 账号, 名称, 额度, 分区));
	}

    public static void 帮战赌注(string UserId, string UserName, int 帮派ID, int 元宝数, string 分区)
    {
        var record = new 帮战记录
        {
            UserId = UserId,
            UserName = UserName,
            帮派ID = 帮派ID,
            元宝数 = 元宝数,
            分区 = 分区
        };

        Task.Run(async () =>
        {
            await DBA.LogSql.Insert(record).ExecuteIdentityAsync();
        });
    }

    public static void 帮战赌注删除(string UserId, string UserName, int 帮派ID, int sl)
	{
		DBA.ExeSqlCommand($"DELETE  FROM  帮战记录  WHERE  UserId  =  '{UserId}'  and  UserName='{UserName}'  and  UserName='{帮派ID}'", "GameLog");
		switch (sl)
		{
		case -1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  败=败+1  WHERE  ID='{帮派ID}'");
			百宝记录(UserId, UserName, 0.0, "帮战失败输掉", 1, 50, World.分区编号);
			break;
		case 0:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  平=平+1  WHERE  ID='{帮派ID}'");
			break;
		case 1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  胜=胜+1  WHERE  ID='{帮派ID}'");
			百宝记录(UserId, UserName, 0.0, "帮战胜利得到", 1, 50, World.分区编号);
			break;
		}
	}

	public static void 申请门徽(int 帮派id, byte[] _门徽)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  门徽={1}  WHERE  ID='{0}'", 帮派id, Converter.ToString1(_门徽)));
	}

	public static byte[] 得到门徽(int id)
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Guild  WHERE  ID  =  {id}");
			if (dBToDataTable == null)
			{
				return null;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				dBToDataTable.Dispose();
				return null;
			}
			if (dBToDataTable.Rows[0]["门徽"].GetType().ToString() == "System.DBNull")
			{
				dBToDataTable.Dispose();
				return null;
			}
			byte[] array = (byte[])dBToDataTable.Rows[0]["门徽"];
			if (array != null)
			{
				dBToDataTable.Dispose();
				return array;
			}
			dBToDataTable.Dispose();
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "得到门徽出错      " + ex.Message);
			return null;
		}
	}

	public static void 帮派赋予职位(int zw, string name)
	{
		DBA.ExeSqlCommand("UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, zw),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, name)
		});
	}

	public static void 赋予帮主职位(string 新帮主, string 老帮主, string 帮派名称)
	{
		string sqlCommand = "UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, 5),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, 老帮主)
		};
		string sqlCommand2 = "UPDATE  TBL_XWWL_Guild  SET  G_Master=@Uname  WHERE  G_Name=@Gname";
		SqlParameter[] prams2 = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Uname", SqlDbType.VarChar, 30, 新帮主),
			SqlDBA.MakeInParam("@Gname", SqlDbType.VarChar, 30, 帮派名称)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
		DBA.ExeSqlCommand(sqlCommand2, prams2);
	}

	public static int 创建帮派确认(string 帮派name)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_SELECT_Guild_DATA  @bpnamea", new SqlParameter[1] { SqlDBA.MakeInParam("@bpnamea", SqlDbType.VarChar, 30, 帮派name) });
	}

	public static int 创建帮派(string name, string 帮派name, int leve, string 分区)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_INT_Guild_DATA_New  @name,@bpname,@leve,@sfzx,@fq", new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, 帮派name),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, leve),
			SqlDBA.MakeInParam("@sfzx", SqlDbType.Int, 0, 1),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
	}

	public static int 加入帮派(string name, string 帮派name, int leve, string 分区)
	{
		return (int)DBA.GetDBValue_3(string.Format("EXEC  XWWL_JR_Guild_DATA_New  @name,@bpname,@leve,@sfzx,@fq", name, 帮派name, leve), new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, 帮派name),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, leve),
			SqlDBA.MakeInParam("@sfzx", SqlDbType.Int, 0, 1),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
	}

	public static int 退出门派(string name)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_Out_Guild_DATA  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
	}

	public static int 逐出门派(string name, string 帮派name)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_OutBz_Guild_DATA  @name,  @bpname", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, 帮派name)
		});
	}

	public static int GetUserName(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  FLD_NAME  FROM  TBL_XWWL_Char  WHERE  FLD_NAME=@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return 1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static DataTable 得到帮派人数(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildMember  WHERE  G_Name  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count != 0) ? dBToDataTable : null;
	}

	public static DataTable 得到帮派在线人数(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_XWWL_GuildMember WHERE FLD_NAME =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count != 0) ? dBToDataTable : null;
	}

	public static DataTable 得到帮派数据(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Guild WHERE  G_Name  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable GetUserNameBp(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("EXEC  XWWL_LOAD_Guild  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count != 0) ? dBToDataTable : null;
	}

	public static DataTable GetUserCWarehouse(string string_0, string string_1, string 分区)
	{
		string sqlCommand = $"select * from [TBL_XWWL_CWarehouse] where FLD_NAME =@Username";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 50, string_1) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		string sqlCommand2 = $"EXEC XWWL_CW_USER_BANK @Userid,@Username,@zb,@fq";
		SqlParameter[] prams2 = new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, World.数据库单个物品大小 * 20, new byte[World.数据库单个物品大小 * 20]),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, 分区)
		};
		DBA.ExeSqlCommand(sqlCommand2, prams2);
		string sqlCommand3 = $"select * from [TBL_XWWL_CWarehouse] where  FLD_NAME =@Username";
		SqlParameter[] prams3 = new SqlParameter[1] { SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1) };
		DataTable dBToDataTable2 = DBA.GetDBToDataTable(sqlCommand3, prams3);
		if (dBToDataTable2 == null)
		{
			return null;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable2;
	}

	public static DataTable GetUserLSarehouse(string string_0, string string_1, string 分区)
	{
	 	string sqlCommand = $"select * from [TBL_XWWL_LWarehouse] where FLD_NAME =@Username";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 50, string_1) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		string sqlCommand2 = $"EXEC XWWL_LW_USER_BANK @Userid,@Username,@zb,@fq";
		SqlParameter[] prams2 = new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, World.数据库单个物品大小 * 3, new byte[World.数据库单个物品大小 * 3]),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, 分区)
		};
		DBA.ExeSqlCommand(sqlCommand2, prams2);
		string sqlCommand3 = $"select * from [TBL_XWWL_LWarehouse] where  FLD_NAME =@Username";
		SqlParameter[] prams3 = new SqlParameter[1] { SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1) };
		DataTable dBToDataTable2 = DBA.GetDBToDataTable(sqlCommand3, prams3);
		if (dBToDataTable2 == null)
		{
			return null;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable2;
	}

	public static DataTable GetUserWarehouse(string Userid, string Username, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from    [TBL_XWWL_Warehouse]    where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, Username)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_USER_BANK  @Userid,@Username,@aa,@zb,@fq", new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, Username),
			SqlDBA.MakeInParam("@aa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, 4620, new byte[4620]),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		DataTable dBToDataTable2 = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Warehouse]  where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, Username)
		});
		if (dBToDataTable2 == null)
		{
			return null;
		}
		return (dBToDataTable2.Rows.Count != 0) ? dBToDataTable2 : null;
	}

	public static DataTable GetUserPublicWarehouse(string Userid, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		Converter.ToString1(new byte[4620]);
		Converter.ToString1(new byte[60]);
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_ID_BANK      @Userid,@aaa,@ck,@ck1,@fq", new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid),
			SqlDBA.MakeInParam("@aaa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@ck", SqlDbType.VarBinary, 4620, new byte[4620]),
			SqlDBA.MakeInParam("@ck1", SqlDbType.VarBinary, 50, new byte[50]),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		DataTable dBToDataTable2 = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID='" + Userid + "'", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, Userid) });
		if (dBToDataTable2 == null)
		{
			return null;
		}
		return (dBToDataTable2.Rows.Count != 0) ? dBToDataTable2 : null;
	}

	public static DataTable 得到玩家每日任务(string userid, string username, int rwpid, bool 是否账号每日)
	{
		string sqlCommand = $"SELECT * FROM [每日任务] where FLD_Userid='{userid}' and FLD_UserName='{username}' and FLD_TaskID={rwpid}";
		if (是否账号每日)
		{
			sqlCommand = $"SELECT * FROM [每日任务] where FLD_Userid='{userid}' and FLD_TaskID={rwpid}";
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return null;
		}
		return dBToDataTable;
	}

	public static int 添加玩家每日任务完成(string userid, string username, int rwpid, string 分区)
	{
		return DBA.ExeSqlCommand($"INSERT INTO [每日任务]([FLD_Userid],[FLD_UserName],[FLD_TaskID],[FLD_LastTime],[分区])VALUES('{userid}','{username}',{rwpid},'{DateTime.Now}','{分区}')");
	}

	public static int 更新玩家每日任务完成(int id, string username)
	{
		return DBA.ExeSqlCommand($"UPDATE [每日任务] SET FLD_UserName = '{username}', FLD_LastTime = '{DateTime.Now}' WHERE ID = {id}");
	}

	public static DataTable 得到自定义任务阶段(int rwpid)
	{
		string sqlCommand = $"SELECT * FROM [TBL_XWWL_MISSION_STAGE] where 任务ID='{rwpid}' order by 任务阶段ID";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return null;
		}
		return dBToDataTable;
	}

	public static int SetUserName(string id, string name, int zy, byte[] coue)
	{
		byte[] array = new byte[World.数据库单个物品大小];
		byte[] array2 = new byte[1155];
		byte[] array3 = new byte[2772];
		byte[] bytes = BitConverter.GetBytes(GetDBItmeId());
		byte[] src = new byte[4];
		byte[] bytes2 = BitConverter.GetBytes(1);
		switch (zy)
		{
		case 1:
			src = BitConverter.GetBytes(100200002);
			break;
		case 2:
			src = BitConverter.GetBytes(200200002);
			break;
		case 3:
			src = BitConverter.GetBytes(300200002);
			break;
		case 4:
			src = BitConverter.GetBytes(400200002);
			break;
		case 5:
			src = BitConverter.GetBytes(500200002);
			break;
		case 6:
			src = BitConverter.GetBytes(700200002);
			break;
		case 7:
			src = BitConverter.GetBytes(800200001);
			break;
		case 8:
			src = BitConverter.GetBytes(100204001);
			break;
		case 9:
			src = BitConverter.GetBytes(200204001);
			break;
		case 10:
			src = BitConverter.GetBytes(900200001);
			break;
		case 11:
			src = BitConverter.GetBytes(400204001);
			break;
		case 12:
			src = BitConverter.GetBytes(300204001);
			break;
		case 13:
			src = BitConverter.GetBytes(500204001);
			break;
		}
		Buffer.BlockCopy(bytes, 0, array, 0, 4);
		Buffer.BlockCopy(src, 0, array, 8, 4);
		Buffer.BlockCopy(bytes2, 0, array, 12, 4);
		Buffer.BlockCopy(array, 0, array3, 0, 77);
		if (World.上线送礼包是否开启 == 1 && World.上线送礼包套装 > 0)
		{
			byte[] array4 = 物品类.CreatNewItem(World.上线送礼包套装, GetDBItmeId(), 1);
			Buffer.BlockCopy(array4, 0, array3, 77, array4.Length);
		}
		if (zy == 4 || zy == 11)
		{
			byte[] array5 = 物品类.CreatNewItem(1000000148, GetDBItmeId(), 100);
			Buffer.BlockCopy(array5, 0, array3, 154, array5.Length);
		}
		byte[] array6 = new byte[231];
		byte[] array7 = 物品类.CreatNewItem(1008002684, GetDBItmeId(), 1);
		Buffer.BlockCopy(array7, 0, array6, 0, array7.Length);
		int num = 0;
		DataTable dBToDataTable = DBA.GetDBToDataTable("Select FLD_INDEX FROM TBL_XWWL_Char Where FLD_ID=@FLD_ID", new SqlParameter[1] { SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, id) });
		if (dBToDataTable.Rows.Count >= 4)
		{
			dBToDataTable.Dispose();
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			num = 0;
		}
		else
		{
			List<int> list = new List<int>();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				int item = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
				list.Add(item);
			}
			for (int j = 0; j < 4; j++)
			{
				if (!list.Contains(j))
				{
					num = j;
					break;
				}
			}
		}
		dBToDataTable.Dispose();
		int num2 = 0;
		int num3 = 0;
		switch (zy)
		{
		case 4:
			num2 = 124;
			num3 = 116;
			break;
		case 6:
			num2 = 130;
			num3 = 114;
			break;
		case 7:
			num2 = 124;
			num3 = 136;
			break;
		case 1:
		case 8:
			num2 = 145;
			num3 = 116;
			break;
		case 10:
			num2 = 145;
			num3 = 116;
			break;
		case 11:
			num2 = 124;
			num3 = 116;
			break;
		case 2:
		case 3:
		case 5:
		case 9:
		case 12:
			num2 = 133;
			num3 = 118;
			break;
		case 13:
			num2 = 118;
			num3 = 136;
			break;
		}
		return (DBA.ExeSqlCommand("EXEC XWWL_INT_USER_DATA @FLD_ID,@name,@rwid,@zy,@hp,@mp,@coue,@xrwhex,@xrwhex2,@xrwhex3", new SqlParameter[10]
		{
			SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, id),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name),
			SqlDBA.MakeInParam("@rwid", SqlDbType.Int, 0, num),
			SqlDBA.MakeInParam("@zy", SqlDbType.Int, 0, zy),
			SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, num2),
			SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, num3),
			SqlDBA.MakeInParam("@coue", SqlDbType.VarBinary, 10, coue),
			SqlDBA.MakeInParam("@xrwhex", SqlDbType.VarBinary, array2.Length, array2),
			SqlDBA.MakeInParam("@xrwhex2", SqlDbType.VarBinary, array3.Length, array3),
			SqlDBA.MakeInParam("@xrwhex3", SqlDbType.VarBinary, array6.Length, array6)
		}) != -1) ? 1 : (-1);
	}

	public static int GetCwUserName(string name, string zrname, int type, long id, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  Name  FROM  TBL_XWWL_Cw  WHERE  Name=@name", name), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_Cw_DATA  @zrname,@name,@id,@type,@zb1,@zb2,@fq", zrname, name, id, type, Converter.ToString(new byte[292]), Converter.ToString(new byte[1168]), 分区), new SqlParameter[7]
			{
				SqlDBA.MakeInParam("@zrname", SqlDbType.VarChar, 30, zrname),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name),
				SqlDBA.MakeInParam("@id", SqlDbType.Int, 0, id),
				SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, type),
				SqlDBA.MakeInParam("@zb1", SqlDbType.VarBinary, 5 * World.数据库单个物品大小, new byte[5 * World.数据库单个物品大小]),
				SqlDBA.MakeInParam("@zb2", SqlDbType.VarBinary, 16 * World.数据库单个物品大小, new byte[16 * World.数据库单个物品大小]),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
			}) != -1) ? 1 : (-1);
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static void 更新门派荣誉(string 人物名, string 帮派名, int 势力, int 等级, int 职业, int 转职, int 荣誉, string 分区)
	{
		try
		{
			SqlParameter[] prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, 人物名),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, 帮派名),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, 势力),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, 等级),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, 职业),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, 转职),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, 荣誉),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, 分区)
			};
			DbPoolClass obj = new DbPoolClass
			{
				名字 = 人物名,
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "UPDATE_menpai_DATA_New"
			};
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "保存门派排行数据出错" + ex.Message);
		}
	}

	public static void 更新武林荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		try
		{
			SqlParameter[] prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
			};
			DbPoolClass obj = new DbPoolClass
			{
				名字 = string_0,
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "UPDATE_wulin_DATA_New"
			};
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "保存武林排行数据出错" + ex.Message);
		}
	}

	public static void 更新势力荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		try
		{
			SqlParameter[] prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
			};
			DbPoolClass obj = new DbPoolClass
			{
				名字 = string_0,
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "UPDATE_shili_DATA_New"
			};
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "保存势力排行数据出错" + ex.Message);
		}
	}

	public static void 更新讨伐荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		try
		{
			SqlParameter[] prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
			};
			DbPoolClass obj = new DbPoolClass
			{
				名字 = string_0,
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "UPDATE_taofa_DATA_New"
			};
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "保存武林排行数据出错" + ex.Message);
		}
	}

	public static int Set个人荣誉数据(int 类型, string 人物名, int 职业, int 等级, int 势力, string 帮派名, string 帮派门主, int 分数, string 分区ID)
	{
		DataTable dataTable = 得到帮派数据(帮派名);
		if (dataTable != null)
		{
			if (dataTable.Rows.Count > 0)
			{
				帮派门主 = dataTable.Rows[0]["G_Master"].ToString();
			}
			dataTable.Dispose();
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT FLD_人物名 FROM 荣誉系统排行 WHERE FLD_人物名=@name  and  FLD_类型=@lx", 人物名, 类型), new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 人物名),
			SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, 类型)
		});
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf,@fq", 类型, 人物名, 职业, 等级, 势力, 帮派名, 帮派门主, 分数, 分区ID), new SqlParameter[9]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, 类型),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 人物名),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, 职业),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, 等级),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, 势力),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, 帮派名),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, 帮派门主),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, 分数),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区ID)
			}) != -1) ? 1 : (-1);
		}
		dBToDataTable.Dispose();
		return (DBA.ExeSqlCommand($"UPDATE 荣誉系统排行 SET FLD_分数=FLD_分数+{分数},FLD_帮派='{帮派名}',FLD_等级={等级},FLD_帮派门主='{帮派门主}' WHERE FLD_人物名='{人物名}' and FLD_类型={类型} ", "GameServer") != -1) ? 1 : (-1);
	}

	public static int Set帮派荣誉数据(string 帮派, string 帮派门主, int 等级, int 职业, int 势力, int 分数, string 分区ID)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  *  FROM  荣誉系统排行  WHERE  FLD_类型  =  3  and  FLD_帮派=@mpname", 帮派), new SqlParameter[1] { SqlDBA.MakeInParam("@mpname", SqlDbType.VarChar, 30, 帮派) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			int num = 3;
			string empty = string.Empty;
			return (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA  @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf,@fq", num, empty, 职业, 等级, 势力, 帮派, 帮派门主, 分数, 分区ID), new SqlParameter[9]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, num),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, empty),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, 职业),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, 等级),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, 势力),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, 帮派),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, 帮派门主),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, 分数),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区ID)
			}) != -1) ? 1 : (-1);
		}
		dBToDataTable.Dispose();
		return (DBA.ExeSqlCommand("UPDATE  荣誉系统排行  SET  FLD_分数  =FLD_分数+1  WHERE  FLD_帮派='" + 帮派 + "'  and  FLD_类型=      3", "GameServer") != -1) ? 1 : (-1);
	}

	public static DataTable 得到传书列表(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  传书记录  WHERE  接收人物名  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) }, "GameLog");
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		dBToDataTable.Dispose();
		return null;
	}

    public static void 设置传书已读(int id, int 是否已读) //EVIAS
    {
        Task.Run(async () =>
        {
            await DBA.LogSql.Update<传书记录>()
                .Where(x => x.ID == id)
                .Set(x => x.阅读标识, 是否已读)
                .ExecuteAffrowsAsync();
        }).ConfigureAwait(false); 
    }

    public static void 变更婚姻状态(string name, int 状态)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MARITAL_STATUS={1}  WHERE  FLD_NAME='{0}'", name, 状态));
	}

	public static void 解除情侣关系(string name)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_QlNAME='{1}',FLD_QlDu={2},FLD_LOVE_WORD='{3}',FLD_MARITAL_STATUS={4},FLD_MARRIED={5}  WHERE  FLD_NAME='{0}'", name, string.Empty, 0, string.Empty, 0, 0));
	}

	public static void 创建传书(string fname, string sname, int npcid, string msg, int Type, string 分区)
	{
		DBA.ExeSqlCommand("EXEC  INT_CS_DATA_New  @fname,  @sname,  @msg,  @npcid, @type, @fq", new SqlParameter[6]
		{
			SqlDBA.MakeInParam("@fname", SqlDbType.VarChar, 30, fname),
			SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, sname),
			SqlDBA.MakeInParam("@msg", SqlDbType.VarChar, 2000, msg),
			SqlDBA.MakeInParam("@npcid", SqlDbType.Int, 0, npcid),
			SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, Type),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		}, "GameLog");
	}

	public static DataTable 得到人物名字(string 名字)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 名字) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static string 取得到人物名字(int 秘钥)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM  TBL_XWWL_Char  WHERE  道具时间 =  @djsj", new SqlParameter[1] { SqlDBA.MakeInParam("@djsj", SqlDbType.Int, 0, 秘钥) });
		if (dBToDataTable != null)
		{
			return (string)dBToDataTable.Rows[0]["FLD_NAME"];
		}
		return string.Empty;
	}

	public static DateTime 得到物品最近一次交易时间(long 物品全局ID)
	{
		DateTime result = new DateTime(1970, 1, 1, 8, 0, 0);
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT top 1 * FROM 物品记录 where 全局ID = {物品全局ID} order by 时间 desc", "GameLog");
		if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
		{
			result = DateTime.Parse(dBToDataTable.Rows[0]["时间"].ToString());
		}
		dBToDataTable.Dispose();
		return result;
	}

	public static void 创建门派荣誉(string 人物名, string 帮派名, int 势力, int 等级, int 职业, int 转职, int 荣誉, string 分区)
	{
		DBA.ExeSqlCommand("EXEC INT_menpai_DATA_New @rwname,@bpname,@zx,@leve,@job,@jobleve,@rongyu,@fq", new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, 人物名),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, 帮派名),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, 势力),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, 等级),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, 职业),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, 转职),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, 荣誉),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, 分区)
		});
	}

	public static void 创建武林荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
	string sqlCommand = $"EXEC INT_wulin_DATA_New @rwname, @bpname,@zx, @leve,@job,@jobleve,@rongyu,@fq";
		SqlParameter[] prams = new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void 创建讨伐荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		string sqlCommand = $"EXEC INT_taofa_DATA_New @rwname, @bpname,@zx, @leve,@job,@jobleve,@rongyu,@fq";
		SqlParameter[] prams = new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void 创建势力荣誉(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		string sqlCommand = $"EXEC INT_shili_DATA_New @rwname, @bpname,@zx, @leve,@job,@jobleve,@rongyu,@fq";
		SqlParameter[] prams = new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static DataTable 得到门派荣誉数据(string 名字, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  荣誉门派排行  WHERE  FLD_BP  =  @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 名字),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable 得到武林荣誉数据(string 名字, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  荣誉武林排行  WHERE  FLD_NAME = @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 名字),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable 得到讨伐荣誉数据(string 名字, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  荣誉讨伐排行  WHERE  FLD_NAME = @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 名字),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable 得到势力荣誉数据(string 名字, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  荣誉势力排行  WHERE  FLD_NAME = @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 名字),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable 得到师傅数据(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_师徒数据  WHERE  FLD_TNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static DataTable 得到徒弟数据(string name)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_师徒数据  WHERE  FLD_SNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) });
		if (dBToDataTable == null)
		{
			return null;
		}
		return (dBToDataTable.Rows.Count == 0) ? null : dBToDataTable;
	}

	public static int 创建师徒关系(string 徒弟, string 师傅, int tlevel, int index, string 分区)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TNAME  FROM  TBL_师徒数据  WHERE  FLD_TNAME=@name", 徒弟), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 徒弟) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return (DBA.ExeSqlCommand(string.Format("EXEC  INT_St_DATA  @sname,@tname,@tlevel,@index,@fq", 师傅, 徒弟, tlevel, index, 分区), new SqlParameter[5]
			{
				SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, 师傅),
				SqlDBA.MakeInParam("@tname", SqlDbType.VarChar, 30, 徒弟),
				SqlDBA.MakeInParam("@tlevel", SqlDbType.Int, 0, tlevel),
				SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, index),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, 分区)
			}) != -1) ? 1 : (-1);
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static int 解除师徒关系(string 徒弟, string 师傅)
	{
		return (DBA.ExeSqlCommand("delete  [TBL_师徒数据]  WHERE  FLD_TNAME  ='" + 徒弟 + "'  and  FLD_SNAME='" + 师傅 + "'", "GameServer") != -1) ? 1 : (-1);
	}

	public static string 取得门派联盟盟主(string 门派名)
	{
		string sqlCommand = $"select * from TBL_XWWL_Guild where G_Name=@name";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 门派名) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return "";
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return "";
		}
		string result = dBToDataTable.Rows[0]["联盟盟主"].ToString();
		dBToDataTable.Dispose();
		return result;
	}

	public static DataTable 取得门派联盟列表(string 门派名)
	{
		if (门派名 == "")
		{
			return null;
		}
		string sqlCommand = $"SELECT * FROM TBL_XWWL_Guild WHERE 联盟盟主 = @name";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 门派名) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static int 更新联盟宣告攻城状态(string 盟主门派名, int 宣告攻城状态)
	{
		string sqlCommand = $"UPDATE TBL_XWWL_Guild SET 宣告攻城={宣告攻城状态} WHERE 联盟盟主='{盟主门派名}'";
		return DBA.ExeSqlCommand(sqlCommand);
	}

	public static int 更新门派联盟盟主(string 申请门派名, string 盟主门派名, int 宣告攻城状态)
	{
		string sqlCommand = $"UPDATE TBL_XWWL_Guild SET 联盟盟主='{盟主门派名}',宣告攻城={宣告攻城状态} WHERE G_Name='{申请门派名}'";
		return DBA.ExeSqlCommand(sqlCommand);
	}

	public static DataTable 得到已申请攻城的门派列表()
	{
		string sqlCommand = $"select * from TBL_XWWL_Guild where 联盟盟主 !='' and 联盟盟主=G_Name";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return null;
		}
		return dBToDataTable;
	}

    // 2025-05-19: 寄售改为拍卖 EVIAS
    public static int 拍卖物品(拍卖物品类 物品) 
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat(@"INSERT INTO [拍卖物品列表] (
			[FLD_物品ID], [FLD_物品名称], [FLD_数量], [FLD_物品类型], [FLD_物品性别],
			[FLD_物品基础防御], [FLD_物品基础最小攻击], [FLD_物品基础最大攻击], [FLD_物品等级], [FLD_物品正邪], [FLD_需求转职等级],
			[FLD_属性0], [FLD_属性1], [FLD_属性2], [FLD_属性3], [FLD_属性4],
			[FLD_强化类型], [FLD_强化数量], [FLD_属性类型], [FLD_属性数量],
			[FLD_物品上架时间], [FLD_物品下架时间], [FLD_一口价], [FLD_物品低价], [FLD_每次加价], [FLD_当前竞价],
			[FLD_拍品状态], [FLD_竞拍者], [FLD_拍卖者], [FLD_参与者], [FLD_竞拍次数],
			[FLD_觉醒], [FLD_中级附魂], [FLD_进化], [FLD_FQ]
		) VALUES (
			{0}, '{1}', {2}, {3}, {4},
			{5}, {6}, {7}, {8}, {9}, {10},
			{11}, {12}, {13}, {14}, {15},
			{16}, {17}, {18}, {19},
			'{20}', '{21}', {22}, {23}, {24}, {25},
			'{26}', '{27}', '{28}', '{29}', {30},
			{31}, {32}, {33}, '{34}'
		)",
		物品.FLD_物品ID, 物品.FLD_物品名称, 物品.FLD_数量, 物品.FLD_物品类型, 物品.FLD_物品性别,
		物品.FLD_物品基础防御, 物品.FLD_物品基础最小攻击, 物品.FLD_物品基础最大攻击, 物品.FLD_物品等级, 物品.FLD_物品正邪, 物品.FLD_需求转职等级,
		物品.FLD_属性0, 物品.FLD_属性1, 物品.FLD_属性2, 物品.FLD_属性3, 物品.FLD_属性4,
		物品.FLD_强化类型, 物品.FLD_强化数量, 物品.FLD_属性类型, 物品.FLD_属性数量,
		物品.FLD_物品上架时间.ToString("yyyy-MM-dd HH:mm:ss"), 物品.FLD_物品下架时间.ToString("yyyy-MM-dd HH:mm:ss"),
		物品.FLD_一口价, 物品.FLD_物品低价, 物品.FLD_每次加价, 物品.FLD_当前竞价,
		物品.FLD_拍品状态, 物品.FLD_竞拍者 ?? "", 物品.FLD_拍卖者, 物品.FLD_参与者 ?? "", 物品.FLD_竞拍次数,
		物品.FLD_觉醒, 物品.FLD_中级附魂, 物品.FLD_进化, 物品.FLD_FQ ?? "");
		return DBA.ExeSqlCommand(stringBuilder.ToString(), "BBG"); 
	}

    // 2025-05-19: 取消拍卖 EVIAS
    public static int 取消拍卖(long id) 
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat("DELETE [拍卖物品列表] WHERE ID = {0}", id);
		return DBA.ExeSqlCommand(stringBuilder.ToString(), "BBG"); 
	}

    // 2025-05-19: 增加得到拍卖数量  EVIAS
    public static int 得到拍卖数量(string 玩家名称)
	{
		string sql = $"Select * from [拍卖物品列表] where [FLD_拍品状态] NOT IN ('已完成', '已取消') AND [FLD_拍卖者] = '{玩家名称}' ";
		var result = DBA.GetDBToDataTable(sql, "BBG");
		int count = result?.Rows.Count ?? 0;
	
		return count;
	}

    // 2025-05-19: 增加得到拍卖物品 EVIAS
    public static 拍卖物品类 得到拍卖物品(int 全局ID) 
	{
		try
		{
			string sql = $"Select * from [拍卖物品列表] where ID = {全局ID}";
			
			DataTable dBToDataTable = DBA.GetDBToDataTable(sql, "BBG"); 
			if (dBToDataTable != null)
			{
				if (dBToDataTable.Rows.Count == 0)
				{
					dBToDataTable.Dispose();
				}
				else
				{
					if (dBToDataTable.Rows.Count <= 1)
					{
						try
						{
							var row = dBToDataTable.Rows[0];
							拍卖物品类 result = new 拍卖物品类 
							{
								ID = Convert.ToInt64(row["ID"]),
								FLD_物品ID = Convert.ToInt64(row["FLD_物品ID"]),
								FLD_物品名称 = row["FLD_物品名称"]?.ToString() ?? "",
								FLD_数量 = Convert.ToInt32(row["FLD_数量"]),
								FLD_物品类型 = Convert.ToInt32(row["FLD_物品类型"]),
								FLD_物品性别 = Convert.ToInt32(row["FLD_物品性别"]),
								FLD_物品基础防御 = Convert.ToInt32(row["FLD_物品基础防御"]),
								FLD_物品基础最小攻击 = Convert.ToInt32(row["FLD_物品基础最小攻击"]),
								FLD_物品基础最大攻击 = Convert.ToInt32(row["FLD_物品基础最大攻击"]),
								FLD_物品等级 = Convert.ToInt32(row["FLD_物品等级"]),
								FLD_物品正邪 = Convert.ToInt32(row["FLD_物品正邪"]),
								FLD_需求转职等级 = Convert.ToInt32(row["FLD_需求转职等级"]),
								FLD_属性0 = Convert.ToInt32(row["FLD_属性0"]),
								FLD_属性1 = Convert.ToInt32(row["FLD_属性1"]),
								FLD_属性2 = Convert.ToInt32(row["FLD_属性2"]),
								FLD_属性3 = Convert.ToInt32(row["FLD_属性3"]),
								FLD_属性4 = Convert.ToInt32(row["FLD_属性4"]),
								FLD_强化类型 = Convert.ToInt32(row["FLD_强化类型"]),
								FLD_强化数量 = Convert.ToInt32(row["FLD_强化数量"]),
								FLD_属性类型 = Convert.ToInt32(row["FLD_属性类型"]),
								FLD_属性数量 = Convert.ToInt32(row["FLD_属性数量"]),
								FLD_物品上架时间 = row["FLD_物品上架时间"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["FLD_物品上架时间"]),
								FLD_物品下架时间 = row["FLD_物品下架时间"] == DBNull.Value ? DateTime.Now.AddDays(World.拍卖持续天数) : Convert.ToDateTime(row["FLD_物品下架时间"]),
								FLD_一口价 = Convert.ToInt32(row["FLD_一口价"]),
								FLD_物品低价 = Convert.ToInt32(row["FLD_物品低价"]),
								FLD_每次加价 = Convert.ToInt32(row["FLD_每次加价"]),
								FLD_当前竞价 = Convert.ToInt32(row["FLD_当前竞价"]),
								FLD_拍品状态 = row["FLD_拍品状态"]?.ToString() ?? "拍卖中",
								FLD_竞拍者 = row["FLD_竞拍者"]?.ToString() ?? "",
								FLD_拍卖者 = row["FLD_拍卖者"]?.ToString() ?? "",
								FLD_参与者 = row["FLD_参与者"]?.ToString() ?? "",
								FLD_竞拍次数 = Convert.ToInt32(row["FLD_竞拍次数"]),
								FLD_觉醒 = Convert.ToInt32(row["FLD_觉醒"]),
								FLD_中级附魂 = Convert.ToInt32(row["FLD_中级附魂"]),
								FLD_进化 = Convert.ToInt32(row["FLD_进化"]),
								FLD_FQ = row["FLD_FQ"]?.ToString() ?? ""
							};

							dBToDataTable.Dispose();
							return result;
						}
						catch (Exception parseEx)
						{
							logo.拍卖记录($"解析拍品数据失败: {parseEx.Message}");
							dBToDataTable.Dispose();
							return null;
						}
					}
					dBToDataTable.Dispose();
				}
			}
		}
		catch (Exception ex)
		{
			string message = ex.Message;
		}
		return null;
	}

	// 2025-05-19: 新增拍卖相关数据库操作方法 EVIAS
	public static int 更新拍卖竞价(int 拍品ID, int 竞拍价格, string 竞拍者, int 竞拍次数, string 参与者)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat(@"UPDATE [拍卖物品列表] SET
			[FLD_当前竞价] = {1},
			[FLD_竞拍者] = '{2}',
			[FLD_竞拍次数] = {3},
			[FLD_参与者] = '{4}'
			WHERE ID = {0}",
			拍品ID, 竞拍价格, 竞拍者, 竞拍次数, 参与者);
		return DBA.ExeSqlCommand(stringBuilder.ToString(), "BBG");
	}

    // 2025-05-19: 新增更新拍卖状态 EVIAS
    public static int 更新拍卖状态(int 拍品ID, string 状态)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat("UPDATE [拍卖物品列表] SET [FLD_拍品状态] = '{1}' WHERE ID = {0}", 拍品ID, 状态);
		return DBA.ExeSqlCommand(stringBuilder.ToString(), "BBG"); 
	}

    // 2025-06-16: 新增更新拍卖下架时间 EVIAS
    public static int 更新拍卖下架时间(int 拍品ID, DateTime 下架时间)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendFormat("UPDATE [拍卖物品列表] SET [FLD_物品下架时间] = '{1}' WHERE ID = {0}",
			拍品ID, 下架时间.ToString("yyyy-MM-dd HH:mm:ss"));
		return DBA.ExeSqlCommand(stringBuilder.ToString(), "BBG");
	}
    // 2025-05-19: 新增竞拍返还元宝	 EVIAS
    public static int 竞拍返还元宝(string 玩家名称, int 元宝数量)
	{
		string 账号ID = ""; 
		try
		{
			DataTable 角色数据 = 得到人物名字(玩家名称);
			if (角色数据 == null || 角色数据.Rows.Count == 0)
				return -1;

			账号ID = 角色数据.Rows[0]["FLD_ID"].ToString();
			角色数据.Dispose();

			// 更新账号表的元宝字段
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.AppendFormat("UPDATE [TBL_ACCOUNT] SET FLD_RXPIONT = FLD_RXPIONT + {1} WHERE FLD_ID = '{0}'", 账号ID, 元宝数量);
			return DBA.ExeSqlCommand(stringBuilder.ToString(), "rxjhaccount");
		}
		catch (Exception ex)
		{
			HandleDatabaseException(ex, "竞拍返还元宝", string.IsNullOrEmpty(账号ID) ? 玩家名称 : 账号ID);
			return -1;
		}
	}

	public static void HandleGameException(Exception ex, Players player, string operation, string details = "")
	{
		try
		{
			// 记录异常统计
			RecordExceptionStatistics(operation, ex);

			// 构建详细错误信息
			var errorInfo = BuildErrorInfo(ex, operation, details, player?.UserName);
			Form1.WriteLine(1, errorInfo);

			// 玩家相关处理
			if (player != null)
			{
				player.报错次数阀值++;

				// 根据异常类型决定是否通知玩家
				if (ShouldNotifyPlayer(ex))
				{
					var errorCode = GetErrorCode(ex);
					player.系统提示($"操作出现异常，请稍后重试。错误代码：{errorCode}", 9, "系统提示");
				}

				// 严重错误时保存数据
				if (IsCriticalError(ex))
				{
					try
					{
						player.保存人物的数据();
						Form1.WriteLine(1, $"严重错误，已保存玩家数据：{player.UserName}");
					}
					catch (Exception saveEx)
					{
						Form1.WriteLine(1, $"保存玩家数据失败：{saveEx.Message}");
					}
				}
			}
		}
		catch (Exception handlerEx)
		{
			Form1.WriteLine(1, $"异常处理器出错: {handlerEx.Message}");
		}
	}

	
	//处理数据库异常
	public static void HandleDatabaseException(Exception ex, string operation, string playerName = "")
	{
		// 记录异常统计
		RecordExceptionStatistics($"DB_{operation}", ex);

		var errorInfo = $"数据库异常 - 操作: {operation}, 玩家: {playerName}, 错误: {ex.Message}";
		Form1.WriteLine(1, errorInfo);

		// SQL异常的特殊处理
		if (ex is SqlException sqlEx)
		{
			HandleSqlException(sqlEx, operation, playerName);
		}
	}

	
	// 处理网络异常
	public static void HandleNetworkException(Exception ex, string clientInfo, string operation)
	{
		// 记录异常统计
		RecordExceptionStatistics($"NET_{operation}", ex);

		var errorInfo = $"网络异常 - 客户端: {clientInfo}, 操作: {operation}, 错误: {ex.Message}";
		Form1.WriteLine(1, errorInfo);
	}

	// 私有辅助方法
	private static string BuildErrorInfo(Exception ex, string operation, string details, string playerName)
	{
		var sb = new StringBuilder();
		sb.AppendLine($"=== 异常详情 ===");
		sb.AppendLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
		sb.AppendLine($"操作: {operation}");
		sb.AppendLine($"玩家: {playerName ?? "未知"}");
		sb.AppendLine($"详情: {details}");
		sb.AppendLine($"异常类型: {ex.GetType().Name}");
		sb.AppendLine($"异常消息: {ex.Message}");
		if (!string.IsNullOrEmpty(ex.StackTrace))
		{
			sb.AppendLine($"堆栈跟踪: {ex.StackTrace}");
		}

		if (ex.InnerException != null)
		{
			sb.AppendLine($"内部异常: {ex.InnerException.Message}");
		}

		return sb.ToString();
	}

	private static bool ShouldNotifyPlayer(Exception ex)
	{
		// 根据异常类型决定是否通知玩家
		return !(ex is ArgumentException || ex is InvalidOperationException);
	}

	private static bool IsCriticalError(Exception ex)
	{
		// 判断是否为严重错误
		return ex is OutOfMemoryException ||
			   ex is StackOverflowException ||
			   ex is AccessViolationException;
	}

	private static string GetErrorCode(Exception ex)
	{
		// 生成错误代码
		return ex.GetType().Name.GetHashCode().ToString("X8");
	}

	private static void HandleSqlException(SqlException sqlEx, string operation, string playerName)
	{
		switch (sqlEx.Number)
		{
			case 2: // 连接超时
				Form1.WriteLine(1, $"数据库连接超时 - 操作: {operation}, 玩家: {playerName}");
				break;
			case 18456: // 登录失败
				Form1.WriteLine(1, $"数据库登录失败 - 操作: {operation}");
				break;
			case 2146232060: // 网络相关错误
				Form1.WriteLine(1, $"数据库网络错误 - 操作: {operation}, 玩家: {playerName}");
				break;
			default:
				Form1.WriteLine(1, $"SQL异常 - 错误号: {sqlEx.Number}, 消息: {sqlEx.Message}, 操作: {operation}");
				break;
		}
	}

	// 处理界面异常
	public static void HandleUIException(Exception ex, string operation, string details = "")
	{
		try
		{
			// 记录异常统计
			RecordExceptionStatistics($"UI_{operation}", ex);

			// 构建详细错误信息
			var errorInfo = BuildErrorInfo(ex, operation, details, "界面操作");
			
			// 记录到日志
			Form1.WriteLine(1, $"界面异常 - 操作: {operation}, 详情: {details}, 错误: {ex.Message}");
			
			// 如果是严重错误，记录完整堆栈
			if (IsCriticalError(ex))
			{
				Form1.WriteLine(1, $"严重界面异常堆栈: {ex.StackTrace}");
			}
		}
		catch (Exception handlerEx)
		{
			Form1.WriteLine(1, $"界面异常处理器出错: {handlerEx.Message}");
		}
	}

	// 处理游戏系统异常
	public static void HandleSystemException(Exception ex, string systemName, string operation, string details = "")
	{
		try
		{
			// 记录异常统计
			RecordExceptionStatistics($"SYS_{systemName}_{operation}", ex);

			// 构建详细错误信息
			var errorInfo = BuildErrorInfo(ex, operation, details, systemName);
			
			// 记录到日志
			Form1.WriteLine(1, $"系统异常 - 系统: {systemName}, 操作: {operation}, 详情: {details}, 错误: {ex.Message}");
			
			// 如果是严重错误，记录完整堆栈
			if (IsCriticalError(ex))
			{
				Form1.WriteLine(1, $"严重系统异常堆栈: {ex.StackTrace}");
			}
		}
		catch (Exception handlerEx)
		{
			Form1.WriteLine(1, $"系统异常处理器出错: {handlerEx.Message}");
		}
	}

	// 处理定时器异常
	public static void HandleTimerException(Exception ex, string timerName, string operation, string details = "")
	{
		try
		{
			// 记录异常统计
			RecordExceptionStatistics($"TIMER_{timerName}_{operation}", ex);

			// 记录到日志
			Form1.WriteLine(1, $"定时器异常 - 定时器: {timerName}, 操作: {operation}, 详情: {details}, 错误: {ex.Message}");
			
			// 定时器异常通常需要记录堆栈信息
			if (ex.StackTrace != null)
			{
				Form1.WriteLine(1, $"定时器异常堆栈: {ex.StackTrace}");
			}
		}
		catch (Exception handlerEx)
		{
			Form1.WriteLine(1, $"定时器异常处理器出错: {handlerEx.Message}");
		}
	}



private static readonly ConcurrentDictionary<string, int> ExceptionStatistics = new ConcurrentDictionary<string, int>();
private static readonly ConcurrentDictionary<string, DateTime> LastExceptionTime = new ConcurrentDictionary<string, DateTime>();


public static void InitializeExceptionStatistics() 
    {
        //EVIAS 性能优化：每5分钟输出一次异常统计
        var timer = new System.Timers.Timer(300000); // 5分钟
	timer.Elapsed += (sender, e) => PrintExceptionStatistics();
	timer.AutoReset = true;
	timer.Enabled = true;

	//EVIAS 每天凌晨2点清理日志文件
	var logCleanupTimer = new System.Timers.Timer(3600000); // 1小时检查一次
	logCleanupTimer.Elapsed += (sender, e) => CheckAndCleanupLogFiles();
	logCleanupTimer.AutoReset = true;
	logCleanupTimer.Enabled = true;
}



private static void RecordExceptionStatistics(string operation, Exception ex) // 记录异常统计
    {
        try
	{
		var key = $"{operation}_{ex.GetType().Name}";
		ExceptionStatistics.AddOrUpdate(key, 1, (k, v) => v + 1);
		LastExceptionTime.AddOrUpdate(key, DateTime.Now, (k, v) => DateTime.Now);
	}
	catch
	{
		// 统计功能本身不应该影响主要功能
	}
}



public static string GetExceptionStatistics() // 获取异常统计信息
    {
        try
	{
		if (ExceptionStatistics.IsEmpty)
		{
			return "暂无异常统计数据";
		}

		var totalExceptions = ExceptionStatistics.Values.Sum();
		var topExceptions = ExceptionStatistics.OrderByDescending(x => x.Value).Take(5);

		var result = $"总异常次数: {totalExceptions}, 异常类型数: {ExceptionStatistics.Count}\n";
		result += "前5名异常:\n";

		foreach (var ex in topExceptions)
		{
			result += $"- {ex.Key}: {ex.Value}次\n";
		}

		return result;
	}
	catch (Exception ex)
	{
		return $"获取异常统计失败: {ex.Message}";
	}
}



private static void PrintExceptionStatistics() // 输出异常统计信息
    {
	try
	{
		if (ExceptionStatistics.IsEmpty)
		{
			return;
		}

		Form1.WriteLine(0, "=== 异常统计报告 ===");
		var sortedStats = ExceptionStatistics.OrderByDescending(x => x.Value).Take(10);

		foreach (var stat in sortedStats)
		{
			var lastTime = LastExceptionTime.TryGetValue(stat.Key, out DateTime time) ? time : DateTime.MinValue;
			Form1.WriteLine(0, $"异常: {stat.Key}, 次数: {stat.Value}, 最后发生: {lastTime:yyyy-MM-dd HH:mm:ss}");
		}

		Form1.WriteLine(0, $"总异常类型数: {ExceptionStatistics.Count}");
		Form1.WriteLine(0, "=== 异常统计报告结束 ===");
	}
	catch (Exception ex)
	{
		Form1.WriteLine(1, $"异常统计输出失败: {ex.Message}");
	}
}

public static void ClearExceptionStatistics() // 清空异常统计数据
{
	try
	{
		ExceptionStatistics.Clear();
		LastExceptionTime.Clear();
		Form1.WriteLine(0, "异常统计数据已清空");
	}
	catch (Exception ex)
	{
		Form1.WriteLine(1, $"清空异常统计失败: {ex.Message}");
		throw;
	}
}


private static void CheckAndCleanupLogFiles() // 2025-0618 EVIAS 日志文件清理功能
    {
	try
	{
		DateTime now = DateTime.Now;

		// 只在凌晨2点执行清理
		if (now.Hour != 2 || now.Minute > 30)
		{
			return;
		}

		// 避免重复执行
		if (Math.Abs((now - _lastLogCleanupTime).TotalHours) < 12)
		{
			return;
		}

		_lastLogCleanupTime = now;
		CleanupLogFiles();
	}
	catch (Exception ex)
	{
		Form1.WriteLine(1, $"日志清理检查失败: {ex.Message}");
	}
}

private static DateTime _lastLogCleanupTime = DateTime.MinValue;


public static void CleanupLogFiles() // 清理过期日志文件
    {
	try
	{
		string logsPath = "logs";
		if (!Directory.Exists(logsPath))
		{
			return;
		}

		int keepDays = World.记录保存天数; // 使用与数据库记录相同的保存天数
		DateTime cutoffDate = DateTime.Now.AddDays(-keepDays);

		Form1.WriteLine(2, $"开始清理 {keepDays} 天前的日志文件...");

		// 定义需要清理的日志文件模式
		string[] logPatterns = {
			"错误日志_*.log",
			"CQLog_*.log",
			"登陆日志_*.log",
			"DropItmeLog_*.log",
			"ItmeLog_*.log",
			"BugLog_*.txt",
			"封包_*.txt",
			"存档_*.txt",
			"踢号日志_*.log",
			"帮战日志_*.log",
			"非法物品日志_*.log",
			"wep抓包日志_*.log",
			"道具加锁解锁_*.log",
			"自动活动_*.log",
			"百宝抽奖_*.log",
			"累计充值_*.log",
			"活动结束_*.log"
		};

		int deletedCount = 0;
		long deletedSize = 0;

		foreach (string pattern in logPatterns)
		{
			string[] files = Directory.GetFiles(logsPath, pattern);

			foreach (string file in files)
			{
				try
				{
					FileInfo fileInfo = new FileInfo(file);

					// 检查文件创建时间是否超过保存天数
					if (fileInfo.CreationTime < cutoffDate)
					{
						deletedSize += fileInfo.Length;
						File.Delete(file);
						deletedCount++;

						Form1.WriteLine(6, $"删除过期日志: {Path.GetFileName(file)} (创建时间: {fileInfo.CreationTime:yyyy-MM-dd})");
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, $"删除日志文件失败 {file}: {ex.Message}");
				}
			}
		}

		if (deletedCount > 0)
		{
			double deletedMB = deletedSize / (1024.0 * 1024.0);
			Form1.WriteLine(2, $"日志清理完成: 删除 {deletedCount} 个文件，释放空间 {deletedMB:F2} MB");
		}
		else
		{
			Form1.WriteLine(2, "日志清理完成: 没有需要清理的过期文件");
		}
	}
	catch (Exception ex)
	{
		Form1.WriteLine(1, $"日志文件清理失败: {ex.Message}");
	}
}

}

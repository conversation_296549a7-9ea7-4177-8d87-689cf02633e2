namespace RxjhServer;

public class 累充系统
{
	private int _FLD_PID;

	private int _FLD_累充金额;

	private string _FLD_NAME;

	private int _FLD_NUMBER;

	private int _FLD_MAGIC0;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private string _FLD_DES;

	private int _是否披风;

	private int _是否绑定;

	private int _使用天数;

	public int FLD_累充金额
	{
		get
		{
			return _FLD_累充金额;
		}
		set
		{
			_FLD_累充金额 = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public string FLD_DES
	{
		get
		{
			return _FLD_DES;
		}
		set
		{
			_FLD_DES = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return _FLD_MAGIC0;
		}
		set
		{
			_FLD_MAGIC0 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int 是否绑定
	{
		get
		{
			return _是否绑定;
		}
		set
		{
			_是否绑定 = value;
		}
	}

	public int 使用天数
	{
		get
		{
			return _使用天数;
		}
		set
		{
			_使用天数 = value;
		}
	}

	public int 是否披风
	{
		get
		{
			return _是否披风;
		}
		set
		{
			_是否披风 = value;
		}
	}

	public int FLD_NUMBER
	{
		get
		{
			return _FLD_NUMBER;
		}
		set
		{
			_FLD_NUMBER = value;
		}
	}
}

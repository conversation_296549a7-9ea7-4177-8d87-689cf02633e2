using System;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class MissionClass : IDisposable
{
	private Players Play;

	public MissionClass(Players Playr)
	{
		Play = Playr;
	}

	public void Dispose()
	{
		Play = null;
	}

	public void 任务(byte[] data, int length)
	{
		Play.封包修改(data, length);
		int num = BitConverter.ToUInt16(data, 10);
		int num2 = BitConverter.ToUInt16(data, 12);
		int 任务阶段ID = BitConverter.ToInt16(data, 14);
		switch (num)
		{
		case 18:
			一转任务(num, num2, 任务阶段ID);
			return;
		case 11:
			二转任务正(num, num2, 任务阶段ID);
			return;
		case 12:
			二转任务邪(num, num2, 任务阶段ID);
			return;
		case 45:
			三转任务正(num, num2, 任务阶段ID);
			return;
		case 46:
			三转任务邪(num, num2, 任务阶段ID);
			return;
		case 73:
			四转任务正(num, num2, 任务阶段ID);
			return;
		case 74:
			四转任务邪(num, num2, 任务阶段ID);
			return;
		case 178:
			五转任务(num, num2, 任务阶段ID);
			return;
		case 300:
			六转任务(num, num2, 任务阶段ID);
			return;
		case 301:
			七转任务(num, num2, 任务阶段ID);
			return;
		case 400:
		case 401:
		case 402:
		case 403:
		case 404:
		case 405:
		case 406:
		case 407:
		case 408:
		case 410:
			八转任务(num, num2, 任务阶段ID);
			return;
		case 615:
		case 617:
		case 620:
			九转任务(num, num2, 任务阶段ID);
			return;
		case 748:
		case 749:
		case 750:
			十转任务(num, num2, 任务阶段ID);
			return;
		case 790:
			十一转任务(num, num2, 任务阶段ID);
			return;
		case 791:
			十二转任务(num, num2, 任务阶段ID); //EVIAS  24.0  十二转任务
            return;
		case 9202:
			凝神珠秘密(num, num2, 任务阶段ID);
			return;
		case 1002:
			灵兽一转任务(num, num2, 任务阶段ID);
			return;
		case 1003:
			灵兽二转正(num, num2, 任务阶段ID);
			return;
		case 1004:
			灵兽二转邪(num, num2, 任务阶段ID);
			return;
		case 1005:
			灵兽三转正(num, num2, 任务阶段ID);
			return;
		case 1006:
			灵兽三转邪(num, num2, 任务阶段ID);
			return;
		case 1012:
			灵兽四转正(num, num2, 任务阶段ID);
			return;
		case 1013:
			灵兽四转邪(num, num2, 任务阶段ID);
			return;
		case 1014:
			灵兽五转正(num, num2, 任务阶段ID);
			return;
		case 1015:
			灵兽五转邪(num, num2, 任务阶段ID);
			return;
		}
		if (num2 == 1)
		{
			任务提示(num, 11, 任务阶段ID);
		}
		if (num2 == 3)
		{
			任务提示(num, 31, 任务阶段ID);
		}
		if (num2 == 5)
		{
			任务提示(num, 51, 任务阶段ID);
		}
	}

	public void 灵兽一转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.人物灵兽 == null)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
			}
			else if (Play.人物灵兽.是否已完成任务(任务ID))
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("灵兽1转任务已完成，无法重复接取", 10, "系统提示");
			}
			else if (Play.人物灵兽.FLD_LEVEL < 15 || Play.人物灵兽.FLD_JOB_LEVEL != 0)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示($"灵兽1转条件不足：等级需要≥15(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要=0(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			Play.灵兽转职业(0, 1);
			任务提示(任务ID, 21, 1);
			Play.人物灵兽.计算基本数据();
			Play.更新灵兽HP_MP_SP();
			Play.更新灵兽武功和状态();
			Play.更新灵兽负重();
			Play.召唤更新显示已装备物品(Play);
			
			Play.人物灵兽.添加已完成任务(任务ID);
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			Play.人物灵兽.保存数据();
			
			Play.更新人物任务();
			Play.系统提示("灵兽1转成功", 10, "系统提示");
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 灵兽二转正(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.人物灵兽 == null)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
			}
			else if (Play.人物灵兽.是否已完成任务(任务ID))
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("灵兽2转正任务已完成，无法重复接取", 10, "系统提示");
			}
			else if (Play.人物灵兽.FLD_LEVEL < 50 || Play.人物灵兽.FLD_JOB_LEVEL < 1)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示($"灵兽2转正条件不足：等级需要≥50(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥1(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			Play.灵兽转职业(1, 2);
			任务提示(任务ID, 21, 1);
			Play.人物灵兽.计算基本数据();
			Play.更新灵兽HP_MP_SP();
			Play.更新灵兽武功和状态();
			Play.更新灵兽负重();
			Play.召唤更新显示已装备物品(Play);
			
			Play.人物灵兽.添加已完成任务(任务ID);
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			Play.人物灵兽.保存数据();
			
			Play.更新人物任务();
			Play.系统提示("灵兽2转成功", 10, "系统提示");
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 灵兽二转邪(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.人物灵兽 == null)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
			}
			else if (Play.人物灵兽.是否已完成任务(任务ID))
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("灵兽2转邪任务已完成，无法重复接取", 10, "系统提示");
			}
			else if (Play.人物灵兽.FLD_LEVEL < 50 || Play.人物灵兽.FLD_JOB_LEVEL < 1)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示($"灵兽2转邪条件不足：等级需要≥50(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥1(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			Play.灵兽转职业(2, 2);
			任务提示(任务ID, 21, 1);
			Play.人物灵兽.计算基本数据();
			Play.更新灵兽HP_MP_SP();
			Play.更新灵兽武功和状态();
			Play.更新灵兽负重();
			Play.召唤更新显示已装备物品(Play);
			
			Play.人物灵兽.添加已完成任务(任务ID);
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			Play.人物灵兽.保存数据();
			
			Play.更新人物任务();
			Play.系统提示("灵兽2转成功", 10, "系统提示");
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 灵兽三转正(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.人物灵兽 == null)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
			}
			else if (Play.人物灵兽.是否已完成任务(任务ID))
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("灵兽3转正任务已完成，无法重复接取", 10, "系统提示");
			}
			else if (Play.人物灵兽.FLD_LEVEL < 75 || Play.人物灵兽.FLD_JOB_LEVEL < 2)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示($"灵兽3转正条件不足：等级需要≥75(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥2(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			Play.灵兽转职业(1, 3);
			任务提示(任务ID, 21, 1);
			Play.人物灵兽.计算基本数据();
			Play.更新灵兽HP_MP_SP();
			Play.更新灵兽武功和状态();
			Play.更新灵兽负重();
			Play.召唤更新显示已装备物品(Play);
			
			Play.人物灵兽.添加已完成任务(任务ID);
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			Play.人物灵兽.保存数据();
			
			Play.更新人物任务();
			Play.系统提示("灵兽3转成功", 10, "系统提示");
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 灵兽三转邪(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.人物灵兽 == null)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
			}
			else if (Play.人物灵兽.是否已完成任务(任务ID))
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示("灵兽3转邪任务已完成，无法重复接取", 10, "系统提示");
			}
			else if (Play.人物灵兽.FLD_LEVEL < 75 || Play.人物灵兽.FLD_JOB_LEVEL < 2)
			{
				任务提示(任务ID, 12, 任务阶段ID);
				Play.系统提示($"灵兽3转邪条件不足：等级需要≥75(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥2(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			Play.灵兽转职业(2, 3);
			任务提示(任务ID, 21, 1);
			Play.人物灵兽.计算基本数据();
			Play.更新灵兽HP_MP_SP();
			Play.更新灵兽武功和状态();
			Play.更新灵兽负重();
			Play.召唤更新显示已装备物品(Play);
			
			Play.人物灵兽.添加已完成任务(任务ID);
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			Play.人物灵兽.保存数据();
			
			Play.更新人物任务();
			Play.系统提示("灵兽3转成功", 10, "系统提示");
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

    // 2025-0624 EVIAS 新增灵兽4转正任务
    public void 灵兽四转正(int 任务ID, int 操作ID, int 任务阶段ID)
    {
        switch (操作ID)
        {
            case 1:
                if (Play.人物灵兽 == null)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
                }
                else if (Play.人物灵兽.是否已完成任务(任务ID))
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("灵兽4转正任务已完成，无法重复接取", 10, "系统提示");
                }
                else if (Play.人物灵兽.FLD_LEVEL < 100 || Play.人物灵兽.FLD_JOB_LEVEL < 3)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示($"灵兽4转正条件不足：等级需要≥100(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥3(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
                }
                else
                {
                    任务提示(任务ID, 11, 任务阶段ID);
                }
                break;
            case 2:
                Form1.WriteLine(3, $"灵兽4转正任务 - 开始执行转职: 当前转职等级={Play.人物灵兽.FLD_JOB_LEVEL}");
                Play.灵兽转职业(1, 4);
                Form1.WriteLine(3, $"灵兽4转正任务 - 转职完成: 新转职等级={Play.人物灵兽.FLD_JOB_LEVEL}");
                任务提示(任务ID, 21, 1);
                Play.人物灵兽.计算基本数据();
                Play.更新灵兽HP_MP_SP();
                Play.更新灵兽武功和状态();
                Play.更新灵兽负重();
                Play.召唤更新显示已装备物品(Play);
                
                Play.人物灵兽.添加已完成任务(任务ID);
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                Play.人物灵兽.保存数据();
                
                Play.更新人物任务();
                Play.系统提示("灵兽4转成功", 10, "系统提示");
                Form1.WriteLine(3, $"灵兽4转正任务 - 任务完成处理结束");
                break;
            case 3:
                任务提示(任务ID, 31, 任务阶段ID);
                break;
            case 4:
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                任务提示(任务ID, 41, 任务阶段ID);
                break;
            case 5:
                任务提示(任务ID, 51, 任务阶段ID);
                break;
        }
    }

    // 2025-0624 EVIAS 新增灵兽4转邪任务
    public void 灵兽四转邪(int 任务ID, int 操作ID, int 任务阶段ID)
    {
        switch (操作ID)
        {
            case 1:
                if (Play.人物灵兽 == null)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
                }
                else if (Play.人物灵兽.是否已完成任务(任务ID))
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("灵兽4转邪任务已完成，无法重复接取", 10, "系统提示");
                }
                else if (Play.人物灵兽.FLD_LEVEL < 100 || Play.人物灵兽.FLD_JOB_LEVEL < 3)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示($"灵兽4转邪条件不足：等级需要≥100(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥3(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
                }
                else
                {
                    任务提示(任务ID, 11, 任务阶段ID);
                }
                break;
            case 2:
                Play.灵兽转职业(2, 4);
                任务提示(任务ID, 21, 1);
                Play.人物灵兽.计算基本数据();
                Play.更新灵兽HP_MP_SP();
                Play.更新灵兽武功和状态();
                Play.更新灵兽负重();
                Play.召唤更新显示已装备物品(Play);
                
                Play.人物灵兽.添加已完成任务(任务ID);
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                Play.人物灵兽.保存数据();
                
                Play.更新人物任务();
                Play.系统提示("灵兽4转成功", 10, "系统提示");
                break;
            case 3:
                任务提示(任务ID, 31, 任务阶段ID);
                break;
            case 4:
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                任务提示(任务ID, 41, 任务阶段ID);
                break;
            case 5:
                任务提示(任务ID, 51, 任务阶段ID);
                break;
        }
    }

    // 2025-0624 EVIAS 新增灵兽5转正任务
    public void 灵兽五转正(int 任务ID, int 操作ID, int 任务阶段ID)
    {
        switch (操作ID)
        {
            case 1:
                if (Play.人物灵兽 == null)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
                }
                else if (Play.人物灵兽.是否已完成任务(任务ID))
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("灵兽5转正任务已完成，无法重复接取", 10, "系统提示");
                }
                else if (Play.人物灵兽.FLD_LEVEL < 125 || Play.人物灵兽.FLD_JOB_LEVEL < 4)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示($"灵兽5转正条件不足：等级需要≥125(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥4(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
                }
                else
                {
                    任务提示(任务ID, 11, 任务阶段ID);
                }
                break;
            case 2:
                Play.灵兽转职业(1, 5);
                任务提示(任务ID, 21, 1);
                Play.人物灵兽.计算基本数据();
                Play.更新灵兽HP_MP_SP();
                Play.更新灵兽武功和状态();
                Play.更新灵兽负重();
                Play.召唤更新显示已装备物品(Play);
                
                Play.人物灵兽.添加已完成任务(任务ID);
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                Play.人物灵兽.保存数据();
                
                Play.更新人物任务();
                Play.系统提示("灵兽5转成功", 10, "系统提示");
                break;
            case 3:
                任务提示(任务ID, 31, 任务阶段ID);
                break;
            case 4:
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                任务提示(任务ID, 41, 任务阶段ID);
                break;
            case 5:
                任务提示(任务ID, 51, 任务阶段ID);
                break;
        }
    }

    // 2025-0624 EVIAS 新增灵兽5转邪任务
    public void 灵兽五转邪(int 任务ID, int 操作ID, int 任务阶段ID)
    {
        switch (操作ID)
        {
            case 1:
                if (Play.人物灵兽 == null)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("你还没有灵兽，无法接取此任务", 10, "系统提示");
                }
                else if (Play.人物灵兽.是否已完成任务(任务ID))
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示("灵兽5转邪任务已完成，无法重复接取", 10, "系统提示");
                }
                else if (Play.人物灵兽.FLD_LEVEL < 125 || Play.人物灵兽.FLD_JOB_LEVEL < 4)
                {
                    任务提示(任务ID, 12, 任务阶段ID);
                    Play.系统提示($"灵兽5转邪条件不足：等级需要≥125(当前{Play.人物灵兽.FLD_LEVEL})，转职等级需要≥4(当前{Play.人物灵兽.FLD_JOB_LEVEL})", 10, "系统提示");
                }
                else
                {
                    任务提示(任务ID, 11, 任务阶段ID);
                }
                break;
            case 2:
                Play.灵兽转职业(2, 5);
                任务提示(任务ID, 21, 1);
                Play.人物灵兽.计算基本数据();
                Play.更新灵兽HP_MP_SP();
                Play.更新灵兽武功和状态();
                Play.更新灵兽负重();
                Play.召唤更新显示已装备物品(Play);
                
                Play.人物灵兽.添加已完成任务(任务ID);
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                Play.人物灵兽.保存数据();
                
                Play.更新人物任务();
                Play.系统提示("灵兽5转成功", 10, "系统提示");
                break;
            case 3:
                任务提示(任务ID, 31, 任务阶段ID);
                break;
            case 4:
                if (Play.任务.ContainsKey(任务ID))
                {
                    Play.任务.TryRemove(任务ID, out var _);
                }
                任务提示(任务ID, 41, 任务阶段ID);
                break;
            case 5:
                任务提示(任务ID, 51, 任务阶段ID);
                break;
        }
    }

    public void 任务提示(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		byte[] array = Converter.hexStringToByte("AA551400000084000600120033000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(任务ID), 0, array, 10, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(操作ID), 0, array, 12, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(任务阶段ID), 0, array, 14, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 4, 2);
		if (Play.Client != null)
		{
			Play.Client.Send(array, array.Length);
		}
	}

	public void 一转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 10 || Play.Player_Job_leve >= 1)
			{
				Play.系统提示("申请[一]转职必须是10级。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
			if (Play.Player_Level >= 10 && Play.Player_Job_leve < 1 && Play.检测转职武器())
			{
				Play.人物转职业(0, 1);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(5, 0);
				Play.更新人物数据(Play);
				Play.更新装备效果to(Play, Play);
				Play.更新武功和状态();
			}
			break;
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 二转任务正(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 35 || Play.Player_Job_leve != 1 || Play.Player_Zx != 0)
			{
				Play.系统提示("申请[二]转职必须是35级一转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 35 || Play.Player_Job_leve >= 2 || Play.Player_Zx != 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(1, 2);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(6, 0);
				Play.神女武功点数 += 5;
				Play.更新人物数据(Play);
				Play.更新装备效果to(Play, Play);
				Play.更新武功和状态();
				if (World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000517, num, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 二转任务邪(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 35 || Play.Player_Job_leve != 1 || Play.Player_Zx != 0)
			{
				Play.系统提示("申请[二]转职必须是35级一转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 35 || Play.Player_Job_leve >= 2 || Play.Player_Zx != 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(2, 2);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(6, 0);
				Play.神女武功点数 += 5;
				Play.更新人物数据(Play);
				Play.更新装备效果to(Play, Play);
				Play.更新武功和状态();
				if (World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000517, num, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 三转任务正(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 60 || Play.Player_Job_leve != 2 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[三]转职必须是60级二转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 60 || Play.Player_Job_leve >= 3 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 3);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(7, 0);
				Play.神女武功点数 += 5;
				Play.更新武功和状态();
				Play.初始化已装备物品();
				Play.更新人物数据(Play);
				if (Play.Player_Job == 8)
				{
					Play.增加物品带属性(1000000564, num, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000518, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 三转任务邪(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 60 || Play.Player_Job_leve != 2 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[三]转职必须是60级二转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 60 || Play.Player_Job_leve >= 3 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 3);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(7, 0);
				Play.神女武功点数 += 5;
				Play.更新人物数据(Play);
				Play.更新武功和状态();
				if (Play.Player_Job == 8)
				{
					Play.增加物品带属性(1000000564, num, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000518, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 四转任务正(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		if (操作ID == 1)
		{
			if (Play.Player_Level < 80 || Play.Player_Job_leve != 3 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[四]转职必须是80级三转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
		}
		switch (操作ID)
		{
		case 2:
		{
			if (Play.Player_Level < 80 || Play.Player_Job_leve >= 4 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 4);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(8, 0);
				Play.新学气功(9, 0);
				Play.发送四转技能书(num);
				Play.神女武功点数 += 5;
				Play.更新人物数据(Play);
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000519, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 四转任务邪(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 80 || Play.Player_Job_leve != 3 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[四]转职必须是80级三转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 80 || Play.Player_Job_leve >= 4 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 4);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(8, 0);
				Play.新学气功(9, 0);
				Play.发送四转技能书(num);
				Play.神女武功点数 += 5;
				Play.更新人物数据(Play);
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000519, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 五转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 100 || Play.Player_Job_leve != 4 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[五]转职必须是100级四转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 100 || Play.Player_Job_leve >= 5 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 5);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.新学气功(10, 0);
				Play.学习技能(0, 25);
				Play.学习技能(0, 26);
				Play.学习技能(0, 27);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.神女武功点数 += 5;
				Play.更新武功和状态();
				if (World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000520, num, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 六转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 115 || Play.Player_Job_leve != 5 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天一]转职必须是110级五转。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 115 || Play.Player_Job_leve >= 6 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 6);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.发送六转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.神女武功点数 += 5;
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000521, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 七转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 120 || Play.Player_Job_leve != 6 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天二]转职必须是120级升天一。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 120 || Play.Player_Job_leve >= 7 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 7);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.发送七转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.神女武功点数 += 10;
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000534, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 八转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 130 || Play.Player_Job_leve != 7 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天三]转职必须是130级升天二。");
				任务提示(任务ID, 12, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 130 || Play.Player_Job_leve >= 8 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 8);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.发送八转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.神女武功点数 += 10;
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000515, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 九转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 140 || Play.Player_Job_leve != 8 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天四]转职必须是140级升天三。");
				任务提示(任务ID, 10, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 140 || Play.Player_Job_leve >= 9 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 9);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.神女武功点数 += 10;
				Play.发送九转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000526, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 十转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 150 || Play.Player_Job_leve != 9 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天五]转职必须是150级升天四。");
				任务提示(任务ID, 10, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 150 || Play.Player_Job_leve >= 10 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 10);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.神女武功点数 += 10;
				Play.发送十转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000525, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 十一转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 160 || Play.Player_Job_leve != 10 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天六]转职必须是160级升天五。");
				任务提示(任务ID, 10, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 160 || Play.Player_Job_leve >= 11 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 11);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.神女武功点数 += 10;
				Play.发送十一转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000529, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	// 2025-0624 EVIAS 新增十二转任务
	public void 十二转任务(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		switch (操作ID)
		{
		case 1:
			if (Play.Player_Level < 170 || Play.Player_Job_leve != 11 || Play.Player_Zx == 0)
			{
				Play.系统提示("申请[升天七]转职必须是170级升天六。");
				任务提示(任务ID, 10, 任务阶段ID);
			}
			else
			{
				任务提示(任务ID, 11, 任务阶段ID);
			}
			break;
		case 2:
		{
			if (Play.Player_Level < 170 || Play.Player_Job_leve >= 12 || Play.Player_Zx == 0 || !Play.检测转职武器())
			{
				break;
			}
			int num = Play.得到包裹空位(Play);
			if (num != -1)
			{
				Play.人物转职业(Play.Player_Zx, 12);
				Play.发送转职武器();
				任务提示(任务ID, 21, 1);
				设置任务(任务ID, 1);
				Play.神女武功点数 += 10;
				Play.发送十二转技能书(num);
				Play.更新人物数据(Play);
				Play.更新经验和历练();
				Play.初始化已装备物品();
				Play.更新武功和状态();
				int num2 = Play.得到包裹空位(Play);
				if (num2 != -1 && World.转职赠送礼包 != 0)
				{
					Play.增加物品带属性(1008000530, num2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
			}
			else
			{
				Play.系统提示("背包空位不足。");
				任务提示(任务ID, 31, 任务阶段ID);
			}
			break;
		}
		case 3:
			任务提示(任务ID, 31, 任务阶段ID);
			break;
		case 4:
			if (Play.任务.ContainsKey(任务ID))
			{
				Play.任务.TryRemove(任务ID, out var _);
			}
			任务提示(任务ID, 41, 任务阶段ID);
			break;
		case 5:
			任务提示(任务ID, 51, 任务阶段ID);
			break;
		}
	}

	public void 设置任务(int 任务ID, int 任务阶段ID)
	{
		if (Play.任务.TryGetValue(任务ID, out var value))
		{
			value.任务阶段ID = 任务阶段ID;
			return;
		}
		任务类 任务类2 = new 任务类();
		任务类2.任务ID = 任务ID;
		任务类2.任务阶段ID = 任务阶段ID;
		if (!Play.任务.ContainsKey(任务ID))
		{
			Play.任务.TryAdd(任务ID, 任务类2);
		}
	}

	public void 凝神珠秘密(int 任务ID, int 操作ID, int 任务阶段ID)
	{
		if (任务阶段ID == 0)
		{
			switch (操作ID)
			{
			case 1:
				任务提示(任务ID, 11, 任务阶段ID);
				break;
			case 2:
				任务提示(任务ID, 21, 任务阶段ID);
				设置任务(任务ID, 1);
				break;
			case 3:
				任务提示(任务ID, 31, 任务阶段ID);
				break;
			}
			return;
		}
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		int num4 = 0;
		int num5 = 0;
		for (int i = 0; i < 36; i++)
		{
			if (BitConverter.ToInt32(Play.装备栏包裹[i].物品ID, 0) == 1000000161)
			{
				num = 1;
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[i].物品ID, 0) == 1000000162)
			{
				num2 = 1;
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[i].物品ID, 0) == 1000000163)
			{
				num3 = 1;
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[i].物品ID, 0) == 1000000164)
			{
				num4 = 1;
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[i].物品ID, 0) == 1000000199)
			{
				num5 = 1;
			}
		}
		if (num == 0 || num2 == 0 || num3 == 0 || num4 == 0 || num5 == 0)
		{
			任务提示(任务ID, 12, 任务阶段ID);
			return;
		}
		for (int j = 0; j < 36; j++)
		{
			if (BitConverter.ToInt32(Play.装备栏包裹[j].物品ID, 0) == 1000000161)
			{
				Play.减去物品(j, 1);
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[j].物品ID, 0) == 1000000162)
			{
				Play.减去物品(j, 1);
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[j].物品ID, 0) == 1000000163)
			{
				Play.减去物品(j, 1);
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[j].物品ID, 0) == 1000000164)
			{
				Play.减去物品(j, 1);
			}
			else if (BitConverter.ToInt32(Play.装备栏包裹[j].物品ID, 0) == 1000000199)
			{
				Play.减去物品(j, 1);
			}
		}
		Play.增加物品(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000365), Play.得到包裹空位(Play), BitConverter.GetBytes(1), new byte[60]);
		任务提示(任务ID, 11, 2);
		设置任务(任务ID, 3);
	}
}

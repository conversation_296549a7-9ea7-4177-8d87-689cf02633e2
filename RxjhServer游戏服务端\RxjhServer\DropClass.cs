using System;
using System.Collections.Generic;
using RxjhServer.DbClss;

namespace RxjhServer;

public class DropClass
{
	private int _FLD_LEVEL1;

	private int _FLD_LEVEL2;

	private int _FLD_PID;

	private int _FLD_PP;

	public int FLD_PIDNew;

	public int FLD_MAGICNew0;

	public int FLD_MAGICNew1;

	public int FLD_MAGICNew2;

	public int FLD_MAGICNew3;

	public int FLD_MAGICNew4;

	private int _FLD_MAGIC0;

	private int _FLD_MAGIC1;

	private int _FLD_SUNX;

	private int _FLD_SUND;

	private int _是否必掉;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private string _FLD_NAME;

	private int _FLD_初级附魂;

	private int _FLD_中级附魂;

	private int _FLD_进化;

	private int _FLD_绑定;

	public int _是否开启公告;

	public int _会员掉落;

	private int _数量控制;

	private int _当前数量;

	private int _最大数量;

	private int _ID;

	public int NPCPID;

	public int MAPID;

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public int 数量控制
	{
		get
		{
			return _数量控制;
		}
		set
		{
			_数量控制 = value;
		}
	}

	public int 当前数量
	{
		get
		{
			return _当前数量;
		}
		set
		{
			_当前数量 = value;
		}
	}

	public int 最大数量
	{
		get
		{
			return _最大数量;
		}
		set
		{
			_最大数量 = value;
		}
	}

	public int 会员掉落
	{
		get
		{
			return _会员掉落;
		}
		set
		{
			_会员掉落 = value;
		}
	}

	public int 是否开启公告
	{
		get
		{
			return _是否开启公告;
		}
		set
		{
			_是否开启公告 = value;
		}
	}

	public int FLD_LEVEL1
	{
		get
		{
			return _FLD_LEVEL1;
		}
		set
		{
			_FLD_LEVEL1 = value;
		}
	}

	public int FLD_LEVEL2
	{
		get
		{
			return _FLD_LEVEL2;
		}
		set
		{
			_FLD_LEVEL2 = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public int FLD_PP
	{
		get
		{
			return _FLD_PP;
		}
		set
		{
			_FLD_PP = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return _FLD_MAGIC0;
		}
		set
		{
			_FLD_MAGIC0 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public int FLD_初级附魂
	{
		get
		{
			return _FLD_初级附魂;
		}
		set
		{
			_FLD_初级附魂 = value;
		}
	}

	public int FLD_中级附魂
	{
		get
		{
			return _FLD_中级附魂;
		}
		set
		{
			_FLD_中级附魂 = value;
		}
	}

	public int FLD_进化
	{
		get
		{
			return _FLD_进化;
		}
		set
		{
			_FLD_进化 = value;
		}
	}

	public int FLD_绑定
	{
		get
		{
			return _FLD_绑定;
		}
		set
		{
			_FLD_绑定 = value;
		}
	}

	public int FLD_SUNX
	{
		get
		{
			return _FLD_SUNX;
		}
		set
		{
			_FLD_SUNX = value;
		}
	}

	public int FLD_SUND
	{
		get
		{
			return _FLD_SUND;
		}
		set
		{
			_FLD_SUND = value;
		}
	}

	public int 是否必掉
	{
		get
		{
			return _是否必掉;
		}
		set
		{
			_是否必掉 = value;
		}
	}

	public static DropClass GetkpDrop(int leve)
	{
		List<DropClass> list = new List<DropClass>();
		new List<DropClass>();
		int num = RNG.Next(0, 8000);
		foreach (DropClass item in World.BossDrop)
		{
			if (item.FLD_PP != 0 && item.FLD_PP != 0 && item.FLD_LEVEL1 <= leve && item.FLD_LEVEL2 >= leve && item.FLD_PP + World.暴率 / 6 >= num)
			{
				list.Add(item);
			}
		}
		if (list.Count == 0)
		{
			return null;
		}
		int index = RNG.Next(0, list.Count);
		return list[index];
	}

	public static bool 高手必掉(int 地图, int 怪物)
	{
		bool result = false;
		try
		{
			if (World.Drop_GS.Count == 0)
			{
				return result;
			}
			foreach (DropClass drop_G in World.Drop_GS)
			{
				if (drop_G.是否必掉 == 1 && 地图 == drop_G.MAPID && 怪物 == drop_G.NPCPID)
				{
					result = true;
					break;
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "高手必掉检查", $"地图: {地图}, 怪物: {怪物}");
		}
		return result;
	}

	public static DropClass 获得高手怪持有物品(int 地图2, int 怪物2)
	{
		try
		{
			foreach (DropClass drop_G in World.Drop_GS)
			{
				if (drop_G.是否必掉 == 1 && 地图2 == drop_G.MAPID && 怪物2 == drop_G.NPCPID)
				{
					return new DropClass
					{
						FLD_NAME = drop_G.FLD_NAME,
						FLD_PID = drop_G.FLD_PID
					};
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "获得高手怪持有物品", $"地图: {地图2}, 怪物: {怪物2}");
		}
		return null;
	}

	public static bool boss必掉(int 地图, int 怪物)
	{
		bool result = false;
		try
		{
			if (World.BossDrop.Count == 0)
			{
				return result;
			}
			foreach (DropClass item in World.BossDrop)
			{
				if (item.是否必掉 == 1 && 地图 == item.MAPID && 怪物 == item.NPCPID)
				{
					result = true;
					break;
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "BOSS必掉检查", $"地图: {地图}, 怪物: {怪物}");
		}
		return result;
	}

	public static DropClass 获得BOSS持有物品(int 地图2, int 怪物2)
	{
		try
		{
			foreach (DropClass item in World.BossDrop)
			{
				if (item.是否必掉 == 1 && 地图2 == item.MAPID && 怪物2 == item.NPCPID)
				{
					return new DropClass
					{
						FLD_NAME = item.FLD_NAME,
						FLD_PID = item.FLD_PID
					};
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "获得BOSS持有物品", $"地图: {地图2}, 怪物: {怪物2}");
		}
		return null;
	}

	public static List<DropClass> GetBossDrop(Players play, int leve, int MID, int NID)
	{
		try
		{
			List<DropClass> list = new List<DropClass>();
			List<DropClass> list2 = new List<DropClass>();
			int num = 0;
			foreach (DropClass item in World.BossDrop)
			{
				if ((item.MAPID != 0 && MID != item.MAPID) || (item.NPCPID != 0 && NID != item.NPCPID) || item.FLD_PP == 0 || item.FLD_LEVEL1 > leve || item.FLD_LEVEL2 < leve)
				{
					continue;
				}
				if (item.会员掉落 == 1)
				{
					if (play.检查金符())
					{
						list.Add(item);
						num += item.FLD_PP;
					}
				}
				else if (item.会员掉落 == 2)
				{
					if (play.FLD_VIP == 1)
					{
						list.Add(item);
						num += item.FLD_PP;
					}
				}
				else
				{
					list.Add(item);
					num += item.FLD_PP;
				}
			}
			if (list.Count == 0)
			{
				return null;
			}
			int num2 = RNG.Next(World.BOSS掉落物品数量下限, World.BOSS掉落物品数量上限);
			int num3 = 0;
			int num4 = RNG.Next(1, num);
			for (int i = 0; i < num2; i++)
			{
				foreach (DropClass item2 in list)
				{
					num3 += item2.FLD_PP;
					if (num3 >= num4)
					{
						list2.Add(item2);
						break;
					}
				}
			}
			return (list2.Count == 0) ? null : list2;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "BOSS掉落出错leve[" + leve + "]" + ex.Message);
			return null;
		}
	}

	public static List<DropClass> GetGSDrop(Players play, int leve, int MID, int NID)
	{
		try
		{
			List<DropClass> list = new List<DropClass>();
			List<DropClass> list2 = new List<DropClass>();
			int num = 0;
			foreach (DropClass drop_G in World.Drop_GS)
			{
				if ((drop_G.MAPID != 0 && MID != drop_G.MAPID) || (drop_G.NPCPID != 0 && NID != drop_G.NPCPID) || drop_G.FLD_PP == 0 || drop_G.FLD_LEVEL1 > leve || drop_G.FLD_LEVEL2 < leve)
				{
					continue;
				}
				if (drop_G.会员掉落 == 1)
				{
					if (play.检查金符())
					{
						list.Add(drop_G);
						num += drop_G.FLD_PP;
					}
				}
				else if (drop_G.会员掉落 == 2)
				{
					if (play.FLD_VIP == 1)
					{
						list.Add(drop_G);
						num += drop_G.FLD_PP;
					}
				}
				else
				{
					list.Add(drop_G);
					num += drop_G.FLD_PP;
				}
			}
			if (list.Count == 0)
			{
				return null;
			}
			int num2 = RNG.Next(1, 5);
			int num3 = RNG.Next(1, num);
			int num4 = 0;
			for (int i = 0; i < num2; i++)
			{
				foreach (DropClass item in list)
				{
					num4 += item.FLD_PP;
					if (num4 >= num3)
					{
						list2.Add(item);
						break;
					}
				}
			}
			return (list2.Count == 0) ? null : list2;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "高手怪掉落出错leve[" + leve + "]" + ex.Message);
			return null;
		}
	}

	public static DropClass GetDrop(Players play, int leve, int MID, int NID)
	{
		try
		{
			List<DropClass> list = new List<DropClass>();
			int num = 0;
			foreach (DropClass item in World.Drop)
			{
				if ((item.MAPID != 0 && MID != item.MAPID) || (item.NPCPID != 0 && NID != item.NPCPID) || item.FLD_PP == 0 || item.FLD_LEVEL1 > leve || item.FLD_LEVEL2 < leve)
				{
					continue;
				}
				if (item.会员掉落 == 1)
				{
					if (play.检查金符())
					{
						list.Add(item);
						num += item.FLD_PP;
					}
				}
				else if (item.会员掉落 == 2)
				{
					if (play.FLD_VIP == 1)
					{
						list.Add(item);
						num += item.FLD_PP;
					}
				}
				else
				{
					list.Add(item);
					num += item.FLD_PP;
				}
			}
			if (list.Count == 0)
			{
				return null;
			}
			int num2 = RNG.Next(1, num);
			int num3 = 0;
			foreach (DropClass item2 in list)
			{
				num3 += item2.FLD_PP;
				if (num3 < num2)
				{
					continue;
				}
				if (item2.数量控制 == 1)
				{
					if (item2.当前数量 >= item2.最大数量)
					{
						return null;
					}
					item2.当前数量++;
					RxjhClass.保存普通暴率(item2);
					return item2;
				}
				return item2;
			}
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "普通怪掉落出错leve[" + leve + "]" + ex.Message);
			return null;
		}
	}
}

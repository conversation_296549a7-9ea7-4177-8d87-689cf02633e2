﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 门战记录 {

		[JsonProperty, Column(DbType = "varchar(500)")]
		public string 分区 { get; set; }

		[JsonProperty, Column(DbType = "smalldatetime", InsertValueSql = "getdate()")]
		public DateTime? 时间 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 战败门派 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 战胜门派 { get; set; }

		[JsonProperty, Column(IsIdentity = true)]
		public int ID { get; set; }

	}

}

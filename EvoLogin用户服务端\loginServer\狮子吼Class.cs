namespace loginServer;

public class 狮子吼Class
{
	private int m_FLD_INDEX;

	private string m_UserName;

	private int m_TxtId;

	private string m_txt;

	private string m_UserClientIP;

	private int m_线Id;

	private int m_Map;

	public int m_样式;

	public int FLD_INDEX
	{
		get
		{
			return m_FLD_INDEX;
		}
		set
		{
			m_FLD_INDEX = value;
		}
	}

	public string UserName
	{
		get
		{
			return m_UserName;
		}
		set
		{
			m_UserName = value;
		}
	}

	public int TxtId
	{
		get
		{
			return m_TxtId;
		}
		set
		{
			m_TxtId = value;
		}
	}

	public string Txt
	{
		get
		{
			return m_txt;
		}
		set
		{
			m_txt = value;
		}
	}

	public string UserClientIP
	{
		get
		{
			return m_UserClientIP;
		}
		set
		{
			m_UserClientIP = value;
		}
	}

	public int 线Id
	{
		get
		{
			return m_线Id;
		}
		set
		{
			m_线Id = value;
		}
	}

	public int 样式
	{
		get
		{
			return m_样式;
		}
		set
		{
			m_样式 = value;
		}
	}

	public int Map
	{
		get
		{
			return m_Map;
		}
		set
		{
			m_Map = value;
		}
	}
}

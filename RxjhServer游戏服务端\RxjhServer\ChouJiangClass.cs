using System;
using System.Collections;

namespace RxjhServer;

public class ChouJiangClass
{
	private int _FLD_PID;

	private string _FLD_NAME;

	private int _FLD_PP;

	private int _FLD_NUMBER;

	private int _FLD_MAGIC0;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _FLD_MAGIC5;

	private int _是否提示;

	private int _是否绑定;

	private int _是否展示;

	private int _使用天数;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public int FLD_PP
	{
		get
		{
			return _FLD_PP;
		}
		set
		{
			_FLD_PP = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return _FLD_MAGIC0;
		}
		set
		{
			_FLD_MAGIC0 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return _FLD_MAGIC5;
		}
		set
		{
			_FLD_MAGIC5 = value;
		}
	}

	public int 是否提示
	{
		get
		{
			return _是否提示;
		}
		set
		{
			_是否提示 = value;
		}
	}

	public int 是否绑定
	{
		get
		{
			return _是否绑定;
		}
		set
		{
			_是否绑定 = value;
		}
	}

	public int 是否展示
	{
		get
		{
			return _是否展示;
		}
		set
		{
			_是否展示 = value;
		}
	}

	public int 使用天数
	{
		get
		{
			return _使用天数;
		}
		set
		{
			_使用天数 = value;
		}
	}

	public int FLD_NUMBER
	{
		get
		{
			return _FLD_NUMBER;
		}
		set
		{
			_FLD_NUMBER = value;
		}
	}

	public static ChouJiangClass GetChouJiang()
	{
		ArrayList arrayList = new ArrayList();
		Random random = new Random(BitConverter.ToInt32(Guid.NewGuid().ToByteArray(), 0));
		int num = random.Next(1, 5000);
		foreach (ChouJiangClass item in World.ChouJiang)
		{
			if (item.FLD_PP >= num)
			{
				arrayList.Add(item);
			}
		}
		if (arrayList.Count == 0)
		{
			return null;
		}
		int index = random.Next(0, arrayList.Count);
		return (ChouJiangClass)arrayList[index];
	}
}

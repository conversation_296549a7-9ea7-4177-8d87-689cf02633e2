using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 强化参数调整 : Form
{
	private IContainer components = null;

	private Button button1;

	private ListView listView2;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader14;

	private TextBox textBox1;

	private Label label1;

	private Label label2;

	private TextBox textBox2;

	private Label label3;

	private TextBox textBox3;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private TextBox textBox4;

	private TextBox textBox5;

	private Label label4;

	private Label label5;

	private TextBox textBox6;

	private Label label6;

	private Label label7;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private Label label8;

	private TextBox textBox7;

	private Label label9;

	private TextBox textBox8;

	private ToolTip toolTip1;

	public 强化参数调整()
	{
		InitializeComponent();
	}

	private void 强化参数调整_Load(object sender, EventArgs e)
	{
		刷新();
	}

	public void 刷新()
	{
		listView2.Items.Clear();
		string sqlCommand = "SELECT * FROM TBL_XWWL_强化参数";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			ListViewItem listViewItem = new ListViewItem();
			listViewItem.SubItems.Clear();
			listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["FLD_强化数"].ToString();
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_首饰加工"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_属性阶段"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_水晶属性阶段"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NPC强化"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_至尊符强化"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_灵宠强化"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_披风强化"].ToString());
			listView2.Items.Add(listViewItem);
		}
		dBToDataTable.Dispose();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			写数据(int.Parse(textBox6.Text), textBox1.Text, textBox2.Text, textBox3.Text, textBox4.Text, textBox5.Text, textBox7.Text, textBox8.Text);
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			listView2.Items[selectedIndices[0]].SubItems[0].Text = textBox6.Text;
			listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox1.Text;
			listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox2.Text;
			listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox3.Text;
			listView2.Items[selectedIndices[0]].SubItems[4].Text = textBox4.Text;
			listView2.Items[selectedIndices[0]].SubItems[5].Text = textBox5.Text;
			listView2.Items[selectedIndices[0]].SubItems[6].Text = textBox7.Text;
			listView2.Items[selectedIndices[0]].SubItems[7].Text = textBox8.Text;
			new World().强化概率参数();
			刷新();
		}
		catch
		{
			MessageBox.Show("输入错误,请检查!");
		}
	}

	private void listView2_MouseClick(object sender, MouseEventArgs e)
	{
		ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
		textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
		textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
		textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
		textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
		textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
		textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[0].Text;
		textBox7.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
		textBox8.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
	}

	private int 写数据(int 强化, string 首饰, string 火属性, string 水晶符, string 刀剑笑, string 至尊符, string 灵宠强化, string 披风强化)
	{
		string sqlCommand = string.Format("UPDATE TBL_XWWL_强化参数 SET  FLD_首饰加工='{1}', FLD_属性阶段='{2}', FLD_水晶属性阶段='{3}', FLD_NPC强化='{4}',FLD_至尊符强化='{5}',FLD_灵宠强化='{6}',FLD_披风强化='{7}' WHERE FLD_强化数={0} ", 强化, 首饰, 火属性, 水晶符, 刀剑笑, 至尊符, 灵宠强化, 披风强化);
		try
		{
			int num = DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			if (num == -1)
			{
				MessageBox.Show("写入表错误1,请检查|");
				return -1;
			}
		}
		catch
		{
			MessageBox.Show("写入表错误2,请检查|");
		}
		return -1;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(强化参数调整));
            this.button1 = new System.Windows.Forms.Button();
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(749, 267);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(111, 29);
            this.button1.TabIndex = 54;
            this.button1.Text = "修改立即生效";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14,
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(12, 12);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(674, 224);
            this.listView2.TabIndex = 55;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listView2_MouseClick);
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "强化数";
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "NPC-首饰加工";
            this.columnHeader12.Width = 84;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "NPC-属性阶段";
            this.columnHeader13.Width = 84;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "属性-水晶符";
            this.columnHeader14.Width = 84;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "NPC-窗口强化";
            this.columnHeader1.Width = 84;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "取玉-至尊取玉";
            this.columnHeader2.Width = 90;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "灵宠强化";
            this.columnHeader3.Width = 90;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "披风强化";
            this.columnHeader4.Width = 90;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(806, 54);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(66, 21);
            this.textBox1.TabIndex = 56;
            this.toolTip1.SetToolTip(this.textBox1, "对应石头属性类型");
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(716, 59);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 57;
            this.label1.Text = "NPC-首饰加工";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(716, 86);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 59;
            this.label2.Text = "NPC-属性阶段";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(806, 81);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(66, 21);
            this.textBox2.TabIndex = 58;
            this.toolTip1.SetToolTip(this.textBox2, "对应石头属性数量");
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(716, 142);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 61;
            this.label3.Text = "NPC-窗口强化";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(806, 110);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(66, 21);
            this.textBox3.TabIndex = 60;
            this.toolTip1.SetToolTip(this.textBox3, "负数减反之加");
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(806, 137);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(66, 21);
            this.textBox4.TabIndex = 62;
            this.toolTip1.SetToolTip(this.textBox4, "负数减反之加");
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(806, 165);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(66, 21);
            this.textBox5.TabIndex = 63;
            this.toolTip1.SetToolTip(this.textBox5, "负数减反之加");
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(806, 27);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(66, 21);
            this.textBox6.TabIndex = 66;
            this.toolTip1.SetToolTip(this.textBox6, "负数减反之加");
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.ForeColor = System.Drawing.Color.Red;
            this.label7.Location = new System.Drawing.Point(716, 12);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(125, 12);
            this.label7.TabIndex = 68;
            this.label7.Text = "鼠标点放这里查看类型";
            this.toolTip1.SetToolTip(this.label7, "\"首饰加工最大10\":1\r\n\"属性阶段最大10\":2\r\n\"水晶属性符最大10\":3\r\n\"窗口强化最大15\":4\r\n\"取玉符最大10\":5  \r\n\"至尊取玉符10以" +
        "上使用\"");
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(806, 195);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(66, 21);
            this.textBox7.TabIndex = 70;
            this.toolTip1.SetToolTip(this.textBox7, "负数减反之加");
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(806, 224);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(66, 21);
            this.textBox8.TabIndex = 72;
            this.toolTip1.SetToolTip(this.textBox8, "负数减反之加");
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(716, 116);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(71, 12);
            this.label4.TabIndex = 64;
            this.label4.Text = "属性-水晶符";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(716, 172);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(71, 12);
            this.label5.TabIndex = 65;
            this.label5.Text = "取玉-至尊符";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(716, 33);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 67;
            this.label6.Text = "强化数值";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(716, 200);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 69;
            this.label8.Text = "灵宠强化";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(716, 233);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 12);
            this.label9.TabIndex = 71;
            this.label9.Text = "披风强化";
            // 
            // 强化参数调整
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(927, 304);
            this.Controls.Add(this.textBox8);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.textBox7);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.textBox6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBox5);
            this.Controls.Add(this.textBox4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.button1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "强化参数调整";
            this.Text = "强化参数调整";
            this.Load += new System.EventHandler(this.强化参数调整_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

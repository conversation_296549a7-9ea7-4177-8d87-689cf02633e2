namespace RxjhServer;

public class MoveClass
{
	private int _MAP;

	private float _X;

	private float _Y;

	private float _Z;

	private int _ToMAP;

	private float _ToX;

	private float _ToY;

	private float _ToZ;

	public int MAP
	{
		get
		{
			return _MAP;
		}
		set
		{
			_MAP = value;
		}
	}

	public float X
	{
		get
		{
			return _X;
		}
		set
		{
			_X = value;
		}
	}

	public float Y
	{
		get
		{
			return _Y;
		}
		set
		{
			_Y = value;
		}
	}

	public float Z
	{
		get
		{
			return _Z;
		}
		set
		{
			_Z = value;
		}
	}

	public int ToMAP
	{
		get
		{
			return _ToMAP;
		}
		set
		{
			_ToMAP = value;
		}
	}

	public float ToX
	{
		get
		{
			return _ToX;
		}
		set
		{
			_ToX = value;
		}
	}

	public float ToY
	{
		get
		{
			return _ToY;
		}
		set
		{
			_ToY = value;
		}
	}

	public float ToZ
	{
		get
		{
			return _ToZ;
		}
		set
		{
			_ToZ = value;
		}
	}
}

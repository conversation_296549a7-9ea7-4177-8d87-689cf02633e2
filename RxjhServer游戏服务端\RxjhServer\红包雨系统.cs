using System;
using System.Timers;

namespace RxjhServer;

public class 红包雨系统 : IDisposable
{
	private System.Timers.Timer 时间1;

	private System.Timers.Timer 时间2;

	private DateTime kssj;

	private int kssjint;

	private DateTime kssj1;

	private int kssjint1;

	public 红包雨系统()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "EventClass-");
			}
			World.发送特殊公告("天降红包活动" + World.红包雨倒计时 + "分钟后开启，大家提早回泫勃派城内来吧，奖励丰富！", 6, "公告");
			kssj = DateTime.Now.AddMinutes(World.红包雨倒计时);
			时间1 = new System.Timers.Timer(10000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "红包雨系统", "初始化", "红包雨系统启动");
			Form1.WriteLine(1, "红包雨 出错：" + ex);
		}
	}

	public void 时间结束事件1(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "红包雨_时间结束事件1");
		}
		try
		{
			int num = (kssjint = (int)kssj.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.发送其他活动开始倒计时(kssjint);
			}
			if (kssjint > 0)
			{
				return;
			}
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
			World.红包雨进程 = 1;
			World.发送特殊公告("天降红包活动已开启，大家回泫勃派城内跑起来吧，奖励丰富！", 6, "公告");
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.人物坐标_地图 == 101)
				{
					value2.更新服务器时间和场景();
				}
			}
			kssj1 = DateTime.Now.AddMinutes(World.红包雨总时间);
			时间2 = new System.Timers.Timer(10000.0);
			时间2.Elapsed += 时间结束事件2;
			时间2.Enabled = true;
			时间2.AutoReset = true;
			时间结束事件2(null, null);
		}
		catch (Exception)
		{
		}
	}

	public void 时间结束事件2(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "红包雨_时间结束事件1");
		}
		try
		{
			int num = (kssjint1 = (int)kssj1.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.发送其他活动开始倒计时(num);
			}
			if (kssjint1 > 0)
			{
				return;
			}
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
			World.红包雨进程 = 0;
			World.发送特殊公告("天降红包活动已结束，期待下次天降红包活动！", 6, "公告");
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.人物坐标_地图 == 101)
				{
					value2.更新服务器时间和场景();
				}
			}
			World.红包雨.Dispose();
		}
		catch (Exception)
		{
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "EventClass-Dispose");
		}
		World.红包雨进程 = 0;
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		if (时间2 != null)
		{
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
		}
		World.红包雨 = null;
	}
}

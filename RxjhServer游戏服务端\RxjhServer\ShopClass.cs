using System.Collections.Generic;

namespace RxjhServer;

public class ShopClass
{
	private int _FLD_NID;

	private int _FLD_INDEX;

	private int _FLD_PID;

	private long _FLD_MONEY;

	private int _FLD_MAGIC0;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _武皇币;

	private int _冰魄水玉;

	private int _FLD_累计充值;

	public int FLD_NID
	{
		get
		{
			return _FLD_NID;
		}
		set
		{
			_FLD_NID = value;
		}
	}

	public int FLD_INDEX
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public long FLD_MONEY
	{
		get
		{
			return _FLD_MONEY;
		}
		set
		{
			_FLD_MONEY = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return _FLD_MAGIC0;
		}
		set
		{
			_FLD_MAGIC0 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int 武皇币
	{
		get
		{
			return _武皇币;
		}
		set
		{
			_武皇币 = value;
		}
	}

	public int 冰魄水玉
	{
		get
		{
			return _冰魄水玉;
		}
		set
		{
			_冰魄水玉 = value;
		}
	}

	public int FLD_累计充值
	{
		get
		{
			return _FLD_累计充值;
		}
		set
		{
			_FLD_累计充值 = value;
		}
	}

	public int FLD_MAGICZh
	{
		get
		{
			int num = 0;
			if (_FLD_MAGIC1 != 0)
			{
				num++;
			}
			if (_FLD_MAGIC2 != 0)
			{
				num++;
			}
			if (_FLD_MAGIC3 != 0)
			{
				num++;
			}
			if (_FLD_MAGIC4 != 0)
			{
				num++;
			}
			return num;
		}
	}

	public static List<ShopClass> GetShopListAll(int id)
	{
		List<ShopClass> list = new List<ShopClass>();
		foreach (ShopClass item in World.Shop)
		{
			if (item.FLD_NID == id)
			{
				list.Add(item);
			}
		}
		return list;
	}

	public static List<ShopClass> GetShopList(int id, int pages)
	{
		List<ShopClass> list = new List<ShopClass>();
		List<ShopClass> list2 = new List<ShopClass>();
		if (pages == 0)
		{
			int num = 0;
			foreach (ShopClass item in World.Shop)
			{
				num++;
				if (item.FLD_NID == id)
				{
					list.Add(item);
				}
				if (list.Count == 60)
				{
					break;
				}
			}
			return list;
		}
		foreach (ShopClass item2 in World.Shop)
		{
			if (item2.FLD_NID == id)
			{
				list.Add(item2);
			}
		}
		for (int i = pages * 60; i < list.Count; i++)
		{
			list2.Add(list[i]);
			if (list2.Count == 60)
			{
				break;
			}
		}
		return list2;
	}

	public static ShopClass Getwp(int NPCID, int id)
	{
		foreach (ShopClass item in World.Shop)
		{
			if (item.FLD_NID == NPCID && item.FLD_PID == id)
			{
				return item;
			}
		}
		return null;
	}
}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace RxjhServer
{
    // 2025-0619 EVIAS 怪物数据缓存管理器
    public class MonsterCacheManager
    {
        private static readonly Lazy<MonsterCacheManager> _instance = new(() => new MonsterCacheManager());
        public static MonsterCacheManager Instance => _instance.Value;

        // 热点数据缓存 - 缓存怪物模板数据
        private readonly ConcurrentDictionary<int, MonSterClss> _hotMonsters = new();
        private readonly ConcurrentDictionary<int, DateTime> _accessTimes = new();

        // PID索引 - 按怪物PID分类
        private readonly ConcurrentDictionary<int, HashSet<int>> _monstersByPID = new();

        // 等级范围索引
        private readonly ConcurrentDictionary<int, HashSet<int>> _monstersByLevel = new();

        // BOSS怪物索引
        private readonly ConcurrentDictionary<int, MonSterClss> _bossMonsters = new();

        // 缓存统计
        private long _cacheHits = 0;
        private long _cacheMisses = 0;
        private long _totalAccess = 0;

        // 缓存配置
        private readonly int _maxHotCacheSize = 500; // 最大热点缓存数量
        private readonly TimeSpan _hotCacheExpiry = TimeSpan.FromMinutes(10); // 热点缓存过期时间

        private MonsterCacheManager() { }

        // 获取怪物数据（带缓存）
        public MonSterClss GetMonster(int monsterId)
        {
            Interlocked.Increment(ref _totalAccess);

            // 先从热点缓存查找
            if (_hotMonsters.TryGetValue(monsterId, out var cachedMonster))
            {
                Interlocked.Increment(ref _cacheHits);
                _accessTimes[monsterId] = DateTime.Now; // 更新访问时间
                return cachedMonster;
            }

            // 从主集合查找
            if (World.MonSter.TryGetValue(monsterId, out var monster))
            {
                Interlocked.Increment(ref _cacheMisses);
                
                // 添加到热点缓存
                AddToHotCache(monsterId, monster);
                return monster;
            }

            return null;
        }

        // 添加到热点缓存
        private void AddToHotCache(int monsterId, MonSterClss monster)
        {
            try
            {
                // 检查缓存大小限制
                if (_hotMonsters.Count >= _maxHotCacheSize)
                {
                    CleanupExpiredCache();
                }

                _hotMonsters.TryAdd(monsterId, monster);
                _accessTimes.TryAdd(monsterId, DateTime.Now);

                // 更新索引
                UpdateIndexes(monster);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("添加热点缓存失败: ", monsterId, " - ", ex.Message));
            }
        }

        // 更新索引
        private void UpdateIndexes(MonSterClss monster)
        {
            try
            {
                // PID索引 - 按怪物PID分类
                var pidMonsters = _monstersByPID.GetOrAdd(monster.FLD_PID, _ => new HashSet<int>());
                lock (pidMonsters)
                {
                    pidMonsters.Add(monster.FLD_PID);
                }

                // 等级索引
                var levelMonsters = _monstersByLevel.GetOrAdd(monster.Level, _ => new HashSet<int>());
                lock (levelMonsters)
                {
                    levelMonsters.Add(monster.FLD_PID);
                }

                // BOSS索引
                if (monster.FLD_BOSS > 0)
                {
                    _bossMonsters.TryAdd(monster.FLD_PID, monster);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("更新怪物索引失败: ", monster.FLD_PID, " - ", ex.Message));
            }
        }

        // 清理过期缓存
        public void CleanupExpiredCache()
        {
            try
            {
                var now = DateTime.Now;
                var expiredIds = _accessTimes
                    .Where(kvp => now.Subtract(kvp.Value) > _hotCacheExpiry)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var id in expiredIds)
                {
                    _hotMonsters.TryRemove(id, out _);
                    _accessTimes.TryRemove(id, out _);
                }

                if (expiredIds.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("清理过期怪物缓存: ", expiredIds.Count, "个"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理过期缓存失败: ", ex.Message));
            }
        }

        // 获取指定PID的怪物模板
        public MonSterClss GetMonsterTemplate(int pid)
        {
            return GetMonster(pid);
        }

        // 获取地图内的怪物实例（通过MapClass）
        public IEnumerable<NpcClass> GetMapMonsterInstances(int mapId)
        {
            try
            {
                if (World.Map.TryGetValue(mapId, out var mapClass))
                {
                    return mapClass.npcTemplate.Values;
                }
                return Enumerable.Empty<NpcClass>();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取地图怪物实例失败: ", mapId, " - ", ex.Message));
                return Enumerable.Empty<NpcClass>();
            }
        }

        // 获取等级范围内的怪物
        public IEnumerable<MonSterClss> GetMonstersByLevelRange(int minLevel, int maxLevel)
        {
            try
            {
                var monsters = new List<MonSterClss>();
                
                for (int level = minLevel; level <= maxLevel; level++)
                {
                    if (_monstersByLevel.TryGetValue(level, out var monsterIds))
                    {
                        lock (monsterIds)
                        {
                            foreach (var id in monsterIds)
                            {
                                var monster = GetMonster(id);
                                if (monster != null)
                                {
                                    monsters.Add(monster);
                                }
                            }
                        }
                    }
                }
                
                return monsters;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取等级范围怪物失败: ", minLevel, "-", maxLevel, " - ", ex.Message));
                return Enumerable.Empty<MonSterClss>();
            }
        }

        // 获取所有BOSS怪物
        public IEnumerable<MonSterClss> GetBossMonsters()
        {
            return _bossMonsters.Values;
        }

        // 查找最近的怪物实例
        public NpcClass FindNearestMonsterInstance(int mapId, float x, float y, float maxDistance = 500f)
        {
            try
            {
                var mapMonsters = GetMapMonsterInstances(mapId);
                NpcClass nearestMonster = null;
                float nearestDistance = float.MaxValue;

                foreach (var monster in mapMonsters)
                {
                    float distance = CalculateDistance(x, y, monster.Rxjh_X, monster.Rxjh_Y);
                    if (distance <= maxDistance && distance < nearestDistance)
                    {
                        nearestDistance = distance;
                        nearestMonster = monster;
                    }
                }

                return nearestMonster;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("查找最近怪物失败: ", ex.Message));
                return null;
            }
        }

        // 2025-0619 EVIAS 手动添加指定怪物到缓存
        public bool AddMonsterToCache(int monsterId, string reason = "手动添加")
        {
            try
            {
                if (World.MonSter.TryGetValue(monsterId, out var monster))
                {
                    AddToHotCache(monsterId, monster);
                    Form1.WriteLine(6, FastString.Concat("已添加怪物到缓存: ", monster.Name, "(", monsterId, ") - ", reason));
                    return true;
                }
                else
                {
                    Form1.WriteLine(1, FastString.Concat("怪物ID不存在: ", monsterId));
                    return false;
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("添加怪物到缓存失败: ", monsterId, " - ", ex.Message));
                return false;
            }
        }

        // 2025-0619 EVIAS 批量添加指定怪物到缓存
        public int AddMonstersToCache(int[] monsterIds, string reason = "批量添加")
        {
            int successCount = 0;
            foreach (int monsterId in monsterIds)
            {
                if (AddMonsterToCache(monsterId, reason))
                {
                    successCount++;
                }
            }
            Form1.WriteLine(6, FastString.Concat("批量添加完成: ", successCount, "/", monsterIds.Length, " - ", reason));
            return successCount;
        }

        // 2025-0619 EVIAS 从缓存中移除指定怪物
        public bool RemoveMonsterFromCache(int monsterId)
        {
            try
            {
                bool removed1 = _hotMonsters.TryRemove(monsterId, out var monster);
                bool removed2 = _accessTimes.TryRemove(monsterId, out _);

                if (removed1 || removed2)
                {
                    string monsterName = monster?.Name ?? monsterId.ToString();
                    Form1.WriteLine(6, FastString.Concat("已从缓存移除怪物: ", monsterName, "(", monsterId, ")"));
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("移除怪物缓存失败: ", monsterId, " - ", ex.Message));
                return false;
            }
        }

        // 预加载热点怪物数据（保留原有功能）
        public void PreloadHotMonsters()
        {
            try
            {
                // 2025-0619 EVIAS 改为可配置的预加载策略
                PreloadDefaultMonsters();
                Form1.WriteLine(6, "怪物热点数据预加载完成");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("预加载热点怪物失败: ", ex.Message));
            }
        }

        // 2025-0619 EVIAS 默认预加载策略
        private void PreloadDefaultMonsters()
        {
            // 只预加载真正的BOSS怪物（可以根据需要调整）
            var bossMonsters = World.MonSter.Values.Where(m => m.FLD_BOSS > 0).Take(50);
            foreach (var monster in bossMonsters)
            {
                AddToHotCache(monster.FLD_PID, monster);
            }
        }

        // 获取缓存统计信息
        public string GetCacheStats()
        {
            double hitRate = _totalAccess > 0 ? (double)_cacheHits / _totalAccess * 100 : 0;
            
            return FastString.Format(
                "怪物缓存: 热点:{0} 命中率:{1:F1}% 访问:{2} 命中:{3} 未命中:{4} PID索引:{5} 等级索引:{6} BOSS:{7}",
                _hotMonsters.Count,
                hitRate,
                _totalAccess,
                _cacheHits,
                _cacheMisses,
                _monstersByPID.Count,
                _monstersByLevel.Count,
                _bossMonsters.Count
            );
        }

        // 获取热点怪物排行
        public string GetHotMonstersRanking()
        {
            try
            {
                var hotMonsters = _accessTimes
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .Select(kvp => 
                    {
                        var monster = GetMonster(kvp.Key);
                        return monster != null ? FastString.Concat(monster.Name, "(", kvp.Key, ")") : kvp.Key.ToString();
                    })
                    .ToArray();

                if (hotMonsters.Length == 0)
                {
                    return "暂无热点怪物数据";
                }

                return FastString.Concat("热点怪物(前10): ", string.Join(" | ", hotMonsters));
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取热点怪物排行失败: ", ex.Message));
                return "排行获取失败";
            }
        }

        // 清理所有缓存
        public void ClearAllCache()
        {
            try
            {
                _hotMonsters.Clear();
                _accessTimes.Clear();
                _monstersByPID.Clear();
                _monstersByLevel.Clear();
                _bossMonsters.Clear();
                
                // 重置统计
                _cacheHits = 0;
                _cacheMisses = 0;
                _totalAccess = 0;
                
                Form1.WriteLine(6, "怪物缓存已清空");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清空怪物缓存失败: ", ex.Message));
            }
        }

        // 计算距离
        private float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            float dx = x1 - x2;
            float dy = y1 - y2;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }

        // 检查是否需要清理缓存
        public bool ShouldCleanupCache()
        {
            return _hotMonsters.Count > _maxHotCacheSize * 0.8; // 达到80%容量时清理
        }
    }
}

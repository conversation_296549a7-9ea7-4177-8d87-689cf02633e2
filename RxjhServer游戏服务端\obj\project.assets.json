{"version": 3, "targets": {".NETFramework,Version=v4.8": {"FreeSql/3.5.202": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.SqlServerForSystem/3.5.202": {"type": "package", "dependencies": {"FreeSql": "3.5.202", "System.Data.SqlClient": "4.8.6"}, "compile": {"lib/net451/FreeSql.Provider.SqlServerForSystem.dll": {"related": ".pdb"}}, "runtime": {"lib/net451/FreeSql.Provider.SqlServerForSystem.dll": {"related": ".pdb"}}}, "HPSocket.Net/6.0.3.1": {"type": "package", "compile": {"lib/net48/HPSocket.Net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net48/HPSocket.Net.dll": {"related": ".pdb;.xml"}}, "build": {"build/HPSocket.Net.targets": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libhpsocket4c.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libhpsocket4c.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-loongarch64/native/libhpsocket4c.so": {"assetType": "native", "rid": "linux-loongarch64"}, "runtimes/linux-x64/native/libhpsocket4c.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libhpsocket4c.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/osx-arm64/native/libhpsocket4c.so": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libhpsocket4c.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/HPSocket4C.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/HPSocket4C.dll": {"assetType": "native", "rid": "win-x86"}}}, "KeraLua/1.4.4": {"type": "package", "compile": {"lib/net46/KeraLua.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/KeraLua.dll": {"related": ".pdb;.xml"}}, "build": {"build/net46/KeraLua.targets": {}}, "runtimeTargets": {"runtimes/android-arm/native/liblua54.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/liblua54.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/liblua54.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/liblua54.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/ios/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "ios"}, "runtimes/linux-arm64/native/liblua54.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/liblua54.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "maccatalyst"}, "runtimes/maccatalyst/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "maccatalyst"}, "runtimes/osx/native/liblua54.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/tvos/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "tvos"}, "runtimes/tvos/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "tvos"}, "runtimes/win-arm/native/lua54.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/lua54.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/lua54.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/lua54.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NLua/1.7.5": {"type": "package", "dependencies": {"KeraLua": "1.4.4"}, "compile": {"lib/net46/NLua.dll": {"related": ".pdb"}}, "runtime": {"lib/net46/NLua.dll": {"related": ".pdb"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Collections.Immutable/9.0.6": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "frameworkAssemblies": ["System.Data", "System.Xml", "mscorlib"], "compile": {"ref/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net461/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Nrbf/9.0.6": {"type": "package", "dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "System.Reflection.Metadata": "9.0.6", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/9.0.6": {"type": "package", "dependencies": {"System.Collections.Immutable": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Resources.Extensions/9.0.6": {"type": "package", "dependencies": {"System.Formats.Nrbf": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Resources.Extensions.targets": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.8/win-x86": {"FreeSql/3.5.202": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.SqlServerForSystem/3.5.202": {"type": "package", "dependencies": {"FreeSql": "3.5.202", "System.Data.SqlClient": "4.8.6"}, "compile": {"lib/net451/FreeSql.Provider.SqlServerForSystem.dll": {"related": ".pdb"}}, "runtime": {"lib/net451/FreeSql.Provider.SqlServerForSystem.dll": {"related": ".pdb"}}}, "HPSocket.Net/6.0.3.1": {"type": "package", "compile": {"lib/net48/HPSocket.Net.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net48/HPSocket.Net.dll": {"related": ".pdb;.xml"}}, "native": {"runtimes/win-x86/native/HPSocket4C.dll": {}}, "build": {"build/HPSocket.Net.targets": {}}}, "KeraLua/1.4.4": {"type": "package", "compile": {"lib/net46/KeraLua.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/KeraLua.dll": {"related": ".pdb;.xml"}}, "native": {"runtimes/win-x86/native/lua54.dll": {}}, "build": {"build/net46/KeraLua.targets": {}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NLua/1.7.5": {"type": "package", "dependencies": {"KeraLua": "1.4.4"}, "compile": {"lib/net46/NLua.dll": {"related": ".pdb"}}, "runtime": {"lib/net46/NLua.dll": {"related": ".pdb"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Collections.Immutable/9.0.6": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "frameworkAssemblies": ["System.Data", "System.Xml", "mscorlib"], "compile": {"ref/net461/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Data.SqlClient.dll": {"related": ".xml"}}}, "System.Formats.Nrbf/9.0.6": {"type": "package", "dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "System.Reflection.Metadata": "9.0.6", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/9.0.6": {"type": "package", "dependencies": {"System.Collections.Immutable": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Resources.Extensions/9.0.6": {"type": "package", "dependencies": {"System.Formats.Nrbf": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Resources.Extensions.targets": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}}}, "libraries": {"FreeSql/3.5.202": {"sha512": "ZlQjx+01HWIyvX72d0EwwodHRRI08N5ejiTa9zG66/mBTGSXOIVGpNCW4KA3WS9AO3FlDxDE2JRcQQ1uei8PYA==", "type": "package", "path": "freesql/3.5.202", "files": [".nupkg.metadata", ".signature.p7s", "freesql.3.5.202.nupkg.sha512", "freesql.nuspec", "lib/net40/FreeSql.dll", "lib/net40/FreeSql.pdb", "lib/net40/FreeSql.xml", "lib/net45/FreeSql.dll", "lib/net45/FreeSql.pdb", "lib/net45/FreeSql.xml", "lib/net451/FreeSql.dll", "lib/net451/FreeSql.pdb", "lib/net451/FreeSql.xml", "lib/netstandard2.0/FreeSql.dll", "lib/netstandard2.0/FreeSql.pdb", "lib/netstandard2.0/FreeSql.xml", "lib/netstandard2.1/FreeSql.dll", "lib/netstandard2.1/FreeSql.pdb", "lib/netstandard2.1/FreeSql.xml", "logo.png", "readme.md"]}, "FreeSql.Provider.SqlServerForSystem/3.5.202": {"sha512": "a4+hTTqZOERahnRWTMsvIMNftzvBFVgONa1QBPKbMwHV1uhXYvSGUKhx17d6gNnr44bbL72J/8ZcHkuPU524Og==", "type": "package", "path": "freesql.provider.sqlserverforsystem/3.5.202", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.sqlserverforsystem.3.5.202.nupkg.sha512", "freesql.provider.sqlserverforsystem.nuspec", "lib/net40/FreeSql.Provider.SqlServerForSystem.dll", "lib/net40/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net45/FreeSql.Provider.SqlServerForSystem.dll", "lib/net45/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net451/FreeSql.Provider.SqlServerForSystem.dll", "lib/net451/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net6.0/FreeSql.Provider.SqlServerForSystem.dll", "lib/net6.0/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net7.0/FreeSql.Provider.SqlServerForSystem.dll", "lib/net7.0/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net8.0/FreeSql.Provider.SqlServerForSystem.dll", "lib/net8.0/FreeSql.Provider.SqlServerForSystem.pdb", "lib/net9.0/FreeSql.Provider.SqlServerForSystem.dll", "lib/net9.0/FreeSql.Provider.SqlServerForSystem.pdb", "lib/netstandard2.0/FreeSql.Provider.SqlServerForSystem.dll", "lib/netstandard2.0/FreeSql.Provider.SqlServerForSystem.pdb", "lib/netstandard2.1/FreeSql.Provider.SqlServerForSystem.dll", "lib/netstandard2.1/FreeSql.Provider.SqlServerForSystem.pdb", "logo.png", "readme.md"]}, "HPSocket.Net/6.0.3.1": {"sha512": "rVwZtuAcA+SZKOrvN5MxSwkmmdb3jibCB+LBv6cXKoVqn5zwZhK3VchxyCebaI9uo2SKvWAgHqh+Rxnj9wPbAA==", "type": "package", "path": "hpsocket.net/6.0.3.1", "files": [".nupkg.metadata", ".signature.p7s", "build/HPSocket.Net.targets", "content/HPSocket/hpsocket_cn.txt", "content/HPSocket/hpsocket_en.txt", "hpsocket.net.6.0.3.1.nupkg.sha512", "hpsocket.net.nuspec", "lib/net20/HPSocket.Net.dll", "lib/net20/HPSocket.Net.pdb", "lib/net20/HPSocket.Net.xml", "lib/net40/HPSocket.Net.dll", "lib/net40/HPSocket.Net.pdb", "lib/net40/HPSocket.Net.xml", "lib/net48/HPSocket.Net.dll", "lib/net48/HPSocket.Net.pdb", "lib/net48/HPSocket.Net.xml", "lib/net6.0/HPSocket.Net.deps.json", "lib/net6.0/HPSocket.Net.dll", "lib/net6.0/HPSocket.Net.pdb", "lib/net6.0/HPSocket.Net.xml", "lib/net7.0/HPSocket.Net.deps.json", "lib/net7.0/HPSocket.Net.dll", "lib/net7.0/HPSocket.Net.pdb", "lib/net7.0/HPSocket.Net.xml", "lib/net8.0/HPSocket.Net.deps.json", "lib/net8.0/HPSocket.Net.dll", "lib/net8.0/HPSocket.Net.pdb", "lib/net8.0/HPSocket.Net.xml", "lib/net9.0/HPSocket.Net.deps.json", "lib/net9.0/HPSocket.Net.dll", "lib/net9.0/HPSocket.Net.pdb", "lib/net9.0/HPSocket.Net.xml", "lib/netstandard2.1/HPSocket.Net.deps.json", "lib/netstandard2.1/HPSocket.Net.dll", "lib/netstandard2.1/HPSocket.Net.pdb", "lib/netstandard2.1/HPSocket.Net.xml", "runtimes/linux-arm/native/libhpsocket4c.so", "runtimes/linux-arm64/native/libhpsocket4c.so", "runtimes/linux-loongarch64/native/libhpsocket4c.so", "runtimes/linux-x64/native/libhpsocket4c.so", "runtimes/linux-x86/native/libhpsocket4c.so", "runtimes/osx-arm64/native/libhpsocket4c.so", "runtimes/osx-x64/native/libhpsocket4c.dylib", "runtimes/win-x64/native/HPSocket4C.dll", "runtimes/win-x86/native/HPSocket4C.dll"]}, "KeraLua/1.4.4": {"sha512": "JsTqvTtHP9DzBK0EigywkQ/EwOHBGgBrk+vtgIqSKHWFGKkR1uIXmt8woDOG8cnprg/8uhS5YRmdCjjlyd15WQ==", "type": "package", "path": "keralua/1.4.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/net46/KeraLua.targets", "keralua.1.4.4.nupkg.sha512", "keralua.nuspec", "lib/MonoAndroid/KeraLua.dll", "lib/MonoAndroid/KeraLua.pdb", "lib/MonoAndroid/KeraLua.xml", "lib/net46/KeraLua.dll", "lib/net46/KeraLua.pdb", "lib/net46/KeraLua.xml", "lib/net8.0-android28/KeraLua.dll", "lib/net8.0-android28/KeraLua.pdb", "lib/net8.0-android28/KeraLua.xml", "lib/net8.0-ios11.0/KeraLua.dll", "lib/net8.0-ios11.0/KeraLua.pdb", "lib/net8.0-ios11.0/KeraLua.xml", "lib/net8.0-maccatalyst13.0/KeraLua.dll", "lib/net8.0-maccatalyst13.0/KeraLua.pdb", "lib/net8.0-maccatalyst13.0/KeraLua.xml", "lib/net8.0-macos10.13/KeraLua.dll", "lib/net8.0-macos10.13/KeraLua.pdb", "lib/net8.0-macos10.13/KeraLua.xml", "lib/net8.0-tvos11.0/KeraLua.dll", "lib/net8.0-tvos11.0/KeraLua.pdb", "lib/net8.0-tvos11.0/KeraLua.xml", "lib/net8.0/KeraLua.dll", "lib/net8.0/KeraLua.pdb", "lib/net8.0/KeraLua.xml", "lib/netstandard2.0/KeraLua.dll", "lib/netstandard2.0/KeraLua.pdb", "lib/netstandard2.0/KeraLua.xml", "lib/uap10.0/KeraLua.dll", "lib/uap10.0/KeraLua.pdb", "lib/uap10.0/KeraLua.pri", "lib/uap10.0/KeraLua.xml", "lib/xamarinios/KeraLua.dll", "lib/xamarinios/KeraLua.pdb", "lib/xamarinios/KeraLua.xml", "lib/xamarinmac/KeraLua.dll", "lib/xamarinmac/KeraLua.pdb", "lib/xamarinmac/KeraLua.xml", "lib/xamarintvos/KeraLua.dll", "lib/xamarintvos/KeraLua.pdb", "lib/xamarintvos/KeraLua.xml", "runtimes/android-arm/native/liblua54.so", "runtimes/android-arm64/native/liblua54.so", "runtimes/android-x64/native/liblua54.so", "runtimes/android-x86/native/liblua54.so", "runtimes/ios/native/liblua54.framework/Info.plist", "runtimes/ios/native/liblua54.framework/liblua54", "runtimes/linux-arm64/native/liblua54.so", "runtimes/linux-x64/native/liblua54.so", "runtimes/maccatalyst/native/liblua54.framework/Info.plist", "runtimes/maccatalyst/native/liblua54.framework/liblua54", "runtimes/osx/native/liblua54.dylib", "runtimes/tvos/native/liblua54.framework/Info.plist", "runtimes/tvos/native/liblua54.framework/liblua54", "runtimes/win-arm/native/lua54.dll", "runtimes/win-arm64/native/lua54.dll", "runtimes/win-x64/native/lua54.dll", "runtimes/win-x86/native/lua54.dll", "runtimes/win10-arm/nativeassets/uap10.0/lua54.dll", "runtimes/win10-arm64/nativeassets/uap10.0/lua54.dll", "runtimes/win10-x64/nativeassets/uap10.0/lua54.dll", "runtimes/win10-x86/nativeassets/uap10.0/lua54.dll"]}, "Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NLua/1.7.5": {"sha512": "ACDMZiuJxQWw3rMeP7nPIIOid5fv2Bp66XKr7q1+vJdfcr9AZ8NRqoKOAf0ZsZGBUODEZhEbwliuwgo0T0s2XA==", "type": "package", "path": "nlua/1.7.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/MonoAndroid/NLua.dll", "lib/MonoAndroid/NLua.pdb", "lib/net46/NLua.dll", "lib/net46/NLua.pdb", "lib/net8.0-android28/NLua.dll", "lib/net8.0-android28/NLua.pdb", "lib/net8.0-ios11.0/NLua.dll", "lib/net8.0-ios11.0/NLua.pdb", "lib/net8.0-maccatalyst13.0/NLua.dll", "lib/net8.0-maccatalyst13.0/NLua.pdb", "lib/net8.0-macos10.13/NLua.dll", "lib/net8.0-macos10.13/NLua.pdb", "lib/net8.0-tvos11.0/NLua.dll", "lib/net8.0-tvos11.0/NLua.pdb", "lib/net8.0/NLua.dll", "lib/net8.0/NLua.pdb", "lib/netstandard2.0/NLua.dll", "lib/netstandard2.0/NLua.pdb", "lib/uap10.0/NLua.dll", "lib/uap10.0/NLua.pdb", "lib/uap10.0/NLua.pri", "lib/xamarinios/NLua.dll", "lib/xamarinios/NLua.pdb", "lib/xamarinmac/NLua.dll", "lib/xamarinmac/NLua.pdb", "lib/xamarintvos/NLua.dll", "lib/xamarintvos/NLua.pdb", "nlua.1.7.5.nupkg.sha512", "nlua.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/9.0.6": {"sha512": "lqvXJBZ9KtKKffQt9PgHj5aCdm8HGpPOZQgN3sHRN8j2Yjh+KANkdomhv8QybsBk3/YDDG83OdIWcsfQfdRglw==", "type": "package", "path": "system.collections.immutable/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/net9.0/System.Collections.Immutable.dll", "lib/net9.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.9.0.6.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Formats.Nrbf/9.0.6": {"sha512": "wbvE4b1C6R7rrx4dT9nMe8N/ezvZe2/v+pTaVYT2lPyzTNpoXxXQ45dN307W08+BW7wZ+OBCLnTWKrurYJgHOg==", "type": "package", "path": "system.formats.nrbf/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Nrbf.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Nrbf.targets", "lib/net462/System.Formats.Nrbf.dll", "lib/net462/System.Formats.Nrbf.xml", "lib/net8.0/System.Formats.Nrbf.dll", "lib/net8.0/System.Formats.Nrbf.xml", "lib/net9.0/System.Formats.Nrbf.dll", "lib/net9.0/System.Formats.Nrbf.xml", "lib/netstandard2.0/System.Formats.Nrbf.dll", "lib/netstandard2.0/System.Formats.Nrbf.xml", "system.formats.nrbf.9.0.6.nupkg.sha512", "system.formats.nrbf.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/9.0.6": {"sha512": "arbJ84A9lkaJMVUc3KCaG29Au8dfK6JceDMB86itqfAfXcSFrFXK1CTiKqfiJHdCz2SydEU6hPsUAewwOap69Q==", "type": "package", "path": "system.reflection.metadata/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Reflection.Metadata.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "lib/net462/System.Reflection.Metadata.dll", "lib/net462/System.Reflection.Metadata.xml", "lib/net8.0/System.Reflection.Metadata.dll", "lib/net8.0/System.Reflection.Metadata.xml", "lib/net9.0/System.Reflection.Metadata.dll", "lib/net9.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.9.0.6.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Resources.Extensions/9.0.6": {"sha512": "415DTH0E+btC8Y0/H7KEQ/c63kYxY7bFTlRsOcz+u8zZOZZk2urhTF2za/aEDPRK2rnNaZZYhaww+vwvGzEp+Q==", "type": "package", "path": "system.resources.extensions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Resources.Extensions.targets", "buildTransitive/net462/System.Resources.Extensions.targets", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Resources.Extensions.targets", "lib/net462/System.Resources.Extensions.dll", "lib/net462/System.Resources.Extensions.xml", "lib/net8.0/System.Resources.Extensions.dll", "lib/net8.0/System.Resources.Extensions.xml", "lib/net9.0/System.Resources.Extensions.dll", "lib/net9.0/System.Resources.Extensions.xml", "lib/netstandard2.0/System.Resources.Extensions.dll", "lib/netstandard2.0/System.Resources.Extensions.xml", "system.resources.extensions.9.0.6.nupkg.sha512", "system.resources.extensions.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["FreeSql.Provider.SqlServerForSystem >= 3.5.202", "HPSocket.Net >= 6.0.3.1", "KeraLua >= 1.4.4", "NLua >= 1.7.5", "Newtonsoft.Json >= 13.0.3", "System.Resources.Extensions >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj", "projectName": "RxjhServer", "projectPath": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"FreeSql.Provider.SqlServerForSystem": {"target": "Package", "version": "[3.5.202, )"}, "HPSocket.Net": {"target": "Package", "version": "[6.0.3.1, )"}, "KeraLua": {"target": "Package", "version": "[1.4.4, )"}, "NLua": {"target": "Package", "version": "[1.7.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Resources.Extensions": {"target": "Package", "version": "[9.0.6, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}
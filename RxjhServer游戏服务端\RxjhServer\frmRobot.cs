using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using RxjhServer.DbClss;
using RxjhServer.Network;
using System.Threading;

namespace RxjhServer;

public class frmRobot : Form
{
	private List<string> waitLogins = new List<string>();

	private Listener listener;

	private UserNameGene NameGene;

	private static List<string> _names = new List<string>();

	private Button btnSetFirsrNames;

	private GroupBox groupBox5;

	private TextBox txtInitLv;

	private Label label7;

	private TextBox txtloginSum;

	private RadioButton tbSelect;

	private RadioButton rbSetSum;

	private Button btnLogin;

	private RadioButton rbAll;

	private Label lblMsg;
	
	// 添加状态栏控件
	private StatusStrip statusStrip;
	private ToolStripStatusLabel statusLabel;

	private GroupBox groupBox3;

	private LinkLabel lbRefresh;

	private ListBox lbCanUseUid;

	private TextBox txtFirstNamePath;

	private GroupBox groupBox2;

	private GroupBox groupBox7;

	private Label label4;

	private GroupBox groupBox1;

	private TextBox txtRegidEnd;

	private Label label5;

	private Button btnReg;

	private TextBox txtRegSum;

	private Label label2;

	private TextBox txtRegidStart;

	private Label label1;

	private TextBox txtFQ;

	private Label label3;

	private GroupBox groupBox4;

	private RadioButton rbGJ;

	private RadioButton rbKD;

	private RadioButton rbGjie;

	private RadioButton radioButton1;

	private TextBox textBox1;

	private Label label6;

	private GroupBox groupBox6;

	private TextBox textBox2;

	private Label label8;

	private TextBox textBox3;

	private Label label9;

	private Button button16;

	private Button button2;

	private Label label13;

	private Button button13;

	private TextBox textBox4;

	private Label label12;

	private RadioButton radioButton2;

	private CancellationTokenSource _cts;

	private Button btnCancel;

	private Button btnKickAll;
	private Button btnDeleteAll;
	private System.Windows.Forms.Timer timer;

	public frmRobot(Listener l)
	{
		InitializeComponent();
		listener = l;
		
		// 确保状态栏初始化
		CreateStatusBar();
		
		// 初始化定时器
		timer = new System.Windows.Forms.Timer();
		timer.Interval = 1000; // 1秒更新一次
		timer.Tick += new EventHandler(timer_Tick);
		timer.Start();
		
		if (World.假人加入门派 == 1)
		{
			button2.Text = "加入门派目前 开启";
		}
		if (World.假人加入门派 == 0)
		{
			button2.Text = "加入门派目前 关闭";
		}
		if (World.假人自动结婚 == 0)
		{
			button16.Text = "结婚系统目前 关闭";
		}
		if (World.假人自动结婚 == 1)
		{
			button16.Text = "结婚系统目前 开启";
		}
	}

	private void btnReg_Click(object sender, EventArgs e)
	{
		Task.Factory.StartNew(Reg);
	}

	private void Reg()
	{
		Invoke((Action)delegate
		{
			int num = 0;
			int num2 = 0;
			int num3 = int.Parse(txtRegSum.Text);
			for (int i = 1; i <= num3; i++)
			{
				ShowMsg($"正在注册第：{i}个账号");
				string text = $"{txtRegidStart.Text}{i}{txtRegidEnd.Text}";
				int num4 = i % 2 + 1;
				string sqlCommand = "select * from [TBL_ACCOUNT] where FLD_ID = '" + text + "'";
				DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
				if (dBToDataTable != null && dBToDataTable.Rows.Count == 0)
				{
					sqlCommand = "INSERT [TBL_ACCOUNT] (FLD_ID,FLD_PASSWORD,FLD_CARD,FLD_NAME,FLD_QU,FLD_ANSWER,FLD_Mail,FLD_SEX,FLD_VIP,FLD_RXPIONT,FLD_REGIP,FLD_FQ,FLD_ONLINE,FLD_SAFEWORD,是否假人) " + $"VALUES ('{text}','{txtFQ.Text}','700000000000000000','热血江湖','是否机器人','机器人','<EMAIL>',{num4},0,0,'127.0.0.1','{World.分区编号}',0,'********'," + textBox1.Text + ")";
					if (DBA.ExeSqlCommand(sqlCommand, "rxjhaccount") > 0)
					{
						num++;
					}
					else
					{
						num2++;
					}
				}
			}
			ShowMsg($"注册账号完毕，成功：{num}个，失败{num2}个");
			InitData();
		});
	}

	private void ShowMsg(string msg)
	{
		try
		{
			if (this.InvokeRequired)
			{
				this.Invoke(new Action<string>(ShowMsg), msg);
				return;
			}

			if (statusStrip != null && statusLabel != null)
			{
				statusLabel.Text = msg;

				// 这些步骤确保状态栏立即更新
				statusStrip.Update();
				statusStrip.Refresh();
				Application.DoEvents();

				// 2025-0619 EVIAS 清理调试代码
			}
			else
			{
				// 2025-0619 EVIAS 清理调试代码，使用统一日志系统
				Form1.WriteLine(6, $"状态栏不可用，消息: {msg}");

				// 尝试创建状态栏
				CreateStatusBar();
			}
		}
		catch (Exception ex)
		{
			// 2025-0619 EVIAS 清理调试代码，使用统一日志系统
			Form1.WriteLine(1, $"显示状态栏消息出错: {ex.Message}");
		}
	}

	private void InitData()
	{
		lbCanUseUid.Items.Clear();
		string sqlCommand = "select * from [TBL_ACCOUNT] where 是否假人=1 and 账号是否在线 != 1";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
		if (dBToDataTable == null || dBToDataTable.Rows.Count <= 0)
		{
			return;
		}
		foreach (DataRow row in dBToDataTable.Rows)
		{
			lbCanUseUid.Items.Add(row["FLD_ID"]);
		}
	}

	private async void btnLogin_Click(object sender, EventArgs e)
	{
		waitLogins.Clear();
		if (tbSelect.Checked)
		{
			if (lbCanUseUid.SelectedItems.Count > 0)
			{
				foreach (object item in lbCanUseUid.SelectedItems)
				{
					waitLogins.Add(item.ToString());
				}
			}
			else
			{
				MessageBox.Show("请先选中需要登录的账号");
				return;
			}
		}
		if (rbSetSum.Checked)
		{
			if (lbCanUseUid.Items.Count < int.Parse(txtloginSum.Text))
			{
				MessageBox.Show($"可用账号数量不足，仅有{lbCanUseUid.Items.Count}个账号");
				return;
			}
			
			for (int i = 0; i < int.Parse(txtloginSum.Text); i++)
			{
				waitLogins.Add(lbCanUseUid.Items[i].ToString());
			}
		}
		if (rbAll.Checked)
		{
			foreach (object item in lbCanUseUid.Items)
			{
				waitLogins.Add(item.ToString());
			}
		}
		
		if (waitLogins.Count > 0)
		{
			btnLogin.Enabled = false;
			btnCancel.Enabled = true;
			ShowMsg("开始登录，请稍候...");
			
			_cts = new CancellationTokenSource();
			try 
			{
				Task<int> task = LoginAsync(waitLogins.ToArray(), _cts.Token);
				int sum = await task;
				await Task.Delay(500);
				RefreshAccountList();
				ShowMsg($"共登录：{sum}个假人");
			}
			catch (OperationCanceledException)
			{
				ShowMsg("登录操作已取消");
				RefreshAccountList();
			}
			finally
			{
				btnLogin.Enabled = true;
				btnCancel.Enabled = false;
				_cts = null;
			}
		}
	}

	private async Task<int> LoginAsync(string[] uids, CancellationToken token)
	{
		int initLv = int.Parse(txtInitLv.Text);
		int sum = 0;
		int current = 0;
		int total = uids.Length;
		
		return await Task.Run(() =>
		{
			foreach (string uid in uids)
			{
				// 检查是否请求取消
				token.ThrowIfCancellationRequested();
				
				current++;
				// 在UI线程更新进度
				Invoke((Action)delegate
				{
					ShowMsg($"正在登录 {current}/{total}: {uid}");
				});
				
				Players players = World.allConnectedChars.Values.FirstOrDefault((Players a) => a.Userid == uid);
				if (players == null)
				{
					try
					{
						string text = RxjhClass.得到账号下第一个人物名字(uid);
						string empty = string.Empty;
						empty = (string.IsNullOrEmpty(text) ? CreatedUser(uid) : text);
						
						Invoke((Action)delegate
						{
							ShowMsg($"登录账号({current}/{total}): {uid}，角色: {empty}");
						});
						
						NetState netState = listener.生成一个假人连接();
						netState.Player.假人连接登陆(uid);
						netState.Player.假人人物登陆(empty);
						netState.Player.是否人物离线挂机 = true;
						if (netState.Player.Player_Level < initLv)
						{
							netState.Player.Player_Level = initLv;
						}
						
						token.ThrowIfCancellationRequested();
						
						if (rbGJ.Checked)
						{
							netState.Player.假人开始挂机();
							netState.假人 = true;
						}
						else if (radioButton2.Checked)
						{
							netState.Player.假人开始打架();
							netState.假人 = true;
						}
						else if (rbGjie.Checked)
						{
							netState.Player.开启自动逛街();
							netState.挂机 = true;
						}
						else if (rbKD.Checked)
						{
							netState.Player.假人自动开店(1);
							netState.挂机 = true;
						}
						else if (radioButton1.Checked)
						{
							netState.Player.假人自动开店(0);
							netState.挂机 = true;
						}
						netState.在线 = true;
						netState.登陆 = true;
						netState.Player.OpClient(1);
						sum++;
					}
					catch (Exception ex)
					{
						if (ex is OperationCanceledException) 
							throw;
							
						Invoke((Action)delegate
						{
							ShowMsg($"登录账号 {uid} 出错: {ex.Message}");
						});
					}
				}
				else
				{
					Invoke((Action)delegate
					{
						ShowMsg($"账号({current}/{total}): {uid}已登录，跳过");
					});
				}
			}
			return sum;
		}, token);
	}

	private string CreatedUser(string id)
	{
		if (_names.Count < 1)
		{
			throw new Exception("名字词库没有数据，不能创建角色.");
		}
		string name = NameGene.GetName();
		int num = RNG.Next(int.Parse(textBox2.Text), int.Parse(textBox3.Text));
		int num2 = RNG.Next(1, 10);
		byte b = 1;
		if (num2 >= 5)
		{
			b = 2;
		}
		if (num == 12)
		{
			b = 1;
		}
		if (num == 13)
		{
			b = 2;
		}
		byte[] array = new byte[10] { 1, 0, 0, 1, 1, 0, 0, 0, 0, 0 };
		array[5] = b;
		byte[] coue = array;
		RxjhClass.SetUserName(id, name, num, coue);
		return name;
	}

	private void frmRobot_Load(object sender, EventArgs e)
	{
		// 设置列表为自定义绘制
		lbCanUseUid.DrawMode = DrawMode.OwnerDrawFixed;
		lbCanUseUid.DrawItem += new DrawItemEventHandler(lbCanUseUid_DrawItem);
		
		// 显示初始消息
		ShowMsg("假人操作系统就绪，当前在线假人：" + World.allConnectedChars.Count + "个");
		
		// 原有代码
		InitData();
		InitDicData();
	}

	// 添加列表绘制处理器
	private void lbCanUseUid_DrawItem(object sender, DrawItemEventArgs e)
	{
		if (e.Index < 0) return;
		
		// 绘制背景
		e.DrawBackground();
		
		string accountId = lbCanUseUid.Items[e.Index].ToString();
		bool isOnline = World.allConnectedChars.Values.Any(p => p.Userid == accountId);
		
		// 根据账号状态设置颜色
		Color textColor = isOnline ? Color.Red : Color.Black;
		string statusText = isOnline ? " [在线]" : "";
		
		// 绘制文本
		using (Brush brush = new SolidBrush(textColor))
		{
			e.Graphics.DrawString(accountId + statusText, e.Font, brush, e.Bounds);
		}
		
		// 如果该项被选中，绘制焦点矩形
		e.DrawFocusRectangle();
	}

	private void InitDicData()
	{
		_names.Clear();
		_names = ReadTxtContent(txtFirstNamePath.Text);
		NameGene = new UserNameGene(_names);
	}

	private List<string> ReadTxtContent(string Path)
	{
		List<string> list = new List<string>();
		StreamReader streamReader = new StreamReader(Path, Encoding.UTF8);
		string text = streamReader.ReadToEnd();
		if (string.IsNullOrEmpty(text))
		{
			MessageBox.Show("词库：" + Path + "没有数据，请检查");
		}
		string[] array = text.Split(',');
		string[] array2 = array;
		string[] array3 = array2;
		foreach (string item in array3)
		{
			list.Add(item);
		}
		return list;
	}

	private void lbRefresh_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
	{
		RefreshAccountList();
	}

	private void button2_Click(object sender, EventArgs e)
	{
		if (World.假人加入门派 == 0)
		{
			World.假人加入门派 = 1;
			button2.Text = "加入门派目前 开启";
		}
		else
		{
			World.假人加入门派 = 0;
			button2.Text = "加入门派目前 关闭";
		}
	}

	private void button16_Click(object sender, EventArgs e)
	{
		if (World.假人自动结婚 == 1)
		{
			World.假人自动结婚 = 0;
			button16.Text = "结婚系统目前 关闭";
		}
		else
		{
			World.假人自动结婚 = 1;
			button16.Text = "结婚系统目前 开启";
		}
	}

	private void button13_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定创建门派吗？", "温馨提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
		int num = 0;
		try
		{
			if (textBox4.Text == "")
			{
				label13.Text = "请输入阿拉伯数字";
				return;
			}
			if (int.Parse(textBox4.Text) >= 6)
			{
				label13.Text = "不可大于6 包括 6";
				return;
			}
			int num2 = int.Parse(textBox4.Text);
			if (num2 == 1)
			{
				num = 1;
				RxjhClass.创建帮派(World.假人帮派数据一[0], World.假人帮派数据一[1], World.限制最高级别, World.分区编号);
				label13.Text = "第一个帮派创建成功";
			}
			if (num2 == 2)
			{
				num = 6;
				RxjhClass.创建帮派(World.假人帮派数据二[0], World.假人帮派数据二[1], World.限制最高级别, World.分区编号);
				label13.Text = "第二个帮派创建成功";
			}
			if (num2 == 3)
			{
				num = 11;
				RxjhClass.创建帮派(World.假人帮派数据三[0], World.假人帮派数据三[1], World.限制最高级别, World.分区编号);
				label13.Text = "第三个帮派创建成功";
			}
			if (num2 == 4)
			{
				num = 11;
				RxjhClass.创建帮派(World.假人帮派数据四[0], World.假人帮派数据四[1], World.限制最高级别, World.分区编号);
				label13.Text = "第四个帮派创建成功";
			}
			if (num2 == 5)
			{
				num = 11;
				RxjhClass.创建帮派(World.假人帮派数据五[0], World.假人帮派数据五[1], World.限制最高级别, World.分区编号);
				label13.Text = "第五个帮派创建成功";
			}
		}
		catch
		{
			label13.Text = "请输入阿拉伯数字";
			Form1.WriteLine(1, "机器人创建帮派 位置---" + num);
		}
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmRobot));
            this.btnSetFirsrNames = new System.Windows.Forms.Button();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.txtInitLv = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.txtloginSum = new System.Windows.Forms.TextBox();
            this.tbSelect = new System.Windows.Forms.RadioButton();
            this.rbSetSum = new System.Windows.Forms.RadioButton();
            this.btnLogin = new System.Windows.Forms.Button();
            this.rbAll = new System.Windows.Forms.RadioButton();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblMsg = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.lbRefresh = new System.Windows.Forms.LinkLabel();
            this.lbCanUseUid = new System.Windows.Forms.ListBox();
            this.txtFirstNamePath = new System.Windows.Forms.TextBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.button16 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.label13 = new System.Windows.Forms.Label();
            this.button13 = new System.Windows.Forms.Button();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.radioButton2 = new System.Windows.Forms.RadioButton();
            this.radioButton1 = new System.Windows.Forms.RadioButton();
            this.rbGJ = new System.Windows.Forms.RadioButton();
            this.rbKD = new System.Windows.Forms.RadioButton();
            this.rbGjie = new System.Windows.Forms.RadioButton();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.btnKickAll = new System.Windows.Forms.Button();
            this.btnDeleteAll = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.txtFQ = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.txtRegidEnd = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.btnReg = new System.Windows.Forms.Button();
            this.txtRegSum = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtRegidStart = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.statusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.groupBox5.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnSetFirsrNames
            // 
            this.btnSetFirsrNames.Location = new System.Drawing.Point(506, 62);
            this.btnSetFirsrNames.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnSetFirsrNames.Name = "btnSetFirsrNames";
            this.btnSetFirsrNames.Size = new System.Drawing.Size(135, 38);
            this.btnSetFirsrNames.TabIndex = 0;
            this.btnSetFirsrNames.Text = "选 择";
            this.btnSetFirsrNames.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.txtInitLv);
            this.groupBox5.Controls.Add(this.label7);
            this.groupBox5.Controls.Add(this.txtloginSum);
            this.groupBox5.Controls.Add(this.tbSelect);
            this.groupBox5.Controls.Add(this.rbSetSum);
            this.groupBox5.Controls.Add(this.btnLogin);
            this.groupBox5.Controls.Add(this.rbAll);
            this.groupBox5.Controls.Add(this.btnCancel);
            this.groupBox5.Location = new System.Drawing.Point(292, 121);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Size = new System.Drawing.Size(765, 168);
            this.groupBox5.TabIndex = 15;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "登录选项";
            // 
            // txtInitLv
            // 
            this.txtInitLv.Location = new System.Drawing.Point(371, 106);
            this.txtInitLv.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtInitLv.Name = "txtInitLv";
            this.txtInitLv.Size = new System.Drawing.Size(90, 28);
            this.txtInitLv.TabIndex = 0;
            this.txtInitLv.Text = "10";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(270, 108);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(98, 18);
            this.label7.TabIndex = 1;
            this.label7.Text = "初始等级：";
            // 
            // txtloginSum
            // 
            this.txtloginSum.Location = new System.Drawing.Point(158, 106);
            this.txtloginSum.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtloginSum.Name = "txtloginSum";
            this.txtloginSum.Size = new System.Drawing.Size(90, 28);
            this.txtloginSum.TabIndex = 2;
            this.txtloginSum.Text = "10";
            // 
            // tbSelect
            // 
            this.tbSelect.AutoSize = true;
            this.tbSelect.Checked = true;
            this.tbSelect.Location = new System.Drawing.Point(22, 36);
            this.tbSelect.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.tbSelect.Name = "tbSelect";
            this.tbSelect.Size = new System.Drawing.Size(87, 22);
            this.tbSelect.TabIndex = 3;
            this.tbSelect.TabStop = true;
            this.tbSelect.Text = "选择项";
            this.tbSelect.UseVisualStyleBackColor = true;
            // 
            // rbSetSum
            // 
            this.rbSetSum.AutoSize = true;
            this.rbSetSum.Location = new System.Drawing.Point(22, 108);
            this.rbSetSum.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.rbSetSum.Name = "rbSetSum";
            this.rbSetSum.Size = new System.Drawing.Size(123, 22);
            this.rbSetSum.TabIndex = 4;
            this.rbSetSum.Text = "自定义数量";
            this.rbSetSum.UseVisualStyleBackColor = true;
            // 
            // btnLogin
            // 
            this.btnLogin.Location = new System.Drawing.Point(506, 99);
            this.btnLogin.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new System.Drawing.Size(225, 42);
            this.btnLogin.TabIndex = 5;
            this.btnLogin.Text = "登 录";
            this.btnLogin.UseVisualStyleBackColor = true;
            this.btnLogin.Click += new System.EventHandler(this.btnLogin_Click);
            // 
            // rbAll
            // 
            this.rbAll.AutoSize = true;
            this.rbAll.Location = new System.Drawing.Point(22, 72);
            this.rbAll.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.rbAll.Name = "rbAll";
            this.rbAll.Size = new System.Drawing.Size(69, 22);
            this.rbAll.TabIndex = 6;
            this.rbAll.Text = "全部";
            this.rbAll.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.Enabled = false;
            this.btnCancel.Location = new System.Drawing.Point(506, 39);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(225, 42);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "取 消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblMsg
            // 
            this.lblMsg.Location = new System.Drawing.Point(0, 0);
            this.lblMsg.Name = "lblMsg";
            this.lblMsg.Size = new System.Drawing.Size(0, 0);
            this.lblMsg.TabIndex = 20;
            this.lblMsg.Visible = false;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.lbRefresh);
            this.groupBox3.Controls.Add(this.lbCanUseUid);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Left;
            this.groupBox3.Location = new System.Drawing.Point(3, 23);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Size = new System.Drawing.Size(281, 719);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "可用账号";
            // 
            // lbRefresh
            // 
            this.lbRefresh.AutoSize = true;
            this.lbRefresh.Location = new System.Drawing.Point(202, 0);
            this.lbRefresh.Name = "lbRefresh";
            this.lbRefresh.Size = new System.Drawing.Size(44, 18);
            this.lbRefresh.TabIndex = 0;
            this.lbRefresh.TabStop = true;
            this.lbRefresh.Text = "刷新";
            this.lbRefresh.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lbRefresh_LinkClicked);
            // 
            // lbCanUseUid
            // 
            this.lbCanUseUid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lbCanUseUid.FormattingEnabled = true;
            this.lbCanUseUid.ItemHeight = 18;
            this.lbCanUseUid.Location = new System.Drawing.Point(3, 23);
            this.lbCanUseUid.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lbCanUseUid.Name = "lbCanUseUid";
            this.lbCanUseUid.SelectionMode = System.Windows.Forms.SelectionMode.MultiSimple;
            this.lbCanUseUid.Size = new System.Drawing.Size(275, 694);
            this.lbCanUseUid.TabIndex = 1;
            // 
            // txtFirstNamePath
            // 
            this.txtFirstNamePath.Location = new System.Drawing.Point(22, 66);
            this.txtFirstNamePath.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtFirstNamePath.Name = "txtFirstNamePath";
            this.txtFirstNamePath.Size = new System.Drawing.Size(472, 28);
            this.txtFirstNamePath.TabIndex = 1;
            this.txtFirstNamePath.Text = "./Script/机器人名字库.txt";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.button16);
            this.groupBox2.Controls.Add(this.button2);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.button13);
            this.groupBox2.Controls.Add(this.btnKickAll);
            this.groupBox2.Controls.Add(this.textBox4);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.groupBox6);
            this.groupBox2.Controls.Add(this.groupBox4);
            this.groupBox2.Controls.Add(this.groupBox7);
            this.groupBox2.Controls.Add(this.groupBox5);
            this.groupBox2.Controls.Add(this.lblMsg);
            this.groupBox2.Controls.Add(this.groupBox3);
            this.groupBox2.Controls.Add(this.btnDeleteAll);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 120);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Size = new System.Drawing.Size(1069, 744);
            this.groupBox2.TabIndex = 3;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "批量登录";
            // 
            // button16
            // 
            this.button16.BackColor = System.Drawing.Color.White;
            this.button16.Location = new System.Drawing.Point(798, 581);
            this.button16.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(225, 42);
            this.button16.TabIndex = 0;
            this.button16.Text = "结婚系统目前 关闭";
            this.button16.UseVisualStyleBackColor = false;
            this.button16.Click += new System.EventHandler(this.button16_Click);
            // 
            // button2
            // 
            this.button2.BackColor = System.Drawing.Color.White;
            this.button2.Location = new System.Drawing.Point(799, 641);
            this.button2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(224, 42);
            this.button2.TabIndex = 1;
            this.button2.Text = "加入门派目前 关闭";
            this.button2.UseVisualStyleBackColor = false;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.ForeColor = System.Drawing.Color.OrangeRed;
            this.label13.Location = new System.Drawing.Point(543, 593);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(44, 18);
            this.label13.TabIndex = 2;
            this.label13.Text = "提示";
            // 
            // button13
            // 
            this.button13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.button13.Location = new System.Drawing.Point(314, 641);
            this.button13.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(225, 42);
            this.button13.TabIndex = 3;
            this.button13.Text = "一键创建门派";
            this.button13.UseVisualStyleBackColor = false;
            this.button13.Click += new System.EventHandler(this.button13_Click);
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(314, 588);
            this.textBox4.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(225, 28);
            this.textBox4.TabIndex = 4;
            this.textBox4.Text = "1，2，3，4，5";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.ForeColor = System.Drawing.Color.Blue;
            this.label12.Location = new System.Drawing.Point(292, 552);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(332, 18);
            this.label12.TabIndex = 5;
            this.label12.Text = "配置表修改帮派名后帮派分配 1 2 3 4 5";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.textBox3);
            this.groupBox6.Controls.Add(this.label9);
            this.groupBox6.Controls.Add(this.textBox2);
            this.groupBox6.Controls.Add(this.label8);
            this.groupBox6.Location = new System.Drawing.Point(292, 308);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Size = new System.Drawing.Size(461, 84);
            this.groupBox6.TabIndex = 19;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "职业选择设置";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(338, 34);
            this.textBox3.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(78, 28);
            this.textBox3.TabIndex = 0;
            this.textBox3.Text = "13";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(236, 36);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(98, 18);
            this.label9.TabIndex = 1;
            this.label9.Text = "职业最大：";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(124, 34);
            this.textBox2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(78, 28);
            this.textBox2.TabIndex = 2;
            this.textBox2.Text = "1";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(22, 36);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(98, 18);
            this.label8.TabIndex = 3;
            this.label8.Text = "职业最小：";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.radioButton2);
            this.groupBox4.Controls.Add(this.radioButton1);
            this.groupBox4.Controls.Add(this.rbGJ);
            this.groupBox4.Controls.Add(this.rbKD);
            this.groupBox4.Controls.Add(this.rbGjie);
            this.groupBox4.Location = new System.Drawing.Point(292, 24);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Size = new System.Drawing.Size(765, 84);
            this.groupBox4.TabIndex = 18;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "离线操作设置";
            // 
            // radioButton2
            // 
            this.radioButton2.AutoSize = true;
            this.radioButton2.Location = new System.Drawing.Point(158, 36);
            this.radioButton2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.radioButton2.Name = "radioButton2";
            this.radioButton2.Size = new System.Drawing.Size(105, 22);
            this.radioButton2.TabIndex = 0;
            this.radioButton2.Text = "离线打架";
            this.radioButton2.UseVisualStyleBackColor = true;
            // 
            // radioButton1
            // 
            this.radioButton1.AutoSize = true;
            this.radioButton1.Location = new System.Drawing.Point(562, 36);
            this.radioButton1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.radioButton1.Name = "radioButton1";
            this.radioButton1.Size = new System.Drawing.Size(105, 22);
            this.radioButton1.TabIndex = 1;
            this.radioButton1.Text = "元宝开店";
            this.radioButton1.UseVisualStyleBackColor = true;
            // 
            // rbGJ
            // 
            this.rbGJ.AutoSize = true;
            this.rbGJ.Checked = true;
            this.rbGJ.Location = new System.Drawing.Point(22, 36);
            this.rbGJ.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.rbGJ.Name = "rbGJ";
            this.rbGJ.Size = new System.Drawing.Size(105, 22);
            this.rbGJ.TabIndex = 2;
            this.rbGJ.TabStop = true;
            this.rbGJ.Text = "离线打怪";
            this.rbGJ.UseVisualStyleBackColor = true;
            // 
            // rbKD
            // 
            this.rbKD.AutoSize = true;
            this.rbKD.Location = new System.Drawing.Point(428, 36);
            this.rbKD.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.rbKD.Name = "rbKD";
            this.rbKD.Size = new System.Drawing.Size(105, 22);
            this.rbKD.TabIndex = 3;
            this.rbKD.Text = "金币开店";
            this.rbKD.UseVisualStyleBackColor = true;
            // 
            // rbGjie
            // 
            this.rbGjie.AutoSize = true;
            this.rbGjie.Location = new System.Drawing.Point(292, 36);
            this.rbGjie.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.rbGjie.Name = "rbGjie";
            this.rbGjie.Size = new System.Drawing.Size(105, 22);
            this.rbGjie.TabIndex = 4;
            this.rbGjie.Text = "离线逛街";
            this.rbGjie.UseVisualStyleBackColor = true;
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.btnSetFirsrNames);
            this.groupBox7.Controls.Add(this.txtFirstNamePath);
            this.groupBox7.Controls.Add(this.label4);
            this.groupBox7.Location = new System.Drawing.Point(292, 411);
            this.groupBox7.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox7.Size = new System.Drawing.Size(765, 121);
            this.groupBox7.TabIndex = 17;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "人物名词库设置";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(22, 36);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(98, 18);
            this.label4.TabIndex = 2;
            this.label4.Text = "名字词库：";
            // 
            // btnKickAll
            // 
            this.btnKickAll.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.btnKickAll.Location = new System.Drawing.Point(799, 306);
            this.btnKickAll.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnKickAll.Name = "btnKickAll";
            this.btnKickAll.Size = new System.Drawing.Size(225, 42);
            this.btnKickAll.TabIndex = 20;
            this.btnKickAll.Text = "踢出全部假人";
            this.btnKickAll.UseVisualStyleBackColor = false;
            this.btnKickAll.Click += new System.EventHandler(this.btnKickAll_Click);
            // 
            // btnDeleteAll
            // 
            this.btnDeleteAll.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.btnDeleteAll.Location = new System.Drawing.Point(799, 356);
            this.btnDeleteAll.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnDeleteAll.Name = "btnDeleteAll";
            this.btnDeleteAll.Size = new System.Drawing.Size(225, 42);
            this.btnDeleteAll.TabIndex = 21;
            this.btnDeleteAll.Text = "删除全部假人";
            this.btnDeleteAll.UseVisualStyleBackColor = false;
            this.btnDeleteAll.Click += new System.EventHandler(this.btnDeleteAll_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.textBox1);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.txtFQ);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.txtRegidEnd);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.btnReg);
            this.groupBox1.Controls.Add(this.txtRegSum);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.txtRegidStart);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Size = new System.Drawing.Size(1069, 120);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "批量注册账号";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(799, 44);
            this.textBox1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(90, 28);
            this.textBox1.TabIndex = 0;
            this.textBox1.Text = "1";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(742, 48);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(44, 18);
            this.label6.TabIndex = 1;
            this.label6.Text = "假人";
            // 
            // txtFQ
            // 
            this.txtFQ.Location = new System.Drawing.Point(484, 44);
            this.txtFQ.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtFQ.Name = "txtFQ";
            this.txtFQ.Size = new System.Drawing.Size(90, 28);
            this.txtFQ.TabIndex = 2;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(428, 48);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(44, 18);
            this.label3.TabIndex = 3;
            this.label3.Text = "密码";
            // 
            // txtRegidEnd
            // 
            this.txtRegidEnd.Location = new System.Drawing.Point(326, 44);
            this.txtRegidEnd.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtRegidEnd.Name = "txtRegidEnd";
            this.txtRegidEnd.Size = new System.Drawing.Size(90, 28);
            this.txtRegidEnd.TabIndex = 4;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(225, 48);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(80, 18);
            this.label5.TabIndex = 5;
            this.label5.Text = "账号结尾";
            // 
            // btnReg
            // 
            this.btnReg.Location = new System.Drawing.Point(900, 38);
            this.btnReg.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnReg.Name = "btnReg";
            this.btnReg.Size = new System.Drawing.Size(135, 42);
            this.btnReg.TabIndex = 6;
            this.btnReg.Text = "注  册";
            this.btnReg.Click += new System.EventHandler(this.btnReg_Click);
            // 
            // txtRegSum
            // 
            this.txtRegSum.Location = new System.Drawing.Point(641, 44);
            this.txtRegSum.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtRegSum.Name = "txtRegSum";
            this.txtRegSum.Size = new System.Drawing.Size(90, 28);
            this.txtRegSum.TabIndex = 7;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(585, 48);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(44, 18);
            this.label2.TabIndex = 8;
            this.label2.Text = "数量";
            // 
            // txtRegidStart
            // 
            this.txtRegidStart.Location = new System.Drawing.Point(124, 44);
            this.txtRegidStart.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtRegidStart.Name = "txtRegidStart";
            this.txtRegidStart.Size = new System.Drawing.Size(90, 28);
            this.txtRegidStart.TabIndex = 9;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(22, 48);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(80, 18);
            this.label1.TabIndex = 10;
            this.label1.Text = "账号开头";
            // 
            // statusStrip
            // 
            this.statusStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.statusLabel});
            this.statusStrip.Location = new System.Drawing.Point(0, 829);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Padding = new System.Windows.Forms.Padding(1, 0, 16, 0);
            this.statusStrip.Size = new System.Drawing.Size(1069, 35);
            this.statusStrip.TabIndex = 4;
            this.statusStrip.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            this.statusLabel.BackColor = System.Drawing.Color.LightYellow;
            this.statusLabel.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Top) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right) 
            | System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom)));
            this.statusLabel.BorderStyle = System.Windows.Forms.Border3DStyle.SunkenOuter;
            this.statusLabel.ForeColor = System.Drawing.Color.Black;
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new System.Drawing.Size(1052, 28);
            this.statusLabel.Spring = true;
            this.statusLabel.Text = "假人操作系统准备就绪...";
            this.statusLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // frmRobot
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1069, 864);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.Name = "frmRobot";
            this.Text = "假人操作面板";
            this.Load += new System.EventHandler(this.frmRobot_Load);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	// 添加一个更高效的列表刷新方法
	private void RefreshAccountList()
	{
		List<string> selectedItems = new List<string>();
		
		// 保存当前选中项
		foreach (object item in lbCanUseUid.SelectedItems)
		{
			selectedItems.Add(item.ToString());
		}
		
		// 记录当前滚动位置
		int topIndex = lbCanUseUid.TopIndex;
		
		// 更新列表
		lbCanUseUid.Items.Clear();
		string sqlCommand = "select * from [TBL_ACCOUNT] where 是否假人=1 and 账号是否在线 != 1";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
		if (dBToDataTable == null || dBToDataTable.Rows.Count <= 0)
		{
			return;
		}
		
		// 记录在线账号，用不同颜色显示
		List<string> onlineAccounts = new List<string>();
		foreach (Players player in World.allConnectedChars.Values)
		{
			if (player != null && !string.IsNullOrEmpty(player.Userid))
			{
				onlineAccounts.Add(player.Userid);
			}
		}
		
		foreach (DataRow row in dBToDataTable.Rows)
		{
			string accountId = row["FLD_ID"].ToString();
			lbCanUseUid.Items.Add(accountId);
			
			// 恢复选中状态
			if (selectedItems.Contains(accountId))
			{
				lbCanUseUid.SetSelected(lbCanUseUid.Items.Count - 1, true);
			}
		}
		
		// 恢复滚动位置
		if (topIndex >= 0 && topIndex < lbCanUseUid.Items.Count)
		{
			lbCanUseUid.TopIndex = topIndex;
		}
	}

	// 添加取消按钮事件处理方法
	private void btnCancel_Click(object sender, EventArgs e)
	{
		if (_cts != null && !_cts.IsCancellationRequested)
		{
			ShowMsg("正在取消登录操作...");
			_cts.Cancel();
		}
	}

	// 创建一个新的状态栏，替代原有的状态栏
	private void CreateStatusBar()
	{
		try
		{
			// 如果已存在状态栏，先移除
			if (statusStrip != null)
			{
				this.Controls.Remove(statusStrip);
			}

			// 创建一个新的状态栏
			statusStrip = new StatusStrip();
			statusStrip.Dock = DockStyle.Bottom;
			statusStrip.BackColor = SystemColors.Control;
			
			// 创建状态标签
			statusLabel = new ToolStripStatusLabel();
			statusLabel.BackColor = Color.LightYellow;
			statusLabel.BorderSides = ToolStripStatusLabelBorderSides.All;
			statusLabel.BorderStyle = Border3DStyle.SunkenOuter;
			statusLabel.Spring = true; // 使标签填充整个状态栏
			statusLabel.TextAlign = ContentAlignment.MiddleLeft;
			statusLabel.Text = "假人操作系统启动中...";
			
			// 添加标签到状态栏
			statusStrip.Items.Add(statusLabel);
			
			// 添加状态栏到窗体
			this.Controls.Add(statusStrip);
			
			// 强制刷新
			statusStrip.Refresh();
			this.Refresh();
			
			// 设置立即显示消息
			ShowMsg("假人操作系统已就绪...");
		}
		catch (Exception ex)
		{
			MessageBox.Show("创建状态栏出错: " + ex.Message);
		}
	}

	// 添加新方法
	private void timer_Tick(object sender, EventArgs e)
	{
		RefreshDummyCount();
	}

    private void RefreshDummyCount()
    {
        try
        {
            // 只统计当前分区的假人数量
            string sql = $@"SELECT COUNT(*) FROM TBL_ACCOUNT 
                      WHERE 是否假人=1 AND FLD_FQ='{World.分区编号}'
                      AND (FLD_ONLINE=1 OR 账号是否在线=1 OR 随机时间在线=1)";

            DataTable dt = DBA.GetDBToDataTable(sql, "rxjhaccount");
            if (dt != null && dt.Rows.Count > 0)
            {
                int count = Convert.ToInt32(dt.Rows[0][0]);
                statusLabel.Text = $"当前在线假人（{World.分区编号}区）：{count}个";
            }
        }
        catch (Exception ex)
        {
            // 2025-0619 EVIAS 清理调试代码，使用统一日志系统
            Form1.WriteLine(1, $"刷新假人数量出错: {ex.Message}");
            statusLabel.Text = "假人数量统计失败";
        }
    }

    private void btnKickAll_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要踢出所有假人吗？", "提示", 
			MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
		{
			try
			{
				// 1. 断开所有内存中的假人连接
				var toKick = new List<Players>();
				foreach (var p in World.allConnectedChars.Values)
				{
					if (p != null && p.Client != null && p.Client.假人)
					{
						toKick.Add(p);
					}
				}
				foreach (var p in toKick)
				{
					try { p.Client.DisposedOffline(); } catch { }
				}
				// 2. 查询所有假人账号ID
				string getIdsSql = "SELECT FLD_ID FROM TBL_ACCOUNT WHERE 是否假人=1 AND FLD_FQ='" + World.分区编号 + "' AND (FLD_ONLINE=1 OR 账号是否在线=1 OR 随机时间在线=1)";
				DataTable idTable = DBA.GetDBToDataTable(getIdsSql, "rxjhaccount");
				List<string> idList = new List<string>();
				if (idTable != null && idTable.Rows.Count > 0)
				{
					foreach (DataRow row in idTable.Rows)
					{
						idList.Add($"'" + row["FLD_ID"].ToString() + "'");
					}
				}
				// 3. 更新账号状态
				string updateAccountSql = @"UPDATE TBL_ACCOUNT 
										  SET FLD_ONLINE=0, 账号是否在线=0, 随机时间在线=0 
										  WHERE 是否假人=1 AND FLD_FQ='" + World.分区编号 + "' AND (FLD_ONLINE=1 OR 账号是否在线=1 OR 随机时间在线=1)";
				int accountCount = DBA.ExeSqlCommand(updateAccountSql, "rxjhaccount");
				int charCount = 0;
				// 4. 如果有ID，批量更新角色表
				if (idList.Count > 0)
				{
					string idIn = string.Join(",", idList);
					string updateCharSql = $@"UPDATE TBL_XWWL_Char 
										   SET 角色是否在线=0 
										   WHERE 角色是否在线=1 AND FLD_ID IN ({idIn})";
					charCount = DBA.ExeSqlCommand(updateCharSql, "GameServer");
				}
				MessageBox.Show($"已踢出{accountCount}个假人账号和{charCount}个假人角色");
				RefreshDummyCount();
				RefreshAccountList();
                World.假人数量 = World.allConnectedChars.Values.Count(p => p.Client != null && p.Client.假人);  // EVIAS 通知主面板
            }
			catch (Exception ex)
			{
				MessageBox.Show("踢出假人时出错: " + ex.Message);
			}
		}
	}

	private void btnDeleteAll_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除所有假人账号吗？此操作不可恢复！", "警告", 
			MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
		{
			try
			{
				// 1. 查询所有假人账号ID
				string getIdsSql = "SELECT FLD_ID FROM TBL_ACCOUNT WHERE 是否假人=1 AND FLD_FQ='" + World.分区编号 + "'";
                DataTable idTable = DBA.GetDBToDataTable(getIdsSql, "rxjhaccount");
				List<string> idList = new List<string>();
				if (idTable != null && idTable.Rows.Count > 0)
				{
					foreach (DataRow row in idTable.Rows) 
					{
						idList.Add($"'" + row["FLD_ID"].ToString() + "'");
					}
				}
				int charCount = 0;
				// 2. 如果有ID，批量删除角色表
				if (idList.Count > 0)
				{
					string idIn = string.Join(",", idList);
					string deleteCharsSql = $@"DELETE FROM TBL_XWWL_Char 
											WHERE FLD_ID IN ({idIn})";
					charCount = DBA.ExeSqlCommand(deleteCharsSql, "GameServer");
				}
				// 3. 删除账号
				string deleteAccountsSql = "DELETE FROM TBL_ACCOUNT WHERE 是否假人=1 AND FLD_FQ='" + World.分区编号 + "'";
				int accountCount = DBA.ExeSqlCommand(deleteAccountsSql, "rxjhaccount");
				MessageBox.Show($"已删除{accountCount}个假人账号和{charCount}个假人角色");
				RefreshDummyCount();
				RefreshAccountList();
			}
			catch (Exception ex)
			{
				MessageBox.Show("删除假人时出错: " + ex.Message);
			}
		}
	}
}

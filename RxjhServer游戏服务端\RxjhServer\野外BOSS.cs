using System;
using System.Collections.Generic;
using System.Timers;

namespace RxjhServer;

public class 野外BOSS : IDisposable
{
	private DateTime 野外BOSS出现时间;

	private DateTime 野外BOSS消失时间;

	private System.Timers.Timer 野外BOSS出现提示时钟;

	private System.Timers.Timer 野外BOSS进行中时钟;

	private List<NpcClass> 野外BOSS怪物列表 = new List<NpcClass>();

	public 野外BOSS()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(17, "野外BOSS初始化事件");
			}
			string[] array = World.野外BOSS配置.Split('/');
			for (int i = 0; i < array.Length; i++)
			{
				string[] array2 = array[i].Split(';');
				if (array2.Length != 0)
				{
					string mapName = 坐标Class.GetMapName(Convert.ToInt32(array2[0]));
					string monSterName = MonSterClss.GetMonSterName(Convert.ToInt32(array2[1]));
					string 内容 = string.Format(World.野外BOSS倒计时 + "分钟后,{0}野外将出现BOSS怪物{1}", new object[2] { mapName, monSterName });
					World.发送特殊公告(内容, 6, "公告");
				}
			}
			野外BOSS出现时间 = DateTime.Now.AddMinutes(World.野外BOSS倒计时);
			野外BOSS出现提示时钟 = new System.Timers.Timer(10000.0);
			野外BOSS出现提示时钟.Elapsed += 时间结束事件1;
			野外BOSS出现提示时钟.Enabled = true;
			野外BOSS出现提示时钟.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "野外BOSS", "初始化事件", "野外BOSS系统初始化");
			Form1.WriteLine(1, "野外BOSS初始化事件出错：" + ex);
		}
	}

	public void 时间结束事件1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(17, "野外BOSS结束事件1");
		}
		try
		{
			int num = (int)野外BOSS出现时间.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			野外BOSS出现提示时钟.Enabled = false;
			野外BOSS出现提示时钟.Close();
			野外BOSS出现提示时钟.Dispose();
			string[] array = World.野外BOSS配置.Split('/');
			for (int i = 0; i < array.Length; i++)
			{
				string[] array2 = array[i].Split(';');
				if (array2.Length != 0)
				{
					NpcClass item = World.AddNpc(Convert.ToInt32(array2[1]), Convert.ToInt32(array2[2]), Convert.ToInt32(array2[3]), Convert.ToInt32(array2[0]), 0, 0, 一次性怪: true, 3600);
					野外BOSS怪物列表.Add(item);
					string mapName = 坐标Class.GetMapName(Convert.ToInt32(array2[0]));
					string monSterName = MonSterClss.GetMonSterName(Convert.ToInt32(array2[1]));
					string 内容 = string.Format("{0}野外出现BOSS怪物{1}", new object[2] { mapName, monSterName });
					World.发送特殊公告(内容, 6, "公告");
				}
			}
			野外BOSS消失时间 = DateTime.Now.AddMinutes(World.野外BOSS总时间);
			野外BOSS进行中时钟 = new System.Timers.Timer(10000.0);
			野外BOSS进行中时钟.Elapsed += 时间结束事件2;
			野外BOSS进行中时钟.Enabled = true;
			野外BOSS进行中时钟.AutoReset = true;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "野外BOSS结束事件1出错：" + ex);
		}
	}

	public void 时间结束事件2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(17, "野外BOSS结束事件2");
		}
		try
		{
			int num = (int)野外BOSS消失时间.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			野外BOSS进行中时钟.Enabled = false;
			野外BOSS进行中时钟.Close();
			野外BOSS进行中时钟.Dispose();
			if (野外BOSS怪物列表 != null)
			{
				foreach (NpcClass item in 野外BOSS怪物列表)
				{
					World.delNpc(item.Rxjh_Map, item.FLD_INDEX);
				}
				野外BOSS怪物列表.Clear();
			}
			Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "野外BOSS结束事件2出错：" + ex);
		}
	}

	void IDisposable.Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(17, "野外BOSS内存释放事件");
		}
		if (野外BOSS出现提示时钟 != null)
		{
			野外BOSS出现提示时钟.Enabled = false;
			野外BOSS出现提示时钟.Close();
			野外BOSS出现提示时钟.Dispose();
		}
		if (野外BOSS进行中时钟 != null)
		{
			野外BOSS进行中时钟.Enabled = false;
			野外BOSS进行中时钟.Close();
			野外BOSS进行中时钟.Dispose();
		}
		World.野外boss = null;
		if (野外BOSS怪物列表 == null)
		{
			return;
		}
		foreach (NpcClass item in 野外BOSS怪物列表)
		{
			World.delNpc(item.Rxjh_Map, item.FLD_INDEX);
		}
		野外BOSS怪物列表.Clear();
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(17, "野外BOSS内存释放事件");
		}
		if (野外BOSS出现提示时钟 != null)
		{
			野外BOSS出现提示时钟.Enabled = false;
			野外BOSS出现提示时钟.Close();
			野外BOSS出现提示时钟.Dispose();
		}
		if (野外BOSS进行中时钟 != null)
		{
			野外BOSS进行中时钟.Enabled = false;
			野外BOSS进行中时钟.Close();
			野外BOSS进行中时钟.Dispose();
		}
		World.野外boss = null;
		if (野外BOSS怪物列表 == null)
		{
			return;
		}
		foreach (NpcClass item in 野外BOSS怪物列表)
		{
			World.delNpc(item.Rxjh_Map, item.FLD_INDEX);
		}
		野外BOSS怪物列表.Clear();
	}
}

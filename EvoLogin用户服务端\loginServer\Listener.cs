using System;
using System.Collections;
using System.Net;
using System.Runtime.InteropServices;

namespace loginServer;

public class Listener
{
	private delegate void ShowMsg(string msg);

	private AppState appState = AppState.Stoped;

	private TcpServer Server = new TcpServer();

	private Extra<ClientInfo> extra = new Extra<ClientInfo>();

	private bool m_Disposed;

	private ShowMsg AddMsgDelegate;

	private void SetServerState()
	{
		AddMsgDelegate = AddMsg;
		Server.OnPrepareListen += OnPrepareListen;
		Server.OnAccept += OnAccept;
		Server.OnSend += OnSend;
		Server.OnPointerDataReceive += OnPointerDataReceive;
		Server.OnClose += OnClose;
		Server.OnShutdown += OnShutdown;
		SetAppState(AppState.Stoped);
	}

	public Listener(ushort port)
	{
		try
		{
			m_Disposed = false;
			SetServerState();
			Start(port);
		}
		catch (Exception ex)
		{
			SetAppState(AppState.Error);
			AddMsg(ex.Message);
		}
	}

	private void Start(ushort port)
	{
		try
		{
			SetAppState(AppState.Starting);
			Server.IpAddress = "0.0.0.0";
			Server.Port = port;
			Server.SendPolicy = SendPolicy.Direct;
			if (Server.Start(World.ServerIDStart))
			{
				AddMsg(string.Format("开始监听端口 {0}:{1}", "0.0.0.0", port));
				SetAppState(AppState.Started);
				return;
			}
			SetAppState(AppState.Stoped);
			throw new Exception($"监听端口失败 -> {Server.ErrorMessage}({Server.ErrorCode})");
		}
		catch (Exception ex)
		{
			AddMsg(ex.Message);
		}
	}

	public void Dispose()
	{
		if (m_Disposed)
		{
			return;
		}
		Form1.WriteLine(1, "关闭登陆服务监听端口");
		m_Disposed = true;
		try
		{
			// 2025-0618 EVIAS 安全释放TcpServer资源
			Server?.Destroy();
			Form1.WriteLine(3, "登陆服务监听器资源已释放");
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "关闭登陆服务", null);
			AddMsg("终止通信服务出错 " + ex.Message);
		}
	}

	private HandleResult OnShutdown()
	{
		AddMsg("停止通信组件服务");
		return HandleResult.Ok;
	}

	private void Disconnect(IntPtr connId)
	{
		try
		{
			if (!Server.Disconnect(connId))
			{
				throw new Exception($"断开连接({connId}) 错误");
			}
			AddMsg($"$({connId}) 断开连接");
		}
		catch (Exception ex)
		{
			AddMsg(ex.Message);
		}
	}

	private HandleResult OnPrepareListen(IntPtr soListen)
	{
		return HandleResult.Ok;
	}

	private ClientInfo InitClientData(IntPtr connId, string ip, ushort port)
	{
		try
		{
			return new ClientInfo
			{
				ConnId = connId,
				IpAddress = ip,
				Port = port,
				Server = Server,
				WorldId = WorldIdd()
			};
		}
		catch
		{
		}
		return null;
	}

	private int WorldIdd()
	{
		bool flag = false;
		NetState value;
		for (int i = 300; i < 65500; i++)
		{
			if (!World.ConnectLst.TryGetValue(i, out value) && i < 3000)
			{
				return i;
			}
		}
		if (!flag)
		{
			for (int j = 300; j < 65500; j++)
			{
				if (!World.ConnectLst.TryGetValue(j, out value))
				{
					return j;
				}
			}
		}
		return 0;
	}

	private static long IpToInt(string ip)
	{
		char[] separator = new char[1] { '.' };
		string[] array = ip.Split(separator);
		return (long.Parse(array[0]) << 24) | (long.Parse(array[1]) << 16) | (long.Parse(array[2]) << 8) | long.Parse(array[3]);
	}

	private HandleResult OnAccept(IntPtr connId, IntPtr pClient)
	{
		int num = 0;
		try
		{
			string ip = string.Empty;
			ushort port = 0;
			if (!Server.GetRemoteAddress(connId, ref ip, ref port))
			{
				AddMsg($" > [{connId},OnAccept] -> GetRemoteAddress(connId, ref ip, ref port) Error");
				return HandleResult.Error;
			}
			if (!ConnInfo.Check(ip, port))
			{
				ConnInfo.Add(new ConnInfo
				{
					Ip = ip,
					Port = port,
					Time = DateTime.Now,
					Kill = true
				});
				Form1.WriteLine(1, $"限制连接ip={ip} port={port}");
				return HandleResult.Error;
			}
			num = 11;
			if (World.封IP)
			{
				IPAddress iPAddress = new IPAddress((uint)IPAddress.HostToNetworkOrder((int)IpToInt(ip)));
				if (World.BipList.Contains(iPAddress) && World.断开连接)
				{
					Disconnect(connId);
				}
				DateTime value = DateTime.Now;
				int num2 = 0;
				foreach (NetState value2 in World.ConnectLst.Values)
				{
					if (value2.ToString() == iPAddress.ToString())
					{
						value = value2.Ljtime;
						num2++;
					}
				}
				if (num2 >= World.游戏登陆端口最大连接数)
				{
					if ((int)DateTime.Now.Subtract(value).TotalMilliseconds < World.游戏登陆端口最大连接时间数)
					{
						Form1.WriteLine(1, "1到达IP最大连接数" + iPAddress.ToString());
						if (World.加入过滤列表 && !World.BipList.Contains(iPAddress))
						{
							World.BipList.Add(iPAddress);
						}
						Disconnect(connId);
						try
						{
							Queue queue = Queue.Synchronized(new Queue());
							foreach (NetState value3 in World.ConnectLst.Values)
							{
								if (value3.ToString() == iPAddress.ToString())
								{
									queue.Enqueue(value3);
								}
							}
							while (queue.Count > 0)
							{
								((NetState)queue.Dequeue()).Dispose();
							}
						}
						catch
						{
							Form1.WriteLine(1, "2到达IP最大连接数" + iPAddress.ToString());
						}
					}
				}
				else
				{
					num = 1;
					ClientInfo clientInfo = InitClientData(connId, ip, port);
					num = 2;
					if (!extra.Set(connId, clientInfo))
					{
						AddMsg($" > [{connId},OnAccept] -> SetConnectionExtra(connId, ci) Error");
						return HandleResult.Error;
					}
					num = 3;
					new NetState(clientInfo).Start();
				}
			}
			else
			{
				num = 12;
				ClientInfo clientInfo2 = InitClientData(connId, ip, port);
				if (!extra.Set(connId, clientInfo2))
				{
					AddMsg($" > [{connId},OnAccept] -> SetConnectionExtra(connId, ci) Error");
					return HandleResult.Error;
				}
				num = 13;
				new NetState(clientInfo2).Start();
			}
			ConnInfo.Add(new ConnInfo
			{
				Ip = ip,
				Port = port,
				Time = DateTime.Now,
				Kill = false
			});
		}
		catch (Exception)
		{
			AddMsg($" > [{connId}{num},OnAccept] -> OnAccept Error");
			return HandleResult.Error;
		}
		return HandleResult.Ok;
	}

	private HandleResult OnSend(IntPtr connId, byte[] bytes)
	{
		return HandleResult.Ok;
	}

	private HandleResult OnPointerDataReceive(IntPtr connId, IntPtr pData, int length)
	{
		try
		{
			ClientInfo clientInfo = extra.Get(connId);
			if (clientInfo == null || length <= 0)
			{
				return HandleResult.Error;
			}
			byte[] array = new byte[length];
			Marshal.Copy(pData, array, 0, length);
			clientInfo.Client.ProcessDataReceived(array, length);
			return HandleResult.Ok;
		}
		catch (Exception)
		{
			return HandleResult.Ignore;
		}
	}

	private HandleResult OnClose(IntPtr connId, SocketOperation enOperation, int errorCode)
	{
		int num = 0;
		try
		{
			ClientInfo clientInfo = extra.Get(connId);
			if (clientInfo != null)
			{
				num = 1;
				// 2025-0618 EVIAS 使用新的玩家管理器查找玩家
				string text = string.Empty;
				if (clientInfo.Client.Player.loginprocess == 2 && !string.IsNullOrEmpty(clientInfo.Client.Player.uidd))
				{
					var player = HelperTools.ConcurrentPlayerManager.GetPlayer(clientInfo.Client.Player.uidd);
					if (player != null)
					{
						text = player.UserId;
					}
				}
				if (text.Length > 0)
				{
					World.登出玩家(text);
				}
				clientInfo.Client.Dispose();
				num = 2;
			}
			num = 3;
			if (!extra.Remove(connId))
			{
				num = 4;
				AddMsg($"[断开错误:SetConnectionExtra({connId}, null) fail ExceStep:{num}] -> OP:{enOperation},CODE:{errorCode}");
			}
		}
		catch (Exception ex)
		{
			AddMsg("断开错误 连接ID:" + connId + " OP:" + enOperation.ToString() + " CODE:" + errorCode + " ExceStep:" + num + " ExceptionMsg:" + ex.Message);
		}
		return HandleResult.Ok;
	}

	private void SetAppState(AppState state)
	{
		appState = state;
		World.SocketState = appState.ToString();
	}

	private void AddMsg(string msg)
	{
		Form1.WriteLine(3, msg);
	}
}

using System;
using System.Collections.Generic;

namespace RxjhServer;

public class 江湖小助手系统
{
	public static void 江湖小助手(Players player)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(player.内挂挂机打怪时间).TotalMilliseconds >= World.内挂打怪说话时间)
			{
				if (World.内挂打怪说话内容.Length != 0)
				{
					player.内挂喊话(player, World.内挂打怪说话内容);
				}
				player.内挂挂机打怪时间 = DateTime.Now;
			}
			if (player.江湖小助手打怪模式 != 1)
			{
				return;
			}
			if (player.人物_HP <= (int)((double)player.人物最大_HP * 0.95) && (long)player.人物_HP > 0L)
			{
				int sl = player.人物最大_HP - player.人物_HP;
				player.加血(sl);
				player.吃药效果(1000000102);
				player.更新HP_MP_SP();
			}
			if (player.人物_MP <= (int)((double)player.人物最大_MP * 0.95) && (long)player.人物_MP > 0L)
			{
				player.加魔(20000);
				player.吃药效果(1000000104);
				player.更新HP_MP_SP();
			}
			long num = player.内挂获取自动拾取物品(player);
			if (num != 0L && World.ItmeTeM.ContainsKey(num))
			{
				int num2 = player.得到包裹空位(player);
				if (player.江湖小助手打怪模式 != 0 || num2 == -1)
				{
					player.内挂拾取物品包(player, num);
				}
			}
			float num3 = player.人物坐标_X - (float)player.自动挂机坐标X;
			float num4 = player.人物坐标_Y - (float)player.自动挂机坐标Y;
			double num5 = (int)Math.Sqrt((double)num3 * (double)num3 + (double)num4 * (double)num4);
			if ((int)num5 > World.离线挂机打怪范围)
			{
				player.离线挂机当前攻击怪物 = 0;
				player.内挂移动包(player, player.自动挂机坐标X, player.自动挂机坐标Y, (float)num5);
				return;
			}
			if (player.江湖小助手打怪模式 == 1)
			{
				player.内挂吃药(player);
			}
			player.离线挂机当前攻击怪物 = 获取自动攻击NPC目标(player, player.自动挂机坐标X, player.自动挂机坐标Y);
			if (player.离线挂机当前攻击怪物 > 10000)
			{
				NpcClass npc = MapClass.GetNpc(player.人物坐标_地图, player.离线挂机当前攻击怪物);
				if (npc != null && !npc.NPC死亡 && npc.Rxjh_HP > 0)
				{
					float num6 = player.人物坐标_X - npc.Rxjh_X;
					float num7 = player.人物坐标_Y - npc.Rxjh_Y;
					double num8 = (int)Math.Sqrt((double)num6 * (double)num6 + (double)num7 * (double)num7);
					if ((int)num8 <= 30)
					{
						player.内挂攻击包(player);
					}
					else
					{
						player.内挂移动包(player, npc.Rxjh_X, npc.Rxjh_Y, (float)num8);
					}
				}
			}
			else
			{
				player.内挂攻击包(player);
			}
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "江湖小助手系统", "自动打怪", "江湖小助手自动战斗");
			Form1.WriteLine(1, "江湖小助手打怪出错:" + ex.Message);
		}
	}

	public static int 获取自动攻击NPC目标(Players player, float float_0, float float_1)
	{
		int result = 0;
		if (player.NpcList != null)
		{
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value in player.NpcList.Values)
			{
				if (player.内挂自动攻击范围Npc(player, World.离线挂机打怪范围, value, float_0, float_1) && !value.NPC死亡 && value.IsNpc != 1 && value.Rxjh_HP > 0)
				{
					list.Add(value);
				}
			}
			if (list.Count > 0)
			{
				int num = World.离线挂机打怪范围;
				foreach (NpcClass item in list)
				{
					float num2 = item.Rxjh_X - player.人物坐标_X;
					float num3 = item.Rxjh_Y - player.人物坐标_Y;
					float num4 = (float)Math.Sqrt(num2 * num2 + num3 * num3);
					if ((int)num4 < num)
					{
						num = (int)num4;
						result = item.FLD_INDEX;
					}
				}
				list.Clear();
			}
		}
		foreach (Players value2 in World.allConnectedChars.Values)
		{
			if (player.人物PK模式 > 0 && value2.人物_HP > 0 && !value2.Player死亡 && value2.人物PK模式 != 0 && !value2.Client.挂机 && player.查找范围玩家(60, value2) && player.人物全服ID != value2.人物全服ID)
			{
				result = value2.人物全服ID;
			}
		}
		return result;
	}
}

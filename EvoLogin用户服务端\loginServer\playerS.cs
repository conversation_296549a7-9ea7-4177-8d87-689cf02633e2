using System;
using System.Threading;
using System.Timers;
using loginServer.DbClss;

namespace loginServer;

public class playerS
{
	// 2025-0618 EVIAS 延长定时器到30分钟，避免在线玩家被误删
	public System.Timers.Timer npcyd = new System.Timers.Timer(1800000.0); // 30分钟

	public System.Timers.Timer 换线timer = new System.Timers.Timer(15000.0);

	private string _绑定账号 = string.Empty;

	public DateTime time;

	private string userid;

	private int _原服务器序号;

	private string _原服务器IP;

	private string _原服务器端口;

	private int _原服务器ID;

	private string _银币广场服务器IP;

	private string _银币广场服务器端口;

	private string username;

	private bool _loginout;

	private string _离线挂机;

	private int _换线中;

	private string _UserIp;

	private string _ServerID;

	private string _WorldID;

	private string _金符;

	private int _顶号;

	private string _职业;

	private int _conn;

	private bool _是否卡技能;

	private Thread _魔法攻击怪物;

	private ParameterizedThreadStart _启动魔法攻击怪物;

	private Thread _魔法攻击怪物1;

	private ParameterizedThreadStart _启动魔法攻击怪物1;

	private Thread _魔法攻击怪物2;

	private ParameterizedThreadStart _启动魔法攻击怪物2;

	private Thread _魔法攻击怪物3;

	private ParameterizedThreadStart _启动魔法攻击怪物3;

	private Thread _魔法攻击怪物4;

	private ParameterizedThreadStart _启动魔法攻击怪物4;

	private Thread _物理攻击怪物;

	private ParameterizedThreadStart _启动物理攻击怪物;

	private Thread _魔法攻击人物;

	private ParameterizedThreadStart _启动魔法攻击人物;

	private Thread _物理攻击人物;

	private ParameterizedThreadStart _启动物理攻击人物;

	private Thread _物理攻击怪物1;

	private ParameterizedThreadStart _启动物理攻击怪物1;

	public int _封包换线;

	public string UserId
	{
		get
		{
			return userid;
		}
		set
		{
			userid = value;
		}
	}

	public int 原服务器序号
	{
		get
		{
			return _原服务器序号;
		}
		set
		{
			_原服务器序号 = value;
		}
	}

	public string 原服务器IP
	{
		get
		{
			return _原服务器IP;
		}
		set
		{
			_原服务器IP = value;
		}
	}

	public int 封包换线
	{
		get
		{
			return _封包换线;
		}
		set
		{
			_封包换线 = value;
		}
	}

	public string 原服务器端口
	{
		get
		{
			return _原服务器端口;
		}
		set
		{
			_原服务器端口 = value;
		}
	}

	public int 原服务器ID
	{
		get
		{
			return _原服务器ID;
		}
		set
		{
			_原服务器ID = value;
		}
	}

	public string 银币广场服务器IP
	{
		get
		{
			return _银币广场服务器IP;
		}
		set
		{
			_银币广场服务器IP = value;
		}
	}

	public string 银币广场服务器端口
	{
		get
		{
			return _银币广场服务器端口;
		}
		set
		{
			_银币广场服务器端口 = value;
		}
	}

	public string UserNmae
	{
		get
		{
			return username;
		}
		set
		{
			username = value;
		}
	}

	public bool LoginOut
	{
		get
		{
			return _loginout;
		}
		set
		{
			_loginout = value;
		}
	}

	public string 绑定账号
	{
		get
		{
			return _绑定账号;
		}
		set
		{
			_绑定账号 = value;
		}
	}

	public string 离线挂机
	{
		get
		{
			return _离线挂机;
		}
		set
		{
			_离线挂机 = value;
		}
	}

	public int 换线中
	{
		get
		{
			return _换线中;
		}
		set
		{
			_换线中 = value;
		}
	}

	public string UserIp
	{
		get
		{
			return _UserIp;
		}
		set
		{
			_UserIp = value;
		}
	}

	public string ServerID
	{
		get
		{
			return _ServerID;
		}
		set
		{
			_ServerID = value;
		}
	}

	public string WorldID
	{
		get
		{
			return _WorldID;
		}
		set
		{
			_WorldID = value;
		}
	}

	public string 金符
	{
		get
		{
			return _金符;
		}
		set
		{
			_金符 = value;
		}
	}

	public int 顶号
	{
		get
		{
			return _顶号;
		}
		set
		{
			_顶号 = value;
		}
	}

	public string 职业
	{
		get
		{
			return _职业;
		}
		set
		{
			_职业 = value;
		}
	}

	public int conn
	{
		get
		{
			return _conn;
		}
		set
		{
			_conn = value;
		}
	}

	public TimeSpan 在线时间 => getsj();

	public bool 是否卡技能
	{
		get
		{
			return _是否卡技能;
		}
		set
		{
			_是否卡技能 = value;
		}
	}

	public Thread 魔法攻击怪物
	{
		get
		{
			return _魔法攻击怪物;
		}
		set
		{
			_魔法攻击怪物 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击怪物
	{
		get
		{
			return _启动魔法攻击怪物;
		}
		set
		{
			_启动魔法攻击怪物 = value;
		}
	}

	public Thread 魔法攻击怪物1
	{
		get
		{
			return _魔法攻击怪物1;
		}
		set
		{
			_魔法攻击怪物1 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击怪物1
	{
		get
		{
			return _启动魔法攻击怪物1;
		}
		set
		{
			_启动魔法攻击怪物1 = value;
		}
	}

	public Thread 魔法攻击怪物2
	{
		get
		{
			return _魔法攻击怪物2;
		}
		set
		{
			_魔法攻击怪物2 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击怪物2
	{
		get
		{
			return _启动魔法攻击怪物2;
		}
		set
		{
			_启动魔法攻击怪物2 = value;
		}
	}

	public Thread 魔法攻击怪物3
	{
		get
		{
			return _魔法攻击怪物3;
		}
		set
		{
			_魔法攻击怪物3 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击怪物3
	{
		get
		{
			return _启动魔法攻击怪物3;
		}
		set
		{
			_启动魔法攻击怪物3 = value;
		}
	}

	public Thread 魔法攻击怪物4
	{
		get
		{
			return _魔法攻击怪物4;
		}
		set
		{
			_魔法攻击怪物4 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击怪物4
	{
		get
		{
			return _启动魔法攻击怪物4;
		}
		set
		{
			_启动魔法攻击怪物4 = value;
		}
	}

	public Thread 物理攻击怪物
	{
		get
		{
			return _物理攻击怪物;
		}
		set
		{
			_物理攻击怪物 = value;
		}
	}

	public ParameterizedThreadStart 启动物理攻击怪物
	{
		get
		{
			return _启动物理攻击怪物;
		}
		set
		{
			_启动物理攻击怪物 = value;
		}
	}

	public Thread 魔法攻击人物
	{
		get
		{
			return _魔法攻击人物;
		}
		set
		{
			_魔法攻击人物 = value;
		}
	}

	public ParameterizedThreadStart 启动魔法攻击人物
	{
		get
		{
			return _启动魔法攻击人物;
		}
		set
		{
			_启动魔法攻击人物 = value;
		}
	}

	public Thread 物理攻击人物
	{
		get
		{
			return _物理攻击人物;
		}
		set
		{
			_物理攻击人物 = value;
		}
	}

	public ParameterizedThreadStart 启动物理攻击人物
	{
		get
		{
			return _启动物理攻击人物;
		}
		set
		{
			_启动物理攻击人物 = value;
		}
	}

	public Thread 物理攻击怪物1
	{
		get
		{
			return _物理攻击怪物1;
		}
		set
		{
			_物理攻击怪物1 = value;
		}
	}

	public ParameterizedThreadStart 启动物理攻击怪物1
	{
		get
		{
			return _启动物理攻击怪物1;
		}
		set
		{
			_启动物理攻击怪物1 = value;
		}
	}

	public playerS()
	{
		LoginOut = false;
		time = DateTime.Now;
		npcyd.Elapsed += 时间结束事件;
		npcyd.Enabled = true;
	}

	public void 换线()
	{
		换线中 = 1;
		换线timer = new System.Timers.Timer(15000.0);
		换线timer.Elapsed += 换线时间结束事件;
		换线timer.Enabled = true;
		换线timer.AutoReset = false;
	}

	public void 换线完成()
	{
		换线中 = 0;
		if (RxjhClass.IsEquals(WorldID, "0"))
		{
			RxjhClass.SetUserIdONLINE(UserId);
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(UserId);
		}
		if (换线timer != null)
		{
			换线timer.Enabled = false;
			换线timer.Close();
			换线timer.Dispose();
			换线timer = null;
		}
	}

	public void 时间结束事件(object source, ElapsedEventArgs e)
	{
		// 2025-0618 EVIAS 添加活跃检测，避免误删在线玩家
		if (conn == 1)
		{
			// 检查玩家是否真的需要清理（超过30分钟无活动）
			TimeSpan inactiveTime = DateTime.Now - time;
			if (inactiveTime.TotalMinutes > 30)
			{
				RxjhClass.SetUserIdONLINE(UserId);
				HelperTools.ConcurrentPlayerManager.RemovePlayer(UserId);
				Form1.WriteLine(3, $"玩家超时清理: {UserId}, 无活动时间: {inactiveTime.TotalMinutes:F1}分钟");
			}
			else
			{
				// 重置定时器，继续监控
				npcyd.Stop();
				npcyd.Start();
				Form1.WriteLine(3, $"玩家活跃检测: {UserId}, 继续监控");
				return;
			}
		}

		try
		{
			npcyd?.Stop();
			npcyd?.Close();
			npcyd?.Dispose();
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "清理玩家定时器", UserId);
		}
	}

	public void 换线时间结束事件(object source, ElapsedEventArgs e)
	{
		try
		{
			if (换线中 == 1)
			{
				RxjhClass.SetUserIdONLINE(UserId);
				// 2025-0618 EVIAS 使用新的玩家管理器
				HelperTools.ConcurrentPlayerManager.RemovePlayer(UserId);
			}
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "换线时间结束处理", UserId);
		}
		finally
		{
			// 2025-0618 EVIAS 安全释放换线定时器
			HelperTools.ResourceManager.SafeDispose(换线timer, $"玩家{UserId}的换线定时器");
			换线timer = null;
		}
	}

	// 2025-0618 EVIAS 更新玩家活跃时间
	public void UpdateActivity()
	{
		try
		{
			time = DateTime.Now;
			// 重置定时器
			if (npcyd != null && npcyd.Enabled)
			{
				npcyd.Stop();
				npcyd.Start();
			}
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "更新玩家活跃度", UserId);
		}
	}

	private TimeSpan getsj()
	{
		return DateTime.Now.Subtract(time);
	}

	// 2025-0618 EVIAS 安全清理玩家所有资源
	public void SafeDispose()
	{
		try
		{
			// 清理主定时器
			HelperTools.ResourceManager.SafeDispose(npcyd, $"玩家{UserId}的主定时器");
			npcyd = null;

			// 清理换线定时器
			HelperTools.ResourceManager.SafeDispose(换线timer, $"玩家{UserId}的换线定时器");
			换线timer = null;

			Form1.WriteLine(3, $"玩家资源清理完成: {UserId}");
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "玩家资源清理", UserId);
		}
	}
}

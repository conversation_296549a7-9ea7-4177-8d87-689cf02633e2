using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RxjhServer
{
    public class 反气功系统
    {
        private static double 获取反气功等级(Players player, int 反气功ID)
        {
            switch (反气功ID)
            {
                case 2001: return player.反剑_无坚不摧;
                case 2002: return player.反剑_护身罡气;
                case 2003: return player.反刀_四两千斤;
                case 2004: return player.反刀_霸气破甲;
                case 2005: return player.反枪_即月狂风;
                case 2006: return player.反枪_怒吼之意;
                case 2007: return player.反弓_无名暗矢;
                case 2008: return player.反弓_致命绝杀;
                case 2009: return player.反医_无中生有;
                case 2010: return player.反医_云心月性;
                case 2011: return player.反刺_烈日炎炎;
                case 2012: return player.反刺_连消带打;
                case 2013: return player.反乐_鸾凤和鸣;
                case 2014: return player.反乐_三潭映月;
                case 2015: return player.反韩_天魔狂血;
                case 2016: return player.反韩_霸气破甲;
                case 2017: return player.反谭_纵横无双;
                case 2018: return player.反谭_招式新法;
                case 2019: return player.反格_会心一击;
                case 2020: return player.反格_电光火石;
                case 2021: return player.反梅_愤怒爆发;
                case 2022: return player.反梅_玄武雷电;
                case 2023: return player.反卢_流星漫天;
                case 2024: return player.反卢_转攻为守;
                case 2025: return player.反神_黑花集中;
                case 2026: return player.反神_尸毒爆发;
                case 2027: return player.反气功_致残;
                default: return 0;
            }
        }

        public static double 计算反气功弱化值(Players 攻击者, Players 被攻击者, int 反气功ID)
        {
            // 只在PVP中生效
            if (攻击者 == null || 被攻击者 == null) return 0;

            // 攻击者的反气功削弱被攻击者的对应气功
            return 反气功弱化值(攻击者, 反气功ID);
        }

        public static double 反气功弱化值(Players player, int code)
        {
            if (player == null) return 0;

            double 反气功等级 = 获取反气功等级(player, code);
            if (反气功等级 <= 0) return 0;

            if (World.反气功List.TryGetValue(code, out var 配置))
            {
                return 反气功等级 * 配置.FLD_每点加成比率值;
            }

            // 使用默认比率1.0
            return 反气功等级;
        }


    }
}

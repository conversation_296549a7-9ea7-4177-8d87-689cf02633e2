using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading;

namespace RxjhServer.DbClss;

// 2025-0618 EVIAS 统计和监控数据库连接使用情况
public static class DbConnectionMonitor
{
	private static int _activeConnections = 0;
	private static int _totalConnections = 0;
	private static int _failedConnections = 0;
	private static int _successfulConnections = 0;

	public static void IncrementActive() => Interlocked.Increment(ref _activeConnections);
	public static void DecrementActive() => Interlocked.Decrement(ref _activeConnections);
	public static void IncrementTotal() => Interlocked.Increment(ref _totalConnections);
	public static void IncrementFailed() => Interlocked.Increment(ref _failedConnections);
	public static void IncrementSuccessful() => Interlocked.Increment(ref _successfulConnections);

	public static string GetStats() =>
		$"活跃连接: {_activeConnections}, 总连接: {_totalConnections}, 成功连接: {_successfulConnections}, 失败连接: {_failedConnections}";

	public static int ActiveConnections => _activeConnections;
	public static int TotalConnections => _totalConnections;
	public static int FailedConnections => _failedConnections;
	public static int SuccessfulConnections => _successfulConnections;

	// 2025-0618 EVIAS 重置统计数据，用于测试和监控
	public static void ResetStats()
	{
		_activeConnections = 0;
		_totalConnections = 0;
		_failedConnections = 0;
		_successfulConnections = 0;
	}

	// 2025-0618 EVIAS 获取详细统计报告
	public static string GetDetailedReport()
	{
		double successRate = _totalConnections > 0 ? (double)_successfulConnections / _totalConnections * 100 : 0;
		return $"数据库连接统计报告:\n" +
			   $"- 当前活跃连接: {_activeConnections}\n" +
			   $"- 总连接数: {_totalConnections}\n" +
			   $"- 成功连接: {_successfulConnections}\n" +
			   $"- 失败连接: {_failedConnections}\n" +
			   $"- 成功率: {successRate:F2}%";
	}
}

public class DbPoolClass
{
	private string conn;

	private SqlParameter[] prams;

	private int type;

	private string sql;

	private string _名字;

	public string Conn
	{
		get
		{
			return conn;
		}
		set
		{
			conn = value;
		}
	}

	public string 名字
	{
		get
		{
			return _名字;
		}
		set
		{
			_名字 = value;
		}
	}

	public SqlParameter[] Prams
	{
		get
		{
			return prams;
		}
		set
		{
			prams = value;
		}
	}

	public int Type
	{
		get
		{
			return type;
		}
		set
		{
			type = value;
		}
	}

	public string Sql
	{
		get
		{
			return sql;
		}
		set
		{
			sql = value;
		}
	}

	// 2025-0618 EVIAS 新增安全的数据库连接管理方法，确保连接正确释放
	public static int DbPoolClassRunSafe(string SqlConnection, string procName, SqlParameter[] prams, int Type, string 名字)
	{
		DbConnectionMonitor.IncrementTotal();
		try
		{
			using (SqlConnection sqlConnection = new SqlConnection(SqlConnection))
			{
				DbConnectionMonitor.IncrementActive();
				try
				{
					int result = (Type != 1) ? ((SqlDBA.RunProc(sqlConnection, procName, prams, 名字) == -1) ? (-1) : 0) : ((SqlDBA.RunProcSql(sqlConnection, procName, prams, 名字) == -1) ? (-1) : 0);
					if (result != -1)
					{
						DbConnectionMonitor.IncrementSuccessful();
					}
					else
					{
						DbConnectionMonitor.IncrementFailed();
					}
					return result;
				}
				finally
				{
					DbConnectionMonitor.DecrementActive();
				}
			}
		}
		catch (Exception ex)
		{
			DbConnectionMonitor.IncrementFailed();
			RxjhClass.HandleDatabaseException(ex, "安全数据库操作", $"存储过程: {procName}, 类型: {Type}, 玩家: {名字}");
			Form1.WriteLine(1, "DbPoolClassRunSafe出错" + ex);
			return -1;
		}
	}

	// 保持原方法不变
	public static int DbPoolClassRun(string SqlConnection, string procName, SqlParameter[] prams, int Type, string 名字)
	{
		try
		{
			SqlConnection sqlConnection = new SqlConnection(SqlConnection);
			return (Type != 1) ? ((SqlDBA.RunProc(sqlConnection, procName, prams, 名字) == -1) ? (-1) : 0) : ((SqlDBA.RunProcSql(sqlConnection, procName, prams, 名字) == -1) ? (-1) : 0);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "DbPoolClassRun出错" + ex);
			return -1;
		}
	}
}

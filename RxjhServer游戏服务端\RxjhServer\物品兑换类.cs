namespace RxjhServer;

public class 物品兑换类
{
	private int _增加;

	private int _颜色;

	private int _武勋;

	private int _元宝;

	private int _积分;

	private string _金钱;

	private int _套装;

	private string _单件物品;

	private string _需要物品;

	private string _命令;

	private string _系统公告;

	public int 增加
	{
		get
		{
			return _增加;
		}
		set
		{
			_增加 = value;
		}
	}

	public int 颜色
	{
		get
		{
			return _颜色;
		}
		set
		{
			_颜色 = value;
		}
	}

	public int 武勋
	{
		get
		{
			return _武勋;
		}
		set
		{
			_武勋 = value;
		}
	}

	public int 元宝
	{
		get
		{
			return _元宝;
		}
		set
		{
			_元宝 = value;
		}
	}

	public int 积分
	{
		get
		{
			return _积分;
		}
		set
		{
			_积分 = value;
		}
	}

	public string 金钱
	{
		get
		{
			return _金钱;
		}
		set
		{
			_金钱 = value;
		}
	}

	public int 套装
	{
		get
		{
			return _套装;
		}
		set
		{
			_套装 = value;
		}
	}

	public string 单件物品
	{
		get
		{
			return _单件物品;
		}
		set
		{
			_单件物品 = value;
		}
	}

	public string 需要物品
	{
		get
		{
			return _需要物品;
		}
		set
		{
			_需要物品 = value;
		}
	}

	public string 命令
	{
		get
		{
			return _命令;
		}
		set
		{
			_命令 = value;
		}
	}

	public string 系统公告
	{
		get
		{
			return _系统公告;
		}
		set
		{
			_系统公告 = value;
		}
	}
}

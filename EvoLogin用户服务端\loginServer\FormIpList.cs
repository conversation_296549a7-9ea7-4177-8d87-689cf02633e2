using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

public class FormIpList : Form
{
	private IContainer components;

	private ListView listView1;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 封IPToolStripMenuItem;

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormIpList));
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.封IPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader5,
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(0, 0);
            this.listView1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(796, 582);
            this.listView1.TabIndex = 3;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "用户IP";
            this.columnHeader5.Width = 124;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "绑定账号";
            this.columnHeader1.Width = 124;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "在线时长";
            this.columnHeader2.Width = 135;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "多开数量";
            this.columnHeader3.Width = 75;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "conn";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.封IPToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(115, 34);
            // 
            // 封IPToolStripMenuItem
            // 
            this.封IPToolStripMenuItem.Name = "封IPToolStripMenuItem";
            this.封IPToolStripMenuItem.Size = new System.Drawing.Size(114, 30);
            this.封IPToolStripMenuItem.Text = "封IP";
            this.封IPToolStripMenuItem.Click += new System.EventHandler(this.封IPToolStripMenuItem_Click);
            // 
            // FormIpList
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(796, 582);
            this.Controls.Add(this.listView1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "FormIpList";
            this.Text = "真实IP列表";
            this.Load += new System.EventHandler(this.FormIpList_Load);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

	}

	public FormIpList()
	{
		InitializeComponent();
	}

	private void FormIpList_Load(object sender, EventArgs e)
	{
		Bing();
	}

	private void Bing()
	{
		try
		{
			listView1.ListViewItemSorter = new ListViewColumnSorter();
			listView1.ColumnClick += ListViewHelper.ListView_ColumnClick;
			foreach (playerS item in World.privateTeams.Values)
			{
				if (item != null)
				{
					listView1.Items.Insert(listView1.Items.Count, new ListViewItem(new string[5]
					{
						item.UserIp,
						item.绑定账号,
						item.在线时间.ToString().Remove(item.在线时间.ToString().Length - 8, 8),
						查询多开数量(item.UserIp).ToString(),
						item.conn.ToString()
					}));
				}
			}
		}
		catch (Exception)
		{
		}
	}

	public static int 查询多开数量(string UserNip)
	{
		int num = 0;
		// 2025-0618 EVIAS 使用新的玩家管理器
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (item != null && RxjhClass.IsEquals(item.UserIp, UserNip) && item.离线挂机 == "0")
			{
				num++;
			}
		}
		return num;
	}

	private void 封IPToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		foreach (playerS value in World.privateTeams.Values)
		{
			if (value.UserIp == text && RxjhClass.GetUserIP(value.UserIp) == 0)
			{
				DBA.ExeSqlCommand("INSERT INTO 封IP表 (FLD_BANEDIP) VALUES ('" + value.UserIp + "')", "rxjhaccount");
			}
		}
	}
}

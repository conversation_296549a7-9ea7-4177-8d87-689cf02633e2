using System;

namespace RxjhVer23;

internal class ExtremeArenaParticipant
{
	public readonly ICompetitionPlayer Player;

	public readonly string Name;

	public readonly int Point;

	public int TotalDamage;

	public int KillCount;

	public int DeathCount;

	public int ReviveCount = 4;

	public DateTime PlayerDeathTime = DateTime.MaxValue;

	public DateTime ShowAnimationTime = DateTime.MaxValue;

	public DateTime RemindExitTime = DateTime.MaxValue;

	public ExtremeArenaParticipant(ICompetitionPlayer player)
	{
		Player = player;
		Name = player.UserName;
		Point = player.ExtremeArenaPoints;
	}
}

using System;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;

namespace loginServer.DbClss
{
    // 2025-0618 EVIAS 安全的数据库访问类，防止SQL注入
    public static class SecureDBA
    {
        // 输入验证正则表达式
        private static readonly Regex UserIdPattern = new Regex(@"^[a-zA-Z0-9_]{1,50}$", RegexOptions.Compiled);
        private static readonly Regex IpPattern = new Regex(@"^(\d{1,3}\.){3}\d{1,3}$", RegexOptions.Compiled);
        
        // 验证用户ID
        public static bool ValidateUserId(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId)) return false;
            if (userId.Length > 50) return false;
            return UserIdPattern.IsMatch(userId);
        }
        
        // 验证IP地址
        public static bool ValidateIP(string ip)
        {
            if (string.IsNullOrWhiteSpace(ip)) return false;
            return IpPattern.IsMatch(ip);
        }
        
        // 安全的账号检查方法
        public static DataTable CheckAccountSecure(string userId, string ip)
        {
            try
            {
                // 输入验证
                if (!ValidateUserId(userId))
                {
                    Form1.WriteLine(1, $"无效的用户ID: {userId}");
                    return null;
                }
                
                if (!ValidateIP(ip))
                {
                    Form1.WriteLine(1, $"无效的IP地址: {ip}");
                    return null;
                }
                
                using var connection = new SqlConnection(DBA.getstrConnection("rxjhaccount"));
                using var command = new SqlCommand("CHECK_ACCOUNT", connection);
                
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = 30;
                
                // 使用参数化查询 - 2025-0618 EVIAS 修复参数名，存储过程期望@id而不是@UserId
                command.Parameters.Add(new SqlParameter("@id", SqlDbType.VarChar, 50) { Value = userId });
                command.Parameters.Add(new SqlParameter("@ip", SqlDbType.VarChar, 15) { Value = ip });
                
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                
                connection.Open();
                adapter.Fill(dataTable);
                
                return dataTable;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"CheckAccountSecure错误: {ex.Message}");
                return null;
            }
        }
        
        // 安全的用户在线状态更新
        public static int UpdateUserOnlineStatus(string userId, bool isOnline)
        {
            try
            {
                if (!ValidateUserId(userId))
                {
                    Form1.WriteLine(1, $"无效的用户ID: {userId}");
                    return -1;
                }
                
                using var connection = new SqlConnection(DBA.getstrConnection("rxjhaccount"));
                using var command = new SqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE=@Online WHERE FLD_ID=@UserId", connection);
                
                command.Parameters.Add(new SqlParameter("@Online", SqlDbType.Bit) { Value = isOnline });
                command.Parameters.Add(new SqlParameter("@UserId", SqlDbType.VarChar, 50) { Value = userId });
                
                connection.Open();
                return command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"UpdateUserOnlineStatus错误: {ex.Message}");
                return -1;
            }
        }
        
        // 安全的登录IP更新
        public static int UpdateLoginIP(string userId, string ip)
        {
            try
            {
                if (!ValidateUserId(userId) || !ValidateIP(ip))
                {
                    Form1.WriteLine(1, $"无效的参数 UserId: {userId}, IP: {ip}");
                    return -1;
                }
                
                using var connection = new SqlConnection(DBA.getstrConnection("rxjhaccount"));
                using var command = new SqlCommand("UPDATE TBL_ACCOUNT SET FLD_LASTLOGINIP=@IP WHERE FLD_ID=@UserId", connection);
                
                command.Parameters.Add(new SqlParameter("@IP", SqlDbType.VarChar, 15) { Value = ip });
                command.Parameters.Add(new SqlParameter("@UserId", SqlDbType.VarChar, 50) { Value = userId });
                
                connection.Open();
                return command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"UpdateLoginIP错误: {ex.Message}");
                return -1;
            }
        }
        
        // 安全的封IP操作
        public static int BanIP(string ip)
        {
            try
            {
                if (!ValidateIP(ip))
                {
                    Form1.WriteLine(1, $"无效的IP地址: {ip}");
                    return -1;
                }
                
                using var connection = new SqlConnection(DBA.getstrConnection("rxjhaccount"));
                using var command = new SqlCommand("INSERT INTO 封IP表 (FLD_BANEDIP) VALUES (@IP)", connection);
                
                command.Parameters.Add(new SqlParameter("@IP", SqlDbType.VarChar, 15) { Value = ip });
                
                connection.Open();
                return command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"BanIP错误: {ex.Message}");
                return -1;
            }
        }
        
        // 安全的账号状态更新
        public static int UpdateAccountStatus(string userId, int status, string name, int lockStatus)
        {
            try
            {
                if (!ValidateUserId(userId))
                {
                    Form1.WriteLine(1, $"无效的用户ID: {userId}");
                    return -1;
                }
                
                // 验证name参数，防止过长或包含危险字符
                if (string.IsNullOrEmpty(name) || name.Length > 100)
                {
                    Form1.WriteLine(1, $"无效的name参数: {name}");
                    return -1;
                }
                
                using var connection = new SqlConnection(DBA.getstrConnection("rxjhaccount"));
                using var command = new SqlCommand(
                    "UPDATE TBL_ACCOUNT SET FLD_ZT=@Status, FLD_NAME=@Name, FLD_LOCK=@LockStatus WHERE FLD_ID=@UserId", 
                    connection);
                
                command.Parameters.Add(new SqlParameter("@Status", SqlDbType.Int) { Value = status });
                command.Parameters.Add(new SqlParameter("@Name", SqlDbType.VarChar, 100) { Value = name });
                command.Parameters.Add(new SqlParameter("@LockStatus", SqlDbType.Int) { Value = lockStatus });
                command.Parameters.Add(new SqlParameter("@UserId", SqlDbType.VarChar, 50) { Value = userId });
                
                connection.Open();
                return command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"UpdateAccountStatus错误: {ex.Message}");
                return -1;
            }
        }
    }
}

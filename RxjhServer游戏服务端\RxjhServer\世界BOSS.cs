using System;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 世界BOSS : IDisposable
{
	private readonly System.Timers.Timer 时间1;

	private System.Timers.Timer 时间2;

	private readonly DateTime kssj;

	private int kssjint = 0;

	private DateTime bossgcsj;

	private int bossgcsjint = 0;

	public 世界BOSS()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "世界BOSS");
			}
			kssj = DateTime.Now.AddMinutes(World.BOSS攻城倒计时);
			时间1 = new System.Timers.Timer(20000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "世界BOSS", "初始化", "创建世界BOSS定时器");
		}
	}

	public void 时间结束事件1(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "世界BOSS时间结束事件1");
		}
		try
		{
			int num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			kssjint = num;
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.发送其他活动开始倒计时(kssjint);
			}
			World.发送特殊公告("距离[世界BOSS]入侵,还剩下[" + kssjint + "]秒，出现在[" + 坐标Class.GetMapName(World.世界BOSS出现地图) + "]..", 6, "公告");
			if (kssjint <= 0)
			{
				时间1.Enabled = false;
				时间1.Close();
				时间1.Dispose();
				World.AddNpc(World.世界BOSS怪物ID, World.世界BOSS坐标X, World.世界BOSS坐标Y, World.世界BOSS出现地图);
				bossgcsj = DateTime.Now.AddMinutes(World.世界BOSS攻城时间);
				时间2 = new System.Timers.Timer(30000.0);
				时间2.Elapsed += 时间结束事件2;
				时间2.Enabled = true;
				时间2.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "世界BOSS", "时间结束事件1", "倒计时和BOSS生成处理");
		}
	}

	public void 时间结束事件2(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "BOSS攻城系统_时间结束事件2");
		}
		try
		{
			int num = (int)bossgcsj.Subtract(DateTime.Now).TotalSeconds;
			bossgcsjint = num;
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.系统提示("距离[世界boss]逃离,还剩下[" + bossgcsjint + "]秒，勇士们努力干吧!", 3, "系统提示");
			}
			if (bossgcsjint <= 0)
			{
				时间2.Enabled = false;
				时间2.Close();
				时间2.Dispose();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "世界BOSS", "时间结束事件2", "BOSS攻城倒计时和清理处理");
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "世界boss-Dispose");
		}
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		if (时间2 != null)
		{
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
		}
		World.世界boss = null;
		World.delBoss(World.世界BOSS出现地图, World.世界BOSS怪物ID);
		World.发送特殊公告("本次世界boss已结束。期待下次的到来！", 6, "公告");
		Form1.WriteLine(22, "世界boss活动结束");
	}
}

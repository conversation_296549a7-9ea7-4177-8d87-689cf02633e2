using System;
using System.Diagnostics;
using System.Runtime;
using System.Threading;

namespace RxjhServer
{
    // 2025-0618 EVIAS 内存监控系统
    public class MemoryMonitor
    {
        private static readonly Lazy<MemoryMonitor> _instance = new(() => new MemoryMonitor());
        public static MemoryMonitor Instance => _instance.Value;

        private readonly System.Timers.Timer _monitorTimer;
        private long _lastGCMemory = 0;
        private long _lastWorkingSet = 0;
        private DateTime _lastGCTime = DateTime.MinValue;
        private int _gcCount = 0;

        private MemoryMonitor()
        {
            // EVIAS 性能优化：每5分钟监控一次内存使用情况
            _monitorTimer = new System.Timers.Timer(300000); // 5分钟 = 300000毫秒
            _monitorTimer.Elapsed += MonitorMemory;
            _monitorTimer.AutoReset = true;
            _monitorTimer.Start();
        }

        // 获取当前内存使用情况
        public MemoryInfo GetMemoryInfo()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var gcMemory = GC.GetTotalMemory(false);
                
                return new MemoryInfo
                {
                    WorkingSet = process.WorkingSet64,
                    PrivateMemory = process.PrivateMemorySize64,
                    GCMemory = gcMemory,
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2)
                };
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"获取内存信息失败: {ex.Message}");
                return new MemoryInfo();
            }
        }

        // 获取内存统计字符串
        public string GetMemoryStats()
        {
            var info = GetMemoryInfo();
            return $"工作集:{info.WorkingSet / 1024 / 1024}MB " +
                   $"GC内存:{info.GCMemory / 1024 / 1024}MB " +
                   $"GC次数:{info.Gen0Collections}/{info.Gen1Collections}/{info.Gen2Collections}";
        }

        // 监控内存使用情况
        private void MonitorMemory(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                var info = GetMemoryInfo();
                
                // 检查内存增长
                if (_lastWorkingSet > 0)
                {
                    long workingSetDiff = info.WorkingSet - _lastWorkingSet;
                    long gcMemoryDiff = info.GCMemory - _lastGCMemory;
                    
                    // EVIAS 性能优化：调整内存增长警告阈值（5分钟间隔）
                    if (workingSetDiff > 200 * 1024 * 1024) 
                    {
                        Form1.WriteLine(1, $"内存警告: 工作集5分钟内增长 {workingSetDiff / 1024 / 1024}MB");
                    }

                    if (gcMemoryDiff > 150 * 1024 * 1024) 
                    {
                        Form1.WriteLine(1, $"内存警告: GC内存5分钟内增长 {gcMemoryDiff / 1024 / 1024}MB");
                    }
                }
                
                // EVIAS 性能优化：调整GC频率检查逻辑
                int currentGCCount = info.Gen0Collections + info.Gen1Collections + info.Gen2Collections;
                if (_gcCount > 0)
                {
                    int gcDiff = currentGCCount - _gcCount;
                    if (gcDiff > 1000) 
                    {
                        Form1.WriteLine(1, $"GC警告: 5分钟内GC {gcDiff}次，可能存在内存压力");
                    }
                }
                
                _lastWorkingSet = info.WorkingSet;
                _lastGCMemory = info.GCMemory;
                _gcCount = currentGCCount;
                
                // EVIAS 性能优化：每次监控都记录状态（5分钟一次）
                Form1.WriteLine(6, $"内存状态: {GetMemoryStats()}");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"内存监控错误: {ex.Message}");
            }
        }

        // 强制垃圾回收（谨慎使用）
        public void ForceGarbageCollection(string reason = null)
        {
            try
            {
                var beforeMemory = GC.GetTotalMemory(false);
                
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var afterMemory = GC.GetTotalMemory(false);
                long freedMemory = beforeMemory - afterMemory;
                
                string message = $"强制GC完成，释放内存: {freedMemory / 1024 / 1024}MB";
                if (!string.IsNullOrEmpty(reason))
                {
                    message += $" 原因: {reason}";
                }
                
                Form1.WriteLine(3, message);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"强制GC失败: {ex.Message}");
            }
        }

        // 检查是否需要内存清理
        public bool ShouldCleanupMemory()
        {
            try
            {
                var info = GetMemoryInfo();
                
                if (info.WorkingSet > 1024 * 1024 * 1024)
                {
                    return true;
                }
                
                if (info.GCMemory > 500 * 1024 * 1024)
                {
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        // 获取集合统计信息
        public string GetCollectionStats()
        {
            try
            {
                return $"玩家:{World.allConnectedChars?.Count ?? 0} " +
                       $"物品:{World.ItmeTeM?.Count ?? 0} " +
                       $"怪物:{World.MonSter?.Count ?? 0} " +
                       $"地图:{World.Map?.Count ?? 0} " +
                       $"SQL队列:{World.SqlPool?.Count ?? 0}";
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"获取集合统计失败: {ex.Message}");
                return "统计获取失败";
            }
        }

        public void Dispose()
        {
            _monitorTimer?.Stop();
            _monitorTimer?.Dispose();
        }
    }

    // 内存信息结构
    public struct MemoryInfo
    {
        public long WorkingSet { get; set; }
        public long PrivateMemory { get; set; }
        public long GCMemory { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
    }
}

using System.IO;
using System.Text;

namespace RxjhServer.Network;

public class PacketReader
{
	private readonly byte[] m_Data;

	private readonly int m_Size;

	private int m_Index;

	public PacketReader(byte[] data, int size, bool fixedSize)
	{
		m_Data = data;
		m_Size = size;
		m_Index = (fixedSize ? 1 : 3);
	}

	public int Seek(int offset, SeekOrigin origin)
	{
		switch (origin)
		{
		case SeekOrigin.Begin:
			m_Index = offset;
			break;
		case SeekOrigin.Current:
			m_Index += offset;
			break;
		case SeekOrigin.End:
			m_Index = m_Size - offset;
			break;
		}
		return m_Index;
	}

	public int ReadInt32()
	{
		if (m_Index + 4 > m_Size)
		{
			return 0;
		}
		return m_Data[m_Index++] | (m_Data[m_Index++] << 8) | (m_Data[m_Index++] << 16) | (m_Data[m_Index++] << 24);
	}

	public int ReadInt16()
	{
		if (m_Index + 2 > m_Size)
		{
			return 0;
		}
		return m_Data[m_Index++] | (m_Data[m_Index++] << 8);
	}

	public int ReadInt8()
	{
		if (m_Index + 1 > m_Size)
		{
			return 0;
		}
		return m_Data[m_Index++];
	}

	public string ReadString(int fixedLength)
	{
		int num = m_Index + fixedLength;
		int index = num;
		if (num > m_Size)
		{
			num = m_Size;
		}
		StringBuilder stringBuilder = new StringBuilder();
		while (m_Index < num)
		{
			byte[] data = m_Data;
			int num2;
			if ((num2 = data[m_Index++]) != 0)
			{
				stringBuilder.Append((char)num2);
				continue;
			}
			break;
		}
		m_Index = index;
		return stringBuilder.ToString();
	}
}

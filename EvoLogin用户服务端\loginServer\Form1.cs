using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using loginServer.HelperTools;

namespace loginServer;

public class Form1 : Form
{
	private static List<TxtClass> txt = new List<TxtClass>();

	private static int _scrollOffset = 0; 
	private static bool _autoScroll = true; 
	private static bool _needsRefresh = false; 

	private bool runn;

	private Thread thThreadRead;

	private Thread thThreadRead2;

	private World world;

	private System.Timers.Timer 自动查询封号;

	private System.Timers.Timer 自动查询真实IP;

	private Listener l;

	private BbcServer T;

	private IContainer components;

	private MenuStrip menuStrip1;

	private ToolStripMenuItem 用户ToolStripMenuItem;

	private ToolStripMenuItem 用户列表ToolStripMenuItem;

	private FlickerFreePanel GraphPanel;

	private System.Windows.Forms.Timer timer1;

	private ToolStripMenuItem 重读配置ToolStripMenuItem;

	private StatusStrip statusStrip1;

	private ToolStripMenuItem 公告发送ToolStripMenuItem;

	private ToolStripMenuItem iP列表ToolStripMenuItem;

	private ToolStrip toolStrip1;

	private ToolStripTextBox toolStripTextBox1;

	private ToolStripButton toolStripButton1;

	private System.Windows.Forms.Timer timer2;

	private ToolStripMenuItem 防攻击CC设置ToolStripMenuItem;

	private ToolStripMenuItem 服务器列表ToolStripMenuItem;

	private ToolStripMenuItem 封号列表查询ToolStripMenuItem;

	private ToolStripMenuItem 踢出所有用户ToolStripMenuItem;
	
    private ToolStripMenuItem 封停IP列表查询ToolStripMenuItem;

	private ToolStripMenuItem 多开IP列表查询ToolStripMenuItem;

	private ToolStripMenuItem 关于ToolStripMenuItem;

	public Form1()
	{
		InitializeComponent();

		this.statusStrip1.Location = new Point(0, this.ClientSize.Height - this.statusStrip1.Height);

		this.menuStrip1.Visible = true;
		this.toolStrip1.Visible = true;

		this.menuStrip1.Dock = DockStyle.None;
		this.toolStrip1.Dock = DockStyle.None;
		this.menuStrip1.Location = new Point(0, 0);
		this.toolStrip1.Location = new Point(this.ClientSize.Width - this.toolStrip1.Width, 0);

		this.KeyPreview = true; // 允许窗体接收键盘事件
		this.KeyDown += Form1_KeyDown;
		this.GraphPanel.MouseWheel += GraphPanel_MouseWheel;

		this.Load += (s, e) => 调整GraphPanel布局();
		this.Resize += (s, e) => 调整GraphPanel布局();
	}

	private void 调整GraphPanel布局()
	{
		try
		{
			int menuStripHeight = menuStrip1.Height;
			int statusStripHeight = statusStrip1.Height;
			int availableHeight = this.ClientSize.Height - menuStripHeight - statusStripHeight;

			GraphPanel.Location = new Point(0, menuStripHeight);
			GraphPanel.Size = new Size(this.ClientSize.Width, availableHeight);

			this.toolStrip1.Location = new Point(this.ClientSize.Width - this.toolStrip1.Width, 0);
		}
		catch (Exception ex)
		{
			Debug.WriteLine($"EvoLogin布局调整错误: {ex.Message}");
		}
	}

    // 2025-0618 EVIAS 优化控制台显示，支持滚动和更好的布局
    private void GraphPanel_Paint(object sender, PaintEventArgs e)
    {
        try
        {
            Graphics graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            graphics.PixelOffsetMode = PixelOffsetMode.None;

            // 清除背景
            graphics.Clear(GraphPanel.BackColor);

            int startY = 5;
            int lineHeight = 16; // 固定行高，避免重叠

            int connectCount = World.ConnectLst?.Count ?? 0;
            int serverCount = World.ServerLst?.Count ?? 0;
            int totalOnlineCount = HelperTools.ConcurrentPlayerManager.PlayerCount; // 总在线人数（包括所有类型）
            int offlineCount = HelperTools.ConcurrentPlayerManager.OfflinePlayerCount;
            int dummyCount = HelperTools.ConcurrentPlayerManager.DummyPlayerCount;
            int cloudCount = HelperTools.ConcurrentPlayerManager.CloudPlayerCount;
            int realIpCount = World.privateTeams?.Count ?? 0;
            int loginQueueCount = World.Connect?.Count ?? 0;
            int shoutQueueCount = World.狮子吼List?.Count ?? 0;

            string statusInfo = string.Format(
                "连接: {0} | 服务器: {1} | 在线: {2} | 离线挂机: {3} | 假人: {4} | 云挂机: {5} | 真实IP: {6} | 登陆队列: {7} | 狮子吼队列: {8}",
                connectCount, serverCount, totalOnlineCount, offlineCount, dummyCount, cloudCount, realIpCount, loginQueueCount, shoutQueueCount
            );

            using (var statusFont = GetStatusFont())
            {
                // 显示统一的状态信息
                graphics.DrawString(statusInfo, statusFont, Brushes.DarkSlateBlue, new Point(10, startY));
            }

            // 日志信息显示 - 2025-0618 EVIAS 调整间距为25
            int yPos = startY + 25; // 调整间距为25
            int maxY = GraphPanel.Height - lineHeight - 2; // 减少底部边距，增加显示空间

            using (var logFont = new Font("Consolas", 8.5f, FontStyle.Regular))
            {
                lock (txt)
                {
                    // 2025-0618 EVIAS 支持滚动的日志显示
                    // 计算可显示的行数
                    int maxLines = (maxY - yPos) / lineHeight;

                    // 根据滚动偏移量计算显示范围
                    int totalLines = txt.Count;
                    int endIndex = _autoScroll ? totalLines : Math.Max(maxLines, totalLines - _scrollOffset);
                    int startIndex = Math.Max(0, endIndex - maxLines);

                    // 确保索引有效
                    endIndex = Math.Min(endIndex, totalLines);
                    startIndex = Math.Max(0, startIndex);

                    // 从计算出的范围显示日志
                    for (int i = startIndex; i < endIndex; i++)
                    {
                        if (yPos + lineHeight > maxY)
                        {
                            break; // 确保当前行能完整显示，否则停止
                        }

                        TxtClass item = txt[i];
                        if (item?.Txt != null)
                        {
                            Brush textBrush = GetTextBrush(item.type);

                            // 处理长文本换行
                            string displayText = WrapText(graphics, item.Txt, logFont, GraphPanel.Width - 20);
                            string[] lines = displayText.Split('\n');

                            // 正序显示行
                            for (int j = 0; j < lines.Length; j++)
                            {
                                if (yPos + lineHeight > maxY)
                                {
                                    break; // 确保当前行能完整显示
                                }

                                graphics.DrawString(lines[j], logFont, textBrush, new Point(10, yPos));
                                yPos += lineHeight;
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"绘图错误: {ex.Message}");
        }
    }

    private Font GetStatusFont()
    {
        try
        {
            return new Font("Microsoft YaHei", 8.5f, FontStyle.Bold);
        }
        catch
        {
            return new Font(SystemFonts.DefaultFont.FontFamily, 8.5f, FontStyle.Bold);
        }
    }

    // 2025-0618 EVIAS 优化文本颜色显示，使用更美观的颜色
    private Brush GetTextBrush(int type)
    {
        switch (type)
        {
            case 1: return Brushes.Crimson;       // 错误信息 - 深红色
            case 2: return Brushes.ForestGreen;   // 成功信息 - 森林绿
            case 3: return Brushes.RoyalBlue;     // 登录信息 - 皇家蓝
            case 4: return Brushes.DarkOrange;    // 系统信息 - 深橙色
            case 5: return Brushes.DarkViolet;    // 特殊信息 - 深紫色
            case 6: return Brushes.Navy;          // 调试信息 - 海军蓝
            case 7: return Brushes.DimGray;       // 数据包信息 - 暗灰色
            case 8: return Brushes.DarkGreen;     // 保存信息 - 深绿色
            default: return Brushes.Black;        // 默认信息 - 黑色
        }
    }

    // 2025-0618 EVIAS 添加文本换行功能
    private string WrapText(Graphics graphics, string text, Font font, float maxWidth)
    {
        if (string.IsNullOrEmpty(text)) return text;

        string[] words = text.Split(' ');
        StringBuilder result = new StringBuilder();
        StringBuilder currentLine = new StringBuilder();

        foreach (string word in words)
        {
            string testLine = currentLine.Length == 0 ? word : currentLine + " " + word;
            SizeF size = graphics.MeasureString(testLine, font);

            if (size.Width <= maxWidth)
            {
                currentLine.Append(currentLine.Length == 0 ? word : " " + word);
            }
            else
            {
                if (currentLine.Length > 0)
                {
                    result.AppendLine(currentLine.ToString());
                    currentLine.Clear();
                }
                currentLine.Append(word);
            }
        }

        if (currentLine.Length > 0)
        {
            result.Append(currentLine.ToString());
        }

        return result.ToString();
    }

    // 2025-0618 EVIAS 优化WriteLine方法，添加时间戳和滚动控制
    public static void WriteLine(int type, string txtt)
	{
		lock (txt)
		{
			// 记录到文件日志
			switch (type)
			{
			case 1:
				logo.FileTxtLog(txtt);
				break;
			case 5:
				logo.FileTxtLog3(txtt);
				break;
			case 6:
				logo.FileTxtLog2(txtt);
				break;
			case 99:
				logo.FileTxtLog1(txtt);
				return;
			}

			// 添加时间戳到控制台显示
			string timestampedText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "  " + txtt;
			txt.Add(new TxtClass(type, timestampedText));

			if (_autoScroll)
			{
				_scrollOffset = 0; // 保持在底部
			}

			// 2025-0618 EVIAS 优化显示行数控制逻辑
			// 根据面板高度动态计算最大显示行数，最少保留100条
			int maxDisplayLines = Math.Max(100, 300); // 增加到300条，提供更多历史记录

			int count = txt.Count;
			if (count > maxDisplayLines)
			{
				int removeCount = count - maxDisplayLines;
				for (int i = 0; i < removeCount; i++)
				{
					txt.RemoveAt(0);
				}
			}

			_needsRefresh = true;
		}
	}

    // 2025-0618 EVIAS 滚动事件处理
    private void GraphPanel_MouseWheel(object sender, MouseEventArgs e)
    {
        try
        {
            int scrollLines = 3; // 每次滚动3行

            if (e.Delta > 0) // 向上滚动 - 查看更早的日志
            {
                _scrollOffset += scrollLines;
                _autoScroll = false;
            }
            else // 向下滚动 - 查看更新的日志
            {
                _scrollOffset = Math.Max(0, _scrollOffset - scrollLines);

                // 如果滚动到底部，恢复自动滚动
                if (_scrollOffset == 0)
                {
                    _autoScroll = true;
                }
            }

            // 限制滚动范围 - 2025-0618 EVIAS 使用统一的行高计算
            lock (txt)
            {
                int maxLines = (GraphPanel.Height - 50) / 16; // 使用固定行高16
                int maxOffset = Math.Max(0, txt.Count - maxLines);
                _scrollOffset = Math.Min(_scrollOffset, maxOffset);
            }

            GraphPanel.Invalidate(); // 重绘
        }
        catch (Exception ex)
        {
            WriteLine(1, $"滚动处理错误: {ex.Message}");
        }
    }

    private void Form1_KeyDown(object sender, KeyEventArgs e)
    {
        try
        {
            switch (e.KeyCode)
            {
                case Keys.Home: // Home键 - 跳到最早的日志
                    lock (txt)
                    {
                        int maxLines = (GraphPanel.Height - 50) / 16;
                        _scrollOffset = Math.Max(0, txt.Count - maxLines);
                        _autoScroll = false;
                    }
                    GraphPanel.Invalidate();
                    break;

                case Keys.End: // End键 - 跳到最新的日志
                    _scrollOffset = 0;
                    _autoScroll = true;
                    GraphPanel.Invalidate();
                    break;

                case Keys.PageUp: // PageUp - 向上翻页
                    int pageUpLines = (GraphPanel.Height - 50) / 16 - 2;
                    _scrollOffset += pageUpLines;
                    _autoScroll = false;
                    lock (txt)
                    {
                        int maxLines = (GraphPanel.Height - 50) / 16;
                        int maxOffset = Math.Max(0, txt.Count - maxLines);
                        _scrollOffset = Math.Min(_scrollOffset, maxOffset);
                    }
                    GraphPanel.Invalidate();
                    break;

                case Keys.PageDown: // PageDown - 向下翻页
                    int pageDownLines = (GraphPanel.Height - 50) / 16 - 2;
                    _scrollOffset = Math.Max(0, _scrollOffset - pageDownLines);
                    if (_scrollOffset == 0)
                    {
                        _autoScroll = true;
                    }
                    GraphPanel.Invalidate();
                    break;
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, $"键盘处理错误: {ex.Message}");
        }
    }

    private void Form1_Load(object sender, EventArgs e)
    {
        world = new World();
        world.SetConfig();
        l = new Listener((ushort)World.游戏登陆服务器端口);
        T = new BbcServer("127.0.0.1", World.帐号验证服务器端口);
        thThreadRead = new Thread(FlushAll);
        thThreadRead.Name = "FlushAll";
        thThreadRead.Start();
        thThreadRead2 = new Thread(FlushSERAll);
        thThreadRead2.Name = "FlushSERAll";
        thThreadRead2.Start();
        自动查询封号 = new System.Timers.Timer(3600000.0);
        自动查询封号.Elapsed += 查询封号;
        自动查询封号.AutoReset = true;
        自动查询封号.Enabled = true;
        自动查询真实IP = new System.Timers.Timer(60000.0);
        自动查询真实IP.Elapsed += 查询真实IP;
        自动查询真实IP.AutoReset = true;
        自动查询真实IP.Enabled = true;

        Text = Text + "_" + World.服务器名 + "_" + Assembly.GetExecutingAssembly().GetName().Version.ToString();

        初始化StatusStrip();
        调整GraphPanel布局();
    }

    private void 初始化StatusStrip()
    {
        try
        {
            statusStrip1.Items.Clear();

            var lblLoginPort = new ToolStripStatusLabel();
            lblLoginPort.Name = "lblLoginPort";
            lblLoginPort.Text = "登录器端口: " + World.游戏登陆服务器端口;
            lblLoginPort.Spring = true; 
            lblLoginPort.TextAlign = ContentAlignment.MiddleLeft;
            lblLoginPort.BorderSides = ToolStripStatusLabelBorderSides.None;
            statusStrip1.Items.Add(lblLoginPort);

            var lblAuthPort = new ToolStripStatusLabel();
            lblAuthPort.Name = "lblAuthPort";
            lblAuthPort.Text = "| 账号验证端口: " + World.帐号验证服务器端口;
            lblAuthPort.Spring = true; 
            lblAuthPort.TextAlign = ContentAlignment.MiddleLeft;
            lblAuthPort.BorderSides = ToolStripStatusLabelBorderSides.None;
            statusStrip1.Items.Add(lblAuthPort);

            var lblOnlineUsers = new ToolStripStatusLabel();
            lblOnlineUsers.Name = "lblOnlineUsers";
            lblOnlineUsers.Text = "| 在线用户: 0";
            lblOnlineUsers.Spring = true; 
            lblOnlineUsers.TextAlign = ContentAlignment.MiddleLeft;
            lblOnlineUsers.BorderSides = ToolStripStatusLabelBorderSides.None;
            statusStrip1.Items.Add(lblOnlineUsers);

            var lblUptime = new ToolStripStatusLabel();
            lblUptime.Name = "lblUptime";
            lblUptime.Text = "| 运行时间: 0天 0时 0分 0秒";
            lblUptime.Spring = true; 
            lblUptime.TextAlign = ContentAlignment.MiddleLeft;
            lblUptime.BorderSides = ToolStripStatusLabelBorderSides.None;
            statusStrip1.Items.Add(lblUptime);

        }
        catch (Exception ex)
        {
            WriteLine(1, "EvoLogin StatusStrip初始化错误: " + ex.Message);
        }
    }

    private void 查询封号(object source, ElapsedEventArgs e)
	{
		try
		{
			World.查询封号();
		}
		catch
		{
		}
	}

	private void 查询真实IP(object source, ElapsedEventArgs e)
	{
		try
		{
			World.privateTeams.Clear();
			var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
			foreach (playerS value in allPlayers)
			{
				if (!World.privateTeams.ContainsKey(value.UserIp.ToString()) && value.离线挂机 == "0")
				{
					World.privateTeams.TryAdd(value.UserIp.ToString(), value);
				}
			}
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "查询真实IP", "");
		}
	}

	private void FlushAll()
	{
		try
		{
			while (true)
			{
				Thread.Sleep(1);
				World.ProcessDisposedQueue();
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, "FlushAll  错误" + ex.Message);
			if (!runn)
			{
				thThreadRead = new Thread(FlushAll);
				thThreadRead.Name = "FlushAll";
				thThreadRead.Start();
			}
		}
	}

	private void FlushSERAll()
	{
		try
		{
			while (true)
			{
				Thread.Sleep(1);
				World.ProcessSerQueue();
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, "FlushSERAll  错误" + ex.Message);
			if (!runn)
			{
				thThreadRead2 = new Thread(FlushSERAll);
				thThreadRead2.Name = "FlushSERAll";
				thThreadRead2.Start();
			}
		}
	}

	private void timer2_Tick(object sender, EventArgs e)
	{
		World.Process狮子吼Queue();
	}

	// 2025-0618 EVIAS 添加关闭确认对话框
	private void Form1_FormClosing(object sender, FormClosingEventArgs e)
	{
		try
		{
			DialogResult result = MessageBox.Show(
				"确定要关闭EvoLogin用户服务端吗？\n\n" +
				"正在登录的用户将无法完成登录过程。\n\n" +
				"是否确认关闭？",
				"关闭确认 - EvoLogin服务端",
				MessageBoxButtons.YesNo,
				MessageBoxIcon.Question,
				MessageBoxDefaultButton.Button2  // 默认选择"否"，更安全
			);

			if (result != DialogResult.Yes)
			{
				e.Cancel = true;
				WriteLine(3, "用户取消了服务端关闭操作");
				return;
			}

			WriteLine(2, "开始关闭EvoLogin服务端...");

			runn = true;
			Thread.Sleep(10);

			if (thThreadRead != null)
			{
				thThreadRead.Abort();
				thThreadRead = null;
				WriteLine(3, "FlushAll线程已关闭");
			}

			if (thThreadRead2 != null)
			{
				thThreadRead2.Abort();
				thThreadRead2 = null;
				WriteLine(3, "FlushSERAll线程已关闭");
			}

			if (l != null)
			{
				l.Dispose();
				l = null;
				WriteLine(3, "登录服务器监听器已关闭");
			}

			if (T != null)
			{
				T.Dispose();
				T = null;
				WriteLine(3, "账号验证服务器已关闭");
			}

			// 安全关闭定时器
			if (自动查询封号 != null)
			{
				自动查询封号.Enabled = false;
				自动查询封号.Close();
				自动查询封号.Dispose();
				自动查询封号 = null;
				WriteLine(3, "自动查询封号定时器已关闭");
			}

			if (自动查询真实IP != null)
			{
				自动查询真实IP.Enabled = false;
				自动查询真实IP.Close();
				自动查询真实IP.Dispose();
				自动查询真实IP = null;
				WriteLine(3, "自动查询真实IP定时器已关闭");
			}

			WriteLine(2, "EvoLogin服务端已安全关闭");
		}
		catch (Exception ex)
		{
			WriteLine(1, $"关闭服务端时发生错误: {ex.Message}");
		}
	}

	private void 用户列表ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new userlist().ShowDialog();
	}

	private void timer1_Tick(object sender, EventArgs e)
	{
		try
		{
			if (_needsRefresh)
			{
				GraphPanel.Invalidate();
				_needsRefresh = false; // 重置标志
			}

			// 更新登录器端口信息
			var lblLoginPort = statusStrip1.Items["lblLoginPort"] as ToolStripStatusLabel;
			if (lblLoginPort != null)
				lblLoginPort.Text = "登录器端口: " + World.游戏登陆服务器端口;

			// 更新账号验证端口信息
			var lblAuthPort = statusStrip1.Items["lblAuthPort"] as ToolStripStatusLabel;
			if (lblAuthPort != null)
				lblAuthPort.Text = "| 账号验证端口: " + World.帐号验证服务器端口;

			var lblOnlineUsers = statusStrip1.Items["lblOnlineUsers"] as ToolStripStatusLabel;
			if (lblOnlineUsers != null)
			{
				int totalOnlineCount = HelperTools.ConcurrentPlayerManager.PlayerCount; // 总在线人数（包括所有类型）
				lblOnlineUsers.Text = $"| 在线用户: {totalOnlineCount}";
			}

			// 更新运行时间
			var lblUptime = statusStrip1.Items["lblUptime"] as ToolStripStatusLabel;
			if (lblUptime != null)
			{
				TimeSpan uptime = DateTime.Now.Subtract(World.StartTime);
				lblUptime.Text = $"| 运行时间: {uptime.Days}天{uptime.Hours}时{uptime.Minutes}分{uptime.Seconds}秒";
			}

			// 2025-0618 EVIAS 每分钟执行一次资源清理
			if (DateTime.Now.Second == 0)
			{
				HelperTools.ResourceManager.ManualCleanup();

				// 每10分钟输出一次资源统计
				if (DateTime.Now.Minute % 10 == 0)
				{
					string stats = HelperTools.ResourceManager.GetResourceStatistics();
					WriteLine(3, $"资源统计: {stats}");

					string playerStats = HelperTools.ConcurrentPlayerManager.GetStatistics();
					WriteLine(3, $"玩家统计: {playerStats}");
				}
			}
		}
		catch (Exception ex)
		{
			Debug.WriteLine($"EvoLogin状态栏更新错误: {ex.Message}");
		}
	}

	private void 重读配置ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		world.SetConfig();
	}

	private void 公告发送ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new FormGg().ShowDialog();
	}

	private void iP列表ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new FormIpList().ShowDialog();
	}

	// 2025-0618 EVIAS 发送十六进制数据包到所有连接的客户端
	private void toolStripButton1_Click(object sender, EventArgs e)
	{
		try
		{
			byte[] packetData = Converter.hexStringToByte(toolStripTextBox1.Text);
			int sentCount = 0;

			foreach (NetState clientConnection in World.ConnectLst.Values)
			{
				if (clientConnection != null)
				{
					clientConnection.Send(packetData, packetData.Length);
					sentCount++;
				}
			}

			WriteLine(3, $"已向 {sentCount} 个连接发送数据包: {toolStripTextBox1.Text}");
		}
		catch (Exception ex)
		{
			WriteLine(1, $"发送数据包失败: {ex.Message}");
		}
	}

	private void 踢出所有用户ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (MessageBox.Show("确定要踢出所有用户吗？..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
			{
				return;
			}
			var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers().ToList();
			foreach (playerS item in allPlayers)
			{
				if (item != null)
				{
					HelperTools.ConcurrentPlayerManager.RemovePlayer(item.UserId);
					World.服务器踢出ID(item.ServerID, item.WorldID);
					HelperTools.ResourceManager.SafeDispose(item.npcyd, $"踢出用户{item.UserId}的npcyd");
					Thread.Sleep(5);
				}
			}
		}
		catch
		{
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.用户ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iP列表ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.用户列表ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.公告发送ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.防攻击CC设置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.踢出所有用户ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.服务器列表ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封号列表查询ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封停IP列表查询ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.多开IP列表查询ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.重读配置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关于ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripTextBox1 = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripButton1 = new System.Windows.Forms.ToolStripButton();
            this.timer2 = new System.Windows.Forms.Timer(this.components);
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.GraphPanel = new loginServer.FlickerFreePanel();
            this.menuStrip1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // menuStrip1
            // 
            this.menuStrip1.Dock = System.Windows.Forms.DockStyle.None;
            this.menuStrip1.GripMargin = new System.Windows.Forms.Padding(2, 2, 0, 2);
            this.menuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.用户ToolStripMenuItem,
            this.重读配置ToolStripMenuItem,
            this.关于ToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(230, 32);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // 用户ToolStripMenuItem
            // 
            this.用户ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.iP列表ToolStripMenuItem,
            this.用户列表ToolStripMenuItem,
            this.公告发送ToolStripMenuItem,
            this.防攻击CC设置ToolStripMenuItem,
            this.踢出所有用户ToolStripMenuItem,
            this.服务器列表ToolStripMenuItem,
            this.封号列表查询ToolStripMenuItem,
            this.封停IP列表查询ToolStripMenuItem,
            this.多开IP列表查询ToolStripMenuItem});
            this.用户ToolStripMenuItem.Name = "用户ToolStripMenuItem";
            this.用户ToolStripMenuItem.Size = new System.Drawing.Size(62, 28);
            this.用户ToolStripMenuItem.Text = "用户";
            // 
            // iP列表ToolStripMenuItem
            // 
            this.iP列表ToolStripMenuItem.Name = "iP列表ToolStripMenuItem";
            this.iP列表ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.iP列表ToolStripMenuItem.Text = "真实IP列表";
            this.iP列表ToolStripMenuItem.Click += new System.EventHandler(this.iP列表ToolStripMenuItem_Click);
            // 
            // 用户列表ToolStripMenuItem
            // 
            this.用户列表ToolStripMenuItem.Name = "用户列表ToolStripMenuItem";
            this.用户列表ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.用户列表ToolStripMenuItem.Text = "用户列表";
            this.用户列表ToolStripMenuItem.Click += new System.EventHandler(this.用户列表ToolStripMenuItem_Click);
            // 
            // 公告发送ToolStripMenuItem
            // 
            this.公告发送ToolStripMenuItem.Name = "公告发送ToolStripMenuItem";
            this.公告发送ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.公告发送ToolStripMenuItem.Text = "公告发送";
            this.公告发送ToolStripMenuItem.Click += new System.EventHandler(this.公告发送ToolStripMenuItem_Click);
            // 
            // 防攻击CC设置ToolStripMenuItem
            // 
            this.防攻击CC设置ToolStripMenuItem.Name = "防攻击CC设置ToolStripMenuItem";
            this.防攻击CC设置ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.防攻击CC设置ToolStripMenuItem.Text = "防攻击CC设置";
            this.防攻击CC设置ToolStripMenuItem.Click += new System.EventHandler(this.防攻击CC设置ToolStripMenuItem_Click);
            // 
            // 踢出所有用户ToolStripMenuItem
            // 
            this.踢出所有用户ToolStripMenuItem.Name = "踢出所有用户ToolStripMenuItem";
            this.踢出所有用户ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.踢出所有用户ToolStripMenuItem.Text = "踢出所有用户";
            this.踢出所有用户ToolStripMenuItem.Click += new System.EventHandler(this.踢出所有用户ToolStripMenuItem_Click);
            // 
            // 服务器列表ToolStripMenuItem
            // 
            this.服务器列表ToolStripMenuItem.Name = "服务器列表ToolStripMenuItem";
            this.服务器列表ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.服务器列表ToolStripMenuItem.Text = "服务器列表";
            this.服务器列表ToolStripMenuItem.Click += new System.EventHandler(this.服务器列表ToolStripMenuItem_Click);
            // 
            // 封号列表查询ToolStripMenuItem
            // 
            this.封号列表查询ToolStripMenuItem.Name = "封号列表查询ToolStripMenuItem";
            this.封号列表查询ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.封号列表查询ToolStripMenuItem.Text = "封号列表查询";
            this.封号列表查询ToolStripMenuItem.Click += new System.EventHandler(this.封号列表查询ToolStripMenuItem_Click);
            // 
            // 封停IP列表查询ToolStripMenuItem
            // 
            this.封停IP列表查询ToolStripMenuItem.Name = "封停IP列表查询ToolStripMenuItem";
            this.封停IP列表查询ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.封停IP列表查询ToolStripMenuItem.Text = "封IP列表查询";
            this.封停IP列表查询ToolStripMenuItem.Click += new System.EventHandler(this.封停IP列表查询ToolStripMenuItem_Click);
            // 
            // 多开IP列表查询ToolStripMenuItem
            // 
            this.多开IP列表查询ToolStripMenuItem.Name = "多开IP列表查询ToolStripMenuItem";
            this.多开IP列表查询ToolStripMenuItem.Size = new System.Drawing.Size(234, 34);
            this.多开IP列表查询ToolStripMenuItem.Text = "多开IP列表查询";
            this.多开IP列表查询ToolStripMenuItem.Click += new System.EventHandler(this.多开IP列表查询ToolStripMenuItem_Click);
            // 
            // 重读配置ToolStripMenuItem
            // 
            this.重读配置ToolStripMenuItem.Name = "重读配置ToolStripMenuItem";
            this.重读配置ToolStripMenuItem.Size = new System.Drawing.Size(98, 28);
            this.重读配置ToolStripMenuItem.Text = "重读配置";
            this.重读配置ToolStripMenuItem.Click += new System.EventHandler(this.重读配置ToolStripMenuItem_Click);
            // 
            // 关于ToolStripMenuItem
            // 
            this.关于ToolStripMenuItem.Name = "关于ToolStripMenuItem";
            this.关于ToolStripMenuItem.Size = new System.Drawing.Size(62, 28);
            this.关于ToolStripMenuItem.Text = "关于";
            this.关于ToolStripMenuItem.Click += new System.EventHandler(this.关于ToolStripMenuItem_Click);
            // 
            // timer1
            // 
            this.timer1.Enabled = true;
            this.timer1.Interval = 500;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // toolStrip1
            // 
            this.toolStrip1.Dock = System.Windows.Forms.DockStyle.None;
            this.toolStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripTextBox1,
            this.toolStripButton1});
            this.toolStrip1.Location = new System.Drawing.Point(695, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Padding = new System.Windows.Forms.Padding(0, 0, 3, 0);
            this.toolStrip1.Size = new System.Drawing.Size(223, 33);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripTextBox1
            // 
            this.toolStripTextBox1.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.toolStripTextBox1.Name = "toolStripTextBox1";
            this.toolStripTextBox1.Size = new System.Drawing.Size(150, 33);
            // 
            // toolStripButton1
            // 
            this.toolStripButton1.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton1.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton1.Name = "toolStripButton1";
            this.toolStripButton1.Size = new System.Drawing.Size(50, 28);
            this.toolStripButton1.Text = "发送";
            this.toolStripButton1.Click += new System.EventHandler(this.toolStripButton1_Click);
            // 
            // timer2
            // 
            this.timer2.Enabled = true;
            this.timer2.Interval = 21000;
            this.timer2.Tick += new System.EventHandler(this.timer2_Tick);
            //
            // statusStrip1 - 2025-0618 EVIAS 与RxjhServer保持一致的设置
            //
            this.statusStrip1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            // 2025-0621 EVIAS 调整状态栏位置和尺寸适配755x455窗口 (455-22=433)
            this.statusStrip1.Location = new System.Drawing.Point(0, 433);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(755, 22);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.SizingGrip = false; // 禁用调整大小手柄
            this.statusStrip1.Stretch = true; // 拉伸填满整个宽度
            //
            // GraphPanel - 2025-0618 EVIAS 添加Anchor属性，与RxjhServer保持一致
            //
            this.GraphPanel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.GraphPanel.BackColor = System.Drawing.Color.White;
            this.GraphPanel.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.GraphPanel.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.GraphPanel.Location = new System.Drawing.Point(0, 33);
            this.GraphPanel.Margin = new System.Windows.Forms.Padding(4);
            this.GraphPanel.Name = "GraphPanel";
            // 2025-0621 EVIAS 调整日志面板尺寸适配755x455窗口 (755x455-33-22=400)
            this.GraphPanel.Size = new System.Drawing.Size(755, 400); // 初始大小，将在Load事件中动态调整
            this.GraphPanel.TabIndex = 0;
            this.GraphPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.GraphPanel_Paint);
            // 
            // Form1
            // 
            // 2025-0621 EVIAS 修正AutoScaleDimensions以适配100% DPI，调整窗口尺寸为755x455
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(755, 455);
            this.Controls.Add(this.GraphPanel);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.menuStrip1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MainMenuStrip = this.menuStrip1;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "Form1";
            this.Text = "EvLogin";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
    }

	private void 防攻击CC设置ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new BinIP().ShowDialog();
	}

	private void 服务器列表ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new FormServer().ShowDialog();
	}

	private void 封号列表查询ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new 封停账号窗口().ShowDialog();
	}

	private void 封停IP列表查询ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new 封停IP窗口().ShowDialog();
	}

	private void 多开IP列表查询ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		new 多开IP窗口().ShowDialog();
	}

	private void 关于ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		MessageBox.Show(
			"EvLogin 服务器管理工具\n\n" +
			"版本: " + Assembly.GetExecutingAssembly().GetName().Version.ToString() + "\n" +
			"服务器: " + World.服务器名 + "\n\n" +
			"本工具用于管理游戏登录服务器，提供用户管理、配置管理、公告发送等功能。\n\n" +
			"© 2024 EvLogin. All rights reserved.",
			"关于 EvLogin",
			MessageBoxButtons.OK,
			MessageBoxIcon.Information
		);
	}
}

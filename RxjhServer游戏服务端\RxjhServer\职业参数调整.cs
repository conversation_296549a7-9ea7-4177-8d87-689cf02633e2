using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 职业参数调整 : Form
{
	private IContainer components = null;

	private Button button1;

	private ListView listView2;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader14;

	private TextBox textBox1;

	private Label label1;

	private Label label2;

	private TextBox textBox2;

	private Label label3;

	private TextBox textBox3;

	private ToolTip toolTip1;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private TextBox textBox4;

	private Label label5;

	private ComboBox comboBox1;

	private Label label20;

	private ColumnHeader columnHeader3;

	private TextBox textBox5;

	private TextBox textBox6;

	private Label label6;

	private Label label7;

	private Label label4;

	public 职业参数调整()
	{
		InitializeComponent();
	}

	private void 职业参数调整_Load(object sender, EventArgs e)
	{
		comboBox1.SelectedIndex = 0;
	}

	private string 职业(int 类型)
	{
		string result = "错误职业";
		switch (类型)
		{
		case 1:
			result = "刀客";
			break;
		case 2:
			result = "剑客";
			break;
		case 3:
			result = "枪客";
			break;
		case 4:
			result = "弓箭手";
			break;
		case 5:
			result = "医生";
			break;
		case 6:
			result = "刺客";
			break;
		case 7:
			result = "琴师";
			break;
		case 8:
			result = "韩飞官";
			break;
		case 9:
			result = "谭花灵";
			break;
		case 10:
			result = "格斗家";
			break;
		case 11:
			result = "梅柳真";
			break;
		case 12:
			result = "卢风郎";
			break;
		case 13:
			result = "东陵神女";
			break;
		}
		return result;
	}

	private string 转数(int 类型)
	{
		string result = "没转职";
		switch (类型)
		{
		case 1:
			result = "一转";
			break;
		case 2:
			result = "二转";
			break;
		case 3:
			result = "三转";
			break;
		case 4:
			result = "四转";
			break;
		case 5:
			result = "五转";
			break;
		case 6:
			result = "六转";
			break;
		case 7:
			result = "七转";
			break;
		case 8:
			result = "八转";
			break;
		case 9:
			result = "九转";
			break;
		case 10:
			result = "十转";
			break;
		case 11:
			result = "十一转";
			break;
		}
		return result;
	}

	public void 刷新(int JOB)
	{
		JOB++;
		listView2.Items.Clear();
		string sqlCommand = string.Format("SELECT * FROM TBL_XWWL_职业参数 where FLD_职业=" + JOB);
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			ListViewItem listViewItem = new ListViewItem();
			listViewItem.SubItems.Clear();
			listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["ID"].ToString();
			string text = 转数(int.Parse(dBToDataTable.Rows[i]["FLD_几转"].ToString()));
			listViewItem.SubItems.Add(text);
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["物理对怪伤害"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["物理对人伤害"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["技能对怪伤害"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["技能对人伤害"].ToString());
			string text2 = 职业(int.Parse(dBToDataTable.Rows[i]["FLD_职业"].ToString()));
			listViewItem.SubItems.Add(text2);
			listView2.Items.Add(listViewItem);
		}
		dBToDataTable.Dispose();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			int num = 0;
			switch (textBox6.Text)
			{
			case "刀客":
				num = 1;
				break;
			case "剑客":
				num = 2;
				break;
			case "枪客":
				num = 3;
				break;
			case "弓箭手":
				num = 4;
				break;
			case "医生":
				num = 5;
				break;
			case "刺客":
				num = 6;
				break;
			case "琴师":
				num = 7;
				break;
			case "韩飞官":
				num = 8;
				break;
			case "谭花灵":
				num = 9;
				break;
			case "格斗家":
				num = 10;
				break;
			case "梅柳真":
				num = 11;
				break;
			case "卢风郎":
				num = 12;
				break;
			case "东陵神女":
				num = 13;
				break;
			}
			int 几转 = 0;
			switch (textBox5.Text)
			{
			case "一转":
				几转 = 1;
				break;
			case "二转":
				几转 = 2;
				break;
			case "三转":
				几转 = 3;
				break;
			case "四转":
				几转 = 4;
				break;
			case "五转":
				几转 = 5;
				break;
			case "六转":
				几转 = 6;
				break;
			case "七转":
				几转 = 7;
				break;
			case "八转":
				几转 = 8;
				break;
			case "九转":
				几转 = 9;
				break;
			case "十转":
				几转 = 10;
				break;
			case "十一转":
				几转 = 11;
				break;
			}
			写数据(num, 几转, textBox1.Text, textBox2.Text, textBox3.Text, textBox4.Text);
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			listView2.Items[selectedIndices[0]].SubItems[6].Text = textBox6.Text;
			listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox5.Text;
			listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox1.Text;
			listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox2.Text;
			listView2.Items[selectedIndices[0]].SubItems[4].Text = textBox3.Text;
			listView2.Items[selectedIndices[0]].SubItems[5].Text = textBox4.Text;
			new World().加载职业系数();
			刷新(num - 1);
		}
		catch
		{
			MessageBox.Show("输入错误,请检查!");
		}
	}

	private void listView2_MouseClick(object sender, MouseEventArgs e)
	{
		ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
		textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
		textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
		textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
		textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
		textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
		textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
	}

	private int 写数据(int 职业, int 几转, string 物理对怪, string 物理对人, string 技能对怪, string 技能对人)
	{
		string sqlCommand = string.Format("UPDATE TBL_XWWL_职业参数 SET 物理对怪伤害='{2}', 物理对人伤害='{3}', 技能对怪伤害='{4}',技能对人伤害='{5}' WHERE FLD_职业={0} AND  FLD_几转={1}", 职业, 几转, 物理对怪, 物理对人, 技能对怪, 技能对人);
		try
		{
			int num = DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			if (num == -1)
			{
				MessageBox.Show("写入表错误1,请检查|");
				return -1;
			}
		}
		catch
		{
			MessageBox.Show("写入表错误2,请检查|");
		}
		return -1;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(职业参数调整));
            this.button1 = new System.Windows.Forms.Button();
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.label4 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label20 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(596, 169);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(111, 29);
            this.button1.TabIndex = 54;
            this.button1.Text = "修改立即生效";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14,
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(12, 42);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(558, 174);
            this.listView2.TabIndex = 55;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listView2_MouseClick);
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "id";
            this.columnHeader11.Width = 40;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "职业转数";
            this.columnHeader12.Width = 84;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "物理对怪伤害";
            this.columnHeader13.Width = 84;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "物理对人伤害";
            this.columnHeader14.Width = 84;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "技能对怪伤害";
            this.columnHeader1.Width = 84;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "技能对人伤害";
            this.columnHeader2.Width = 84;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "职业名称";
            this.columnHeader3.Width = 84;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(647, 62);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(66, 21);
            this.textBox1.TabIndex = 56;
            this.toolTip1.SetToolTip(this.textBox1, "对应石头属性类型");
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(589, 67);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 57;
            this.label1.Text = "物理对人";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(589, 95);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 59;
            this.label2.Text = "物理对怪";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(648, 89);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(66, 21);
            this.textBox2.TabIndex = 58;
            this.toolTip1.SetToolTip(this.textBox2, "对应石头属性数量");
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(589, 122);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 61;
            this.label3.Text = "技能对怪";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(647, 116);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(66, 21);
            this.textBox3.TabIndex = 60;
            this.toolTip1.SetToolTip(this.textBox3, "负数减反之加");
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.ForeColor = System.Drawing.Color.Red;
            this.label4.Location = new System.Drawing.Point(588, 203);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(125, 12);
            this.label4.TabIndex = 63;
            this.label4.Text = "鼠标点放这里查看类型";
            this.toolTip1.SetToolTip(this.label4, "\"平砍对怪伤害\":1\r\n\"对砍对人伤害\":2\r\n\"技能对怪伤害\":3\r\n\"技能对人伤害\":4\r\n\"1.0倍不增加\":5  \r\n\"越大伤害越高\":6\r\n\"小于1.0" +
        "伤害降低\":7\r\n\"可以设置每转\":9   \r\n\"对应的伤害值\":10  \r\n\"重读配置即可生效\" ");
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(648, 143);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(66, 21);
            this.textBox4.TabIndex = 64;
            this.toolTip1.SetToolTip(this.textBox4, "负数减反之加");
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(648, 35);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(66, 21);
            this.textBox5.TabIndex = 68;
            this.toolTip1.SetToolTip(this.textBox5, "负数减反之加");
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(648, 8);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(66, 21);
            this.textBox6.TabIndex = 69;
            this.toolTip1.SetToolTip(this.textBox6, "负数减反之加");
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(589, 149);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 65;
            this.label5.Text = "技能对人";
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "刀客",
            "剑客",
            "枪客",
            "弓箭手",
            "医生",
            "刺客",
            "乐师",
            "韩飞官",
            "谭花灵",
            "格斗家",
            "梅柳真",
            "卢风朗",
            "东陵神女"});
            this.comboBox1.Location = new System.Drawing.Point(72, 11);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(93, 20);
            this.comboBox1.TabIndex = 66;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(12, 15);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(59, 12);
            this.label20.TabIndex = 67;
            this.label20.Text = "职业选择:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(588, 13);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(29, 12);
            this.label6.TabIndex = 70;
            this.label6.Text = "职业";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(588, 39);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 71;
            this.label7.Text = "几转";
            // 
            // 职业参数调整
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(726, 222);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.textBox6);
            this.Controls.Add(this.textBox5);
            this.Controls.Add(this.label20);
            this.Controls.Add(this.comboBox1);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.textBox4);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.button1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "职业参数调整";
            this.Text = "职业参数调整";
            this.Load += new System.EventHandler(this.职业参数调整_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		刷新(comboBox1.SelectedIndex);
	}
}

using System;
using System.Timers;

namespace RxjhServer;

public class BossClass : IDisposable
{
	private System.Timers.Timer 南林钟离开始时间公告时间间隔;

	private System.Timers.Timer 南林钟离开始时间间隔;

	private System.Timers.Timer 南林钟离结束时间间隔;

	private DateTime 南林钟离开始倒计时间;

	private float x = 0f;

	private float y = 0f;

	public BossClass()
	{
		南林钟离开始倒计时间 = DateTime.Now.AddMinutes(5.0);
		南林钟离开始时间公告时间间隔 = new System.Timers.Timer(60000.0);
		南林钟离开始时间公告时间间隔.Elapsed += 发送距离南林钟离开始时间公告;
		南林钟离开始时间公告时间间隔.Enabled = true;
		南林钟离开始时间公告时间间隔.AutoReset = true;
		发送距离南林钟离开始时间公告(null, null);
		南林钟离开始时间间隔 = new System.Timers.Timer(300000.0);
		南林钟离开始时间间隔.Elapsed += 南林钟离开始;
		南林钟离开始时间间隔.Enabled = true;
	}

	public void 发送距离南林钟离开始时间公告(object y1, ElapsedEventArgs y2)
	{
		try
		{
			int num = (int)南林钟离开始倒计时间.Subtract(DateTime.Now).TotalSeconds;
			if (num == 0 || num <= 60)
			{
				return;
			}
			string[] array = World.南林钟离一参数.Split(';');
			try
			{
				if (World.MonSter.TryGetValue(int.Parse(array[0]), out var value))
				{
					World.发送特殊公告(num / 60 + "分后，在" + World.服务器ID + "线" + 坐标Class.getmapname(int.Parse(array[1])) + "将刷新" + value.Name + "BOSS", 6, "公告");
				}
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "南林钟离 时间结束事件1 出错：" + ex);
			}
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "南林钟离 时间结束事件1 出错：" + ex2);
		}
	}

	public void 南林钟离开始(object y1, ElapsedEventArgs y2)
	{
		南林钟离开始时间公告时间间隔.Enabled = false;
		南林钟离开始时间公告时间间隔.Close();
		南林钟离开始时间公告时间间隔.Dispose();
		南林钟离开始时间间隔.Enabled = false;
		南林钟离开始时间间隔.Close();
		南林钟离开始时间间隔.Dispose();
		南林钟离结束时间间隔 = new System.Timers.Timer((double)World.南林钟离总时间 * 60000.0);
		南林钟离结束时间间隔.Elapsed += 南林钟离战结束;
		南林钟离结束时间间隔.Enabled = true;
		switch (RNG.Next(1, 24))
		{
		case 1:
			x = -1885f;
			y = -105f;
			break;
		case 2:
			x = -1705f;
			y = -431f;
			break;
		case 3:
			x = -1785f;
			y = -1142f;
			break;
		case 4:
			x = -2109f;
			y = -1609f;
			break;
		case 5:
			x = -1536f;
			y = -2005f;
			break;
		case 6:
			x = -863f;
			y = -1498f;
			break;
		case 7:
			x = 836f;
			y = -1717f;
			break;
		case 8:
			x = 1613f;
			y = -1567f;
			break;
		case 9:
			x = 2272f;
			y = -2128f;
			break;
		case 10:
			x = 1456f;
			y = -176f;
			break;
		case 11:
			x = 2032f;
			y = -112f;
			break;
		case 12:
			x = 1408f;
			y = 208f;
			break;
		case 13:
			x = 1984f;
			y = 1184f;
			break;
		case 14:
			x = 1232f;
			y = 1328f;
			break;
		case 15:
			x = 1008f;
			y = 1536f;
			break;
		case 16:
			x = 752f;
			y = 1504f;
			break;
		case 17:
			x = 320f;
			y = 1936f;
			break;
		case 18:
			x = 80f;
			y = 1856f;
			break;
		case 19:
			x = -560f;
			y = 1856f;
			break;
		case 20:
			x = -1120f;
			y = 992f;
			break;
		case 21:
			x = -2288f;
			y = 2020f;
			break;
		case 22:
			x = -2160f;
			y = 1600f;
			break;
		case 23:
			x = -1840f;
			y = 912f;
			break;
		}
		string[] array = World.南林钟离一参数.Split(';');
		World.AddBossEveNpc(int.Parse(array[0]), x, y, int.Parse(array[1]));
		try
		{
			if (World.MonSter.TryGetValue(int.Parse(array[0]), out var value))
			{
				World.发送特殊公告("现在" + 坐标Class.getmapname(int.Parse(array[1])) + "出现偷灵宠（蛋）的" + value.Name + "，去南林寻找钟离吧。", 6, "公告");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "南林钟离 时间结束事件1 出错：" + ex);
		}
	}

	public void 南林钟离战结束(object y1, ElapsedEventArgs y2)
	{
		南林钟离结束时间间隔.Enabled = false;
		南林钟离结束时间间隔.Close();
		南林钟离结束时间间隔.Dispose();
		Dispose();
	}

	public void Dispose()
	{
		if (南林钟离开始时间公告时间间隔 != null)
		{
			南林钟离开始时间公告时间间隔.Enabled = false;
			南林钟离开始时间公告时间间隔.Close();
			南林钟离开始时间公告时间间隔.Dispose();
		}
		if (南林钟离开始时间间隔 != null)
		{
			南林钟离开始时间间隔.Enabled = false;
			南林钟离开始时间间隔.Close();
			南林钟离开始时间间隔.Dispose();
		}
		if (南林钟离结束时间间隔 != null)
		{
			南林钟离结束时间间隔.Enabled = false;
			南林钟离结束时间间隔.Close();
			南林钟离结束时间间隔.Dispose();
		}
		World.南林钟离 = null;
	}
}

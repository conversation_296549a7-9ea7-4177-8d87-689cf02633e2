using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class BBGEdit : Form
{
	private IContainer components = null;

	private GroupBox groupBox1;

	private Label label28;

	private Label label27;

	private ListBox listBox1;

	private GroupBox groupBox3;

	private Button button2;

	private TextBox textBox15;

	private GroupBox groupBox2;

	private Label label20;

	private Button button1;

	private TextBox textBox14;

	private TextBox textBox12;

	private TextBox textBox11;

	private TextBox textBox10;

	private TextBox textBox9;

	private TextBox textBox8;

	private TextBox textBox7;

	private TextBox textBox6;

	private TextBox textBox5;

	private TextBox textBox4;

	private TextBox textBox3;

	private TextBox textBox1;

	private Label label25;

	private Label label13;

	private Label label12;

	private Label label11;

	private Label label10;

	private Label label8;

	private Label label7;

	private Label label6;

	private Label label5;

	private Label label4;

	private Label label2;

	private Label label1;

	private Button button4;

	private Button button5;

	private TextBox textBox18;

	private Label label16;

	private TextBox textBox17;

	private Label label15;

	private TextBox textBox16;

	private Label label14;

	private TextBox textBox13;

	private Label label9;

	private TextBox textBox2;

	private ComboBox comboBox1;

	private Label label17;

	private Button button3;

	private Button button6;

	private ComboBox comboBox11;

	private Label label40;

	private ListBox listBox3;

	private TextBox textBox20;

	private Label label19;

	private TextBox textBox19;

	private Label label18;

	private TextBox textBox21;

	private Label label21;

	private Label label3;

	public BBGEdit()
	{
		InitializeComponent();
		label28.Text = "0";
	}

	private void 刷新()
	{
		try
		{
			listBox1.Items.Clear();
			string sqlCommand = "select FLD_PID,FLD_NAME from ITEMSELL";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
				listBox1.Items.Add(keyValuePair);
			}
			label28.Text = dBToDataTable.Rows.Count.ToString();
			dBToDataTable.Dispose();
		}
		catch
		{
		}
	}

	private void button3_Click(object sender, EventArgs e)
	{
		刷新();
	}

	private void listBox1_SelectedValueChanged(object sender, EventArgs e)
	{
		try
		{
			KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBox1.SelectedItem;
			textBox1.Text = keyValuePair.Key;
			textBox3.Text = keyValuePair.Value;
			string sqlCommand = "select * from ITEMSELL where FLD_PID='" + textBox1.Text + "'";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
			if (dBToDataTable != null)
			{
				textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
				textBox3.Text = dBToDataTable.Rows[0]["FLD_NAME"].ToString();
				textBox4.Text = dBToDataTable.Rows[0]["FLD_PRICE"].ToString();
				textBox5.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
				textBox6.Text = dBToDataTable.Rows[0]["FLD_RETURN"].ToString();
				textBox7.Text = dBToDataTable.Rows[0]["FLD_NUMBER"].ToString();
				textBox8.Text = dBToDataTable.Rows[0]["FLD_MAGIC0"].ToString();
				textBox9.Text = dBToDataTable.Rows[0]["FLD_MAGIC1"].ToString();
				textBox10.Text = dBToDataTable.Rows[0]["FLD_MAGIC2"].ToString();
				textBox11.Text = dBToDataTable.Rows[0]["FLD_MAGIC3"].ToString();
				textBox12.Text = dBToDataTable.Rows[0]["FLD_MAGIC4"].ToString();
				textBox2.Text = dBToDataTable.Rows[0]["FLD_初级附魂"].ToString();
				textBox13.Text = dBToDataTable.Rows[0]["FLD_中级附魂"].ToString();
				textBox16.Text = dBToDataTable.Rows[0]["FLD_进化"].ToString();
				textBox17.Text = dBToDataTable.Rows[0]["FLD_绑定"].ToString();
				textBox18.Text = dBToDataTable.Rows[0]["FLD_DAYS"].ToString();
				textBox14.Text = dBToDataTable.Rows[0]["FLD_DESC"].ToString();
				textBox19.Text = dBToDataTable.Rows[0]["FLD_披风"].ToString();
				textBox20.Text = dBToDataTable.Rows[0]["FLD_JF"].ToString();
				DateTime dateTime = DateTime.Parse(dBToDataTable.Rows[0]["FLD_TIME"].ToString());
				if (dateTime > DateTime.Now)
				{
					double totalHours = (dateTime - DateTime.Now).TotalHours;
					textBox21.Text = ((int)totalHours).ToString();
				}
				else
				{
					textBox21.Text = "0";
				}
			}
			dBToDataTable.Dispose();
		}
		catch (Exception)
		{
			MessageBox.Show("未知错误!", "提示");
		}
	}

	private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = ((ItemDef.MyItem)comboBox11.SelectedItem).Value.ToString();
		listBox3.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			listBox3.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
	}

	private void button2_Click(object sender, EventArgs e)
	{
		listBox1.Items.Clear();
		if (comboBox1.SelectedIndex != -1)
		{
			try
			{
				string text = "";
				int result;
				switch (comboBox1.SelectedIndex)
				{
				case 0:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品ID!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) <= 0)
					{
						MessageBox.Show("必须是正整数");
						return;
					}
					int num2 = Convert.ToInt32(textBox15.Text);
					text = $"select * from ITEMSELL where FLD_PID={num2}";
					break;
				}
				case 1:
					text = "select * from ITEMSELL where FLD_NAME like '%" + textBox15.Text + "%'";
					break;
				case 2:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num = Convert.ToInt32(textBox15.Text);
					text = $"select * from ITEMSELL where FLD_TYPE={num}";
					break;
				}
				default:
					text = "select * from ITEMSELL";
					break;
				}
				DataTable dBToDataTable = DBA.GetDBToDataTable(text, "BBG");
				if (dBToDataTable != null)
				{
					for (int i = 0; i < dBToDataTable.Rows.Count; i++)
					{
						KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
						listBox1.Items.Add(keyValuePair);
					}
					label28.Text = dBToDataTable.Rows.Count.ToString();
				}
				else
				{
					MessageBox.Show("无此物品,请检查PID是否正确！", "提示");
				}
				dBToDataTable.Dispose();
				return;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.ToString(), "错误");
				return;
			}
		}
		MessageBox.Show("请选择查询的类型", "提示");
	}

	public void 加载百宝阁()
	{
		string sqlCommand = "SELECT * FROM ITEMSELL";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载百宝阁物品----没有百宝阁数据");
		}
		else
		{
			World.百宝阁属性物品类list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				百宝阁类 百宝阁类2 = new 百宝阁类();
				百宝阁类2.PID = (int)dBToDataTable.Rows[i]["FLD_PID"];
				百宝阁类2.NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
				百宝阁类2.PRICE = int.Parse(dBToDataTable.Rows[i]["FLD_PRICE"].ToString());
				百宝阁类2.DESC = dBToDataTable.Rows[i]["FLD_DESC"].ToString();
				百宝阁类2.TYPE = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
				百宝阁类2.RETURN = (int)dBToDataTable.Rows[i]["FLD_RETURN"];
				百宝阁类2.NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"];
				百宝阁类2.MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
				百宝阁类2.MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
				百宝阁类2.MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
				百宝阁类2.MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
				百宝阁类2.MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
				百宝阁类2.觉醒 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"];
				百宝阁类2.中级魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"];
				百宝阁类2.进化 = (int)dBToDataTable.Rows[i]["FLD_进化"];
				百宝阁类2.绑定 = (int)dBToDataTable.Rows[i]["FLD_绑定"];
				百宝阁类2.使用天数 = (int)dBToDataTable.Rows[i]["FLD_DAYS"];
				百宝阁类2.时间 = DateTime.Parse(dBToDataTable.Rows[i]["FLD_TIME"].ToString());
				if (百宝阁类2.PID > 0)
				{
					World.百宝阁属性物品类list.TryAdd(百宝阁类2.PID, 百宝阁类2);
				}
			}
		}
		dBToDataTable.Dispose();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (listBox1.Items.Count == 0)
		{
			MessageBox.Show("请先查询数据库!", "提示");
			return;
		}
		if (textBox1.Text == "")
		{
			MessageBox.Show("请先选择要修改的物品!", "提示");
			return;
		}
		if (textBox14.Text.Length > 120)
		{
			MessageBox.Show("装备说明不能超过120个文字!");
			return;
		}
		if (textBox3.Text.Length > 15)
		{
			MessageBox.Show("装备名称不能超过15个文字!");
			return;
		}
		try
		{
			string sqlCommand = string.Format("UPDATE ITEMSELL  SET FLD_NAME='{1}',FLD_PRICE={2}, FLD_TYPE={3},FLD_RETURN={4},FLD_NUMBER={5},FLD_MAGIC0={6},FLD_MAGIC1={7},FLD_MAGIC2={8},FLD_MAGIC3={9},FLD_MAGIC4={10},FLD_DESC='{11}',FLD_初级附魂={12},FLD_中级附魂={13},FLD_进化={14},FLD_绑定={15},FLD_DAYS={16},FLD_披风={17},FLD_JF={18},FLD_TIME='{19}' WHERE FLD_PID={0}", int.Parse(textBox1.Text), textBox3.Text, int.Parse(textBox4.Text), int.Parse(textBox5.Text), int.Parse(textBox6.Text), int.Parse(textBox7.Text), int.Parse(textBox8.Text), int.Parse(textBox9.Text), int.Parse(textBox10.Text), int.Parse(textBox11.Text), int.Parse(textBox12.Text), textBox14.Text, int.Parse(textBox2.Text), int.Parse(textBox13.Text), int.Parse(textBox16.Text), int.Parse(textBox17.Text), int.Parse(textBox18.Text), int.Parse(textBox19.Text), int.Parse(textBox20.Text), DateTime.Now.AddHours(int.Parse(textBox21.Text)));
			DBA.ExeSqlCommand(sqlCommand, "BBG");
			MessageBox.Show("修改成功!");
			加载百宝阁();
		}
		catch (Exception)
		{
			MessageBox.Show("修改出错!");
		}
	}

	private void button4_Click(object sender, EventArgs e)
	{
		if (listBox1.Items.Count == 0)
		{
			MessageBox.Show("请先查询数据库!", "提示");
			return;
		}
		if (textBox1.Text == "")
		{
			MessageBox.Show("请先选择要修改的物品!", "提示");
			return;
		}
		if (textBox14.Text.Length > 120)
		{
			MessageBox.Show("装备说明不能超过120个文字!");
			return;
		}
		if (textBox3.Text.Length > 15)
		{
			MessageBox.Show("装备名称不能超过15个文字!");
			return;
		}
		try
		{
			for (int i = 0; i < listBox1.Items.Count; i++)
			{
				if (listBox1.Items[i].Equals(textBox3.Text))
				{
					MessageBox.Show("物品重复请重新添加!");
					return;
				}
			}
			string sqlCommand = $"insert INTO ITEMSELL ( FLD_PID,FLD_NAME, FLD_PRICE,FLD_TYPE,FLD_RETURN,FLD_NUMBER,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_DESC,FLD_初级附魂,FLD_中级附魂,FLD_进化,FLD_绑定,FLD_DAYS,FLD_披风,FLD_JF,FLD_TIME)VALUES({int.Parse(textBox1.Text)},'{textBox3.Text}',{int.Parse(textBox4.Text)},{int.Parse(textBox5.Text)},{int.Parse(textBox6.Text)},{int.Parse(textBox7.Text)},{int.Parse(textBox8.Text)},{int.Parse(textBox9.Text)},{int.Parse(textBox10.Text)},{int.Parse(textBox11.Text)},{int.Parse(textBox12.Text)},'{textBox14.Text}',{int.Parse(textBox2.Text)},{int.Parse(textBox13.Text)},{int.Parse(textBox16.Text)},{int.Parse(textBox17.Text)},{int.Parse(textBox18.Text)},{int.Parse(textBox19.Text)},{int.Parse(textBox20.Text)},'{DateTime.Now.AddHours(int.Parse(textBox21.Text))}')";
			DBA.ExeSqlCommand(sqlCommand, "BBG");
			MessageBox.Show("添加成功!");
			加载百宝阁();
			刷新();
		}
		catch (Exception)
		{
			MessageBox.Show("添加出错!");
		}
	}

	private void button5_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(listBox1.Text) && listBox1.Items.Count > 0)
		{
			string sqlCommand = "delete  from ITEMSELL where FLD_NAME='" + textBox3.Text + "'";
			if (DBA.ExeSqlCommand(sqlCommand, "BBG") == -1)
			{
				MessageBox.Show("删除错误!");
				return;
			}
			加载百宝阁();
			刷新();
			MessageBox.Show("删除成功!");
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BBGEdit));
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label28 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.listBox1 = new System.Windows.Forms.ListBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label17 = new System.Windows.Forms.Label();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.textBox15 = new System.Windows.Forms.TextBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.textBox21 = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.textBox20 = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.textBox19 = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.button6 = new System.Windows.Forms.Button();
            this.textBox18 = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.textBox17 = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.textBox16 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.button5 = new System.Windows.Forms.Button();
            this.button4 = new System.Windows.Forms.Button();
            this.label20 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.textBox14 = new System.Windows.Forms.TextBox();
            this.textBox12 = new System.Windows.Forms.TextBox();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.textBox10 = new System.Windows.Forms.TextBox();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.comboBox11 = new System.Windows.Forms.ComboBox();
            this.label40 = new System.Windows.Forms.Label();
            this.listBox3 = new System.Windows.Forms.ListBox();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label28);
            this.groupBox1.Controls.Add(this.label27);
            this.groupBox1.Controls.Add(this.listBox1);
            this.groupBox1.Location = new System.Drawing.Point(12, 78);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(229, 585);
            this.groupBox1.TabIndex = 19;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "物品列表";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.ForeColor = System.Drawing.Color.Red;
            this.label28.Location = new System.Drawing.Point(71, 561);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(47, 12);
            this.label28.TabIndex = 14;
            this.label28.Text = "label28";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(10, 561);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(59, 12);
            this.label27.TabIndex = 13;
            this.label27.Text = "物品总数:";
            // 
            // listBox1
            // 
            this.listBox1.FormattingEnabled = true;
            this.listBox1.ItemHeight = 12;
            this.listBox1.Location = new System.Drawing.Point(8, 32);
            this.listBox1.Name = "listBox1";
            this.listBox1.Size = new System.Drawing.Size(213, 520);
            this.listBox1.TabIndex = 1;
            this.listBox1.Click += new System.EventHandler(this.listBox1_SelectedValueChanged);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.comboBox1);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.button3);
            this.groupBox3.Controls.Add(this.button2);
            this.groupBox3.Controls.Add(this.textBox15);
            this.groupBox3.Location = new System.Drawing.Point(12, 10);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(484, 48);
            this.groupBox3.TabIndex = 21;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "百宝阁内物品";
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "PID",
            "物品名",
            "物品种类"});
            this.comboBox1.Location = new System.Drawing.Point(181, 19);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(94, 20);
            this.comboBox1.TabIndex = 56;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(110, 23);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(65, 12);
            this.label17.TabIndex = 55;
            this.label17.Text = "按条件查找";
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(10, 16);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(92, 23);
            this.button3.TabIndex = 54;
            this.button3.Text = "查看所有物品";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(394, 18);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(67, 23);
            this.button2.TabIndex = 9;
            this.button2.Text = "查找";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // textBox15
            // 
            this.textBox15.Location = new System.Drawing.Point(281, 19);
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new System.Drawing.Size(107, 21);
            this.textBox15.TabIndex = 6;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.textBox21);
            this.groupBox2.Controls.Add(this.label21);
            this.groupBox2.Controls.Add(this.textBox20);
            this.groupBox2.Controls.Add(this.label19);
            this.groupBox2.Controls.Add(this.textBox19);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.button6);
            this.groupBox2.Controls.Add(this.textBox18);
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.textBox17);
            this.groupBox2.Controls.Add(this.label15);
            this.groupBox2.Controls.Add(this.textBox16);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.textBox13);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.textBox2);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.button5);
            this.groupBox2.Controls.Add(this.button4);
            this.groupBox2.Controls.Add(this.label20);
            this.groupBox2.Controls.Add(this.button1);
            this.groupBox2.Controls.Add(this.textBox14);
            this.groupBox2.Controls.Add(this.textBox12);
            this.groupBox2.Controls.Add(this.textBox11);
            this.groupBox2.Controls.Add(this.textBox10);
            this.groupBox2.Controls.Add(this.textBox9);
            this.groupBox2.Controls.Add(this.textBox8);
            this.groupBox2.Controls.Add(this.textBox7);
            this.groupBox2.Controls.Add(this.textBox6);
            this.groupBox2.Controls.Add(this.textBox5);
            this.groupBox2.Controls.Add(this.textBox4);
            this.groupBox2.Controls.Add(this.textBox3);
            this.groupBox2.Controls.Add(this.textBox1);
            this.groupBox2.Controls.Add(this.label25);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Location = new System.Drawing.Point(251, 78);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(404, 585);
            this.groupBox2.TabIndex = 20;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "物品属性";
            // 
            // textBox21
            // 
            this.textBox21.Location = new System.Drawing.Point(108, 518);
            this.textBox21.Name = "textBox21";
            this.textBox21.Size = new System.Drawing.Size(136, 21);
            this.textBox21.TabIndex = 76;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(12, 522);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(59, 12);
            this.label21.TabIndex = 75;
            this.label21.Text = "FLD_小时:";
            // 
            // textBox20
            // 
            this.textBox20.Location = new System.Drawing.Point(108, 491);
            this.textBox20.Name = "textBox20";
            this.textBox20.Size = new System.Drawing.Size(136, 21);
            this.textBox20.TabIndex = 74;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(13, 497);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(47, 12);
            this.label19.TabIndex = 73;
            this.label19.Text = "FLD_JF:";
            // 
            // textBox19
            // 
            this.textBox19.Location = new System.Drawing.Point(109, 464);
            this.textBox19.Name = "textBox19";
            this.textBox19.Size = new System.Drawing.Size(136, 21);
            this.textBox19.TabIndex = 72;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(13, 469);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(59, 12);
            this.label18.TabIndex = 71;
            this.label18.Text = "FLD_披风:";
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(259, 25);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(67, 23);
            this.button6.TabIndex = 70;
            this.button6.Text = "选择物品";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // textBox18
            // 
            this.textBox18.Location = new System.Drawing.Point(109, 437);
            this.textBox18.Name = "textBox18";
            this.textBox18.Size = new System.Drawing.Size(136, 21);
            this.textBox18.TabIndex = 69;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(13, 440);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(59, 12);
            this.label16.TabIndex = 68;
            this.label16.Text = "FLD_DAYS:";
            // 
            // textBox17
            // 
            this.textBox17.Location = new System.Drawing.Point(109, 407);
            this.textBox17.Name = "textBox17";
            this.textBox17.Size = new System.Drawing.Size(136, 21);
            this.textBox17.TabIndex = 67;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(13, 410);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(59, 12);
            this.label15.TabIndex = 66;
            this.label15.Text = "FLD_绑定:";
            // 
            // textBox16
            // 
            this.textBox16.Location = new System.Drawing.Point(109, 379);
            this.textBox16.Name = "textBox16";
            this.textBox16.Size = new System.Drawing.Size(136, 21);
            this.textBox16.TabIndex = 65;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(13, 382);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(59, 12);
            this.label14.TabIndex = 64;
            this.label14.Text = "FLD_进化:";
            // 
            // textBox13
            // 
            this.textBox13.Location = new System.Drawing.Point(108, 350);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(136, 21);
            this.textBox13.TabIndex = 63;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 353);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(83, 12);
            this.label9.TabIndex = 62;
            this.label9.Text = "FLD_中级附魂:";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(108, 323);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(136, 21);
            this.textBox2.TabIndex = 61;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 326);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(83, 12);
            this.label3.TabIndex = 60;
            this.label3.Text = "FLD_初级附魂:";
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(108, 554);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(75, 23);
            this.button5.TabIndex = 59;
            this.button5.Text = "删除";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(214, 554);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(75, 23);
            this.button4.TabIndex = 54;
            this.button4.Text = "添加";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(316, 98);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(77, 12);
            this.label20.TabIndex = 53;
            this.label20.Text = "注:120字以内";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(20, 554);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 52;
            this.button1.Text = "修改";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBox14
            // 
            this.textBox14.Location = new System.Drawing.Point(259, 120);
            this.textBox14.Multiline = true;
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new System.Drawing.Size(134, 414);
            this.textBox14.TabIndex = 51;
            // 
            // textBox12
            // 
            this.textBox12.Location = new System.Drawing.Point(108, 296);
            this.textBox12.Name = "textBox12";
            this.textBox12.Size = new System.Drawing.Size(136, 21);
            this.textBox12.TabIndex = 37;
            // 
            // textBox11
            // 
            this.textBox11.Location = new System.Drawing.Point(108, 269);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(136, 21);
            this.textBox11.TabIndex = 36;
            // 
            // textBox10
            // 
            this.textBox10.Location = new System.Drawing.Point(108, 242);
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new System.Drawing.Size(136, 21);
            this.textBox10.TabIndex = 35;
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(108, 215);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(136, 21);
            this.textBox9.TabIndex = 34;
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(108, 188);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(136, 21);
            this.textBox8.TabIndex = 33;
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(108, 161);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(136, 21);
            this.textBox7.TabIndex = 32;
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(108, 134);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(136, 21);
            this.textBox6.TabIndex = 31;
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(108, 107);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(136, 21);
            this.textBox5.TabIndex = 30;
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(108, 80);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(136, 21);
            this.textBox4.TabIndex = 29;
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(108, 53);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(136, 21);
            this.textBox3.TabIndex = 28;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(108, 26);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(136, 21);
            this.textBox1.TabIndex = 26;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(257, 100);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(53, 12);
            this.label25.TabIndex = 24;
            this.label25.Text = "FLD_DES:";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(12, 83);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(65, 12);
            this.label13.TabIndex = 12;
            this.label13.Text = "FLD_PRICE:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(12, 191);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(71, 12);
            this.label12.TabIndex = 11;
            this.label12.Text = "FLD_MAGIC0:";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(12, 245);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(71, 12);
            this.label11.TabIndex = 10;
            this.label11.Text = "FLD_MAGIC2:";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(12, 218);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 12);
            this.label10.TabIndex = 9;
            this.label10.Text = "FLD_MAGIC1:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(12, 137);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(71, 12);
            this.label8.TabIndex = 7;
            this.label8.Text = "FLD_RETURN:";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 164);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 12);
            this.label7.TabIndex = 6;
            this.label7.Text = "FLD_NUMBER:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(12, 299);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(71, 12);
            this.label6.TabIndex = 5;
            this.label6.Text = "FLD_MAGIC4:";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(12, 272);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(71, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "FLD_MAGIC3:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 110);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 3;
            this.label4.Text = "FLD_TYPE:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 56);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "FLD_NAME:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "FLD_PID:";
            // 
            // comboBox11
            // 
            this.comboBox11.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox11.FormattingEnabled = true;
            this.comboBox11.Location = new System.Drawing.Point(729, 90);
            this.comboBox11.MaxDropDownItems = 20;
            this.comboBox11.Name = "comboBox11";
            this.comboBox11.Size = new System.Drawing.Size(128, 20);
            this.comboBox11.TabIndex = 43;
            this.comboBox11.SelectedIndexChanged += new System.EventHandler(this.comboBox11_SelectedIndexChanged);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(668, 93);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(53, 12);
            this.label40.TabIndex = 42;
            this.label40.Text = "物品种类";
            // 
            // listBox3
            // 
            this.listBox3.FormattingEnabled = true;
            this.listBox3.ItemHeight = 12;
            this.listBox3.Location = new System.Drawing.Point(663, 118);
            this.listBox3.Name = "listBox3";
            this.listBox3.Size = new System.Drawing.Size(273, 544);
            this.listBox3.TabIndex = 41;
            this.listBox3.SelectedIndexChanged += new System.EventHandler(this.listBox3_SelectedIndexChanged);
            // 
            // BBGEdit
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(946, 675);
            this.Controls.Add(this.comboBox11);
            this.Controls.Add(this.label40);
            this.Controls.Add(this.listBox3);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "BBGEdit";
            this.Text = "百宝阁修改";
            this.Load += new System.EventHandler(this.BBGEdit_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBox3.SelectedItem;
			textBox1.Text = keyValuePair.Key;
			textBox3.Text = keyValuePair.Value;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "奥里给" + ex.StackTrace);
		}
	}

	private void BBGEdit_Load(object sender, EventArgs e)
	{
		刷新();
		ItemDef.AddComBoxItemReside2(comboBox11);
	}

	private void button6_Click(object sender, EventArgs e)
	{
		ItemSel itemSel = new ItemSel();
		itemSel.PID = textBox1;
		itemSel.NAME = textBox3;
		itemSel.Show();
	}
}

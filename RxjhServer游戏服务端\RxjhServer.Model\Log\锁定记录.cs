﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 锁定记录 {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int ID { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 分区 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 类型 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 秘钥 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 全局ID { get; set; }

		[JsonProperty, Column(InsertValueSql = "getdate()")]
		public DateTime? 时间 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 物品名 { get; set; }

		[JsonProperty, Column(StringLength = 100)]
		public string 物品属性 { get; set; }

		[JsonProperty]
		public int? 物品数量 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 物品ID { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string ToUserId { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string ToUserName { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string UserId { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string UserName { get; set; }

	}

}

namespace RxjhServer;

public class 职业系数类
{
	private int _ID;

	private int _FLD_职业;

	private int _FLD_几转;

	private double _物理对怪伤害;

	private double _物理对人伤害;

	private double _技能对怪伤害;

	private double _技能对人伤害;

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public int FLD_职业
	{
		get
		{
			return _FLD_职业;
		}
		set
		{
			_FLD_职业 = value;
		}
	}

	public int FLD_几转
	{
		get
		{
			return _FLD_几转;
		}
		set
		{
			_FLD_几转 = value;
		}
	}

	public double 物理对怪伤害
	{
		get
		{
			return _物理对怪伤害;
		}
		set
		{
			_物理对怪伤害 = value;
		}
	}

	public double 物理对人伤害
	{
		get
		{
			return _物理对人伤害;
		}
		set
		{
			_物理对人伤害 = value;
		}
	}

	public double 技能对怪伤害
	{
		get
		{
			return _技能对怪伤害;
		}
		set
		{
			_技能对怪伤害 = value;
		}
	}

	public double 技能对人伤害
	{
		get
		{
			return _技能对人伤害;
		}
		set
		{
			_技能对人伤害 = value;
		}
	}
}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;

namespace RxjhServer
{
    // 2025-0618 EVIAS 配置缓存管理器
    public class ConfigCache
    {
        private static readonly Lazy<ConfigCache> _instance = new(() => new ConfigCache());
        public static ConfigCache Instance => _instance.Value;

        // 配置缓存
        private readonly ConcurrentDictionary<string, ConfigItem> _cache = new();
        
        // 缓存统计
        private long _hitCount = 0;
        private long _missCount = 0;
        private long _totalRequests = 0;

        // 缓存配置
        private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(10); // 默认10分钟过期
        private readonly int _maxCacheSize = 1000; // 最大缓存项数

        private ConfigCache() { }

        // 获取配置值（带缓存）
        public string GetValue(string section, string key, string defaultValue = "")
        {
            if (string.IsNullOrEmpty(section) || string.IsNullOrEmpty(key))
                return defaultValue;

            Interlocked.Increment(ref _totalRequests);
            
            string cacheKey = FastString.Concat(section, ":", key);
            
            // 尝试从缓存获取
            if (_cache.TryGetValue(cacheKey, out var cachedItem))
            {
                // 检查是否过期
                if (DateTime.Now < cachedItem.ExpiryTime)
                {
                    Interlocked.Increment(ref _hitCount);
                    return cachedItem.Value ?? defaultValue;
                }
                else
                {
                    // 过期，移除缓存项
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            // 缓存未命中，从配置文件读取
            Interlocked.Increment(ref _missCount);
            
            try
            {
                string value = RxjhServer.DbClss.Config.IniReadValue(section, key).Trim();
                
                // 添加到缓存
                var newItem = new ConfigItem
                {
                    Value = value,
                    ExpiryTime = DateTime.Now.Add(_defaultExpiry)
                };
                
                // 检查缓存大小限制
                if (_cache.Count >= _maxCacheSize)
                {
                    CleanupExpiredItems();
                }
                
                _cache.TryAdd(cacheKey, newItem);
                
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("读取配置失败: ", section, ":", key, " - ", ex.Message));
                return defaultValue;
            }
        }

        // 获取整数配置值
        public int GetIntValue(string section, string key, int defaultValue = 0)
        {
            string value = GetValue(section, key);
            if (int.TryParse(value, out int result))
            {
                return result;
            }
            return defaultValue;
        }

        // 获取布尔配置值
        public bool GetBoolValue(string section, string key, bool defaultValue = false)
        {
            string value = GetValue(section, key);
            if (bool.TryParse(value, out bool result))
            {
                return result;
            }
            // 支持1/0格式
            if (int.TryParse(value, out int intValue))
            {
                return intValue != 0;
            }
            return defaultValue;
        }

        // 获取浮点配置值
        public float GetFloatValue(string section, string key, float defaultValue = 0f)
        {
            string value = GetValue(section, key);
            if (float.TryParse(value, out float result))
            {
                return result;
            }
            return defaultValue;
        }

        // 设置配置值（同时更新缓存和文件）
        public void SetValue(string section, string key, string value)
        {
            if (string.IsNullOrEmpty(section) || string.IsNullOrEmpty(key))
                return;

            try
            {
                // 写入配置文件
                RxjhServer.DbClss.Config.IniWriteValue(section, key, value);
                
                // 更新缓存
                string cacheKey = FastString.Concat(section, ":", key);
                var newItem = new ConfigItem
                {
                    Value = value,
                    ExpiryTime = DateTime.Now.Add(_defaultExpiry)
                };
                
                _cache.AddOrUpdate(cacheKey, newItem, (k, v) => newItem);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("写入配置失败: ", section, ":", key, " - ", ex.Message));
            }
        }

        // 清除指定配置的缓存
        public void InvalidateCache(string section, string key)
        {
            if (string.IsNullOrEmpty(section) || string.IsNullOrEmpty(key))
                return;

            string cacheKey = FastString.Concat(section, ":", key);
            _cache.TryRemove(cacheKey, out _);
        }

        // 清除所有缓存
        public void ClearCache()
        {
            _cache.Clear();
            Form1.WriteLine(6, "配置缓存已清空");
        }

        // 清理过期的缓存项
        public void CleanupExpiredItems()
        {
            try
            {
                var now = DateTime.Now;
                var expiredKeys = new List<string>();
                
                foreach (var kvp in _cache)
                {
                    if (now >= kvp.Value.ExpiryTime)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }
                
                foreach (var key in expiredKeys)
                {
                    _cache.TryRemove(key, out _);
                }
                
                if (expiredKeys.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("清理过期配置缓存: ", expiredKeys.Count, "项"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理配置缓存失败: ", ex.Message));
            }
        }

        // 获取缓存统计信息
        public string GetCacheStats()
        {
            double hitRate = _totalRequests > 0 ? (double)_hitCount / _totalRequests * 100 : 0;
            
            return FastString.Format(
                "配置缓存: 大小:{0} 命中率:{1:F1}% 请求:{2} 命中:{3} 未命中:{4}",
                _cache.Count,
                hitRate,
                _totalRequests,
                _hitCount,
                _missCount
            );
        }

        // 预加载常用配置
        public void PreloadCommonConfigs()
        {
            try
            {
                // 预加载游戏服务器常用配置
                GetValue("GameServer", "主服务器");
                GetValue("GameServer", "服务器名");
                GetValue("GameServer", "最大在线");
                GetValue("GameServer", "游戏服务器端口");
                GetValue("GameServer", "神豪上线公告");
                GetValue("GameServer", "前十上线公告");
                GetValue("GameServer", "Vip上线公告");
                GetValue("GameServer", "VIP地图");
                
                Form1.WriteLine(6, "常用配置预加载完成");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("预加载配置失败: ", ex.Message));
            }
        }

        // 配置项结构
        private class ConfigItem
        {
            public string Value { get; set; }
            public DateTime ExpiryTime { get; set; }
        }
    }

    // 2025-0618 EVIAS 配置缓存扩展方法
    public static class ConfigCacheExtensions
    {
        // 快速获取GameServer配置
        public static string GetGameServerConfig(string key, string defaultValue = "")
        {
            return ConfigCache.Instance.GetValue("GameServer", key, defaultValue);
        }

        // 快速获取GameServer整数配置
        public static int GetGameServerIntConfig(string key, int defaultValue = 0)
        {
            return ConfigCache.Instance.GetIntValue("GameServer", key, defaultValue);
        }

        // 快速获取GameServer布尔配置
        public static bool GetGameServerBoolConfig(string key, bool defaultValue = false)
        {
            return ConfigCache.Instance.GetBoolValue("GameServer", key, defaultValue);
        }
    }
}

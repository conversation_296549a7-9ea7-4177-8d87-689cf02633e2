using System;
using System.Collections.Generic;
using System.Linq;

namespace RxjhVer23;

internal class NewWebShop : INewWebShopService
{
	private const int MaxMenuNameTextSize = 32;

	private const int MaxSaleIdTextSize = 16;

	private const int MaxSaleNameTextSize = 32;

	private const int MaxSaleDescTextSize = 4096;

	private const int MaxItemsPerPage = 12;

	private const int MaxPurchaseQuantity = 10;

	private const int MinPurchaseQuantity = 1;

	private const int MaxCartPurchaseQuantity = 10;

	private const int MinCartPurchaseQuantity = 1;

	private readonly object _opLock = new object();

	private readonly int _serverId = 51;

	private readonly int _serverGroupId = 51;

	private readonly string _webShopURL = "http://webshopd2.rxjh.cdcgames.net/login_result.asp1orrj5126DC1KzGp3Ansb0d3n7kM8Sdv";

	private readonly string _sqlConnectString = SqlConnectInfo.GetSqlConnectString(new SqlConnectInfo());

	private readonly Dictionary<int, string> _mainMenus = new Dictionary<int, string>();

	private readonly Dictionary<int, KeyValuePair<int, string>> _subMenus = new Dictionary<int, KeyValuePair<int, string>>();

	private readonly Dictionary<int, string> _udefineMenus = new Dictionary<int, string>
	{
		{ 1, "추천" },
		{ 2, "인기" },
		{ 3, "신규" }
	};

	private readonly Dictionary<string, NewWebShopSaleItem> _saleItems = new Dictionary<string, NewWebShopSaleItem>();

	private readonly Dictionary<string, INewWebShopAccount> _accounts = new Dictionary<string, INewWebShopAccount>();

	public NewWebShop(int serverId, int serverGroupId, string webShopURL, string sqlConnectString)
	{
		_serverId = serverId;
		_serverGroupId = serverGroupId;
		_webShopURL = webShopURL;
		_sqlConnectString = sqlConnectString;
		List<INewWebShopSaleItem> saleItems = RxjhWebShopItem.GetSaleItems(sqlConnectString);
		foreach (INewWebShopSaleItem item in saleItems)
		{
			NewWebShopSaleItem newWebShopSaleItem = new NewWebShopSaleItem(item);
			_saleItems.Add(newWebShopSaleItem.SaleId, newWebShopSaleItem);
		}
		ICustomMenu customMenus = RxjhWebShopMenu.GetCustomMenus(sqlConnectString);
		foreach (KeyValuePair<string, int> item2 in customMenus.MainMenu)
		{
			_mainMenus.Add(item2.Value, item2.Key);
		}
		foreach (KeyValuePair<int, string> item3 in customMenus.SubMenu)
		{
			_subMenus.Add(item3.Key, new KeyValuePair<int, string>(customMenus.MainMenu[LocalTextProcessing.RemoveUnderscores(item3.Value)], LocalTextProcessing.GetTextAfterUnderscore(item3.Value)));
		}
	}

	public void ReloadSaleItemAndCustomMenu()
	{
		lock (_opLock)
		{
			List<INewWebShopSaleItem> saleItems = RxjhWebShopItem.GetSaleItems(_sqlConnectString);
			_saleItems.Clear();
			foreach (INewWebShopSaleItem item in saleItems)
			{
				NewWebShopSaleItem newWebShopSaleItem = new NewWebShopSaleItem(item);
				_saleItems.Add(newWebShopSaleItem.SaleId, newWebShopSaleItem);
			}
			ICustomMenu customMenus = RxjhWebShopMenu.GetCustomMenus(_sqlConnectString);
			_mainMenus.Clear();
			foreach (KeyValuePair<string, int> item2 in customMenus.MainMenu)
			{
				_mainMenus.Add(item2.Value, item2.Key);
			}
			_subMenus.Clear();
			foreach (KeyValuePair<int, string> item3 in customMenus.SubMenu)
			{
				_subMenus.Add(item3.Key, new KeyValuePair<int, string>(customMenus.MainMenu[LocalTextProcessing.RemoveUnderscores(item3.Value)], LocalTextProcessing.GetTextAfterUnderscore(item3.Value)));
			}
			foreach (INewWebShopAccount value in _accounts.Values)
			{
				SpMenu(value.NewWebShopUser);
				SpSinglePageItemList(value.NewWebShopUser, 0, 0, 0);
			}
		}
	}

	public void OnProcessFullPacket(INewWebShopUser user, byte[] fullPacket)
	{
		byte[] array = new byte[fullPacket.Length - 6];
		Buffer.BlockCopy(fullPacket, 4, array, 0, array.Length);
		ushort num = BitConverter.ToUInt16(array, 4);
		lock (_opLock)
		{
			if (num == 212)
			{
				RpOpenOrCloseWebShop(user, num, array);
			}
			else if (_accounts.ContainsKey(user.Userid))
			{
				switch (num)
				{
				case 660:
					RpGetMenu(user, num, array);
					break;
				case 641:
					RpGetSinglePageItemList(user, num, array);
					break;
				case 643:
					RpGetSingleItemDetail(user, num, array);
					break;
				case 645:
					RpSearchItemName(user, num, array);
					break;
				case 647:
					RpPurchaseSingleItem(user, num, array);
					break;
				case 668:
					RpPurchaseMultipleItems(user, num, array);
					break;
				}
			}
		}
	}

	public void OnUserExit(INewWebShopUser user)
	{
		lock (_opLock)
		{
			SpClose(user);
		}
	}

	private int SearchItemName(string text, int pageIndex, out List<NewWebShopSaleItem> saleItems)
	{
		saleItems = new List<NewWebShopSaleItem>();
		int num = 0;
		int num2 = 0;
		foreach (NewWebShopSaleItem value in _saleItems.Values)
		{
			if (value.ItemName.IndexOf(text) != -1)
			{
				if (num2 == pageIndex)
				{
					saleItems.Add(value);
				}
				if (++num >= 12)
				{
					num = 0;
					num2++;
				}
			}
		}
		return num2;
	}

	private int SearchItemDesc(string text, int pageIndex, out List<NewWebShopSaleItem> saleItems)
	{
		saleItems = new List<NewWebShopSaleItem>();
		int num = 0;
		int num2 = 0;
		foreach (NewWebShopSaleItem value in _saleItems.Values)
		{
			if (value.ItemDesc.IndexOf(text) != -1)
			{
				if (num2 == pageIndex)
				{
					saleItems.Add(value);
				}
				if (++num >= 12)
				{
					num = 0;
					num2++;
				}
			}
		}
		return num2;
	}

	private NewWebShopSaleItem GetSingleItem(string saleId)
	{
		foreach (NewWebShopSaleItem value in _saleItems.Values)
		{
			if (value.SaleId == saleId)
			{
				return value;
			}
		}
		return new NewWebShopSaleItem();
	}

	private List<NewWebShopSaleItem> GetMultipleItems(List<string> saleIds)
	{
		List<NewWebShopSaleItem> list = new List<NewWebShopSaleItem>();
		foreach (NewWebShopSaleItem value in _saleItems.Values)
		{
			if (saleIds.Contains(value.SaleId))
			{
				list.Add(value);
			}
		}
		return list;
	}

	private int GetSinglePageItemList(int mainMenuId, int subMenuId, int pageIndex, out List<NewWebShopSaleItem> singlePageItems)
	{
		singlePageItems = new List<NewWebShopSaleItem>();
		int num = 0;
		if (mainMenuId == 0)
		{
			using Dictionary<int, string>.Enumerator enumerator = _mainMenus.GetEnumerator();
			if (enumerator.MoveNext())
			{
				KeyValuePair<int, string> current = enumerator.Current;
				int num2 = 0;
				foreach (NewWebShopSaleItem value in _saleItems.Values)
				{
					if (value.MainMenuId == current.Key)
					{
						if (num == pageIndex)
						{
							singlePageItems.Add(value);
						}
						if (++num2 >= 12)
						{
							num2 = 0;
							num++;
						}
					}
				}
			}
		}
		else
		{
			int num3 = 0;
			foreach (NewWebShopSaleItem value2 in _saleItems.Values)
			{
				if (value2.MainMenuId == mainMenuId && (subMenuId == 0 || value2.SubMenuId == subMenuId))
				{
					if (num == pageIndex)
					{
						singlePageItems.Add(value2);
					}
					if (++num3 >= 12)
					{
						num3 = 0;
						num++;
					}
				}
			}
		}
		return num;
	}

	private void PurchaseSingleItem(INewWebShopUser user, int count, string saleId)
	{
		if (!_accounts.TryGetValue(user.Userid, out var value))
		{
			return;
		}
		if (count > 10 || count < 1)
		{
			SpPurchaseSingleItemFailure(user, count, saleId, PurchaseFailureReasonEnum.InsufficientBackpackSpace);
			return;
		}
		NewWebShopSaleItem singleItem = GetSingleItem(saleId);
		if (singleItem.SaleId != saleId)
		{
			SpPurchaseSingleItemFailure(user, count, saleId, PurchaseFailureReasonEnum.InsufficientFunds);
		}
		else if (user.CheckSingleItemBackpackSpace(singleItem, count))
		{
			int num = singleItem.DiscountedPrice * count;
			if (value.RxPiont < num || value.GiftedRxPiont < num)
			{
				SpPurchaseSingleItemFailure(user, count, saleId, PurchaseFailureReasonEnum.InsufficientFunds);
				return;
			}
			user.DistributeSingleItemToBackpack(singleItem, count);
			SpPurchaseSingleItemSuccess(user, count, saleId);
			SpHoldFunds(user);
		}
		else
		{
			SpPurchaseSingleItemFailure(user, count, saleId, PurchaseFailureReasonEnum.InsufficientBackpackSpace);
		}
	}

	private void PurchaseMultipleItems(INewWebShopUser user, List<int> counts, List<string> saleIds)
	{
		if (!_accounts.TryGetValue(user.Userid, out var value))
		{
			return;
		}
		if (counts.Count != saleIds.Count || counts.Count > 10 || counts.Count < 1)
		{
			SpPurchaseFailure(user, counts, saleIds, PurchaseFailureReasonEnum.InsufficientBackpackSpace);
			return;
		}
		List<NewWebShopSaleItem> multipleItems = GetMultipleItems(saleIds);
		if (multipleItems.Count != counts.Count)
		{
			SpPurchaseFailure(user, counts, saleIds, PurchaseFailureReasonEnum.InsufficientFunds);
		}
		else if (user.CheckMultipleItemsBackpackSpace(((IEnumerable<INewWebShopSaleItem>)multipleItems).ToList(), counts))
		{
			int num = 0;
			for (int i = 0; i < multipleItems.Count; i++)
			{
				if (counts[i] > 10 || counts[i] < 1)
				{
					SpPurchaseFailure(user, counts, saleIds, PurchaseFailureReasonEnum.InsufficientBackpackSpace);
					return;
				}
				num += multipleItems[i].DiscountedPrice * counts[i];
			}
			if (value.RxPiont < num || value.GiftedRxPiont < num)
			{
				SpPurchaseFailure(user, counts, saleIds, PurchaseFailureReasonEnum.InsufficientFunds);
				return;
			}
			user.DistributeMultipleItemsToBackpack(((IEnumerable<INewWebShopSaleItem>)multipleItems).ToList(), counts);
			SpPurchaseMultipleItemsSuccess(user, counts, saleIds);
			SpHoldFunds(user);
		}
		else
		{
			SpPurchaseFailure(user, counts, saleIds, PurchaseFailureReasonEnum.InsufficientBackpackSpace);
		}
	}

	private void SendFullPacket(INewWebShopUser user, PacketDataWriteStream stream, int opcode, int guid)
	{
		byte[] packetData = stream.GetPacketData(opcode, guid);
		List<byte> list = new List<byte>();
		list.AddRange(BitConverter.GetBytes((ushort)21930));
		list.AddRange(BitConverter.GetBytes((ushort)packetData.Length));
		list.AddRange(packetData);
		list.AddRange(BitConverter.GetBytes((ushort)43605));
		user.SendFullPacket(list.ToArray());
	}

	private void SpOpen(INewWebShopUser user)
	{
		if (!_accounts.ContainsKey(user.Userid))
		{
			_accounts.Add(user.Userid, new NewWebShopAccount(user));
			using (PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream())
			{
				packetDataWriteStream.Write4(262);
				packetDataWriteStream.Write4(15200);
				packetDataWriteStream.Write2(_serverId);
				packetDataWriteStream.Write2(_serverGroupId);
				packetDataWriteStream.WriteAsciiFixed(_webShopURL);
				packetDataWriteStream.Write1(0);
				SendFullPacket(user, packetDataWriteStream, 213, 0);
			}
			user.查百宝阁元宝数();
		}
	}

	private void SpClose(INewWebShopUser user)
	{
		if (!_accounts.ContainsKey(user.Userid))
		{
			return;
		}
		_accounts.Remove(user.Userid);
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write1(2);
		SendFullPacket(user, packetDataWriteStream, 213, user.人物全服ID);
	}

	private void SpMenu(INewWebShopUser user)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(_mainMenus.Count);
		int num = 0;
		foreach (KeyValuePair<int, string> mainMenu in _mainMenus)
		{
			packetDataWriteStream.Write4(0);
			packetDataWriteStream.Write4(++num);
			packetDataWriteStream.Write4(mainMenu.Key);
			packetDataWriteStream.WriteString(mainMenu.Value, 32);
		}
		packetDataWriteStream.Write4(_subMenus.Count);
		int num2 = 0;
		foreach (KeyValuePair<int, KeyValuePair<int, string>> subMenu in _subMenus)
		{
			packetDataWriteStream.Write4(++num2);
			packetDataWriteStream.Write4(subMenu.Key);
			packetDataWriteStream.Write4(subMenu.Value.Key);
			packetDataWriteStream.WriteString(subMenu.Value.Value, 32);
		}
		packetDataWriteStream.Write4(_udefineMenus.Count);
		foreach (KeyValuePair<int, string> udefineMenu in _udefineMenus)
		{
			packetDataWriteStream.WriteString(udefineMenu.Value, 32);
		}
		SendFullPacket(user, packetDataWriteStream, 640, user.人物全服ID);
	}

	private void SpHoldFunds(INewWebShopUser user)
	{
		if (!_accounts.TryGetValue(user.Userid, out var value))
		{
			return;
		}
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(value.UdefineFunds);
		packetDataWriteStream.Write4(value.RxPiont);
		packetDataWriteStream.Write4(value.GiftedRxPiont);
		SendFullPacket(user, packetDataWriteStream, 653, user.人物全服ID);
	}

	private void SpSinglePageItemList(INewWebShopUser user, int mainMenuId, int subMenuId, int pageIndex)
	{
		List<NewWebShopSaleItem> singlePageItems;
		int singlePageItemList = GetSinglePageItemList(mainMenuId, subMenuId, pageIndex, out singlePageItems);
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(mainMenuId);
		packetDataWriteStream.Write4(subMenuId);
		packetDataWriteStream.Write4(pageIndex);
		packetDataWriteStream.Write4(singlePageItemList);
		packetDataWriteStream.Write4(singlePageItems.Count);
		foreach (NewWebShopSaleItem item in singlePageItems)
		{
			packetDataWriteStream.WriteString(item.SaleId, 16);
			packetDataWriteStream.Write8(item.ItemId);
			packetDataWriteStream.WriteString(item.ItemName, 32);
			packetDataWriteStream.Write4((int)item.SalePriceType);
			packetDataWriteStream.Write4(item.CalePrice);
			packetDataWriteStream.Write4(item.DiscountedPrice);
			packetDataWriteStream.Write4(item.MainMenuId);
			packetDataWriteStream.Write4(item.SubMenuId);
			int[] reservedFieldArray = item.ReservedFieldArray;
			int[] array = reservedFieldArray;
			foreach (int value in array)
			{
				packetDataWriteStream.Write4(value);
			}
		}
		byte[] value2 = new byte[160];
		for (int j = 0; j < 12 - singlePageItems.Count; j++)
		{
			packetDataWriteStream.Write(value2);
		}
		SendFullPacket(user, packetDataWriteStream, 642, user.人物全服ID);
	}

	private void SpSearchItemName(INewWebShopUser user, string text, int pageIndex, int searchType)
	{
		List<NewWebShopSaleItem> saleItems = new List<NewWebShopSaleItem>();
		int value = 0;
		switch (searchType)
		{
		case 0:
			value = SearchItemName(text, pageIndex, out saleItems);
			break;
		case 1:
			value = SearchItemDesc(text, pageIndex, out saleItems);
			break;
		}
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(pageIndex);
		packetDataWriteStream.Write4(value);
		packetDataWriteStream.Write4(saleItems.Count);
		foreach (NewWebShopSaleItem item in saleItems)
		{
			packetDataWriteStream.WriteString(item.SaleId, 16);
			packetDataWriteStream.Write8(item.ItemId);
			packetDataWriteStream.WriteString(item.ItemName, 32);
			packetDataWriteStream.Write4((int)item.SalePriceType);
			packetDataWriteStream.Write4(item.CalePrice);
			packetDataWriteStream.Write4(item.DiscountedPrice);
			packetDataWriteStream.Write4(item.MainMenuId);
			packetDataWriteStream.Write4(item.SubMenuId);
			int[] reservedFieldArray = item.ReservedFieldArray;
			int[] array = reservedFieldArray;
			foreach (int value2 in array)
			{
				packetDataWriteStream.Write4(value2);
			}
		}
		byte[] value3 = new byte[160];
		for (int j = 0; j < 12 - saleItems.Count; j++)
		{
			packetDataWriteStream.Write(value3);
		}
		SendFullPacket(user, packetDataWriteStream, 646, user.人物全服ID);
	}

	private void SpSingleItemDetail(INewWebShopUser user, string saleId)
	{
		NewWebShopSaleItem singleItem = GetSingleItem(saleId);
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.WriteString(singleItem.SaleId, 16);
		packetDataWriteStream.Write8(singleItem.ItemId);
		packetDataWriteStream.WriteString(singleItem.ItemName, 32);
		packetDataWriteStream.WriteString(singleItem.ItemDesc, 4096);
		packetDataWriteStream.Write4((int)singleItem.SalePriceType);
		packetDataWriteStream.Write4(singleItem.CalePrice);
		packetDataWriteStream.Write4(singleItem.DiscountedPrice);
		packetDataWriteStream.Write4(singleItem.MainMenuId);
		packetDataWriteStream.Write4(singleItem.SubMenuId);
		packetDataWriteStream.Write(new byte[95]);
		SendFullPacket(user, packetDataWriteStream, 644, user.人物全服ID);
	}

	private void SpPurchaseSingleItemSuccess(INewWebShopUser user, int count, string saleId)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(count);
		packetDataWriteStream.WriteString(saleId, 16);
		SendFullPacket(user, packetDataWriteStream, 648, user.人物全服ID);
	}

	private void SpPurchaseSingleItemFailure(INewWebShopUser user, int count, string saleId, PurchaseFailureReasonEnum reason)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(648);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4((int)reason);
		SendFullPacket(user, packetDataWriteStream, 652, user.人物全服ID);
	}

	private void SpPurchaseMultipleItemsSuccess(INewWebShopUser user, List<int> counts, List<string> saleIds)
	{
		for (int i = 0; i < saleIds.Count; i++)
		{
			using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
			packetDataWriteStream.Write4(1);
			packetDataWriteStream.WriteString(saleIds[i], 16);
			for (int j = 0; j < 9; j++)
			{
				packetDataWriteStream.Write4(0);
				packetDataWriteStream.WriteString("", 16);
			}
			SendFullPacket(user, packetDataWriteStream, 669, user.人物全服ID);
		}
	}

	private void SpPurchaseFailure(INewWebShopUser user, List<int> counts, List<string> saleIds, PurchaseFailureReasonEnum reason)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		for (int i = 0; i < saleIds.Count; i++)
		{
			packetDataWriteStream.Write4((int)((i == 0) ? reason : ((PurchaseFailureReasonEnum)0)));
			packetDataWriteStream.WriteString(saleIds[i], 16);
		}
		for (int j = 0; j < 10 - saleIds.Count; j++)
		{
			packetDataWriteStream.Write4(0);
			packetDataWriteStream.WriteString("", 16);
		}
		SendFullPacket(user, packetDataWriteStream, 669, user.人物全服ID);
	}

	private void RpOpenOrCloseWebShop(INewWebShopUser user, ushort opcode, byte[] data)
	{
		switch (BitConverter.ToInt32(data, 8))
		{
		case 1:
			SpOpen(user);
			break;
		case 2:
			SpClose(user);
			break;
		}
	}

	private void RpGetMenu(INewWebShopUser user, ushort opcode, byte[] data)
	{
		SpMenu(user);
		SpHoldFunds(user);
	}

	private void RpSearchItemName(INewWebShopUser user, ushort opcode, byte[] data)
	{
		int pageIndex = BitConverter.ToInt32(data, 16);
		int searchType = BitConverter.ToInt32(data, 20);
		string stringByGb = LocalTextProcessing.GetStringByGb18030(data, 24, 32);
		SpSearchItemName(user, stringByGb, pageIndex, searchType);
	}

	private void RpGetSinglePageItemList(INewWebShopUser user, ushort opcode, byte[] data)
	{
		int mainMenuId = BitConverter.ToInt32(data, 8);
		int subMenuId = BitConverter.ToInt32(data, 12);
		int pageIndex = BitConverter.ToInt32(data, 16);
		SpSinglePageItemList(user, mainMenuId, subMenuId, pageIndex);
	}

	private void RpGetSingleItemDetail(INewWebShopUser user, ushort opcode, byte[] data)
	{
		string stringByGb = LocalTextProcessing.GetStringByGb18030(data, 8, 16);
		SpSingleItemDetail(user, stringByGb);
	}

	private void RpPurchaseSingleItem(INewWebShopUser user, ushort opcode, byte[] data)
	{
		int num = BitConverter.ToInt32(data, 8);
		string stringByGb = LocalTextProcessing.GetStringByGb18030(data, 12, 16);
		if (num >= 1 && num <= 10 && _saleItems.ContainsKey(stringByGb))
		{
			PurchaseSingleItem(user, num, stringByGb);
		}
	}

	private void RpPurchaseMultipleItems(INewWebShopUser user, ushort opcode, byte[] data)
	{
		List<int> list = new List<int>();
		List<string> list2 = new List<string>();
		for (int i = 0; i < 10; i++)
		{
			int num = BitConverter.ToInt32(data, 8 + i * 20);
			string stringByGb = LocalTextProcessing.GetStringByGb18030(data, 12 + i * 20, 16);
			if (num >= 1 && num <= 10 && _saleItems.ContainsKey(stringByGb))
			{
				list.Add(num);
				list2.Add(stringByGb);
			}
		}
		if (list.Count >= 1 && list.Count <= 10)
		{
			PurchaseMultipleItems(user, list, list2);
		}
	}
}

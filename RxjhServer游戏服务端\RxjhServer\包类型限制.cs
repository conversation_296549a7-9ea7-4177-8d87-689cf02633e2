using System;

namespace RxjhServer;

public class 包类型限制
{
	public int 包类型;

	public DateTime 上次包时间;

	public static bool 是否超过规定时间(Players player, int num)
	{
		try
		{
			if (World.同类型数据包处理间隔 > 0)
			{
				if (player.上次收到的包.TryGetValue(num, out var value))
				{
					if (DateTime.Now.Subtract(value.上次包时间).TotalMilliseconds < (double)World.同类型数据包处理间隔)
					{
						return false;
					}
					value.上次包时间 = DateTime.Now;
				}
				else
				{
					value = new 包类型限制();
					value.包类型 = num;
					value.上次包时间 = DateTime.Now;
					player.上次收到的包.TryAdd(num, value);
				}
			}
		}
		catch (Exception)
		{
		}
		return true;
	}
}

namespace RxjhTool;

public class YbiClass
{
	private int _ID;

	private int _FLD_PID;

	private string _FLD_Name;

	private string _FLD_说明;

	private byte[] _FLD_byte;

	private int _FLD_QUESTITEM;

	private int _FLD_NJ;

	private int _FLD_RESIDE1;

	private int _FLD_RESIDE2;

	private int _FLD_SEX;

	private int _FLD_AT1;

	private int _FLD_AT2;

	private int _FLD_LEVEL;

	private int _FLD_JOB_LEVEL;

	private int _FLD_ZX;

	private int _FLD_EL;

	private int _FLD_WX;

	private int _FLD_WXJD;

	private int _FLD_MONEY;

	private int _FLD_WEIGHT;

	private int _FLD_TYPE;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _FLD_MAGIC5;

	private int _FLD_SIDE;

	private int _FLD_DF;

	public int _FLD_MM1;

	public int _FLD_MM2;

	public int _FLD_TZ;

	public int _FLD_QGSLL;

	public int _FLD_CJL;

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public string FLD_Name
	{
		get
		{
			return _FLD_Name;
		}
		set
		{
			_FLD_Name = value;
		}
	}

	public string FLD_说明
	{
		get
		{
			return _FLD_说明;
		}
		set
		{
			_FLD_说明 = value;
		}
	}

	public byte[] FLD_byte
	{
		get
		{
			return _FLD_byte;
		}
		set
		{
			_FLD_byte = value;
		}
	}

	public int FLD_QUESTITEM
	{
		get
		{
			return _FLD_QUESTITEM;
		}
		set
		{
			_FLD_QUESTITEM = value;
		}
	}

	public int FLD_NJ
	{
		get
		{
			return _FLD_NJ;
		}
		set
		{
			_FLD_NJ = value;
		}
	}

	public int FLD_RESIDE1
	{
		get
		{
			return _FLD_RESIDE1;
		}
		set
		{
			_FLD_RESIDE1 = value;
		}
	}

	public int FLD_RESIDE2
	{
		get
		{
			return _FLD_RESIDE2;
		}
		set
		{
			_FLD_RESIDE2 = value;
		}
	}

	public int FLD_SEX
	{
		get
		{
			return _FLD_SEX;
		}
		set
		{
			_FLD_SEX = value;
		}
	}

	public int FLD_AT1
	{
		get
		{
			return _FLD_AT1;
		}
		set
		{
			_FLD_AT1 = value;
		}
	}

	public int FLD_AT2
	{
		get
		{
			return _FLD_AT2;
		}
		set
		{
			_FLD_AT2 = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_JOB_LEVEL
	{
		get
		{
			return _FLD_JOB_LEVEL;
		}
		set
		{
			_FLD_JOB_LEVEL = value;
		}
	}

	public int FLD_ZX
	{
		get
		{
			return _FLD_ZX;
		}
		set
		{
			_FLD_ZX = value;
		}
	}

	public int FLD_EL
	{
		get
		{
			return _FLD_EL;
		}
		set
		{
			_FLD_EL = value;
		}
	}

	public int FLD_MM1
	{
		get
		{
			return _FLD_MM1;
		}
		set
		{
			_FLD_MM1 = value;
		}
	}

	public int FLD_MM2
	{
		get
		{
			return _FLD_MM2;
		}
		set
		{
			_FLD_MM2 = value;
		}
	}

	public int FLD_WX
	{
		get
		{
			return _FLD_WX;
		}
		set
		{
			_FLD_WX = value;
		}
	}

	public int FLD_WXJD
	{
		get
		{
			return _FLD_WXJD;
		}
		set
		{
			_FLD_WXJD = value;
		}
	}

	public int FLD_MONEY
	{
		get
		{
			return _FLD_MONEY;
		}
		set
		{
			_FLD_MONEY = value;
		}
	}

	public int FLD_WEIGHT
	{
		get
		{
			return _FLD_WEIGHT;
		}
		set
		{
			_FLD_WEIGHT = value;
		}
	}

	public int FLD_TYPE
	{
		get
		{
			return _FLD_TYPE;
		}
		set
		{
			_FLD_TYPE = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_TZ
	{
		get
		{
			return _FLD_TZ;
		}
		set
		{
			_FLD_TZ = value;
		}
	}

	public int FLD_QGSLL
	{
		get
		{
			return _FLD_QGSLL;
		}
		set
		{
			_FLD_QGSLL = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return _FLD_MAGIC5;
		}
		set
		{
			_FLD_MAGIC5 = value;
		}
	}

	public int FLD_SIDE
	{
		get
		{
			return _FLD_SIDE;
		}
		set
		{
			_FLD_SIDE = value;
		}
	}

	public int FLD_DF
	{
		get
		{
			return _FLD_DF;
		}
		set
		{
			_FLD_DF = value;
		}
	}

	public int FLD_CJL
	{
		get
		{
			return _FLD_CJL;
		}
		set
		{
			_FLD_CJL = value;
		}
	}
}

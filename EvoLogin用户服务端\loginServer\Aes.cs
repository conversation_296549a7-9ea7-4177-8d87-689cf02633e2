using System;
using System.IO;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace loginServer;

public class Aes
{
	// 密钥已经更新为与注册码生成器中"EvoLogin"项目一致的密钥
	public const string Key1 = "Kj8nM2pQ9xL5vT3rY7hF4dA1cB6eW8sZ";
	public const string Key2 = "X7yN4mK2jR9wP5vB3cF8tH1aE6qL9dS4";

	public static string Decrypt(string key, string code)
	{
		try
		{
			using AesManaged aesManaged = new AesManaged();
			byte[] array = (aesManaged.Key = Encoding.ASCII.GetBytes(key));
			// 简化IV获取，与注册码生成器保持一致
			aesManaged.IV = array.Take(16).ToArray();
			byte[] cipherText = Convert.FromBase64String(code);
			return DecryptStringFromBytes_Aes(cipherText, aesManaged.Key, aesManaged.IV);
		}
		catch
		{
			return null;
		}
	}

	public static string DecryptStringFromBytes_Aes(byte[] cipherText, byte[] Key, byte[] IV)
	{
		if (cipherText == null || cipherText.Length == 0)
		{
			return null;
		}
		if (Key == null || Key.Length == 0)
		{
			return null;
		}
		if (IV == null || IV.Length == 0)
		{
			return null;
		}
		string result = null;
		using (AesManaged aesManaged = new AesManaged())
		{
			aesManaged.Key = Key;
			aesManaged.IV = IV;
			ICryptoTransform transform = aesManaged.CreateDecryptor(aesManaged.Key, aesManaged.IV);
			using MemoryStream stream = new MemoryStream(cipherText);
			using CryptoStream stream2 = new CryptoStream(stream, transform, CryptoStreamMode.Read);
			using StreamReader streamReader = new StreamReader(stream2);
			result = streamReader.ReadToEnd();
		}
		return result;
	}

	public static string GetMachineCode()
	{
		return GetCpuId() + GetDiskNo();
	}

	public static string GetDiskNo()
	{
		ManagementClass managementClass = new ManagementClass("Win32_NetworkAdapterConfiguration");
		ManagementObject managementObject = new ManagementObject("win32_logicaldisk.deviceid=\"c:\"");
		managementObject.Get();
		return managementObject.GetPropertyValue("VolumeSerialNumber").ToString();
	}

	public static string GetCpuId()
	{
		string result = null;
		ManagementClass managementClass = new ManagementClass("win32_Processor");
		ManagementObjectCollection instances = managementClass.GetInstances();
		using (ManagementObjectCollection.ManagementObjectEnumerator managementObjectEnumerator = instances.GetEnumerator())
		{
			if (managementObjectEnumerator.MoveNext())
			{
				ManagementObject managementObject = (ManagementObject)managementObjectEnumerator.Current;
				result = managementObject.Properties["Processorid"].Value.ToString();
			}
		}
		return result;
	}

	public static string Encrypt(string plainText, string key)
	{
		try
		{
			using AesManaged aesManaged = new AesManaged();
			byte[] keyBytes = Encoding.ASCII.GetBytes(key);
			aesManaged.Key = keyBytes;
			aesManaged.IV = keyBytes.Take(16).ToArray();

			byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);

			using MemoryStream memoryStream = new MemoryStream();
			using CryptoStream cryptoStream = new CryptoStream(memoryStream, aesManaged.CreateEncryptor(), CryptoStreamMode.Write);
			cryptoStream.Write(plainTextBytes, 0, plainTextBytes.Length);
			cryptoStream.FlushFinalBlock();

			byte[] cipherTextBytes = memoryStream.ToArray();
			return Convert.ToBase64String(cipherTextBytes);
		}
		catch (Exception)
		{
			return null;
		}
	}

	// 这个方法仅用于生成旧格式的注册码，保留以便向后兼容
	public static string GenerateRegistrationCode(string machineCode, DateTime expiryDate, string secretKey, string salt)
	{
		string dataToEncrypt = $"{machineCode}|{expiryDate:yyyy-MM-dd}|{salt}";
		return Encrypt(dataToEncrypt, secretKey);
	}

	// 这是与注册码生成器完全一致的验证单个注册码的方法
	private static bool ValidateRegistrationCode(string projectId, string registrationCode, string machineCode, string secretKey, out DateTime expiryDate)
	{
		expiryDate = DateTime.MinValue;
		
		try
		{
			string decryptedData = Decrypt(secretKey, registrationCode);
			if (string.IsNullOrEmpty(decryptedData)) return false;
			
			string[] parts = decryptedData.Split('|');
			if (parts.Length != 4) return false;
			
			// 验证项目ID
			if (parts[0] != projectId) return false;
			
			// 验证机器码
			if (parts[1] != machineCode) return false;
			
			// 解析有效期
			if (!DateTime.TryParse(parts[2], out expiryDate)) return false;
			
			// 检查是否过期
			return DateTime.Now <= expiryDate;
		}
		catch
		{
			return false;
		}
	}

	// 提供临时的兼容方法，以适配现有代码中对旧格式的调用
	public static bool ValidateRegistrationCode(string registrationCode, string machineCode, string secretKey, out DateTime expiryDate)
	{
		// 这个方法已弃用，仅保留向后兼容性
		expiryDate = DateTime.MinValue;
		
		try
		{
			string decryptedData = Decrypt(secretKey, registrationCode);
			if (string.IsNullOrEmpty(decryptedData)) return false;
			
			string[] parts = decryptedData.Split('|');
			
			// 支持新格式
			if (parts.Length == 4)
			{
				// 不检查项目ID，直接验证机器码
				if (!parts[1].Equals(machineCode)) return false;
				
				if (!DateTime.TryParse(parts[2], out expiryDate)) return false;
			}
			// 旧格式
			else if (parts.Length == 3)
			{
				if (!parts[0].Equals(machineCode)) return false;
				
				if (!DateTime.TryParse(parts[1], out expiryDate)) return false;
			}
			else
			{
				return false;
			}
			
			return DateTime.Now <= expiryDate;
		}
		catch
		{
			return false;
		}
	}

	// 这是与注册码生成器完全一致的验证两个注册码的方法
	public static bool ValidateRegistrationCodes(string projectId, string code1, string code2, string machineCode, out DateTime expiryDate)
	{
		expiryDate = DateTime.MinValue;
		
		DateTime expiryDate1, expiryDate2;
		
		// 使用与注册码生成器相同的方法
		bool isValid1 = ValidateRegistrationCode(projectId, code1, machineCode, Key1, out expiryDate1);
		bool isValid2 = ValidateRegistrationCode(projectId, code2, machineCode, Key2, out expiryDate2);

		if (!isValid1 || !isValid2)
		{
			return false;
		}

		// 使用较早的过期时间
		expiryDate = expiryDate1 < expiryDate2 ? expiryDate1 : expiryDate2;
		return true;
	}
}

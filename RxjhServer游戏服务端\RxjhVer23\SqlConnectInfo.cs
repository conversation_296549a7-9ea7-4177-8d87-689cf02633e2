namespace RxjhVer23;

public class SqlConnectInfo
{
	public string ServerAddress;

	public string UserId;

	public string PassWord;

	public string DatabaseName;

	internal static string GetSqlConnectString(SqlConnectInfo sqlConnectInfo)
	{
		return "Data Source=" + sqlConnectInfo.ServerAddress + ";uid=" + sqlConnectInfo.UserId + ";pwd=" + sqlConnectInfo.PassWord + ";database=" + sqlConnectInfo.DatabaseName + ";Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1";
	}
}

using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyTitle("RxjhServer")]
[assembly: AssemblyDescription("V23.0")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("EVias")]
[assembly: AssemblyProduct("RxjhServer")]
[assembly: AssemblyCopyright("Copyright © Evias 2025")]
[assembly: AssemblyTrademark("EVias™")]
[assembly: ComVisible(false)]
[assembly: Guid("afbcbe80-6df6-4694-bfd1-b1fe82b13a4e")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]




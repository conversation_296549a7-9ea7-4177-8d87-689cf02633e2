using System.Collections.Concurrent;
using System.Collections.Generic;

namespace RxjhServer;

public class 制作物品类
{
	public List<制作需要物品类> 需要物品 = new List<制作需要物品类>();

	public int 物品ID;

	public string 物品名;

	public int 物品数量;

	public int 制作类型;

	public int 制作等级;

	public int 消耗精力;

	public int 制作成功率;

	public static ConcurrentDictionary<int, 制作物品类> Get制作物品类列表(int int_0, int int_1)
	{
		ConcurrentDictionary<int, 制作物品类> concurrentDictionary = new ConcurrentDictionary<int, 制作物品类>();
		foreach (制作物品类 value in World.制作物品列表.Values)
		{
			if ((value.制作类型 == int_0 || value.制作类型 == 4) && int_1 >= value.制作等级)
			{
				concurrentDictionary.TryAdd(value.物品ID, value);
			}
		}
		return concurrentDictionary;
	}
}

using System;

namespace RxjhServer;

public class 兑换码Class
{
	private int _ID;

	private string _FLD_CDK;

	private string _玩家名字;

    private string _玩家帐号;

    private int _称号;

	private int _是否使用;

	private int _物品编号;

	private int _元宝;

	private string _IP地址;

	private DateTime _时间;

	private string _说明;

	private string _分区;

	private string _硬件指纹; // EVIAS 硬件指纹

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public string FLD_CDK
	{
		get
		{
			return _FLD_CDK;
		}
		set
		{
			_FLD_CDK = value;
		}
	}

	public string 玩家名字
	{
		get
		{
			return _玩家名字;
		}
		set
		{
			_玩家名字 = value;
		}
	}

    public string 玩家帐号
    {
        get
        {
            return _玩家帐号;
        }
        set
        {
            _玩家帐号 = value;
        }
    }

    public int 称号
	{
		get
		{
			return _称号;
		}
		set
		{
			_称号 = value;
		}
	}

	public int 物品编号
	{
		get
		{
			return _物品编号;
		}
		set
		{
			_物品编号 = value;
		}
	}

	public int 元宝
	{
		get
		{
			return _元宝;
		}
		set
		{
			_元宝 = value;
		}
	}

	public string IP地址
	{
		get
		{
			return _IP地址;
		}
		set
		{
			_IP地址 = value;
		}
	}

	public int 是否使用
	{
		get
		{
			return _是否使用;
		}
		set
		{
			_是否使用 = value;
		}
	}

	public DateTime 时间
	{
		get
		{
			return _时间;
		}
		set
		{
			_时间 = value;
		}
	}

	public string 说明
	{
		get
		{
			return _说明;
		}
		set
		{
			_说明 = value;
		}
	}

	public string 分区
	{
		get
		{
			return _分区;
		}
		set
		{
			_分区 = value;
		}
	}

	public string 硬件指纹 // EVIAS  硬件指纹
	{
		get
		{
			return _硬件指纹;
		}
		set
		{
			_硬件指纹 = value;
		}
	}

	public static int CDK反值(string CDK) //EVIAS 增加其它礼包
    {
        foreach (兑换码Class value in World.兑换码.Values)
        {
            if (value.FLD_CDK == CDK && value.玩家名字 == "")
            {
                if (value.说明 == "动态称号")
                {
                    return 1; // 类型1：动态称号
                }
                else if (value.说明 == "其它礼包")
                {
                    return 3; 
                }
            }
        }
        return 0; 
    }
}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using RxjhServer.DbClss;
using RxjhServer.Network;
using System.Numerics;
using RxjhServer.HelperTools;
using Newtonsoft.Json.Linq;

namespace RxjhServer
{
    public static class 攻击系统
    {
        public static void 攻击(Players player, byte[] data, int length)
        {
            int num = 0;
            try
            {
                if (player.人物坐标_地图 == 45001 && !World.极限比武台服务.IsAttackAllowed(player))
                {
                    return;
                }
                if (World.当前是否是银币线路 == 1 && player.人物坐标_地图 != 8001 && player.人物坐标_地图 != 8002 && player.人物坐标_地图 != 8003 && player.人物坐标_地图 != 8004 && player.人物坐标_地图 != 8005 && player.人物坐标_地图 != 801 && player.人物坐标_地图 != 7101 && player.人物坐标_地图 != 7001 && player.人物坐标_地图 != 41001 && player.人物坐标_地图 != 2301 && player.人物坐标_地图 != 42001 && player.人物坐标_地图 != 43001)
                {
                    player.系统提示("银币广场互通线禁止活动外地图打怪PK。");
                    return;
                }
                if (player.安全锁状态ID == 1)
                {
                    player.系统提示("账号锁定状态禁止操作,点锁图标解锁。", 10, "系统提示");
                    return;
                }
                player.封包修改(data, length);
                num = 1;
                if (player.人物_HP <= 0 || player.Player死亡 || player.退出中 || player.交易.交易中 || player.打开仓库中 || player.Client.挂机 || player.进店中 || player.人物锁定 || player.个人商店 != null)
                {
                    return;
                }
                if (player.潜行模式 == 1)
                {
                    player.潜行状态(0);
                }
                byte[] array = new byte[4];
                Buffer.BlockCopy(data, 4, array, 0, 2);
                int num2 = BitConverter.ToInt32(array, 0);
                num = 2;
                byte[] array2 = new byte[4];
                byte[] array3 = new byte[4];
                Buffer.BlockCopy(data, 14, array2, 0, 4);
                Buffer.BlockCopy(data, 10, array3, 0, 2);
                int num3 = BitConverter.ToInt32(array2, 0);
                int num4 = BitConverter.ToInt32(array3, 0);
                num = 3;
                byte[] array4 = new byte[4];
                byte[] array5 = new byte[4];
                byte[] array6 = new byte[4];
                Buffer.BlockCopy(data, 18, array4, 0, 4);
                Buffer.BlockCopy(data, 22, array5, 0, 4);
                Buffer.BlockCopy(data, 26, array6, 0, 4);
                byte[] array7 = new byte[4];
                num = 4;
                player.自动坐标Stop();
                if (num3 == 0 && player.江湖小助手打怪模式 == 1)
                {
                    if (player.组队id != 0 && player.Player_Job == 5)
                    {
                        num3 = 501203;
                        if (player.Player_Job_leve >= 6)
                        {
                            if (!player.追加状态列表.ContainsKey(501501))
                            {
                                num3 = 501501;
                            }
                            else if (!player.追加状态列表.ContainsKey(501502))
                            {
                                num3 = 501502;
                            }
                            else if (!player.追加状态列表.ContainsKey(501601))
                            {
                                num3 = 501601;
                            }
                            else if (!player.追加状态列表.ContainsKey(501602))
                            {
                                num3 = 501602;
                            }
                            else if (!player.追加状态列表.ContainsKey(501603))
                            {
                                num3 = 501603;
                            }
                            num4 = player.人物全服ID;
                        }
                        else
                        {
                            if (!player.追加状态列表.ContainsKey(501301))
                            {
                                num3 = 501301;
                            }
                            else if (!player.追加状态列表.ContainsKey(501303))
                            {
                                num3 = 501303;
                            }
                            else if (!player.追加状态列表.ContainsKey(501401))
                            {
                                num3 = 501401;
                            }
                            else if (!player.追加状态列表.ContainsKey(501402))
                            {
                                num3 = 501402;
                            }
                            else if (!player.追加状态列表.ContainsKey(501403))
                            {
                                num3 = 501403;
                            }
                            num4 = player.人物全服ID;
                        }
                    }
                    else if (player.组队id != 0 && player.Player_Job == 4 && !player.追加状态列表.ContainsKey(401303))
                    {
                        num3 = 401303;
                        if (!player.追加状态列表.ContainsKey(401202))
                        {
                            num3 = 401202;
                        }
                        else if (!player.追加状态列表.ContainsKey(401203))
                        {
                            num3 = 401203;
                        }
                        else if (!player.追加状态列表.ContainsKey(401301))
                        {
                            num3 = 401301;
                        }
                        else if (!player.追加状态列表.ContainsKey(401302))
                        {
                            num3 = 401302;
                        }
                        num4 = player.人物全服ID;
                    }
                    else
                    {
                        num3 = player.江湖小助手武功ID;
                    }
                }
                num = 5;
                if (num4 < 10000 && World.禁止PK != 0)
                {
                    player.系统提示("本线禁止PK", 9, "系统提示");
                    return;
                }
                num = 6;
                if (player.人物坐标_地图 == 8001 && World.武林血战进程 != 2)
                {
                    player.系统提示("回合未开始,禁止PK", 9, "系统提示");
                    return;
                }
                if (num3 == 0)
                {
                    Buffer.BlockCopy(data, 34, array7, 0, 4);
                    player.当前杀怪数量 = BitConverter.ToInt32(array7, 0);
                    num = 7;
                }
                攻击(player, num3, num4, array4, array6, array5);
                num = 8;
            }
            catch (Exception ex)
            {
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "1攻击" + num + "|" + player.人物全服ID + "|" + player.ToString() + "|" + ex.Message);
                }
            }
        }

        public static void 攻击(Players player, int 武功ID, int 人物ID, byte[] Rxjh__X, byte[] Rxjh__Y, byte[] Rxjh__Z)
        {
            int num = 0;
            try
            {
                player.人物坐标_X = BitConverter.ToSingle(Rxjh__X, 0);
                player.人物坐标_Y = BitConverter.ToSingle(Rxjh__Y, 0);
                player.上次坐标X = player.人物坐标_X;
                player.上次坐标Y = player.人物坐标_Y;
                player.移动时间 = DateTime.Now;
                num = 1;
                int num2 = (int)DateTime.Now.Subtract(player.Pktime).TotalMilliseconds;
                if (World.外挂PK时间 > 0 && 人物ID < 10000 && 武功ID == player.Pk武功ID)
                {
                    if (num2 < World.外挂PK时间)
                    {
                        player.发送激活技能数据(武功ID, 0);
                        return;
                    }
                    if (num2 < World.外挂PK时间 / 3)
                    {
                        player.发送激活技能数据(武功ID, 0);
                        return;
                    }
                }
                player.Pk武功ID = 武功ID;
                if (player.人物灵兽 != null && player.人物灵兽.骑 == 1 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100001 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100002 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100003 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100004 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100005 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100006 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100007 && player.人物灵兽.宠物以装备[4].Get物品ID != 601100008)
                {
                    player.发送激活技能数据(武功ID, 10);
                    return;
                }
                if (player.异常状态 != null && player.GetAbnormalState(8))
                {
                    player.发送激活技能数据(武功ID, 11);
                    return;
                }
                if (player.人物坐标_地图 == 9001 || player.人物坐标_地图 == 9101 || player.人物坐标_地图 == 9201)
                {
                    player.发送激活技能数据(武功ID, 11);
                    return;
                }
                player.PK死亡 = false;
                player.Player无敌 = false;
                if (武功ID != 0)
                {
                    if (num2 < 1500)
                    {
                        return;
                    }
                    player.自动攻击Stop();
                    if (!World.TBL_KONGFU.TryGetValue(武功ID, out var value) || ((value.FLD_武功类型 == 0 || value.FLD_武功类型 == 3) && BitConverter.ToInt32(Rxjh__Z, 0) == 0))
                    {
                        return;
                    }
                    if (人物ID >= 10000 && World.是否允许快速攻击 == 0)
                    {
                        if (num2 < value.FLD_TIME)
                        {
                            return;
                        }
                    }
                    else if (num2 < World.外挂PK时间)
                    {
                        return;
                    }
                    player.攻击确认次数 = 0;
                    int num3 = 判断是否触发绝命技(player, 人物ID, 武功ID);
                    num = 2;
                    武功类 武功类2 = World.TBL_KONGFU[武功ID];
                    if (武功ID != 0 && 武功类2.FLD_TYPE >= 5 && num3 == -1)
                    {
                        num = 3;
                        return;
                    }
                    num = 4;
                    player.连续攻击确认次数 = 1;
                    魔法攻击(player, 武功ID, 人物ID);
                    player.Pktime = DateTime.Now;
                }
                else if (num2 > 500)
                {
                    player.攻击确认次数 = 0;
                    player.连续攻击确认次数 = 1;
                    num = 5;
                    物理攻击(player, 人物ID);
                    player.Pktime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "攻击处理", $"步骤: {num}, 武功ID: {武功ID}, 目标: {人物ID}");
                player.自动攻击Stop();
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "2攻击" + num + "|" + player.人物全服ID + "|" + 武功ID + "|" + ex.Message);
                }
            }
        }

        public static void 物理攻击(Players player, int 人物ID)
        {
            try
            {
                if (人物ID >= 10000)
                {
                    物理攻击怪物(player, 人物ID);
                }
                else
                {
                    物理攻击人物(player, 人物ID);
                }
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "物理攻击", $"目标ID: {人物ID}");
                player.系统提示("你物理攻击出错,请联系客服处理");
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "物理攻击|" + player.报错次数阀值 + "|" + player.UserName + "|" + ex.Message);
                }
            }
        }

        public static void 物理攻击怪物(Players player, int 目标人物ID)
        {
            int num = 0;
            try
            {
                if (!MapClass.GetnpcTemplate(player.人物坐标_地图).TryGetValue(目标人物ID, out var value))
                {
                    return;
                }
                if (!player.攻击怪物检测(value, 0))
                {
                    player.自动攻击Stop();
                    return;
                }
                num = 2;
                double num2 = value.FLD_DF * (World.怪物防御百分比 - value.FLD_JSDF);
                double num3 = player.FLD_人物基本_攻击;

                //24.0 EVIAS 灵兽特殊效果增加 20250722 
                bool 灵兽无视防御 = false;
                bool 灵兽出血效果 = false;
                bool 灵兽狂暴效果 = false;

                if (player.人物灵兽 != null && player.人物灵兽.FLD_LEVEL >= 15)
                {
                    if (player.人物灵兽.武功新[1, 4] != null && player.人物灵兽.武功新[1, 4].FLD_PID == 1401)
                    {
                        var 武功 = player.人物灵兽.武功新[1, 4];
                        double 基础概率 = 1.6; // 基础1.6%概率 
                        double 等级加成 = 武功.武功_等级 * 0.4;
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            灵兽无视防御 = true;
                            player.显示大字(player.人物灵兽.全服ID, 1401); // 灵兽无视防御效果
                        }
                    }

                    if (player.人物灵兽.武功新[1, 5] != null && player.人物灵兽.武功新[1, 5].FLD_PID == 1402)
                    {
                        var 武功 = player.人物灵兽.武功新[1, 5];
                        double 基础概率 = 0.9; // 基础0.9%概率 
                        double 等级加成 = 武功.武功_等级 * 0.1; 
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            灵兽出血效果 = true;
                            player.显示大字(player.人物灵兽.全服ID, 1402); // 灵兽出血效果
                        }
                    }

                    if (player.人物灵兽.武功新[1, 6] != null && player.人物灵兽.武功新[1, 6].FLD_PID == 1403)
                    {
                        var 武功 = player.人物灵兽.武功新[1, 6];
                        double 基础概率 = 1.3; // 基础1.3%概率 
                        double 等级加成 = 武功.武功_等级 * 0.2; 
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            灵兽狂暴效果 = true;
                            player.显示大字(player.人物灵兽.全服ID, 1403); // 灵兽狂暴效果
                        }
                    }
                }

                if (灵兽无视防御)
                {
                    num2 *= 0.3; // 无视70%防御，只保留30%防御
                }

                if (player.FLD_装备_追加_降低百分比防御 + player.武勋降低百分比防御 > 0.0 && !灵兽无视防御)
                {
                    num2 *= 1.0 - player.FLD_装备_追加_降低百分比防御 - player.武勋降低百分比防御;
                }
                if (value.FLD_BOSS == 0 && player.中级附魂_复仇 > 0 && RNG.Next(1, 100) <= player.中级附魂_复仇)
                {
                    player.显示大字(player.人物全服ID, 401);
                    int num4 = 0;
                    num4 = ((value.Level <= 130) ? (value.Max_Rxjh_HP / 3) : 20000);
                    value.发送复仇显示伤害血量(num4);
                    value.Rxjh_HP -= num4;
                    value.Play_Add(player, num4);
                    if (value.Rxjh_HP <= 0 && !value.NPC死亡)
                    {
                        value.发送死亡数据(player.人物全服ID);
                    }
                }
                num = 3;
                if (player.离线挂机打怪模式 != 1)
                {
                    player.组队升天四气功触发(player);
                }
                if (player.Player_Job == 1)
                {
                    if ((double)RNG.Next(1, 110) <= player.破甲几率)
                    {
                        num2 *= player.得到气功加成值(1, 5, 2);
                        player.显示大字(player.人物全服ID, 16);
                    }
                }
                else if (player.Player_Job == 2)
                {
                    double num5 = player.剑_无坚不摧 + player.剑_乘胜追击;
                    double num6 = RNG.Next(1, 100);
                    if (num6 < player.剑_无坚不摧)
                    {
                        player.显示大字(player.人物全服ID, 120);
                        num2 *= 0.5;
                    }
                    else if (num6 < num5)
                    {
                        if (player.剑_乘胜追击 > 0.5)
                        {
                            player.剑_乘胜追击 = 0.5;
                        }
                        player.显示大字(player.人物全服ID, 120);
                        num2 *= player.得到气功加成值(2, 9, 2) - player.剑_乘胜追击 * 0.01;
                    }
                }
                else if (player.Player_Job == 8)
                {
                    if ((double)RNG.Next(1, 110) <= player.破甲几率)
                    {
                        num2 *= player.得到气功加成值(8, 7, 2);
                        player.显示大字(player.人物全服ID, 16);
                    }
                }

                double num7 = num3;
                int num8 = player.FLD_人物基本_命中;
                if (player.Player_Job == 6)
                {
                    if (player.刺_连消带打数量 != 0.0)
                    {
                        num7 += player.刺_连消带打数量 * 0.5;
                        player.刺_连消带打数量 = 0.0;
                    }
                    if (player.追加状态列表 != null && player.GetAddState(801201))
                    {
                        num8 = (int)((double)num8 * (1.0 + player.刺_先发制人));
                    }
                }
                double num9 = num7 - num2 + (double)num8 * 0.25;
                num = 4;
                int num10 = RNG.Next(0, 1) switch
                {
                    1 => 127,
                    0 => 126,
                    _ => 126,
                };

                if (BitConverter.ToInt32(player.装备栏已穿装备[3].物品ID, 0) == 0)
                {
                    Random random = new Random();
                    int 攻击类型 = RNG.Next(0, 1) switch
                    {
                        1 => 113,
                        0 => 112,
                        _ => 112,
                    };
                    if (player.Player_Job == 6)
                    {
                        int num11 = num10;
                        int num12 = num11;
                        if ((uint)(num12 - 112) <= 1u)
                        {
                            num9 *= 2.0;
                        }
                    }
                    double num13 = random.Next((int)num9 - 15, (int)num9 + 15);
                    if (num13 <= 1.0)
                    {
                        num13 = RNG.Next(1, 5);
                    }
                    攻击计算完成(player, 目标人物ID, 0, (int)num13, 攻击类型, value.Rxjh_HP, 0);
                    return;
                }
                if (player.Player_Job == 1)
                {
                    if ((double)RNG.Next(1, 100) < player.暗影绝杀)
                    {
                        player.显示大字(player.人物全服ID, 18);
                        num9 *= player.得到气功加成值(1, 9, 2);
                    }
                }
                else if (player.Player_Job == 2)
                {
                    if ((double)RNG.Next(1, 110) < player.剑_怒海狂澜)
                    {
                        num9 *= player.得到气功加成值(2, 7, 2);
                        player.显示大字(player.人物全服ID, 82);
                    }
                    if (player.剑_破天一剑 != 0.0)
                    {
                        num9 *= 1.0 + player.剑_破天一剑;
                    }
                }
                else if (player.Player_Job == 4)
                {
                    if ((double)RNG.Next(1, 130) <= player.弓_心神凝聚)
                    {
                        num10 = 136;
                        num9 *= player.得到气功加成值(4, 7, 2);
                    }
                    else if ((double)RNG.Next(1, 130) <= player.弓_流星三矢)
                    {
                        player.显示大字(player.人物全服ID, 47);
                        if (player.追加状态列表.ContainsKey(700047))
                        {
                            player.追加状态列表[700047].时间结束事件();
                        }
                        追加状态类 追加状态类2 = new 追加状态类(player, player.弓_流星三矢时间, 700047.0, 1.0);
                        player.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
                        player.状态效果(BitConverter.GetBytes(700047), 1, (int)player.弓_流星三矢时间);
                        player.更新武功和状态();
                        if (player.追加状态列表.ContainsKey(700047))
                        {
                            num9 = ((num7 - num2) * 1.5 + (double)player.FLD_人物基本_命中 * 0.25) * 2.0;
                            num10 = 129;
                        }
                    }
                    else
                    {
                        switch (RNG.Next(0, 6))
                        {
                            default:
                                num10 = 126;
                                break;
                            case 1:
                                num10 = 126;
                                break;
                            case 2:
                                num10 = 126;
                                break;
                            case 3:
                                num10 = 127;
                                break;
                            case 4:
                                num10 = 128;
                                num9 = ((num7 - num2) * 1.5 + (double)player.FLD_人物基本_命中 * 0.25) * 2.0;
                                break;
                            case 5:
                                num10 = 134;
                                num9 = ((num7 - num2) * 1.5 + (double)player.FLD_人物基本_命中 * 0.25) * 2.5;
                                break;
                        }
                    }
                    if ((double)RNG.Next(1, 130) <= player.弓_致命绝杀)
                    {
                        num9 *= player.得到气功加成值(4, 11, 2);
                        player.显示大字(player.人物全服ID, 140);
                        player.触发弓箭致命绝杀 = true;
                    }
                    num9 += player.弓_锐利之箭 + 1.0;
                    if ((double)RNG.Next(1, 130) <= player.弓_无明暗矢)
                    {
                        num9 *= 1.05 + player.弓_无明暗矢 * 0.01;
                        player.显示大字(player.人物全服ID, 49);
                        player.触发物理无明暗矢 = true;
                    }
                    if ((double)RNG.Next(1, 100) <= player.升天五式_千里一击)
                    {
                        num9 *= 1.0 + player.升天五式_千里一击 * 0.01;
                        player.显示大字(player.人物全服ID, 1017);
                    }
                }
                else if (player.Player_Job == 6)
                {
                    double num14 = player.刺_心神凝聚;
                    if (player.当前激活技能ID != 0 && player.当前激活技能ID != 830401 && player.当前激活技能ID != 840401 && num14 != 0.0)
                    {
                        num14 += 20.0;
                    }
                    if ((double)RNG.Next(1, 130) <= num14)
                    {
                        num10 = 136;
                        num9 *= player.得到气功加成值(6, 3, 2);
                        if (player.刺_致手绝命 != 0.0)
                        {
                            num9 += num9 * player.刺_致手绝命;
                        }
                    }
                    else if ((double)RNG.Next(1, 130) <= player.刺_以怒还怒)
                    {
                        player.显示大字(player.人物全服ID, 372);
                        num10 = 134;
                        num9 *= player.得到气功加成值(6, 6, 2);
                        if ((double)RNG.Next(1, 100) <= player.刺_一招残杀)
                        {
                            player.显示大字(player.人物全服ID, 1022);
                            num9 *= player.得到气功加成值(6, 11, 2);
                        }
                    }
                    else if ((double)RNG.Next(1, 130) <= player.刺_连环飞舞)
                    {
                        player.显示大字(player.人物全服ID, 272);
                        num10 = RNG.Next(0, 5) switch

                        {
                            1 => 129,
                            2 => 130,
                            3 => 131,
                            4 => 132,
                            5 => 133,
                            _ => 129,
                        };

                        num9 *= 1.3;
                    }
                    if (player.刺_荆轲之怒 != 0.0)
                    {
                        player.人物_SP += (int)(3.0 + (double)player.Player_Level * 0.5 * 0.01 * player.刺_荆轲之怒);
                    }
                    else if (num9 <= 0.0)
                    {
                        player.人物_SP++;
                    }
                    else
                    {
                        player.人物_SP += 2;
                    }
                    if (player.刺_升天三气功_无情打击 != 0.0)
                    {
                        num9 += num7 * player.刺_升天三气功_无情打击;
                    }
                    if (player.当前激活技能ID != 0 && World.TBL_KONGFU.TryGetValue(player.当前激活技能ID, out var value2))
                    {
                        int num16;
                        if (value2.FLD_武功类型 == 3)
                        {
                            int num15 = value2.FLD_MP + (player.武功新[3, value2.FLD_INDEX].武功_等级 - 1) * player.武功新[3, value2.FLD_INDEX].FLD_每级加MP;
                            if (player.人物_MP < num15)
                            {
                                player.发送激活技能数据(value2.FLD_PID, 2);
                                return;
                            }
                            num16 = value2.FLD_AT + (player.武功新[3, value2.FLD_INDEX].武功_等级 - 1) * value2.FLD_每级加危害;
                            player.魔法使用(num15);
                        }
                        else
                        {
                            if (player.人物_MP < value2.FLD_MP)
                            {
                                player.发送激活技能数据(value2.FLD_PID, 2);
                                return;
                            }
                            num16 = value2.FLD_AT;
                            player.魔法使用(value2.FLD_MP);
                        }
                        num9 = ((player.当前激活技能ID == 830401 || player.当前激活技能ID == 840401) ? (num9 + (double)num16 * 0.5) : (num9 + (double)num16 * 0.25));
                    }
                }
                else if (player.Player_Job == 7)
                {
                    num10 = RNG.Next(0, 5) switch
                    {
                        1 => 126,
                        2 => 129,
                        3 => 134,
                        _ => 126,
                    };
                    if (!player.检查和弦状态())
                    {
                        if ((double)RNG.Next(1, 110) < player.琴师_梅花三弄)
                        {
                            player.显示大字(player.人物全服ID, 87);
                            int num17 = RNG.Next(900401, 900404);
                            追加状态类 value3 = new 追加状态类(player, 10000, num17, 0);
                            player.追加状态列表.TryAdd(num17, value3);
                            player.状态效果(BitConverter.GetBytes(num17), 1, 10000);
                            player.更新人物数据(player);
                        }
                    }
                    else if (player.追加状态列表.ContainsKey(900401))
                    {
                        if ((double)RNG.Next(1, 100) < player.琴师_阳明春晓概率 + player.琴_三和弦_状态效果 && !value.异常状态.ContainsKey(22))
                        {
                            double num18 = player.琴师_阳明春晓时间;
                            异常状态类 value4 = new 异常状态类(value, player.人物全服ID, (int)num18, 22, 0.0);
                            value.异常状态.TryAdd(22, value4);
                            value.FLD_JSAT = player.琴师_阳明春晓攻击;
                            if ((double)RNG.Next(1, 100) <= player.琴师_阳明春晓绝望)
                            {
                                if (value.异常状态.ContainsKey(23))
                                {
                                    value.异常状态[23].时间结束事件();
                                }
                                异常状态类 value5 = new 异常状态类(value, player.人物全服ID, 20000, 23, 0.0);
                                value.异常状态.TryAdd(23, value5);
                            }
                        }
                        if ((double)RNG.Next(1, 100) < player.琴师_潇湘雨夜概率 + player.琴_三和弦_状态效果 && !value.异常状态.ContainsKey(25))
                        {
                            double num19 = player.琴师_潇湘雨夜时间;
                            异常状态类 value6 = new 异常状态类(value, player.人物全服ID, (int)num19, 25, 0.0);
                            value.异常状态.TryAdd(25, value6);
                            value.FLD_JSDF = player.琴师_潇湘雨夜防御;
                            if ((double)RNG.Next(1, 100) <= player.琴师_潇湘雨夜不安)
                            {
                                if (value.异常状态.ContainsKey(24))
                                {
                                    value.异常状态[24].时间结束事件();
                                }
                                异常状态类 value7 = new 异常状态类(value, player.人物全服ID, 20000, 24, 0.0);
                                value.异常状态.TryAdd(24, value7);
                            }
                        }
                    }
                }
                else if (player.Player_Job == 9)
                {
                    if ((double)RNG.Next(1, 100) < player.谭_怒海狂澜)
                    {
                        num9 *= player.得到气功加成值(9, 10, 2);
                        player.显示大字(player.人物全服ID, 82);
                    }
                }
                else if (player.Player_Job == 11)
                {
                    if ((double)RNG.Next(1, 100) <= player.梅_升天一气功_玄武雷电)
                    {
                        num9 *= 1.4;
                        player.显示大字(player.人物全服ID, 803);
                    }
                    if (player.人物坐标_地图 != 7301 && (double)RNG.Next(1, 100) <= player.梅_升天二气功_玄武诅咒)
                    {
                        num9 += (double)player.人物最大_HP * 0.2;
                        player.显示大字(player.人物全服ID, 806);
                    }
                }
                switch (player.Player_Job)
                {
                    case 1:
                        if ((double)RNG.Next(1, 100) <= player.刀_连环飞舞)
                        {
                            int num20 = RNG.Next(0, 10);
                            num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                            switch (num20)
                            {
                                case 1:
                                    num10 = 129;
                                    break;
                                case 2:
                                    num10 = 130;
                                    break;
                                case 3:
                                    num10 = 131;
                                    break;
                                case 4:
                                    num10 = 132;
                                    break;
                                case 5:
                                    num10 = 133;
                                    break;
                                default:
                                    num10 = 129;
                                    break;
                                case 7:
                                    num10 = 128;
                                    break;
                                case 8:
                                    num10 = 134;
                                    num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                    break;
                            }
                        }
                        break;
                    case 2:
                        if ((double)RNG.Next(1, 100) <= player.剑_连环飞舞)
                        {
                            int num21 = RNG.Next(0, 10);
                            num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                            switch (num21)
                            {
                                case 1:
                                    num10 = 129;
                                    break;
                                case 2:
                                    num10 = 130;
                                    break;
                                case 3:
                                    num10 = 131;
                                    break;
                                case 4:
                                    num10 = 132;
                                    break;
                                case 5:
                                    num10 = 133;
                                    break;
                                default:
                                    num10 = 129;
                                    break;
                                case 7:
                                    num10 = 128;
                                    break;
                                case 8:
                                    num10 = 134;
                                    num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                    break;
                            }
                        }
                        break;
                    case 3:
                    case 12:
                        if ((double)RNG.Next(1, 100) <= player.枪_连环飞舞)
                        {
                            num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                            switch (RNG.Next(0, 10))
                            {
                                case 1:
                                    num10 = 129;
                                    break;
                                case 2:
                                    num10 = 130;
                                    break;
                                case 3:
                                    num10 = 131;
                                    break;
                                case 4:
                                    num10 = 132;
                                    break;
                                case 5:
                                    num10 = 133;
                                    break;
                                default:
                                    num10 = 129;
                                    break;
                                case 7:
                                    num10 = 128;
                                    break;
                                case 8:
                                    num10 = 134;
                                    num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                    break;
                            }
                        }
                        break;
                    case 5:
                        num10 = RNG.Next(0, 6) switch
                        {
                            1 => 126,
                            2 => 126,
                            3 => 127,
                            6 => 127,
                            _ => 126,
                        };
                        break;

                    case 7:
                        switch (RNG.Next(0, 10))
                        {
                            case 1:
                                num10 = 126;
                                break;
                            case 2:
                                num10 = 126;
                                break;
                            case 3:
                                num10 = 127;
                                break;
                            case 4:
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                num10 = 128;
                                break;
                            case 5:
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                num10 = 129;
                                break;
                            case 6:
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                num10 = 130;
                                break;
                            default:
                                num10 = 126;
                                break;
                            case 8:
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                num10 = 132;
                                break;
                            case 9:
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                num10 = 133;
                                break;
                            case 10:
                                num10 = 134;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                break;
                        }
                        break;
                    case 8:
                    case 9:
                    case 10:
                        switch (RNG.Next(0, 10))
                        {
                            default:
                                num10 = 126;
                                break;
                            case 1:
                                num10 = 126;
                                break;
                            case 2:
                                num10 = 127;
                                break;
                            case 3:
                                num10 = 128;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 4:
                                num10 = 128;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 5:
                                num10 = 129;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 6:
                                num10 = 130;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 7:
                                num10 = 131;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 8:
                                num10 = 132;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 9:
                                num10 = 133;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                break;
                            case 10:
                                num10 = 134;
                                num9 = ((num7 - num2) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                break;
                        }
                        break;
                    case 11:
                        switch (RNG.Next(0, 6))
                        {
                            default:
                                num10 = 126;
                                break;
                            case 1:
                                num10 = 126;
                                break;
                            case 2:
                                num10 = 126;
                                break;
                            case 3:
                                num10 = 127;
                                break;
                            case 4:
                                num10 = 128;
                                num9 = ((num7 - num2) * 1.5 + (double)player.FLD_人物基本_命中 * 0.25) * 2.0;
                                break;
                            case 5:
                                num10 = 134;
                                num9 = ((num7 - num2) * 1.5 + (double)player.FLD_人物基本_命中 * 0.25) * 2.5;
                                break;
                        }
                        break;
                    case 13:
                        num10 = RNG.Next(0, 6) switch
                        {
                            1 => 126,
                            2 => 126,
                            3 => 127,
                            6 => 127,
                            _ => 126,
                        };
                        break;
                }

                if (player.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= player.FLD_装备_追加_中毒概率百分比 && !value.ContainsKeyInAbnormalState(3))
                {
                    异常状态类 value8 = new 异常状态类(value, value.FLD_INDEX, 60000, 3, 0.0);
                    value.异常状态.TryAdd(3, value8);
                }
                num = 5;
                double num22 = RNG.Next((int)num9 - 15, (int)num9 + 15) + player.装备追加对怪攻击 + player.药品追加对怪攻击 + player.讨伐追加对怪攻击;
                if (num22 <= 0.0)
                {
                    num22 = RNG.Next(1, 5);
                }
                else
                {
                    if (player.Player_Job == 2)
                    {
                        if ((double)RNG.Next(1, 110) <= player.剑_冲冠一怒 && !player.怒)
                        {
                            player.显示大字(player.人物全服ID, 29);
                            player.人物_SP += (int)((double)player.人物_SP * player.剑_冲冠一怒 * 0.005);
                        }
                        if ((double)RNG.Next(1, 100) <= player.剑_移花接木)
                        {
                            player.显示大字(player.人物全服ID, 26);
                            player.加血((int)(num22 * 0.5));
                        }
                    }
                    if (player.Player_Job == 9)
                    {
                        if ((double)RNG.Next(1, 110) <= player.谭_冲冠一怒 && !player.怒)
                        {
                            player.显示大字(player.人物全服ID, 29);
                            player.人物_SP += (int)((double)player.人物_SP * player.谭_冲冠一怒 * 0.005);
                        }
                        if ((double)RNG.Next(1, 100) <= player.谭_移花接木)
                        {
                            player.显示大字(player.人物全服ID, 26);
                            player.加血((int)(num22 * 0.5));
                        }
                    }
                }
                if (num22 <= 1.0)
                {
                    num22 = RNG.Next(1, 5);
                }
                else if (player.中级附魂_愤怒 != 0 && RNG.Next(1, 100) <= player.中级附魂_愤怒)
                {
                    num22 *= 1.2;
                    player.显示大字(player.人物全服ID, 404);
                }
                if (player.Player_Job == 6)
                {
                    switch (num10)
                    {
                        case 126:
                        case 127:
                            num22 *= 1.4;
                            break;
                        case 128:
                        case 129:
                        case 130:
                        case 131:
                        case 132:
                        case 133:
                            num22 *= 1.8;
                            break;
                        case 136:
                            num22 *= 2.0;
                            break;
                        case 134:
                            num22 *= 3.0;
                            break;
                    }
                }
                if (num22 > 1.0)
                {
                    num22 *= 获得物理对怪伤害(player.Player_Job, player.Player_Job_leve) + player.人物对怪物总伤害;
                }
                if (player.人物灵兽 != null)
                {
                    int num23 = 0;
                    if (player.人物灵兽.武功新[0, 3] != null)
                    {
                        num23 = 200;
                    }
                    else if (player.人物灵兽.武功新[0, 2] != null)
                    {
                        num23 = 150;
                    }
                    else if (player.人物灵兽.武功新[0, 1] != null)
                    {
                        num23 = 100;
                    }

                    //24.0 EVIAS 灵兽特殊效果 - 狂暴效果
                    double 灵兽攻击力加成 = 1.0;
                    if (灵兽狂暴效果)
                    {
                        灵兽攻击力加成 = 1.5; // 狂暴效果：攻击力增加50%
                        player.显示大字(player.人物灵兽.全服ID, 1403); 
                    }

                    num22 += (double)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num23) * 灵兽攻击力加成;
                }
                if (player.FLD_装备_追加_伤害值 > 0)
                {
                    num22 += (double)player.FLD_装备_追加_伤害值;
                }

                //24.0 EVIAS 灵兽出血效果
                if (灵兽出血效果)
                {
                    if (!value.ContainsKeyInAbnormalState(55))
                    {
                        double 出血伤害 = player.人物灵兽.灵兽基本攻击; // 每秒造成灵兽攻击力的伤害
                        异常状态类 出血状态 = new 异常状态类(player, value, player.人物全服ID, 10000, 55, 出血伤害); // 10秒出血，使用传统出血图标 55
                        出血状态.异常状态类出血怪物(player, 出血伤害);
                        value.异常状态.TryAdd(55, 出血状态);

                    }
                }

                攻击计算完成(player, 目标人物ID, 0, (int)num22, num10, value.Rxjh_HP, 0);
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "物理攻击怪物", $"步骤: {num}, 目标怪物ID: {目标人物ID}");
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "物理攻击怪物[" + num + "]出错:" + player.报错次数阀值 + "/" + ex.Message);
                }
            }
        }

        public static void 物理攻击人物(Players player, int 目标人物ID)
        {
            int num = 0;
            try
            {
                if (!player.PlayList.TryGetValue(目标人物ID, out var value) || value.GM模式 == 19 || value.Player死亡 || value.人物_HP <= 0 || value.Player_Level < 35 || value.个人商店 != null || (player.人物坐标_地图 == 45001 && !World.极限比武台服务.IsAttackAllowed(value)))
                {
                    return;
                }
                if (player.Player死亡 || player.人物_HP <= 0 || player.Player_Level < 35 || player.个人商店 != null || player.人物PK模式 == 0 || player.打开仓库中)
                {
                    player.自动攻击Stop();
                }
                else if ((player.Player_Job == 4 || player.Player_Job == 11) && BitConverter.ToInt32(player.装备栏已穿装备[12].物品数量, 0) == 0 && player.装备栏已穿装备[3].Get物品ID != 0)
                {
                    player.发送激活技能数据(player.当前激活技能ID, 6);
                    player.初始化已装备物品();
                }
                else
                {
                    if (player.人物坐标_地图 != value.人物坐标_地图)
                    {
                        return;
                    }
                    foreach (int item in World.限制PK地图列表)
                    {
                        if (player.人物坐标_地图 == item)
                        {
                            player.系统提示("此地图禁止PK", 9, "限制地图");
                            return;
                        }
                    }
                    if (World.工作日限时地图是否开启 == 0)
                    {
                        foreach (int item2 in World.周末全天PK地图列表)
                        {
                            if (player.人物坐标_地图 == item2)
                            {
                                player.系统提示("此地图本时间段禁止PK", 9, "系统提示");
                                return;
                            }
                        }
                    }
                    if (World.限时地图是否开启 == 0)
                    {
                        foreach (int item3 in World.限时PK地图列表)
                        {
                            if (player.人物坐标_地图 == item3)
                            {
                                player.系统提示("此地图本时间段禁止PK", 9, "系统提示");
                                return;
                            }
                        }
                    }
                    num = 6;
                    if (player.人物PK模式 == 1 && player.人物坐标_地图 != 45001)
                    {
                        if (World.仙魔大战进程 != 0)
                        {
                            if (player.仙魔大战派别 == value.仙魔大战派别)
                            {
                                player.系统提示("仙魔大战中同种族禁止pk....", 9, "系统提示");
                                return;
                            }
                        }
                        else if (World.势力战进程 != 0)
                        {
                            if (player.Player_Zx == value.Player_Zx)
                            {
                                player.系统提示("势力战中同势力禁止pk....", 9, "系统提示");
                                return;
                            }
                        }
                        else if (player.Player_Zx == value.Player_Zx)
                        {
                            player.系统提示("同派系禁止pk修改pk模式在试试....", 9, "系统提示");
                            return;
                        }
                    }
                    num = 7;
                    if (player.人物坐标_地图 != 801 && player.人物坐标_地图 != 8001 && player.人物坐标_地图 != 41001 && player.人物坐标_地图 != 7301 && player.人物坐标_地图 != 42001 && Math.Abs(player.Player_Level - value.Player_Level) > World.PK等级差)
                    {
                        player.系统提示("等级差" + World.PK等级差 + "级以上，不能攻击。");
                        return;
                    }
                    if (player.Player_WuXun < World.武勋保护数量)
                    {
                        player.系统提示("武勋低于" + World.武勋保护数量 + "，无法主动pk。。");
                        return;
                    }
                    num = 8;
                    if (value.人物坐标_地图 != 2301 && player.人物坐标_地图 != 2301 && player.人物坐标_地图 != 7301 && value.人物坐标_地图 != 7301 && value.人物坐标_地图 != 41001 && player.人物坐标_地图 != 41001 && value.人物坐标_地图 != 801 && player.人物坐标_地图 != 801 && value.人物坐标_地图 != 42001 && player.人物坐标_地图 != 42001)
                    {
                        if (value.检查玩家是否在挂机双倍区域(player) && value.追加状态列表.ContainsKey(900000619) && !value.异常状态.ContainsKey(28))
                        {
                            value.安全模式 = 0;
                        }
                        else if (!value.检查玩家是否在挂机双倍区域(player) && value.追加状态列表.ContainsKey(900000619) && !value.异常状态.ContainsKey(28))
                        {
                            value.安全模式 = 1;
                        }
                        if (value.安全模式 == 1 && !value.安全区禁止下毒(player))
                        {
                            player.系统提示("对方处于安全模式中,禁止物理攻击。");
                            return;
                        }
                        if (player.安全模式 == 1 && !player.安全区禁止下毒(player))
                        {
                            player.系统提示("自己处于安全模式中,禁止物理攻击。");
                            return;
                        }
                        if (player.触发新手安全区(player) || player.触发新手安全区(value))
                        {
                            player.系统提示("地图安全区域中禁止物理攻击。");
                            return;
                        }
                        if (player.Player_WuXun < World.武勋保护数量)
                        {
                            player.系统提示("您武勋为负数到[" + World.武勋保护数量 + "]时无法主动PK");
                            return;
                        }
                    }
                    num = 11;
                    float num2 = value.人物坐标_X - player.人物坐标_X;
                    float num3 = value.人物坐标_Y - player.人物坐标_Y;
                    double num4 = Math.Sqrt((double)num2 * (double)num2 + (double)num3 * (double)num3);
                    switch (player.Player_Job)
                    {
                        default:
                            if (num4 > World.其他职业PK距离)
                            {
                                return;
                            }
                            break;
                        case 11:
                            if (num4 > World.梅柳真PK距离 + player.梅_玄武的指点)
                            {
                                return;
                            }
                            break;
                        case 5:
                            if (num4 > World.医生PK距离)
                            {
                                return;
                            }
                            break;
                        case 4:
                            if (num4 > World.弓箭手PK距离 + player.弓_猎鹰之眼)
                            {
                                return;
                            }
                            break;
                        case 13:
                            if (num4 > World.神女PK距离 * (1.0 + player.神女黑花漫开))
                            {
                                return;
                            }
                            break;
                    }
                    num = 12;
                    if (value.Player无敌)
                    {
                        player.自动攻击Stop();
                        player.系统提示("对方刚刚复活,请慢点攻击。", 50, "系统提示");
                        return;
                    }
                    num = 13;
                    int num5 = player.计算武器四神相克(player.装备栏已穿装备[3].FLD_FJ_四神之力, value.装备栏已穿装备[0].FLD_FJ_四神之力);
                    int num6 = player.计算衣服四神相克(player.装备栏已穿装备[0].FLD_FJ_四神之力, value.装备栏已穿装备[3].FLD_FJ_四神之力);
                    double num7 = value.FLD_人物基本_防御;
                    double num8 = player.FLD_人物基本_攻击;
                    int num9 = 0;
                    num = 14;
                    if (num5 == 1)
                    {
                        num7 -= (double)value.衣服防御力 * (1.0 + (double)(player.装备栏已穿装备[3].FLD_FJ_觉醒 / 100));
                    }
                    if (num6 == 1)
                    {
                        num8 += (double)player.武器攻击力 * (1.0 + (double)(player.装备栏已穿装备[3].FLD_FJ_觉醒 / 100));
                    }
                    double num10 = num7 * (1.0 - player.FLD_装备_追加_降低百分比防御 - player.武勋降低百分比防御);
                    if (!player.检查毒蛇出洞状态())
                    {
                        if (player.Player_Job == 1)
                        {
                            if ((double)RNG.Next(1, 110) <= player.破甲几率)
                            {
                                player.显示大字(player.人物全服ID, 16);
                                num10 *= player.得到气功加成值(1, 5, 2);
                            }
                        }
                        else if (player.Player_Job == 8)
                        {
                            if ((double)RNG.Next(1, 110) <= player.破甲几率)
                            {
                                player.显示大字(player.人物全服ID, 16);
                                num10 *= player.得到气功加成值(8, 7, 2);
                            }
                        }
                        else if (player.Player_Job == 2)
                        {
                            double num11 = player.剑_无坚不摧 + player.剑_乘胜追击;
                            double num12 = RNG.Next(1, 100);
                            if (num12 < player.剑_无坚不摧)
                            {
                                player.显示大字(player.人物全服ID, 120);
                                num10 *= 0.5;
                            }
                            else if (num12 < num11)
                            {
                                if (player.剑_乘胜追击 > 0.5)
                                {
                                    player.剑_乘胜追击 = 0.5;
                                }
                                player.显示大字(player.人物全服ID, 120);
                                num10 *= player.得到气功加成值(2, 9, 2) - player.剑_乘胜追击 * 0.01;
                            }
                        }
                    }
                    int num13 = player.FLD_人物基本_命中;
                    if (player.Player_Job == 6 && !player.检查毒蛇出洞状态())
                    {
                        if (player.刺_连消带打数量 != 0.0)
                        {
                            num8 += player.刺_连消带打数量 / 2.0;
                            player.刺_连消带打数量 = 0.0;
                        }
                        if (player.追加状态列表 != null && player.GetAddState(801201))
                        {
                            num13 = (int)((double)num13 * (1.0 + player.刺_先发制人));
                        }
                    }
                    if (!value.检查毒蛇出洞状态())
                    {
                        //24.0 EVIAS 削弱被攻击者的转攻为守
                        double ruo = 反气功系统.计算反气功弱化值(player, value, 2024);
                        if (value.Player_Job == 3 && (double)RNG.Next(1, 100) <= value.枪_转攻为守 - ruo)
                        {
                            value.显示大字(value.人物全服ID, 130);
                            num10 += (double)value.FLD_攻击 * value.枪_转攻为守 * 0.005;
                        }
                        if (value.Player_Job == 10 && (double)RNG.Next(1, 100) <= value.拳师_转攻为守)
                        {
                            value.显示大字(value.人物全服ID, 130);
                            num10 += (double)value.FLD_攻击 * value.拳师_转攻为守 * 0.005;
                        }
                    }
                    double num14 = num8 - num10 + (double)num13 * 0.25 - (double)value.FLD_人物基本_回避 * 0.25;
                    bool flag = false;
                    int num15 = RNG.Next(0, 1) switch
                    {
                        1 => 127,
                        0 => 126,
                        _ => 126,
                    };
                    num = 15;
                    if (BitConverter.ToInt32(player.装备栏已穿装备[3].物品ID, 0) == 0)
                    {
                        Random random = new Random();
                        int 攻击类型 = RNG.Next(0, 1) switch
                        {
                            1 => 113,
                            0 => 112,
                            _ => 112,
                        };
                        double num16 = random.Next((int)num14 - 15, (int)num14 + 15);
                        if (num16 <= 1.0)
                        {
                            num16 = RNG.Next(1, 5);
                        }
                        if (value.Player_Job == 11)
                        {
                            if (value.梅_障力激活 > 0.0)
                            {
                                num9 = (int)(num16 * (value.梅_障力激活 * 0.01));
                                if (num9 > value.人物_AP)
                                {
                                    num9 = value.人物_AP;
                                }
                                value.人物_AP -= num9;
                            }
                        }
                        else
                        {
                            num9 = 0;
                        }
                        double num17 = num16 - (double)num9;
                        if (num17 <= 1.0)
                        {
                            num17 = 1.0;
                        }
                        攻击计算完成(player, 目标人物ID, 0, (int)num17, 攻击类型, 0, num9);
                        return;
                    }
                    if (player.Player_Job == 2)
                    {
                        double num18 = RNG.Next(1, 110);
                        if (player.检查毒蛇出洞状态())
                        {
                            num18 *= 1000.0;
                        }
                        if (num18 < player.剑_怒海狂澜)
                        {
                            num14 *= player.得到气功加成值(2, 7, 2);
                        }
                    }
                    else if (player.Player_Job == 4)
                    {
                        if (value.检查烈日炎炎状态() && BitConverter.ToInt32(value.装备栏已穿装备[0].物品ID, 0) != 0)
                        {
                            num7 -= (double)value.装备栏已穿装备[0].物品防御力;
                        }
                        int num19 = RNG.Next(1, 130);
                        int num20 = RNG.Next(1, 110);
                        int num21 = RNG.Next(1, 130);
                        int num22 = RNG.Next(1, 130);
                        num14 = (num8 - num7 * 0.7) * World.弓手攻击倍数 + (double)(player.FLD_人物基本_命中 / 4) - (double)(value.FLD_人物基本_回避 / 4) + (double)(player.人物_追加_PVP战力 * 10);
                        if (player.检查毒蛇出洞状态())
                        {
                            num22 *= 1000;
                            num19 *= 1000;
                            num20 *= 1000;
                            num21 *= 1000;
                        }
                        if ((double)num19 <= player.弓_心神凝聚)
                        {
                            num15 = 136;
                            num14 *= 3.0;
                            player.弓群攻触发心神 = true;
                        }
                        else if ((double)num20 <= player.弓_流星三矢)
                        {
                            player.显示大字(player.人物全服ID, 47);
                            if (player.追加状态列表.ContainsKey(700047))
                            {
                                player.追加状态列表[700047].时间结束事件();
                            }
                            追加状态类 追加状态类2 = new 追加状态类(player, player.弓_流星三矢时间, 700047.0, 1.0);
                            player.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
                            player.状态效果(BitConverter.GetBytes(700047), 1, (int)player.弓_流星三矢时间);
                            player.更新武功和状态();
                            if (player.追加状态列表.ContainsKey(700047))
                            {
                                num14 *= 1.45;
                                num15 = 129;
                            }
                        }
                        else
                        {
                            switch (RNG.Next(0, 4))
                            {
                                default:
                                    num15 = 126;
                                    break;
                                case 1:
                                    num15 = 126;
                                    break;
                                case 2:
                                    num15 = 126;
                                    break;
                                case 3:
                                    num15 = 127;
                                    break;
                                case 4:
                                    num15 = 128;
                                    num14 = 1.2;
                                    break;
                            }
                        }
                        if ((double)num21 <= player.弓_致命绝杀)
                        {
                            num14 *= player.得到气功加成值(4, 11, 2);
                            player.显示大字(player.人物全服ID, 140);
                            player.触发弓箭致命绝杀 = true;
                        }
                        num14 += player.弓_锐利之箭 + 1.0;
                        if ((double)num22 <= player.弓_无明暗矢)
                        {
                            num14 *= 1.05 + player.弓_无明暗矢 * 0.01;
                            player.显示大字(player.人物全服ID, 49);
                            player.触发物理无明暗矢 = true;
                        }
                        if ((double)RNG.Next(1, 100) <= player.升天五式_千里一击)
                        {
                            num14 *= 1.0 + player.升天五式_千里一击 * 0.01;
                            player.显示大字(player.人物全服ID, 1017);
                        }
                    }
                    else if (player.Player_Job == 6)
                    {
                        if (value.检查烈日炎炎状态() && BitConverter.ToInt32(value.装备栏已穿装备[0].物品ID, 0) != 0)
                        {
                            num7 -= (double)value.装备栏已穿装备[0].物品防御力;
                        }
                        num14 = (num8 - num7 * 0.8) * World.刺客攻击倍数 + (double)(player.FLD_人物基本_命中 / 4) - (double)(value.FLD_人物基本_回避 / 4) + (double)(player.人物_追加_PVP战力 * 10);
                        int num23 = RNG.Next(1, 130);
                        int num24 = RNG.Next(1, 130);
                        if (player.检查毒蛇出洞状态())
                        {
                            num23 *= 1000;
                            num24 *= 1000;
                        }
                        double num25 = player.刺_心神凝聚;
                        if (player.当前激活技能ID != 0 && player.当前激活技能ID != 830401 && player.当前激活技能ID != 840401 && num25 != 0.0)
                        {
                            num25 += 20.0;
                        }
                        if ((double)num24 <= num25)
                        {
                            num15 = 136;
                            num14 *= player.得到气功加成值(6, 3, 2);
                            if (player.刺_致手绝命 != 0.0)
                            {
                                num14 += num14 * player.刺_致手绝命;
                            }
                        }
                        else if ((double)num23 <= player.刺_以怒还怒)
                        {
                            player.显示大字(player.人物全服ID, 372);
                            num15 = 134;
                            num14 *= player.得到气功加成值(6, 6, 2);
                            if ((double)RNG.Next(1, 100) <= player.刺_一招残杀)
                            {
                                player.显示大字(player.人物全服ID, 1022);
                                num14 *= player.得到气功加成值(6, 11, 2);
                            }
                        }
                        else
                        {
                            int num26 = RNG.Next(1, 120);
                            if (player.检查毒蛇出洞状态())
                            {
                                num26 *= 1000;
                            }
                            if ((double)num26 <= player.刺_连环飞舞)
                            {
                                player.显示大字(player.人物全服ID, 272);
                                num15 = RNG.Next(0, 5) switch
                                {
                                    1 => 129,
                                    2 => 130,
                                    3 => 131,
                                    4 => 132,
                                    5 => 133,
                                    _ => 129,
                                };
                                num14 = 1.4;
                                num = 16;
                            }
                        }
                        if (player.刺_升天三气功_无情打击 != 0.0)
                        {
                            num14 += num8 * player.刺_升天三气功_无情打击;
                        }
                        if (player.当前激活技能ID != 0 && World.TBL_KONGFU.TryGetValue(player.当前激活技能ID, out var value2))
                        {
                            int num28;
                            if (value2.FLD_武功类型 == 3)
                            {
                                int num27 = value2.FLD_MP + (player.武功新[3, value2.FLD_INDEX].武功_等级 - 1) * player.武功新[3, value2.FLD_INDEX].FLD_每级加MP;
                                if (player.人物_MP < num27)
                                {
                                    player.发送激活技能数据(value2.FLD_PID, 2);
                                    return;
                                }
                                num28 = value2.FLD_AT + (player.武功新[3, value2.FLD_INDEX].武功_等级 - 1) * value2.FLD_每级加危害;
                                player.魔法使用(num27);
                            }
                            else
                            {
                                if (player.人物_MP < value2.FLD_MP)
                                {
                                    player.发送激活技能数据(value2.FLD_PID, 2);
                                    return;
                                }
                                num28 = value2.FLD_AT;
                                player.魔法使用(value2.FLD_MP);
                            }
                            num14 = ((player.当前激活技能ID == 830401 || player.当前激活技能ID == 840401) ? (num14 + (double)num28 * 0.5) : (num14 + (double)num28 * 0.2));
                        }
                        num = 17;
                    }
                    else if (player.Player_Job == 7)
                    {
                        num15 = RNG.Next(0, 5) switch
                        {
                            1 => 126,
                            2 => 129,
                            3 => 134,
                            _ => 126,
                        };
                        if (!player.检查和弦状态())
                        {
                            if ((double)RNG.Next(1, 100) < player.琴师_梅花三弄)
                            {
                                player.显示大字(player.人物全服ID, 87);
                                int num26 = RNG.Next(900401, 900404);
                                追加状态类 value3 = new 追加状态类(player, 10000, num26, 0);
                                player.追加状态列表.TryAdd(num26, value3);
                                player.状态效果(BitConverter.GetBytes(num26), 1, 10000);
                                player.更新人物数据(player);
                            }
                        }
                        else if (player.追加状态列表.ContainsKey(900401))
                        {
                            if ((double)RNG.Next(1, 100) < player.琴师_阳明春晓概率 + player.琴_三和弦_状态效果 && !value.异常状态.ContainsKey(22))
                            {
                                double num30 = player.琴师_阳明春晓时间;
                                异常状态类 value4 = new 异常状态类(value, (int)num30, 22, player.琴师_阳明春晓攻击);
                                value.异常状态.TryAdd(22, value4);
                                value.琴师_阳明春晓_减少攻击 = player.琴师_阳明春晓攻击;
                                if ((double)RNG.Next(1, 100) <= player.琴师_阳明春晓绝望)
                                {
                                    if (value.异常状态.ContainsKey(23))
                                    {
                                        value.异常状态[23].时间结束事件();
                                    }
                                    异常状态类 value5 = new 异常状态类(value, 20000, 23, 0.0);
                                    value.异常状态.TryAdd(23, value5);
                                }
                            }
                            if ((double)RNG.Next(1, 100) < player.琴师_潇湘雨夜概率 + player.琴_三和弦_状态效果 && !value.异常状态.ContainsKey(25))
                            {
                                double num31 = player.琴师_潇湘雨夜时间;
                                异常状态类 value6 = new 异常状态类(value, (int)num31, 25, player.琴师_潇湘雨夜防御);
                                value.异常状态.TryAdd(25, value6);
                                value.琴师_潇湘雨夜_减少防御 = player.琴师_潇湘雨夜防御;
                                if ((double)RNG.Next(1, 100) <= player.琴师_潇湘雨夜不安)
                                {
                                    if (value.异常状态.ContainsKey(24))
                                    {
                                        value.异常状态[24].时间结束事件();
                                    }
                                    异常状态类 value7 = new 异常状态类(value, 20000, 24, 0.0);
                                    value.异常状态.TryAdd(24, value7);
                                }
                            }
                        }
                        else
                        {
                            player.追加状态列表.ContainsKey(900402);
                        }
                    }
                    else if (player.Player_Job == 9)
                    {
                        double num32 = RNG.Next(1, 110);
                        if (player.检查毒蛇出洞状态())
                        {
                            num32 *= 1000.0;
                        }
                        if (num32 < player.谭_怒海狂澜)
                        {
                            num14 *= player.得到气功加成值(9, 10, 2);
                        }
                    }
                    else if (player.Player_Job == 11)
                    {
                        if ((value.Player_Job == 2 || value.Player_Job == 9) && player.梅_嫉妒的化身 > 0.0 && (double)RNG.Next(1, 100) <= player.梅_嫉妒的化身)
                        {
                            num14 *= 1.0 + player.得到气功加成值(player.Player_Job, 9, 2);
                            player.显示大字(player.人物全服ID, 808);
                        }
                        //24.0 EVIAS 削弱攻击者的玄武雷电
                        double ruo = 反气功系统.计算反气功弱化值(value, player, 2022);
                        if ((double)RNG.Next(1, 100) <= player.梅_升天一气功_玄武雷电 - ruo)
                        {
                            num14 *= 1.4;
                            player.显示大字(player.人物全服ID, 803);
                        }
                        if (player.人物坐标_地图 != 7301 && (double)RNG.Next(1, 100) <= player.梅_升天二气功_玄武诅咒)
                        {
                            num14 += (double)player.人物最大_HP * 0.2;
                            player.显示大字(player.人物全服ID, 806);
                        }
                    }
                    switch (player.Player_Job)
                    {
                        case 1:
                            {
                                int num34 = RNG.Next(1, 100);
                                if (player.检查毒蛇出洞状态())
                                {
                                    num34 *= 1000;
                                }
                                if ((double)num34 <= player.刀_连环飞舞)
                                {
                                    player.显示大字(player.人物全服ID, 272);
                                    num15 = RNG.Next(0, 7) switch
                                    {
                                        1 => 129,
                                        2 => 130,
                                        3 => 131,
                                        4 => 132,
                                        5 => 133,
                                        7 => 128,
                                        _ => 129,
                                    };
                                    num14 = (num8 - num10 + (double)(num13 / 4)) * 2.0;
                                }
                                break;
                            }
                        case 2:
                            {
                                int num35 = RNG.Next(1, 100);
                                if (player.检查毒蛇出洞状态())
                                {
                                    num35 *= 1000;
                                }
                                if ((double)num35 <= player.剑_连环飞舞)
                                {
                                    player.显示大字(player.人物全服ID, 272);
                                    num15 = RNG.Next(0, 7) switch
                                    {
                                        1 => 129,
                                        2 => 130,
                                        3 => 131,
                                        4 => 132,
                                        5 => 133,
                                        7 => 128,
                                        _ => 129,
                                    };
                                    num14 = (num8 - num10 + (double)(num13 / 4)) * 2.0;
                                }
                                break;
                            }
                        case 3:
                        case 12:
                            {
                                int num33 = RNG.Next(1, 100);
                                if (player.检查毒蛇出洞状态())
                                {
                                    num33 *= 1000;
                                }
                                if ((double)num33 <= player.枪_连环飞舞)
                                {
                                    player.显示大字(player.人物全服ID, 272);
                                    num15 = RNG.Next(0, 7) switch
                                    {
                                        1 => 129,
                                        2 => 130,
                                        3 => 131,
                                        4 => 132,
                                        5 => 133,
                                        7 => 128,
                                        _ => 129,
                                    };
                                    num14 = (num8 - num10 + (double)(num13 / 4)) * 2.0;
                                }
                                break;
                            }
                        case 5:
                            num15 = RNG.Next(0, 6) switch
                            {
                                1 => 126,
                                2 => 126,
                                3 => 127,
                                6 => 127,
                                _ => 126,
                            };
                            break;
                        case 7:
                            switch (RNG.Next(0, 10))
                            {
                                case 1:
                                    num15 = 126;
                                    break;
                                case 2:
                                    num15 = 126;
                                    break;
                                case 3:
                                    num15 = 127;
                                    break;
                                case 4:
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    num15 = 128;
                                    break;
                                case 5:
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    num15 = 129;
                                    break;
                                case 6:
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    num15 = 130;
                                    break;
                                default:
                                    num15 = 126;
                                    break;
                                case 8:
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    num15 = 132;
                                    break;
                                case 9:
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    num15 = 133;
                                    break;
                                case 10:
                                    num15 = 134;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                    break;
                            }
                            break;
                        case 8:
                        case 9:
                        case 10:
                            switch (RNG.Next(0, 10))
                            {
                                default:
                                    num15 = 126;
                                    break;
                                case 1:
                                    num15 = 126;
                                    break;
                                case 2:
                                    num15 = 127;
                                    break;
                                case 3:
                                    num15 = 128;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 4:
                                    num15 = 128;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 5:
                                    num15 = 129;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 6:
                                    num15 = 130;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 7:
                                    num15 = 131;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 8:
                                    num15 = 132;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 9:
                                    num15 = 133;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.0;
                                    break;
                                case 10:
                                    num15 = 134;
                                    num14 = ((num8 - num10) * 1.5 + (double)(player.FLD_人物基本_命中 / 4)) * 2.5;
                                    break;
                            }
                            break;
                        case 13:
                            num15 = RNG.Next(0, 6) switch
                            {
                                1 => 126,
                                2 => 126,
                                3 => 127,
                                6 => 127,
                                _ => 126,
                            };
                            break;
                    }
                    if (!value.检查毒蛇出洞状态())
                    {
                        if (value.Player_Job == 2)
                        {
                            //24.0 EVIAS 削弱被攻击者的护身罡气
                            double ruo = 反气功系统.计算反气功弱化值(player, value, 2002);
                            if ((double)RNG.Next(1, 110) <= value.剑_升天一气功_护身罡气 - ruo)
                            {
                                num14 *= 0.5;
                                value.显示大字(value.人物全服ID, 25);
                            }
                        }
                        else if (value.Player_Job == 4)
                        {
                            if (num4 > 100.0)
                            {
                                num14 *= 1.0 - value.弓恶尽矢穷;
                            }
                        }
                        else if (value.Player_Job == 5)
                        {
                            if ((double)RNG.Next(1, 100) <= value.升天一气功_狂风天意 && !value.怒)
                            {
                                value.人物_SP = value.人物最大_SP + 5;
                            }
                            if ((double)RNG.Next(1, 100) <= value.升天五式_形移妖相)
                            {
                                num14 = 0.0;
                                flag = true;
                                value.显示大字(value.人物全服ID, 1018);
                            }
                            //24.0 EVIAS 削弱被攻击者的云心月性
                            double ruo = 反气功系统.计算反气功弱化值(player, value, 2010);
                            if ((double)RNG.Next(1, 100) <= value.医生云心月性 - ruo && !value.GetAddState(700574) && (int)DateTime.Now.Subtract(value.云心月性释放间隔).TotalSeconds > 10)
                            {
                                num14 = 0.0;
                                flag = true;
                                value.显示大字(value.人物全服ID, 574);
                                value.寒冰领域释放间隔 = DateTime.Now;
                                追加状态类 value8 = new 追加状态类(value, 5000, 700574, 0);
                                value.追加状态列表.TryAdd(700574, value8);
                                value.状态效果(BitConverter.GetBytes(700574), 1, 5000);
                            }
                            if (value.GetAddState(700574))
                            {
                                num14 = 0.0;
                                flag = true;
                            }
                        }
                        else if (value.Player_Job == 6)
                        {
                            if ((double)RNG.Next(1, 110) <= value.刺_三花聚顶)
                            {
                                //24.0 EVIAS 削弱被攻击者的连消带打效果
                                double ruo = 反气功系统.计算反气功弱化值(player, value, 2012);
                                value.刺_连消带打数量 = num14 * Math.Max(0, value.刺_连消带打 - ruo);
                                num14 = 0.0;
                                flag = true;
                            }
                            if ((double)RNG.Next(1, 110) <= value.刺_升天一气功_夜魔缠身)
                            {
                                num14 *= 0.7;
                                value.显示大字(value.人物全服ID, 370);
                            }
                            if ((double)RNG.Next(1, 100) <= value.刺_升天二气功_顺水推舟)
                            {
                                value.加血((int)(num14 * 0.2));
                                value.显示大字(value.人物全服ID, 371);
                            }
                        }
                        else if (value.Player_Job == 9)
                        {
                            if ((double)RNG.Next(1, 100) <= value.谭_护身罡气)
                            {
                                num14 *= 0.5;
                                value.显示大字(value.人物全服ID, 25);
                            }
                            if ((double)RNG.Next(1, 100) <= value.谭_升天三气功_以柔克刚 + value.升天五式_惊涛骇浪)
                            {
                                value.显示大字(value.人物全服ID, 700);
                                num14 *= 1.0 - value.谭_升天三气功_以柔克刚 * 0.01;
                                if (RNG.Next(1, 6) <= 2 && value.GetAddState(1008001198))
                                {
                                    if (value.追加状态列表 != null && value.GetAddState(1008001198))
                                    {
                                        value.追加状态列表[1008001198].时间结束事件();
                                    }
                                    追加状态类 追加状态类3 = new 追加状态类(player, 3000, 1008001198, 0);
                                    value.追加状态列表.TryAdd(追加状态类3.FLD_PID, 追加状态类3);
                                    value.FLD_人物_追加百分比_回避 += 0.1;
                                    value.状态效果(BitConverter.GetBytes(1008001198), 1, 3000);
                                    value.更新武功和状态();
                                }
                            }
                        }
                        else if (value.Player_Job == 10)
                        {
                            if (num14 > (double)(value.人物_HP / 2) && (double)RNG.Next(1, 110) <= value.拳师_金刚不坏)
                            {
                                value.显示大字(value.人物全服ID, 554);
                                num14 *= 1.0 - value.拳师_金刚不坏 * 0.01;
                            }
                        }
                        else if (value.Player_Job == 11)
                        {
                            if ((player.Player_Job == 1 || player.Player_Job == 8) && value.梅_嫉妒的化身 > 0.0 && (double)RNG.Next(1, 100) <= value.梅_嫉妒的化身)
                            {
                                double num36 = num14 * (player.得到气功加成值(value.Player_Job, 9, 2) / 2.0);
                                num14 -= num36;
                                value.显示大字(value.人物全服ID, 808);
                            }
                            if (value.梅_障力恢复 > 0.0 && value.人物_AP * 2 < value.人物最大_AP && (double)RNG.Next(1, 100) <= value.梅_障力恢复)
                            {
                                value.人物_AP = value.人物最大_AP;
                                value.显示大字(value.人物全服ID, 801);
                            }
                            if (value.梅_愤怒爆发 > 0.0 && RNG.Next(1, 100) <= 40 && value.怒点 < 3)
                            {
                                Players players = value;
                                players.怒点++;
                            }
                            if (value.梅_吸血进击 > 0.0 && (double)RNG.Next(1, 100) <= value.梅_吸血进击 && value.人物_HP * 2 < value.人物最大_HP)
                            {
                                int num37 = (int)(num14 / 2.0);
                                if (num37 > 2000)
                                {
                                    num37 = 2000;
                                }
                                value.加血(num37);
                                value.显示大字(value.人物全服ID, 804);
                                value.更新HP_MP_SP();
                            }
                        }
                    }
                    if (player.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= player.FLD_装备_追加_中毒概率百分比 && !value.GetAbnormalState(3))
                    {
                        异常状态类 value9 = new 异常状态类(value, 60000, 3, 0.0);
                        value.异常状态.TryAdd(3, value9);
                    }
                    if (value.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= value.FLD_装备_追加_中毒概率百分比 && !player.GetAbnormalState(3))
                    {
                        player.异常状态.TryAdd(3, new 异常状态类(player, 60000, 3, 0.0));
                        player.中毒 = true;
                    }
                    if (num5 != 1)
                    {
                        num14 *= 1.0 - value.FLD_装备_追加_降低百分比攻击;
                    }
                    if (value.FLD_装备_降低_伤害值 > 0.0)
                    {
                        num14 -= (double)(int)value.FLD_装备_降低_伤害值;
                    }
                    num = 18;
                    if (value.全职业气功防御 + value.大魔神添加全职业气功防御几率 >= (double)RNG.Next(1, 100) && value.Player_Level >= 120)
                    {
                        num14 *= 0.7;
                        value.显示大字(value.人物全服ID, 701);
                    }
                    player.升天四气功触发(value);
                    double num38 = RNG.Next((int)num14 - 15, (int)num14 + 15);
                    if (num38 <= 0.0)
                    {
                        num38 = 0.0;
                    }
                    else if (!player.检查毒蛇出洞状态())
                    {
                        if (player.升天五式_致残 > 0.0 && player.升天五式_致残 > (double)RNG.Next(1, 125) && !value.检查致残状态())
                        {
                            player.显示大字(player.人物全服ID, 1014);
                            if ((double)RNG.Next(1, 100) <= value.神女抗击身法)
                            {
                                value.显示大字(value.人物全服ID, 582);
                            }
                            else if ((double)RNG.Next(1, 100) <= value.剑百毒不侵)
                            {
                                value.显示大字(value.人物全服ID, 571);
                            }
                            else
                            {
                                追加状态类 value10 = new 追加状态类(value, 1500, 1008002012, 0);
                                value.追加状态列表.TryAdd(1008002012, value10);
                                value.状态效果(BitConverter.GetBytes(1008002012), 1, 1500);
                                value.更新人物数据(value);
                                value.更新广播人物数据();
                                value.吃药时间 = DateTime.Now.AddSeconds(1.5);
                            }
                        }
                        if (player.Player_Job == 2)
                        {
                            if ((double)RNG.Next(1, 110) <= player.剑_冲冠一怒 && !player.怒)
                            {
                                player.显示大字(player.人物全服ID, 29);
                                player.人物_SP += (int)((double)player.人物_SP * player.剑_冲冠一怒 * 0.005);
                            }
                            if ((double)RNG.Next(1, 100) <= player.剑_移花接木)
                            {
                                player.显示大字(player.人物全服ID, 26);
                                player.加血((int)(num38 * 0.5));
                            }
                        }
                        if (player.Player_Job == 9)
                        {
                            if ((double)RNG.Next(1, 110) <= player.谭_冲冠一怒 && !player.怒)
                            {
                                player.显示大字(player.人物全服ID, 29);
                                player.人物_SP += (int)((double)player.人物_SP * player.谭_冲冠一怒 * 0.005);
                            }
                            if ((double)RNG.Next(1, 100) <= player.谭_移花接木)
                            {
                                player.显示大字(player.人物全服ID, 26);
                                player.加血((int)(num38 * 0.5));
                            }
                        }
                    }
                    double num39 = RNG.Next((int)num38 - 15, (int)num38 + 15);
                    if (num39 <= 1.0)
                    {
                        num39 = RNG.Next(1, 5);
                    }
                    else if (player.中级附魂_愤怒 != 0 && RNG.Next(1, 100) <= player.中级附魂_愤怒)
                    {
                        num39 *= 1.2;
                        player.显示大字(player.人物全服ID, 404);
                    }
                    if (value.Player_Job == 11)
                    {
                        if (value.梅_障力激活 > 0.0)
                        {
                            num9 = (int)(num39 * (value.梅_障力激活 * 0.01));
                            if (num9 > value.人物_AP)
                            {
                                num9 = value.人物_AP;
                            }
                            value.人物_AP -= num9;
                        }
                    }
                    else
                    {
                        num9 = 0;
                    }
                    double num40 = num39 - (double)num9;
                    if (player.人物_追加_PVP战力 - value.人物_追加_PVP战力 > 0)
                    {
                        int num41 = player.人物_追加_PVP战力 - value.人物_追加_PVP战力;
                        num40 *= 1.0 + 0.01 * (double)num41;
                    }
                    if (player.人物_追加_PVP战力 - value.人物_追加_PVP战力 < 0)
                    {
                        int num42 = player.人物_追加_PVP战力 - value.人物_追加_PVP战力;
                        num40 *= 1.0 - 0.01 * (double)(-num42);
                    }
                    if (num40 <= 1.0)
                    {
                        num40 = 1.0;
                    }
                    if (num40 > 1.0)
                    {
                        num40 *= 获得物理对人伤害(player.Player_Job, player.Player_Zx);
                    }
                    if (player.Player_Job == 6)
                    {
                        switch (num15)
                        {
                            case 126:
                            case 127:
                                num40 *= 1.4;
                                break;
                            case 128:
                            case 129:
                            case 130:
                            case 131:
                            case 132:
                            case 133:
                                num40 *= 1.8;
                                break;
                            case 134:
                                num40 *= 3.0;
                                break;
                            case 136:
                                num40 *= 2.0;
                                break;
                        }
                    }
                    num40 -= (double)(value.减免对方伤害 + value.药品减免对方伤害);
                    if (player.FLD_装备_追加_伤害值 > 0 && !flag)
                    {
                        num40 += (double)player.FLD_装备_追加_伤害值;
                    }
                    攻击计算完成(player, 目标人物ID, 0, (int)num40, num15, 0, num9);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "物理攻击人物[" + num + "]出错:" + player.报错次数阀值 + "/" + ex.Message);
            }
        }

        public static void 魔法攻击(Players player, int 武功ID, int 人物ID)
        {
            int num = 0;
            try
            {
                num = 1;
                if (人物ID >= 10000)
                {
                    num = 2;
                    技能攻击怪物(player, 武功ID, 人物ID);

                }
                else
                {
                    num = 3;
                    技能攻击人物(player, 武功ID, 人物ID);
                }
            }
            catch (Exception ex)
            {
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "魔法攻击出错" + num + "|" + 武功ID + " | " + 人物ID + " |" + ex.Message);
                }
            }
        }

        public static void 技能攻击人物(Players player, int 武功ID, int 人物ID)
        {
            try
            {
                if (!World.TBL_KONGFU.TryGetValue(武功ID, out var value) || (value.FLD_PID != 601201 && value.FLD_PID != 601202 && (player.Player_Job != value.FLD_JOB || player.Player_Job_leve < value.FLD_JOBLEVEL || player.Player_Level < value.FLD_LEVEL || (value.FLD_ZX != 0 && player.Player_Zx != value.FLD_ZX))) || ((value.FLD_PID == 601201 || value.FLD_PID == 601202 || value.FLD_PID == 100301 || value.FLD_PID == 200301 || value.FLD_PID == 300301 || value.FLD_PID == 400301 || value.FLD_PID == 500301 || value.FLD_PID == 800301) && player.FLD_情侣.Length == 0))
                {
                    return;
                }
                Random random = new Random();
                if (!World.allConnectedChars.TryGetValue(人物ID, out var value2) || value2.Player死亡 || value2.人物_HP <= 0 || player.Player死亡 || player.Client.挂机 || player.人物_HP <= 0 || player.个人商店 != null || player.打开仓库中 || value2.个人商店 != null || (player.人物坐标_地图 == 45001 && !World.极限比武台服务.IsAttackAllowed(value2)))
                {
                    return;
                }
                if (BitConverter.ToInt32(player.装备栏已穿装备[3].物品ID, 0) == 0)
                {
                    player.发送激活技能数据(武功ID, 6);
                    return;
                }
                if ((player.Player_Job == 4 || player.Player_Job == 11) && BitConverter.ToInt32(player.装备栏已穿装备[12].物品数量, 0) == 0)
                {
                    player.发送激活技能数据(武功ID, 6);
                    player.初始化已装备物品();
                    return;
                }
                bool flag = false;
                if (player.人物坐标_地图 != value2.人物坐标_地图)
                {
                    return;
                }
                int num = player.判断拳师连击(武功ID);
                if (num != 0)
                {
                    武功ID = num;
                }
                if (!World.TBL_KONGFU.TryGetValue(武功ID, out value) || (value.FLD_PID != 601201 && value.FLD_PID != 601202 && (player.Player_Job != value.FLD_JOB || player.Player_Job_leve < value.FLD_JOBLEVEL || player.Player_Level < value.FLD_LEVEL || (value.FLD_ZX != 0 && player.Player_Zx != value.FLD_ZX))) || ((value.FLD_PID == 601201 || value.FLD_PID == 601202 || value.FLD_PID == 100301 || value.FLD_PID == 200301 || value.FLD_PID == 300301 || value.FLD_PID == 400301 || value.FLD_PID == 500301 || value.FLD_PID == 800301) && player.FLD_情侣.Length == 0))
                {
                    return;
                }
                if (player.Player_Job == 10 && 武功ID == 3000401)
                {
                    异常状态类 value3 = new 异常状态类(value2, 3000, 4, 0.0);
                    value2.异常状态.TryAdd(4, value3);
                    player.移动(value2.人物坐标_X, value2.人物坐标_Y, 15f, value2.人物坐标_地图);
                }
                if (player.神女异常状态.ContainsKey(39))
                {
                    if (player.超负荷等级 == 1)
                    {
                        player.人物_HP -= (int)((double)player.人物最大_HP * 0.05);
                    }
                    else if (player.超负荷等级 == 2)
                    {
                        player.人物_HP -= (int)((double)player.人物最大_HP * 0.1);
                    }
                    else if (player.超负荷等级 == 3)
                    {
                        player.人物_HP -= (int)((double)player.人物最大_HP * 0.15);
                    }
                }
                if (player.神女异常状态.ContainsKey(36))
                {
                    Random random2 = new Random();
                    int fLD_PID = player.武功类List[random2.Next(player.武功类List.Count)].FLD_PID;
                    武功ID = fLD_PID;
                }
                switch (武功ID)
                {
                    case 601201:
                    case 601202:
                    case 4000101:
                    case 4000401:
                    case 4000501:
                    case 4000601:
                    case 4000701:
                    case 4000801:
                    case 4000901:
                    case 4002101:
                    case 4002201:
                    case 4002301:
                    case 4002401:
                    case 4002501:
                    case 4006101:
                        使用辅助技能(player, value, value2, 武功ID, 人物ID);
                        return;
                }
                if (value.FLD_武功类型 == 1)
                {
                    使用辅助技能(player, value, value2, 武功ID, 人物ID);
                    return;
                }
                if (player.人物PK模式 == 1 && player.人物坐标_地图 != 45001)
                {
                    if (World.仙魔大战进程 != 0)
                    {
                        if (player.仙魔大战派别 == value2.仙魔大战派别)
                        {
                            player.系统提示("仙魔大战中同种族禁止pk....", 9, "系统提示");
                            return;
                        }
                    }
                    else if (World.势力战进程 != 0)
                    {
                        if (player.Player_Zx == value2.Player_Zx)
                        {
                            player.系统提示("势力战中同势力禁止pk....", 9, "系统提示");
                            return;
                        }
                    }
                    else if (player.Player_Zx == value2.Player_Zx)
                    {
                        player.系统提示("同派系禁止pk修改pk模式在试试....", 9, "系统提示");
                        return;
                    }
                }
                foreach (int item in World.限制PK地图列表)
                {
                    if (player.人物坐标_地图 == item)
                    {
                        player.系统提示("此地图禁止PK", 9, "地图限制");
                        return;
                    }
                }
                if (World.工作日限时地图是否开启 == 0)
                {
                    foreach (int item2 in World.周末全天PK地图列表)
                    {
                        if (player.人物坐标_地图 == item2)
                        {
                            player.系统提示("此地图本时间段禁止PK", 9, "系统提示");
                            return;
                        }
                    }
                }
                if (World.限时地图是否开启 == 0)
                {
                    foreach (int item3 in World.限时PK地图列表)
                    {
                        if (player.人物坐标_地图 == item3)
                        {
                            player.系统提示("此地图本时间段禁止PK", 9, "系统提示");
                            return;
                        }
                    }
                }
                if (player.人物坐标_地图 == 42001 && player.门派联盟盟主 == value2.门派联盟盟主)
                {
                    player.系统提示("攻城战中不能击杀本帮派成员。");
                    return;
                }
                if (player.人物PK模式 == 0 && !player.Client.假人 && !player.Client.云挂机)
                {
                    return;
                }
                if (player.Player_WuXun < World.武勋保护数量)
                {
                    player.系统提示("武勋低于" + World.武勋保护数量 + "，无法主动pk。。");
                    return;
                }
                if (value2.GM模式 == 19 || 人物ID == player.人物全服ID || player.触发新手安全区(player) || player.触发新手安全区(value2) || player.Player_Level < 35 || value2.Player_Level < 35 || (player.人物坐标_地图 == 7301 && player.帮派名字 == value2.帮派名字) || (value.FLD_ZX != 0 && player.Player_Zx != value.FLD_ZX) || (player.Player_Job_leve < value.FLD_JOBLEVEL && player.Player_Job_leve < value.FLD_JOBLEVEL - 1))
                {
                    return;
                }
                float num2 = value2.人物坐标_X - player.人物坐标_X;
                float num3 = value2.人物坐标_Y - player.人物坐标_Y;
                double num4 = Math.Sqrt((double)num2 * (double)num2 + (double)num3 * (double)num3);
                switch (player.Player_Job)
                {
                    default:
                        if (num4 > World.其他职业PK距离)
                        {
                            return;
                        }
                        break;
                    case 11:
                        if (num4 > World.梅柳真PK距离 + player.梅_玄武的指点)
                        {
                            return;
                        }
                        break;
                    case 5:
                        if (num4 > World.医生PK距离)
                        {
                            return;
                        }
                        break;
                    case 4:
                        if (num4 > World.弓箭手PK距离 + player.弓_猎鹰之眼)
                        {
                            return;
                        }
                        break;
                    case 13:
                        if (num4 > World.神女PK距离 * (1.0 + player.神女黑花漫开))
                        {
                            return;
                        }
                        break;
                }
                if (value2.人物坐标_地图 != 2301 && player.人物坐标_地图 != 2301 && player.人物坐标_地图 != 7301 && value2.人物坐标_地图 != 7301 && value2.人物坐标_地图 != 41001 && player.人物坐标_地图 != 41001 && value2.人物坐标_地图 != 801 && player.人物坐标_地图 != 801 && value2.人物坐标_地图 != 42001 && player.人物坐标_地图 != 42001)
                {
                    if (value2.检查玩家是否在挂机双倍区域(player) && value2.追加状态列表.ContainsKey(900000619) && !value2.异常状态.ContainsKey(28))
                    {
                        value2.安全模式 = 0;
                    }
                    else if (!value2.检查玩家是否在挂机双倍区域(player) && value2.追加状态列表.ContainsKey(900000619) && !value2.异常状态.ContainsKey(28))
                    {
                        value2.安全模式 = 1;
                    }
                    if (value2.安全模式 == 1 && !value2.安全区禁止下毒(player))
                    {
                        player.系统提示("对方处于安全模式中,禁止技能攻击。");
                        return;
                    }
                    if (player.安全模式 == 1 && !player.安全区禁止下毒(player))
                    {
                        player.系统提示("自己处于安全模式中,禁止技能攻击。");
                        return;
                    }
                    if (player.Player_WuXun <= World.武勋保护数量)
                    {
                        player.系统提示("您武勋为负数到[" + World.武勋保护数量 + "]时无法主动PK");
                        return;
                    }
                }
                if (player.人物坐标_地图 != 801 && player.人物坐标_地图 != 8001 && player.人物坐标_地图 != 41001 && player.人物坐标_地图 != 7301 && player.人物坐标_地图 != 42001 && Math.Abs(player.Player_Level - value2.Player_Level) > World.PK等级差)
                {
                    player.系统提示("等级差" + World.PK等级差 + "以上，不能攻击。");
                    return;
                }
                if (value2.Player无敌)
                {
                    player.系统提示("对方刚刚复活,请慢点攻击。", 50, "系统提示");
                    return;
                }
                player.发送打坐数据(5);
                if (value.FLD_武功类型 == 3)
                {
                    int num5 = value.FLD_MP + (player.武功新[value.FLD_武功类型, value.FLD_INDEX].武功_等级 - 1) * value.FLD_每级加MP;
                    if (player.人物_MP < num5)
                    {
                        player.魔法不足提示();
                        return;
                    }
                    player.魔法使用(num5);
                }
                else if (value.FLD_武功类型 == 2 && player.Player_Job != 13)
                {
                    if (player.人物_MP < player.夫妻武功攻击力MP)
                    {
                        player.魔法不足提示();
                        return;
                    }
                    player.魔法使用(player.夫妻武功攻击力MP);
                }
                else
                {
                    if (player.人物_MP < value.FLD_MP)
                    {
                        player.魔法不足提示();
                        return;
                    }
                    player.魔法使用(value.FLD_MP);
                }
                player.更新HP_MP_SP();
                int num6 = player.计算武器四神相克(player.装备栏已穿装备[3].FLD_FJ_四神之力, value2.装备栏已穿装备[0].FLD_FJ_四神之力);
                int num7 = player.计算衣服四神相克(player.装备栏已穿装备[0].FLD_FJ_四神之力, value2.装备栏已穿装备[3].FLD_FJ_四神之力);
                double num8 = value2.FLD_人物基本_防御;
                double num9 = player.FLD_人物基本_攻击;
                int num10 = 0;
                if (num6 == 1)
                {
                    num8 -= (double)value2.衣服防御力 * (1.0 + (double)(player.装备栏已穿装备[3].FLD_FJ_觉醒 / 100));
                }
                if (num7 == 1)
                {
                    num9 += (double)player.武器攻击力 * (1.0 + (double)(player.装备栏已穿装备[3].FLD_FJ_觉醒 / 100));
                }
                double num11 = num8 * (1.0 - player.FLD_装备_追加_降低百分比防御 - player.武勋降低百分比防御 - value2.属性封印);
                if (!value2.检查毒蛇出洞状态() && value2.Player_Job == 12 && value2.牢不可破 > 0.0)
                {
                    value2.衣服属性提升 = 0;
                    if (value2.牢不可破 >= (double)RNG.Next(1, 100))
                    {
                        num11 += num11 * ((double)value2.装备栏已穿装备[0].物品属性阶段数 * 0.005 * 2.0);
                        value2.衣服属性提升 = 2;
                        value2.显示大字(value2.人物全服ID, 1010);
                    }
                }
                if (player.陵劲淬砺 > 0.0)
                {
                    player.武器属性提升 = 0;
                    if (player.陵劲淬砺 >= (double)RNG.Next(1, 100))
                    {
                        num9 += num9 * ((double)value2.装备栏已穿装备[3].物品属性阶段数 * 0.005 * 2.0);
                        player.武器属性提升 = 2;
                        player.显示大字(player.人物全服ID, 1011);
                    }
                }
                if (!player.检查毒蛇出洞状态())
                {
                    if (player.Player_Job == 1)
                    {
                        //24.0 EVIAS 削弱攻击者的破甲
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2004);
                        if ((double)RNG.Next(1, 110) <= player.破甲几率 - ruo)
                        {
                            player.显示大字(player.人物全服ID, 16);
                            num11 *= player.得到气功加成值(1, 5, 2);
                        }
                    }
                    else if (player.Player_Job == 8)
                    {
                        //24.0 EVIAS 削弱攻击者的破甲
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2016);
                        if ((double)RNG.Next(1, 110) <= player.破甲几率 - ruo)
                        {
                            player.显示大字(player.人物全服ID, 16);
                            num11 *= player.得到气功加成值(8, 7, 2);
                        }
                    }
                }
                if (!value2.检查毒蛇出洞状态())
                {
                    if (value2.Player_Job == 3 && (double)RNG.Next(1, 100) <= value2.枪_转攻为守)
                    {
                        value2.显示大字(value2.人物全服ID, 130);
                        num11 += (double)value2.FLD_攻击 * 0.2;
                    }
                    if (value2.Player_Job == 10 && (double)RNG.Next(1, 100) <= value2.拳师_转攻为守)
                    {
                        value2.显示大字(value2.人物全服ID, 130);
                        num11 += (double)value2.FLD_攻击 * 0.2;
                    }
                    if (value2.Player_Job == 12 && (double)RNG.Next(1, 100) <= value2.卢_转攻为守)
                    {
                        value2.显示大字(value2.人物全服ID, 130);
                        num11 += (double)value2.FLD_攻击 * 0.2;
                    }
                }
                int num12 = 0;
                if (value.FLD_武功类型 == 3)
                {
                    num12 = value.FLD_AT + (player.武功新[value.FLD_武功类型, value.FLD_INDEX].武功_等级 - 1) * value.FLD_每级加危害;
                }
                else if (value.FLD_武功类型 == 2)
                {
                    num12 = player.夫妻武功攻击力;
                }
                else if (value.FLD_每级危害.Length <= 0)
                {
                    num12 = ((player.Player_Job == 10 && num != 0) ? ((int)((double)(num12 + player.Player_Level * 10) * (1.0 + player.拳师_水火一体))) : ((value.FLD_PID == player.师傅数据.STWG1 || value.FLD_PID == player.师傅数据.STWG2 || value.FLD_PID == player.师傅数据.STWG2) ? (value.FLD_AT * (10 - player.师傅数据.STLEVEL) / 10) : ((value.FLD_武功类型 != 2) ? player.计算升天武功威力(value) : player.夫妻武功攻击力)));
                }
                else
                {
                    int at = value.GetAt(value.FLD_PID, player.武功新[value.FLD_武功类型, value.FLD_INDEX].武功_等级);
                    if (at > 0)
                    {
                        num12 = at;
                    }
                }
                if (player.爆毒状态 > 0.0)
                {
                    num12 += (int)((double)num12 * player.爆毒状态);
                    player.爆毒状态 = 0.0;
                }
                if (player.Player_Job == 11)
                {
                    if (!player.检查毒蛇出洞状态())
                    {
                        if (player.梅_玄武危化 > 0.0)
                        {
                            num12 = (int)((double)num12 * (1.0 + player.梅_玄武危化));
                        }
                        if (player.怒点 >= 3)
                        {
                            player.怒点 = 0;
                            //24.0 EVIAS 削弱攻击者的愤怒爆发
                            double ruo = 反气功系统.计算反气功弱化值(value2, player, 2021);
                            if (player.梅_愤怒爆发 - ruo > 0.0)
                            {
                                num12 = (int)((double)num12 * (1.0 + player.梅_愤怒爆发));
                                player.显示大字(player.人物全服ID, 802);
                            }
                            player.更新HP_MP_SP();
                        }
                    }
                    int num13 = 0;
                    int num14 = 0;
                    if (player.人物坐标_地图 == 41001)
                    {
                        num14 = 1;
                    }
                    else if (player.人物坐标_地图 == 7301)
                    {
                        num14 = 2;
                    }
                    foreach (Players value20 in player.PlayList.Values)
                    {
                        if (!player.查找范围玩家(60 + (int)player.梅_玄武的指点, value20))
                        {
                            continue;
                        }
                        switch (num14)
                        {
                            case 0:
                                if (Math.Abs(player.Player_Level - value20.Player_Level) <= World.PK等级差 && !player.触发新手安全区(value20) && value20.Player_Level >= 35 && value20.Player_Zx != player.Player_Zx)
                                {
                                    int num17 = num13 + 1;
                                    num13 = num17;
                                    if (num13 >= 5)
                                    {
                                        num13 = 5;
                                    }
                                }
                                break;
                            case 1:
                                if (value20.仙魔大战派别 != player.仙魔大战派别)
                                {
                                    int num16 = num13 + 1;
                                    num13 = num16;
                                }
                                if (num13 >= 5)
                                {
                                    num13 = 5;
                                }
                                break;
                            case 2:
                                if (value20.帮派名字 != player.帮派名字)
                                {
                                    int num15 = num13 + 1;
                                    num13 = num15;
                                }
                                if (num13 >= 5)
                                {
                                    num13 = 5;
                                }
                                break;
                        }
                    }
                    if (num13 > 0)
                    {
                        num12 = (int)((double)num12 * (1.0 + player.梅_升天三气功_杀人鬼 * (double)num13));
                    }
                }
                double num18 = (double)value2.FLD_人物武功_防御 * World.武功防降低百分比;
                if (!player.检查毒蛇出洞状态() && player.Player_Job == 2)
                {
                    double num19 = player.剑_无坚不摧 + player.剑_乘胜追击;
                    double num20 = RNG.Next(1, 100);
                    //24.0 EVIAS 削弱攻击者的无坚不摧
                    double ruo = 反气功系统.计算反气功弱化值(value2, player, 2001);
                    if (num20 < player.剑_无坚不摧 - ruo)
                    {
                        player.显示大字(人物ID, 120);
                        num11 *= 0.5;
                        num18 *= 0.5;
                    }
                    else if (num20 < num19)
                    {
                        if (player.剑_乘胜追击 > 0.5)
                        {
                            player.剑_乘胜追击 = 0.5;
                        }
                        player.显示大字(人物ID, 120);
                        num11 *= 0.5 - player.剑_乘胜追击 * 0.01;
                        num18 *= 0.5 - player.剑_乘胜追击 * 0.01;
                    }
                }
                if (!player.检查毒蛇出洞状态() && player.Player_Job == 13 && (double)RNG.Next(1, 130) <= player.神女万毒不侵)
                {
                    player.显示大字(value2.人物全服ID, 1023);
                    num11 *= 0.8;
                    num18 *= 0.8;
                }
                if (player.武勋阶段 >= 8)
                {
                    num11 -= num11 * 0.05;
                    num18 -= num18 * 0.05;
                }
                double num21 = (num9 - num11) * 1.5 + (double)num12 * (1.0 + player.FLD_装备_武功攻击力增加百分比 + player.FLD_人物_武功攻击力增加百分比 + player.FLD_人物_气功_武功攻击力增加百分比 + player.武器武功攻击力百分比 + player.FLD_装备_武功攻击力石头百分比 + player.FLD_宠物_追加_武功攻击) - num18 * World.武功防增加百分比 + (double)(player.FLD_人物基本_命中 / 4) - (double)(value2.FLD_人物基本_回避 / 4) + (double)(player.人物_追加_PVP战力 * 20);
                if (!player.检查毒蛇出洞状态() && value2.强化防具追加百分比 < RNG.Next(1, 100))
                {
                    if (RNG.Next(1, 1000) <= value2.人物武功回避 - player.人物武功命中)
                    {
                        num21 = 0.0;
                        flag = true;
                        value2.显示大字(value2.人物全服ID, 402);
                    }
                    if (RNG.Next(1, 1000) <= player.人物武功命中 - value2.人物武功回避)
                    {
                        num21 *= 1.0 + (double)(player.人物武功命中 - value2.人物武功回避) * 0.005;
                        player.显示大字(player.人物全服ID, 405);
                    }
                    if (player.Player_Job == 1)
                    {
                        if ((double)RNG.Next(1, 100) <= player.真武绝击)
                        {
                            player.显示大字(player.人物全服ID, 17);
                            if ((double)RNG.Next(1, 100) <= value2.韩飞官真气还原)
                            {
                                num21 *= 1.0;
                                value2.显示大字(value2.人物全服ID, 577);
                            }
                            else
                            {
                                num21 *= player.得到气功加成值(1, 7, 2);
                            }
                        }
                        if ((double)RNG.Next(1, 100) <= player.暗影绝杀)
                        {
                            player.显示大字(player.人物全服ID, 18);
                            num21 *= player.得到气功加成值(1, 9, 2);
                        }
                        if ((double)RNG.Next(1, 120) <= player.刀_梵音破镜 + player.升天五式_龙魂附体)
                        {
                            player.显示大字(player.人物全服ID, 312);
                            double num22 = player.得到气功加成值(1, 10, 2);
                            if (player.刀_升天三气功_火龙之火 > 0.0)
                            {
                                num22 += num22 * player.刀_升天三气功_火龙之火;
                            }
                            num21 *= 1.0 + num22;
                        }
                    }
                    else if (player.Player_Job == 2)
                    {
                        if ((double)RNG.Next(1, 110) <= player.剑_怒海狂澜)
                        {
                            num21 *= player.得到气功加成值(2, 7, 2);
                            player.显示大字(player.人物全服ID, 82);
                        }
                        if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 >= 1)
                        {
                            player.显示大字(player.人物全服ID, 321);
                            num21 += (double)player.天地同寿回避累积攻击力;
                            player.天地同寿回避累积攻击力 = 0;
                            player.天地同寿回避次数 = 0;
                        }
                        if ((double)RNG.Next(1, 100) <= player.升天五式_惊天动地)
                        {
                            num21 *= 1.4;
                            player.显示大字(player.人物全服ID, 1015);
                        }
                    }
                    else if (player.Player_Job == 3)
                    {
                        double num23 = 0.4;
                        double num24 = player.枪_升天一气功_破甲刺魂;
                        double num25 = RNG.Next(1, 100);
                        if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5)
                        {
                            if (player.怒)
                            {
                                num24 += player.枪_升天二气功_以退为进;
                                num23 += player.枪_升天二气功_以退为进 * 0.01;
                            }
                            if (num25 <= num24)
                            {
                                num21 += (double)player.FLD_装备_追加_防御 * num23;
                                player.显示大字(player.人物全服ID, 330);
                            }
                        }
                        //24.0 EVIAS 削弱攻击者的怒吼之意
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2006);
                        if ((double)RNG.Next(1, 100) <= player.枪_怒意之吼 - ruo )
                        {
                            double num26 = 1.2;
                            player.显示大字(player.人物全服ID, 332);
                            if (player.怒)
                            {
                                num26 = 1.2 + player.枪_升天三气功_怒意之火;
                            }
                            num21 *= num26;
                        }
                        if (!player.怒 && (double)RNG.Next(1, 100) <= player.升天五式_灭世狂舞)
                        {
                            num21 *= 1.2;
                            player.显示大字(player.人物全服ID, 1016);
                        }
                        if ((double)RNG.Next(1, 100) <= player.枪寒冰领域 && player.Player_Job == 3 && (int)DateTime.Now.Subtract(player.寒冰领域释放间隔).TotalSeconds > 10)
                        {
                            player.显示大字(player.人物全服ID, 572);
                            player.寒冰领域释放间隔 = DateTime.Now;
                            List<Players> list = player.群攻查找范围RW2(player, 5);
                            if (list != null && list.Count > 0)
                            {
                                foreach (Players item4 in list)
                                {
                                    PlayersBes.冰冻判断(572, item4);
                                }
                            }
                        }
                    }
                    else if (player.Player_Job == 4)
                    {
                        num21 += player.弓_锐利之箭 + 1.0;
                        //24.0 EVIAS 削弱攻击者的无明暗矢
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2007);
                        if ((double)RNG.Next(1, 100) <= player.弓_无明暗矢 - ruo)
                        {
                            num21 *= 1.05 + player.弓_无明暗矢 * 0.01;
                            player.显示大字(player.人物全服ID, 49);
                            player.触发魔法无明暗矢 = true;
                        }
                        if (value.FLD_TYPE == 4)
                        {
                            if (value2.检查烈日炎炎状态() && BitConverter.ToInt32(value2.装备栏已穿装备[0].物品ID, 0) != 0)
                            {
                                num11 -= (double)value2.装备栏已穿装备[0].物品防御力;
                            }
                            num21 = (num9 - num11 * 0.7) * World.弓手攻击倍数 + (double)(player.FLD_人物基本_命中 / 4) - (double)(value2.FLD_人物基本_回避 / 4) + (double)(player.人物_追加_PVP战力 * 10);
                            int num27 = RNG.Next(1, 110);
                            int num28 = RNG.Next(1, 120);
                            if ((double)RNG.Next(1, 100) <= player.弓_升天三气功_天外三矢)
                            {
                                player.显示大字(player.人物全服ID, 342);
                                if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5)
                                {
                                    num21 *= 1.25;
                                }
                            }
                            if ((double)num27 <= player.弓_心神凝聚)
                            {
                                player.弓群攻触发心神 = true;
                                num21 *= 3.0;
                            }
                            //24.0 EVIAS 削弱攻击者的致命绝杀
                            double ruos = 反气功系统.计算反气功弱化值(value2, player, 2008);
                            if ((double)num28 <= player.弓_致命绝杀 - ruos )
                            {
                                num21 *= player.得到气功加成值(4, 11, 2);
                                player.显示大字(player.人物全服ID, 140);
                                player.触发弓箭致命绝杀 = true;
                            }
                        }
                        if (value.FLD_武功类型 == 3 && (double)RNG.Next(1, 100) <= player.弓_升天一气功_绝影射魂)
                        {
                            player.显示大字(player.人物全服ID, 340);
                            player.触发绝影射魂 = true;
                        }
                        if ((double)RNG.Next(1, 100) <= player.升天五式_千里一击)
                        {
                            num21 *= 1.0 + player.升天五式_千里一击 * 0.01;
                            player.显示大字(player.人物全服ID, 1017);
                        }
                    }
                    else if (player.Player_Job == 5)
                    {
                        num21 *= 1.0 + player.医_长攻击力 * player.得到气功加成值(5, 5, 2);
                        if ((double)RNG.Next(1, 100) <= player.真武绝击)
                        {
                            player.显示大字(player.人物全服ID, 17);
                            if ((double)RNG.Next(1, 100) <= value2.韩飞官真气还原)
                            {
                                num21 *= 1.0;
                                value2.显示大字(value2.人物全服ID, 577);
                            }
                            else
                            {
                                num21 *= player.得到气功加成值(5, 7, 2);
                            }
                        }
                        double num29 = 0.0;
                        if (player.医_无中生有 - player.医_狂意护体 > 0.0)
                        {
                            num29 = player.医_无中生有 - player.医_狂意护体;
                        }
                        double num30 = num29 * 0.01;
                        //24.0 EVIAS 削弱攻击者的无中生有
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2009);
                        if ((double)RNG.Next(1, 100) <= player.医_无中生有 - ruo)
                        {
                            player.显示大字(player.人物全服ID, 351);
                            if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && (double)RNG.Next(1, 100) <= player.医_升天三气功_明镜止水)
                            {
                                player.显示大字(player.人物全服ID, 352);
                                num30 *= 2.0;
                            }
                            if (num30 > 0.0)
                            {
                                num21 += (double)(int)(num21 * num30);
                            }
                        }
                    }
                    else if (player.Player_Job == 6)
                    {
                        if (value.FLD_PID == 801303)
                        {
                            if ((int)DateTime.Now.Subtract(player.Pktime801303).TotalSeconds < 30)
                            {
                                return;
                            }
                            player.Pktime801303 = DateTime.Now;
                            num21 = 0.0;
                            flag = true;
                            player.怒气++;
                            if (player.怒气 > 5)
                            {
                                player.怒气 = 5;
                            }
                        }
                        else if (value.FLD_INDEX == 1 || value.FLD_INDEX == 5 || value.FLD_INDEX == 9 || value.FLD_INDEX == 13 || value.FLD_INDEX == 17 || value.FLD_INDEX == 21 || value.FLD_INDEX == 25)
                        {
                            if (player.怒气 > 0)
                            {
                                if (RNG.Next(1, 100) <= 35)
                                {
                                    if (!value2.GetAbnormalState(9))
                                    {
                                        double num31 = 10000.0 + player.刺_千蛛万手;
                                        异常状态类 value4 = new 异常状态类(value2, (int)num31, 9, 0.0);
                                        value2.异常状态.TryAdd(9, value4);
                                        value2.delFLD_追加百分比_防御(0.07);
                                        value2.更新武功和状态();
                                    }
                                    if (!value2.GetAbnormalState(11))
                                    {
                                        double num32 = 10000.0 + player.刺_千蛛万手;
                                        异常状态类 value5 = new 异常状态类(value2, (int)num32, 11, 0.0);
                                        value2.异常状态.TryAdd(11, value5);
                                    }
                                }
                                player.怒气--;
                            }
                        }
                        else if (value.FLD_INDEX == 2 || value.FLD_INDEX == 6 || value.FLD_INDEX == 10 || value.FLD_INDEX == 14 || value.FLD_INDEX == 18 || value.FLD_INDEX == 22 || value.FLD_INDEX == 26)
                        {
                            if (player.怒气 > 0)
                            {
                                if (!value2.GetAbnormalState(9) && RNG.Next(1, 100) <= 35)
                                {
                                    double num33 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 value6 = new 异常状态类(value2, (int)num33, 9, 0.0);
                                    value2.异常状态.TryAdd(9, value6);
                                    value2.delFLD_追加百分比_防御(0.07);
                                    value2.更新武功和状态();
                                }
                                double num34 = 0.005;
                                switch (value.FLD_INDEX)
                                {
                                    case 10:
                                        num34 = 0.009;
                                        break;
                                    case 6:
                                        num34 = 0.007;
                                        break;
                                    case 2:
                                        num34 = 0.005;
                                        break;
                                    case 18:
                                        num34 = 0.013;
                                        break;
                                    case 14:
                                        num34 = 0.011;
                                        break;
                                    case 26:
                                        num34 = 0.02;
                                        break;
                                    case 22:
                                        num34 = 0.015;
                                        break;
                                }
                                if (!value2.GetAbnormalState(10))
                                {
                                    double num35 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 异常状态类2 = new 异常状态类(value2, (int)num35, 10, (int)(num21 * num34));
                                    value2.异常状态.TryAdd(10, 异常状态类2);
                                    异常状态类2.异常状态类出血人物(num21 * num34);
                                }
                                player.怒气--;
                            }
                        }
                        else if ((value.FLD_INDEX == 3 || value.FLD_INDEX == 7 || value.FLD_INDEX == 11 || value.FLD_INDEX == 15 || value.FLD_INDEX == 19 || value.FLD_INDEX == 23 || value.FLD_INDEX == 27) && player.怒气 > 0)
                        {
                            num21 += num21 * 0.3 * (double)player.怒气;
                            player.显示大字(player.人物全服ID, 80);
                            player.怒气 = 0;
                        }
                    }
                    if (player.Player_Job == 7)
                    {
                        if (!player.检查和弦状态())
                        {
                            if ((double)RNG.Next(1, 100) < player.琴师_梅花三弄)
                            {
                                player.显示大字(人物ID, 87);
                                int num36 = RNG.Next(1, 100);
                                int num37;
                                if (num36 <= 33)
                                {
                                    num37 = 900401;
                                    player.琴师状态 = 16;
                                }
                                else if (num36 >= 33 && num36 <= 66)
                                {
                                    num37 = 900402;
                                    player.琴师状态 = 32;
                                }
                                else
                                {
                                    num37 = 900403;
                                    player.琴师状态 = 64;
                                }
                                追加状态类 value7 = new 追加状态类(player, 10000, num37, 0);
                                player.追加状态列表.TryAdd(num37, value7);
                                player.状态效果(BitConverter.GetBytes(num37), 1, 10000);
                                player.更新人物数据(player);
                            }
                        }
                        else if (player.追加状态列表.ContainsKey(900401))
                        {
                            if ((double)RNG.Next(1, 100) < player.琴师_阳明春晓概率 + player.琴_三和弦_状态效果 && !value2.异常状态.ContainsKey(22))
                            {
                                double num38 = player.琴师_阳明春晓时间;
                                异常状态类 value8 = new 异常状态类(value2, (int)num38, 22, player.琴师_阳明春晓攻击);
                                value2.异常状态.TryAdd(22, value8);
                                value2.琴师_阳明春晓_减少攻击 = player.琴师_阳明春晓攻击;
                                if ((double)RNG.Next(1, 100) <= player.琴师_阳明春晓绝望)
                                {
                                    if (value2.异常状态.ContainsKey(23))
                                    {
                                        value2.异常状态[23].时间结束事件();
                                    }
                                    异常状态类 value9 = new 异常状态类(value2, 20000, 23, 0.0);
                                    value2.异常状态.TryAdd(23, value9);
                                }
                            }
                            if ((double)RNG.Next(1, 100) < player.琴师_潇湘雨夜概率 + player.琴_三和弦_状态效果 && !value2.异常状态.ContainsKey(25))
                            {
                                double num39 = player.琴师_潇湘雨夜时间;
                                异常状态类 value10 = new 异常状态类(value2, (int)num39, 25, player.琴师_潇湘雨夜防御);
                                value2.异常状态.TryAdd(25, value10);
                                value2.琴师_潇湘雨夜_减少防御 = player.琴师_潇湘雨夜防御;
                                if ((double)RNG.Next(1, 100) <= player.琴师_潇湘雨夜不安)
                                {
                                    if (value2.异常状态.ContainsKey(24))
                                    {
                                        value2.异常状态[24].时间结束事件();
                                    }
                                    异常状态类 value11 = new 异常状态类(value2, 20000, 24, 0.0);
                                    value2.异常状态.TryAdd(24, value11);
                                }
                            }
                        }
                        else if (player.追加状态列表.ContainsKey(900402) && value.FLD_TYPE != 4)
                        {
                            //24.0 EVIAS 削弱攻击者的鸾凤和鸣
                            double ruo = 反气功系统.计算反气功弱化值(value2, player, 2013);
                            double num40 = player.琴_七和弦_状态效果;
                            if ((double)RNG.Next(1, 100) < player.琴师_鸾凤和鸣 - ruo + player.琴师_升天一气功_飞花点翠)
                            {
                                player.显示大字(人物ID, 88);
                                num40 = player.琴_七和弦_状态效果 * 2.0 * (1.0 + player.琴师_升天一气功_飞花点翠加成);
                            }
                            num21 *= 1.0 + num40 * player.琴师_升天三气功_子夜秋歌;
                        }
                        if ((double)RNG.Next(1, 100) <= player.升天五式_龙爪纤指手)
                        {
                            player.显示大字(player.人物全服ID, 1019);
                            num21 *= 1.2;
                        }
                    }
                    else if (player.Player_Job == 8)
                    {
                        double num41 = 0.0;
                        double num42 = 1.0;
                        if (value.FLD_INDEX == 29 || value.FLD_INDEX == 30 || value.FLD_INDEX == 31)
                        {
                            num42 = 2.0;
                        }
                        if ((double)RNG.Next(1, 100) <= player.韩_天魔极血概率 * num42)
                        {
                            player.显示大字(player.人物全服ID, 20252);
                            num41 = player.韩_升天二气功_天魔护体;
                            num21 *= 1.8;
                            if (player.触发天魔极血)
                            {
                                num21 += player.韩飞官_天魔狂血攻击力;
                                player.韩飞官_天魔狂血攻击力 = 0.0;
                                player.触发天魔极血 = false;
                            }
                        }
                        if ((double)RNG.Next(1, 100) <= player.真武绝击)
                        {
                            player.显示大字(player.人物全服ID, 17);
                            if ((double)RNG.Next(1, 100) <= value2.韩飞官真气还原)
                            {
                                num21 *= 1.0;
                                value2.显示大字(value2.人物全服ID, 577);
                            }
                            else
                            {
                                num21 *= player.得到气功加成值(8, 8, 2);
                            }
                        }
                        if ((double)RNG.Next(1, 100) <= player.暗影绝杀)
                        {
                            player.显示大字(player.人物全服ID, 18);
                            num21 *= player.得到气功加成值(8, 11, 2);
                        }
                        if ((double)RNG.Next(1, 110) <= player.韩_升天一气功_行风弄舞 + num41)
                        {
                            player.显示大字(player.人物全服ID, 600);
                            num21 *= 1.25;
                        }
                        else
                        {
                            //24.0 EVIAS 削弱攻击者的天魔狂血
                            double ruo = 反气功系统.计算反气功弱化值(value2, player, 2015);
                            if ((double)RNG.Next(1, 120) <= (player.韩_天魔狂血概率 - ruo) * num42)
                            {
                                player.显示大字(player.人物全服ID, 252);
                                num21 *= 1.4;
                                player.韩飞官_天魔狂血攻击力 = num21 * (0.4 * num42 + player.升天五式_天魔之力);
                                player.触发天魔极血 = true;
                            }
                        }
                        if (value.FLD_PID == 1000501 && value2.异常状态 != null && !value2.GetAbnormalState(8))
                        {
                            异常状态类 value12 = new 异常状态类(value2, 4000, 8, 0.0);
                            value2.异常状态.TryAdd(8, value12);
                            value2.人物锁定 = true;
                        }
                    }
                    else if (player.Player_Job == 9)
                    {
                        if ((double)RNG.Next(1, 100) < player.谭_怒海狂澜)
                        {
                            num21 *= player.得到气功加成值(9, 10, 2);
                            player.显示大字(player.人物全服ID, 82);
                        }
                        if (player.触发缩影步 && value2.异常状态 != null && !value2.GetAbnormalState(26))
                        {
                            player.触发缩影步 = false;
                            异常状态类 value13 = new 异常状态类(value2, 3000, 26, 0.0);
                            value2.异常状态.TryAdd(26, value13);
                            value2.人物锁定 = true;
                            player.显示大字(player.人物全服ID, 407);
                        }
                        if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 >= 1)
                        {
                            player.显示大字(player.人物全服ID, 321);
                            num21 += (double)player.天地同寿回避累积攻击力;
                            player.天地同寿回避累积攻击力 = 0;
                            player.天地同寿回避次数 = 0;
                        }
                        if (player.触发天地回流)
                        {
                            player.触发天地回流 = false;
                            num21 *= 1.45;
                            player.显示大字(player.人物全服ID, 407);
                        }
                    }
                    else if (player.Player_Job == 10)
                    {
                        if (num == 3000109)
                        {
                            num21 *= 1.45;
                        }
                        //24.0 EVIAS 削弱攻击者的会心一击
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2019);
                        if (num != 0)
                        {
                            if ((double)RNG.Next(1, 100) <= player.拳师_会心一击 - ruo)
                            {
                                player.显示大字(player.人物全服ID, 557);
                                num21 *= 1.0 + player.拳师_会心一击威力;
                            }
                            if ((double)RNG.Next(1, 100) <= player.拳_升天一气功_夺命连环)
                            {
                                player.显示大字(player.人物全服ID, 561);
                                num21 *= 1.45;
                            }
                        }
                        else if ((double)RNG.Next(1, 100) <= player.拳师_会心一击)
                        {
                            player.显示大字(player.人物全服ID, 557);
                            num21 *= 1.0 + player.拳师_会心一击威力 / 2.0;
                        }
                        num21 *= 1.0 + player.拳师_磨杵成针;
                    }
                    else if (player.Player_Job == 11)
                    {
                        if ((double)RNG.Next(1, 100) <= player.梅_玄武强击 * player.得到气功加成值(player.Player_Job, 5, 1))
                        {
                            num21 *= 1.0 + player.梅_玄武强击 * player.得到气功加成值(player.Player_Job, 5, 2);
                        }
                        if ((value2.Player_Job == 2 || value2.Player_Job == 9) && player.梅_嫉妒的化身 > 0.0 && (double)RNG.Next(1, 100) <= player.梅_嫉妒的化身)
                        {
                            num21 *= 1.0 + player.得到气功加成值(player.Player_Job, 9, 2);
                            player.显示大字(player.人物全服ID, 808);
                        }
                        if ((double)RNG.Next(1, 100) <= player.梅_升天一气功_玄武雷电)
                        {
                            num21 *= 1.4;
                            player.显示大字(player.人物全服ID, 803);
                        }
                        if (player.人物坐标_地图 != 7301 && (double)RNG.Next(1, 100) <= player.梅_升天二气功_玄武诅咒)
                        {
                            num21 += (double)player.人物最大_HP * 0.2;
                            player.显示大字(player.人物全服ID, 806);
                        }
                    }
                    else if (player.Player_Job == 12)
                    {
                        if ((double)RNG.Next(1, 100) <= player.真武绝击)
                        {
                            player.显示大字(player.人物全服ID, 17);
                            if ((double)RNG.Next(1, 100) <= value2.韩飞官真气还原)
                            {
                                num21 *= 1.0;
                                value2.显示大字(value2.人物全服ID, 577);
                            }
                            else
                            {
                                num21 *= player.得到气功加成值(12, 6, 2);
                            }
                        }
                        double num43 = RNG.Next(1, 100);
                        //24.0 EVIAS 削弱攻击者的流星漫天
                        double ruo = 反气功系统.计算反气功弱化值(value2, player, 2023);
                        if (player.流星漫天 > 0.0 && player.流星漫天 - ruo + player.升天五式_破空坠星 >= (double)RNG.Next(1, 100))
                        {
                            double num44 = RNG.Next(1, 100);
                            player.显示大字(player.人物全服ID, 1005);
                            if (player.弱点攻破 >= num44)
                            {
                                player.触发流星漫天 = true;
                                if (player.卢_破血狂风 > 0.0 && player.卢_破血狂风 >= num43)
                                {
                                    num21 *= player.得到气功加成值(12, 10, 2) + 1.0;
                                    player.显示大字(player.人物全服ID, 1012);
                                }
                                else
                                {
                                    num21 *= player.得到气功加成值(12, 10, 2);
                                }
                                player.显示大字(player.人物全服ID, 1009);
                            }
                            else if (player.技冠群雄 >= (double)RNG.Next(1, 100))
                            {
                                player.显示大字(player.人物全服ID, 1013);
                                num21 *= 2.0;
                            }
                        }
                    }
                    else if (player.Player_Job == 13)
                    {
                        num21 *= 1.0 + player.神女长功击力;
                        int num45 = RNG.Next(1, 100);
                        if (player.神女杀星义虎 >= (double)RNG.Next(1, 100))
                        {
                            player.显示大字(player.人物全服ID, 1027);
                            num21 *= 1.15 + player.神女杀星义气;
                        }
                        else if ((double)num45 <= player.神女杀星义杀)
                        {
                            player.显示大字(player.人物全服ID, 1026);
                            num21 *= 1.35 + player.神女杀星义气;
                        }
                        if (player.神女真武绝击 >= (double)RNG.Next(1, 100))
                        {
                            player.显示大字(player.人物全服ID, 17);
                            if ((double)RNG.Next(1, 100) <= value2.韩飞官真气还原)
                            {
                                num21 *= 1.0;
                                value2.显示大字(value2.人物全服ID, 577);
                            }
                            else
                            {
                                num21 *= player.得到气功加成值(13, 10, 2);
                            }
                        }
                        if (player.神女蛊毒解除 >= (double)RNG.Next(1, 100))
                        {
                            num21 *= 1.1;
                            player.显示大字(player.人物全服ID, 1025);
                        }
                        if (player.神女愤怒调节 > 0.0 && player.神女愤怒调节 > (double)RNG.Next(1, 100))
                        {
                            if (!value2.怒)
                            {
                                value2.人物_SP = 0;
                            }
                            else
                            {
                                value2.清除怒气();
                            }
                            player.显示大字(player.人物全服ID, 1024);
                            value2.更新HP_MP_SP();
                        }
                    }
                    int num46 = RNG.Next(1, 125);
                    //24.0 削弱攻击者的致残（通用反致残）
                    double ruo1 = 反气功系统.计算反气功弱化值(value2, player, 2027);
                    if (player.升天五式_致残 > 0.0 && player.升天五式_致残 - ruo1 > (double)num46 && !value2.检查致残状态())
                    {
                        player.显示大字(人物ID, 1014);
                        if ((double)RNG.Next(1, 100) <= value2.神女抗击身法)
                        {
                            value2.显示大字(value2.人物全服ID, 582);
                        }
                        else if ((double)RNG.Next(1, 100) <= value2.剑百毒不侵)
                        {
                            value2.显示大字(value2.人物全服ID, 571);
                        }
                        else
                        {
                            追加状态类 value14 = new 追加状态类(value2, 1500, 1008002012, 0);
                            value2.追加状态列表.TryAdd(1008002012, value14);
                            value2.状态效果(BitConverter.GetBytes(1008002012), 1, 1500);
                            value2.更新人物数据(value2);
                            value2.更新广播人物数据();
                            value2.吃药时间 = DateTime.Now.AddSeconds(1.5);
                        }
                    }
                }
                if (!value2.检查毒蛇出洞状态())
                {
                    if (value2.Player_Job == 2)
                    {
                        //24.0 EVIAS 削弱被攻击者的护身罡气
                        double ruo = 反气功系统.计算反气功弱化值(player, value2, 2002);
                        if ((double)RNG.Next(1, 110) <= value2.剑_升天一气功_护身罡气 - ruo)
                        {
                            num21 *= 0.5;
                            value2.显示大字(value2.人物全服ID, 25);
                        }
                        if ((double)RNG.Next(1, 110) <= value2.剑_回柳身法)
                        {
                            if (player.Player_Job == 2)
                            {
                                if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                                {
                                    player.天地同寿回避次数++;
                                    player.天地同寿回避累积攻击力 += (int)(num21 * player.剑_升天二气功_天地同寿);
                                }
                            }
                            else if (player.Player_Job == 9 && value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                            {
                                player.天地同寿回避次数++;
                                player.天地同寿回避累积攻击力 += (int)(num21 * player.谭_升天二气功_天地同寿);
                            }
                            //24.0 EVIAS 削弱攻击者的电光石火
                            double ruos = 反气功系统.计算反气功弱化值(value2, player, 2020);
                            if (player.Player_Job == 10 && (double)RNG.Next(1, 100) <= player.拳_升天二气功_电光石火 - ruos)
                            {
                                player.显示大字(player.人物全服ID, 562);
                                if (!value2.GetAbnormalState(4))
                                {
                                    异常状态类 value15 = new 异常状态类(value2, 3000, 4, 0.0);
                                    value2.异常状态.TryAdd(4, value15);
                                }
                            }
                            num21 = 0.0;
                            flag = true;
                            value2.显示大字(value2.人物全服ID, 402);
                        }
                    }
                    else if (value2.Player_Job == 4)
                    {
                        if (num4 > 100.0)
                        {
                            num21 *= 1.0 - value2.弓恶尽矢穷;
                        }
                    }
                    else if (value2.Player_Job == 5)
                    {
                        if ((double)RNG.Next(1, 100) <= value2.升天一气功_狂风天意 && !value2.怒)
                        {
                            value2.人物_SP = value2.人物最大_SP + 5;
                        }
                        if ((double)RNG.Next(1, 100) <= value2.升天五式_形移妖相)
                        {
                            num21 = 0.0;
                            flag = true;
                            value2.显示大字(value2.人物全服ID, 1018);
                        }
                        if ((double)RNG.Next(1, 100) <= value2.医生云心月性 && !value2.GetAddState(700574) && (int)DateTime.Now.Subtract(value2.云心月性释放间隔).TotalSeconds > 10)
                        {
                            num21 = 0.0;
                            flag = true;
                            value2.显示大字(value2.人物全服ID, 574);
                            value2.寒冰领域释放间隔 = DateTime.Now;
                            追加状态类 value16 = new 追加状态类(value2, 5000, 700574, 0);
                            value2.追加状态列表.TryAdd(700574, value16);
                            value2.状态效果(BitConverter.GetBytes(700574), 1, 5000);
                        }
                        if (value2.GetAddState(700574))
                        {
                            num21 = 0.0;
                            flag = true;
                        }
                    }
                    else if (value2.Player_Job == 6)
                    {
                        if ((double)RNG.Next(1, 110) <= value2.刺_升天一气功_夜魔缠身)
                        {
                            num21 *= 0.7;
                            value2.显示大字(value2.人物全服ID, 370);
                        }
                        if ((double)RNG.Next(1, 100) <= value2.刺_升天二气功_顺水推舟)
                        {
                            value2.加血((int)(num21 * 0.2));
                            value2.显示大字(value2.人物全服ID, 371);
                        }
                        if ((double)RNG.Next(1, 110) <= value2.刺_三花聚顶)
                        {
                            //24.0 EVIAS 削弱被攻击者的连消带打
                            double ruo = 反气功系统.计算反气功弱化值(player, value2, 2012);
                            value2.刺_连消带打数量 = num21 * Math.Max(0, value2.刺_连消带打 - ruo);
                            if (player.Player_Job == 2)
                            {
                                if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                                {
                                    player.天地同寿回避次数++;
                                    player.天地同寿回避累积攻击力 += (int)(num21 * player.剑_升天二气功_天地同寿);
                                }
                            }
                            else if (player.Player_Job == 9 && value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                            {
                                player.天地同寿回避次数++;
                                player.天地同寿回避累积攻击力 += (int)(num21 * player.谭_升天二气功_天地同寿);
                            }
                            //24.0 EVIAS 削弱攻击者的电光石火
                            double ruo2 = 反气功系统.计算反气功弱化值(value2, player, 2020);
                            if (player.Player_Job == 10 && (double)RNG.Next(1, 100) <= player.拳_升天二气功_电光石火 - ruo2)
                            {
                                player.显示大字(player.人物全服ID, 562);
                                if (!value2.GetAbnormalState(4))
                                {
                                    异常状态类 value17 = new 异常状态类(value2, 3000, 4, 0.0);
                                    value2.异常状态.TryAdd(4, value17);
                                }
                            }
                            num21 = 0.0;
                            flag = true;
                        }
                    }
                    else if (value2.Player_Job == 9)
                    {
                        if ((double)RNG.Next(1, 100) <= value2.谭_护身罡气)
                        {
                            num21 *= 0.5;
                            value2.显示大字(value2.人物全服ID, 25);
                        }
                        if ((double)RNG.Next(1, 110) <= value2.谭_回柳身法)
                        {
                            if (player.Player_Job == 2)
                            {
                                if (value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                                {
                                    player.天地同寿回避次数++;
                                    player.天地同寿回避累积攻击力 += (int)(num21 * player.剑_升天二气功_天地同寿);
                                }
                            }
                            else if (player.Player_Job == 9 && value.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && player.天地同寿回避次数 < 3)
                            {
                                player.天地同寿回避次数++;
                                player.天地同寿回避累积攻击力 += (int)(num21 * player.谭_升天二气功_天地同寿);
                            }
                            //24.0 EVIAS 削弱攻击者的电光石火
                            double ruos = 反气功系统.计算反气功弱化值(value2, player, 2020);
                            if (player.Player_Job == 10 && (double)RNG.Next(1, 100) <= player.拳_升天二气功_电光石火 - ruos)
                            {
                                player.显示大字(player.人物全服ID, 562);
                                if (!value2.GetAbnormalState(4))
                                {
                                    异常状态类 value18 = new 异常状态类(value2, 3000, 4, 0.0);
                                    value2.异常状态.TryAdd(4, value18);
                                }
                            }
                            num21 = 0.0;
                            flag = true;
                            value2.显示大字(value2.人物全服ID, 402);
                        }
                        if ((double)RNG.Next(1, 110) <= value2.谭_升天三气功_以柔克刚 + value2.升天五式_惊涛骇浪)
                        {
                            value2.显示大字(value2.人物全服ID, 700);
                            num21 *= 1.0 - value2.谭_升天三气功_以柔克刚 * 0.01;
                            if (RNG.Next(1, 6) >= 2 && !value2.GetAddState(1008001198))
                            {
                                if (value2.追加状态列表 != null && value2.GetAddState(1008001198))
                                {
                                    value2.追加状态列表[1008001198].时间结束事件();
                                }
                                追加状态类 追加状态类2 = new 追加状态类(player, 3000, 1008001198, 0);
                                value2.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
                                value2.FLD_人物_追加百分比_回避 += 0.1;
                                value2.状态效果(BitConverter.GetBytes(1008001198), 1, 3000);
                                value2.更新武功和状态();
                            }
                        }
                    }
                    else if (value2.Player_Job == 10)
                    {
                        if (num21 > (double)(value2.人物_HP / 2) && (double)RNG.Next(1, 110) <= value2.拳师_金刚不坏)
                        {
                            value2.显示大字(value2.人物全服ID, 554);
                            num21 *= 1.0 - value2.拳师_金刚不坏 * 0.01;
                        }
                        if ((double)RNG.Next(1, 100) <= value2.升天五式_不死之躯)
                        {
                            value2.显示大字(value2.人物全服ID, 1021);
                            num21 = 0.0;
                            flag = true;
                        }
                    }
                    else if (value2.Player_Job == 11)
                    {
                        if ((player.Player_Job == 1 || player.Player_Job == 8) && value2.梅_嫉妒的化身 > 0.0 && (double)RNG.Next(1, 100) <= value2.梅_嫉妒的化身)
                        {
                            double num47 = num21 * (player.得到气功加成值(value2.Player_Job, 9, 2) / 2.0);
                            num21 -= num47;
                            value2.显示大字(value2.人物全服ID, 808);
                        }
                        if (value2.梅_障力恢复 > 0.0 && value2.人物_AP * 2 < value2.人物最大_AP && (double)RNG.Next(1, 100) <= value2.梅_障力恢复)
                        {
                            value2.人物_AP = value2.人物最大_AP;
                            value2.显示大字(value2.人物全服ID, 801);
                        }
                        if (value2.梅_愤怒爆发 > 0.0 && RNG.Next(1, 100) <= 40 && value2.怒点 < 3)
                        {
                            Players players = value2;
                            Players players2 = players;
                            players2.怒点++;
                        }
                        if (value2.梅_吸血进击 > 0.0 && (double)RNG.Next(1, 100) <= value2.梅_吸血进击 && value2.人物_HP * 2 < value2.人物最大_HP)
                        {
                            int num48 = (int)(num21 / 2.0);
                            if (num48 > 2000)
                            {
                                num48 = 2000;
                            }
                            value2.加血(num48);
                            value2.显示大字(value2.人物全服ID, 804);
                            value2.更新HP_MP_SP();
                        }
                    }
                }
                if (player.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= player.FLD_装备_追加_中毒概率百分比 && value2.异常状态 != null && !value2.GetAbnormalState(3))
                {
                    异常状态类 value19 = new 异常状态类(value2, 60000, 3, 0.0);
                    value2.异常状态.TryAdd(3, value19);
                    value2.中毒 = true;
                }
                if (value2.FLD_装备_追加_中毒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= value2.FLD_装备_追加_中毒概率百分比 && !player.GetAbnormalState(3))
                {
                    player.异常状态.TryAdd(3, new 异常状态类(player, 60000, 3, 0.0));
                    player.中毒 = true;
                }
                if (value2.FLD_装备_降低_伤害值 > 0.0)
                {
                    num21 -= (double)(int)value2.FLD_装备_降低_伤害值;
                }
                if (value2.全职业气功防御 + value2.大魔神添加全职业气功防御几率 >= (double)RNG.Next(1, 100) && value2.Player_Level >= 120)
                {
                    num21 *= 0.7;
                    value2.显示大字(value2.人物全服ID, 701);
                }
                player.升天四气功触发(value2);
                double num49;
                if (num21 <= 0.0)
                {
                    num49 = 0.0;
                }
                else
                {
                    if (num21 > 1800000000.0)
                    {
                        num21 = 1800000000.0;
                    }
                    num49 = RNG.Next((int)num21 - 15, (int)num21 + 15);
                    if (player.中级附魂_愤怒 != 0 && RNG.Next(1, 100) <= player.中级附魂_愤怒)
                    {
                        num49 *= 1.2;
                        player.显示大字(player.人物全服ID, 404);
                    }
                    if (!player.检查毒蛇出洞状态())
                    {
                        if (player.Player_Job == 2)
                        {
                            if ((double)RNG.Next(1, 110) <= player.剑_冲冠一怒 && !player.怒)
                            {
                                player.显示大字(player.人物全服ID, 29);
                                player.人物_SP += (int)((double)player.人物_SP * player.剑_冲冠一怒 * 0.005);
                            }
                            if ((double)RNG.Next(1, 100) <= player.剑_移花接木)
                            {
                                player.显示大字(player.人物全服ID, 26);
                                player.加血((int)(num49 * 0.5));
                            }
                        }
                        if (player.Player_Job == 9)
                        {
                            if ((double)RNG.Next(1, 110) <= player.谭_冲冠一怒 && !player.怒)
                            {
                                player.显示大字(player.人物全服ID, 29);
                                player.人物_SP += (int)((double)player.人物_SP * player.谭_冲冠一怒 * 0.005);
                            }
                            if ((double)RNG.Next(1, 100) <= player.谭_移花接木)
                            {
                                player.显示大字(player.人物全服ID, 26);
                                player.加血((int)(num49 * 0.5));
                            }
                        }
                    }
                }
                if (num49 <= 1.0)
                {
                    num49 = RNG.Next(1, 5);
                }
                if (value2.Player_Job == 11)
                {
                    if (value2.梅_障力激活 > 0.0)
                    {
                        num10 = (int)(num49 * (value2.梅_障力激活 * 0.01));
                        if (num10 > value2.人物_AP)
                        {
                            num10 = value2.人物_AP;
                        }
                        value2.人物_AP -= num10;
                    }
                }
                else
                {
                    num10 = 0;
                }
                double num50 = num49 - (double)num10;
                if (player.人物_追加_PVP战力 - value2.人物_追加_PVP战力 > 0)
                {
                    int num51 = player.人物_追加_PVP战力 - value2.人物_追加_PVP战力;
                    num50 *= 1.0 + 0.01 * (double)num51;
                }
                if (player.人物_追加_PVP战力 - value2.人物_追加_PVP战力 < 0)
                {
                    int num52 = player.人物_追加_PVP战力 - value2.人物_追加_PVP战力;
                    num50 *= 1.0 - 0.01 * (double)(-num52);
                }
                if (num50 <= 1.0)
                {
                    num50 = 1.0;
                }
                if (num50 > 1.0)
                {
                    num50 *= 获得技能对人伤害(player.Player_Job, player.Player_Job_leve) - player.FLD_负数武勋伤害减少;
                }
                num50 -= (double)(value2.减免对方伤害 + value2.药品减免对方伤害);
                if (player.FLD_装备_追加_伤害值 > 0 && !flag)
                {
                    num50 += (double)player.FLD_装备_追加_伤害值;
                }
                攻击计算完成(player, 人物ID, 武功ID, (int)num50, 0, 0, num10);
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "魔法攻击人物", $"武功ID: {武功ID}, 目标: {人物ID}");
                if (World.是否开启票红字 == 1)
                {
                    player.系统提示("你魔法攻击人物出错,请联系客服处理");
                    Form1.WriteLine(1, "魔法攻击人物出错" + player.人物全服ID + "|" + 武功ID + " | " + 人物ID + " | " + player.报错次数阀值 + " | " + ex.Message);
                }
            }
        }

        public static void 技能攻击怪物(Players player, int 武功ID, int 人物ID)
        {
            try
            {
                if (!MapClass.GetnpcTemplate(player.人物坐标_地图).TryGetValue(人物ID, out var value))
                {
                    return;
                }
                if (!player.攻击怪物检测(value, 0))
                {
                    player.自动攻击Stop();
                }
                else
                {
                    if (!World.TBL_KONGFU.TryGetValue(武功ID, out var value2) || value.IsNpc == 1 || player.人物坐标_地图 != value.Rxjh_Map || BitConverter.ToInt32(player.装备栏已穿装备[3].物品ID, 0) == 0)
                    {
                        return;
                    }
                    if ((player.Player_Job == 4 || player.Player_Job == 11) && BitConverter.ToInt32(player.装备栏已穿装备[12].物品数量, 0) == 0)
                    {
                        player.初始化已装备物品();
                        return;
                    }
                    if (value2.FLD_TYPE >= 5)
                    {
                        攻击系统.攻击计算完成(player, value.FLD_INDEX, 武功ID, value.Rxjh_HP + 100, 0, value.Rxjh_HP, 0);
                        return;
                    }
                    int num = player.判断拳师连击(武功ID);
                    if (num != 0)
                    {
                        武功ID = num;
                    }
                    if ((value2.FLD_PID == 400001 && !World.TBL_KONGFU.TryGetValue(player.当前激活技能ID, out value2)) || player.Player_Job != value2.FLD_JOB || (value2.FLD_ZX != 0 && player.师傅数据.STNAME == "" && player.Player_Zx != value2.FLD_ZX))
                    {
                        return;
                    }
                    if (value2.FLD_武功类型 == 2 && player.Player_Job != 13)
                    {
                        if (player.人物_MP < player.夫妻武功攻击力MP)
                        {
                            player.魔法不足提示();
                            return;
                        }
                        player.魔法使用(player.夫妻武功攻击力MP);
                    }
                    else if (value2.武功_等级 == 3)
                    {
                        int num2 = value2.FLD_MP + (value2.武功_等级 - 1) * value2.FLD_每级加MP;
                        if (player.人物_MP < num2)
                        {
                            player.魔法不足提示();
                            return;
                        }
                        player.魔法使用(num2);
                    }
                    else
                    {
                        if (player.人物_MP < value2.FLD_MP)
                        {
                            player.魔法不足提示();
                            return;
                        }
                        player.魔法使用(value2.FLD_MP);
                    }
                    if (value2.FLD_INDEX == -1)
                    {
                        player.武功连击记数器 = 0;
                        new 武功类();
                        武功类 武功 = player.新武功连击[0];
                        攻击系统.攻击计算完成(player, 人物ID, 武功ID, 0, 0, value.Rxjh_HP, 0);
                        new Thread(new ThreadWithState(player, 武功, value, 人物ID, num).ThreadProc).Start();
                        return;
                    }
                    if (player.武功连击记数器 > 0)
                    {
                        if (player.武功连击记数器 < player.武功l.Count)
                        {
                            new Thread(new ThreadWithState(player, player.武功l[player.武功连击记数器], value, 人物ID, num).ThreadProc).Start();
                        }
                        else
                        {
                            player.武功连击记数器 = 0;
                        }
                        return;
                    }
                    if (value2.FLD_武功类型 == 1 && player.Player_Job == 13)
                    {
                        if (武功ID == 6002205 && !value.ContainsKeyInAbnormalState(44) && value.FLD_BOSS != 1)
                        {
                            value.怪物阎王爆 = true;
                            异常状态类 value3 = new 异常状态类(player, value, player.人物全服ID, 8000, 44, 0.0);
                            value.异常状态.TryAdd(44, value3);
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value.Rxjh_HP, 0, 0);
                        }
                        if (武功ID != 6002206 || value.FLD_BOSS == 1 || !value.怪物阎王爆)
                        {
                            return;
                        }
                        List<NpcClass> list = value.传染怪物范围(player, 4);
                        if (list != null && list.Count > 0)
                        {
                            foreach (NpcClass item in list)
                            {
                                if (value.怪物阎王爆)
                                {
                                    异常状态类 value4 = new 异常状态类(player, item, player.人物全服ID, 8000, 44, 0.0);
                                    item.异常状态.TryAdd(44, value4);
                                }
                            }
                        }
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value.Rxjh_HP, 0, 0);
                        return;
                    }
                    if (player.Player_Job == 10 && 武功ID == 3000401)
                    {
                        player.移动(value.Rxjh_X, value.Rxjh_Y, 15f, value.Rxjh_Map);
                    }
                    if (value.Rxjh_Map == World.世界BOSS出现地图 && value.FLD_PID == World.世界BOSS怪物ID && World.世界boss != null)
                    {
                        if (RNG.Next(1, 100) <= World.BOSS掉落元宝几率)
                        {
                            int num3 = RNG.Next(World.BOSS掉落元宝数量下限, World.BOSS掉落元宝数量上限);
                            player.检察元宝数据(num3, 1, "世界BOSS");
                            RxjhClass.百宝记录(player.Userid, player.UserName, 0.0, "BOSS获得", 1, num3, World.分区编号);
                            player.系统提示("恭喜你获取得：" + num3 + "元宝", 10, "世界BOSS");
                        }
                        else if (RNG.Next(1, 100) <= World.BOSS掉落钻石几率)
                        {
                            int num4 = RNG.Next(World.BOSS掉落钻石数量下限, World.BOSS掉落钻石数量上限);
                            player.检察钻石数据(num4, 1, "世界BOSS");
                            RxjhClass.百宝记录(player.Userid, player.UserName, 0.0, "BOSS获得", 1, num4, World.分区编号);
                            player.系统提示("恭喜你获取得：" + num4 + "钻石", 10, "世界BOSS");
                        }
                        else if (RNG.Next(1, 100) <= World.BOSS掉落物品几率)
                        {
                            string[] array = World.世界BOSS奖励物品.Split(';');
                            player.增加物品带属性(int.Parse(array[0]), player.得到包裹空位位置(), int.Parse(array[1]), int.Parse(array[2]), int.Parse(array[3]), int.Parse(array[4]), int.Parse(array[5]), int.Parse(array[6]), int.Parse(array[7]), int.Parse(array[8]), int.Parse(array[9]), int.Parse(array[10]), int.Parse(array[11]));
                            player.系统提示("恭喜你获取得:" + ItmeClass.得到物品名称(int.Parse(array[0])) + " 数量:" + int.Parse(array[1]), 10, "世界BOSS");
                        }
                        else if (RNG.Next(1, 100) <= World.BOSS掉落金币几率)
                        {
                            int num5 = RNG.Next(World.BOSS掉落金币数量下限, World.BOSS掉落金币数量上限);
                            player.Player_Money += (uint)num5;
                            player.得到钱的提示((uint)num5);
                            player.更新金钱和负重();
                        }
                        else if (RNG.Next(1, 100) <= World.BOSS掉落武勋几率)
                        {
                            int 武勋 = RNG.Next(World.BOSS掉落武勋数量下限, World.BOSS掉落武勋数量上限);
                            player.武勋加减(武勋, 1, "世界BOSS");
                            player.更新武功和状态();
                        }
                    }
                    //24.0 EVIAS 灵兽特殊效果检查 20250724 
                    if (player.人物灵兽 != null && player.人物灵兽.FLD_LEVEL >= 15)
                    {

                        if (player.人物灵兽.武功新[1, 4] != null && player.人物灵兽.武功新[1, 4].FLD_PID == 1401)
                        {
                            var 武功 = player.人物灵兽.武功新[1, 4];
                            double 基础概率 = 1.6;               // 基础1.6%概率
                            double 等级加成 = 武功.武功_等级 * 0.4; // 每级增加0.4%
                            double 触发概率 = 基础概率 + 等级加成;
                            if (RNG.Next(1, 101) <= 触发概率)
                            {
                                player.显示大字(player.人物灵兽.全服ID, 1401);
                            }
                        }

                        if (player.人物灵兽.武功新[1, 5] != null && player.人物灵兽.武功新[1, 5].FLD_PID == 1402)
                        {
                            var 武功 = player.人物灵兽.武功新[1, 5];
                            double 基础概率 = 0.9;               // 基础0.9%概率
                            double 等级加成 = 武功.武功_等级 * 0.1; // 每级增加0.1%
                            double 触发概率 = 基础概率 + 等级加成;
                            if (RNG.Next(1, 101) <= 触发概率)
                            {
                                player.显示大字(player.人物灵兽.全服ID, 1402);
                            }
                        }

                        if (player.人物灵兽.武功新[1, 6] != null && player.人物灵兽.武功新[1, 6].FLD_PID == 1403)
                        {
                            var 武功 = player.人物灵兽.武功新[1, 6];
                            double 基础概率 = 1.3;               // 基础1.3%概率 
                            double 等级加成 = 武功.武功_等级 * 0.2; // 每级增加0.2%
                            double 触发概率 = 基础概率 + 等级加成;
                            if (RNG.Next(1, 101) <= 触发概率)
                            {
                                player.显示大字(player.人物灵兽.全服ID, 1403);
                            }
                        }
                    }

                    攻击计算(player, value2, value, 武功ID, 人物ID, num);
                }
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "魔法攻击怪物", $"武功ID: {武功ID}, 目标怪物: {人物ID}");
                if (World.是否开启票红字 == 1)
                {
                    player.系统提示("你魔法攻击怪物出错,请联系客服处理");
                    Form1.WriteLine(1, "魔法攻击怪物 " + player.人物全服ID + "|" + 武功ID + " | " + 人物ID + " | " + player.报错次数阀值 + "丨" + ex.Message);
                }
            }
        }

        public static void 攻击确认人物(Players player, int 人物ID, int 攻击类型, int 攻击力)
        {
            try
            {
                if (!World.allConnectedChars.TryGetValue(人物ID, out var value))
                {
                    return;
                }
                if (World.PK掉耐久度 == 1)
                {
                    int num = 0;
                    if (RNG.Next(1, 1000) <= 50)
                    {
                        num = 1;
                        if (player.装备栏已穿装备[0].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[0].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[0].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[0].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[1].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[1].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[1].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[1].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[2].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[2].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[2].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[2].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[3].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[3].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[3].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[3].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[4].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[4].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[4].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[4].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[5].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[5].FLD_FJ_NJ -= num;
                            if (player.装备栏已穿装备[5].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[5].FLD_FJ_NJ = 0;
                            }
                        }
                    }
                }
                if (value.Player_Job == 3)
                {
                    if (value.枪_狂神降世 != 0.0 && !value.怒)
                    {
                        value.人物_SP += (int)(3.0 + (double)(value.Player_Level * 2) * value.枪_狂神降世);
                    }
                }
                else if (value.Player_Job == 10)
                {
                    if (value.拳师_狂神降世 != 0.0 && !value.怒)
                    {
                        value.人物_SP += (int)(3.0 + (double)(value.Player_Level * 2) * value.拳师_狂神降世);
                    }
                }
                else if (value.Player_Job == 6)
                {
                    if (value.刺_荆轲之怒 != 0.0)
                    {
                        value.人物_SP += (int)(3.0 + (double)value.Player_Level * 0.5 * 0.01 * value.刺_荆轲之怒);
                    }
                    else if (攻击力 <= 0)
                    {
                        value.人物_SP++;
                    }
                    else
                    {
                        value.人物_SP += 2;
                    }
                }
                else if (value.Player_Job == 7)
                {
                    if (攻击力 <= 0)
                    {
                        value.人物_SP++;
                    }
                    else
                    {
                        value.人物_SP += 2;
                    }
                }
                else if (攻击力 <= 0)
                {
                    if (!value.怒)
                    {
                        value.人物_SP++;
                    }
                }
                else if (!value.怒)
                {
                    value.人物_SP += 2;
                }
                if (value.FLD_装备_追加_愤怒 > 0 && !value.怒)
                {
                    value.人物_SP += player.FLD_装备_追加_愤怒;
                }
                if (player.FLD_装备_追加_初始化愤怒概率百分比 > 0.0 && (double)RNG.Next(1, 100) <= player.FLD_装备_追加_初始化愤怒概率百分比)
                {
                    value.人物_SP = 0;
                }
                if (攻击力 <= 0)
                {
                    攻击力 = 1;
                }
                if (player.人物坐标_地图 == 45001)
                {
                    World.极限比武台服务.OnAttackSuccess(player, 攻击力);
                }
                value.人物_HP -= 攻击力;
                if (value.神女异常状态 != null && value.神女异常状态.ContainsKey(44))
                {
                    value.阎王爆累计伤害 += 攻击力;
                }
                if (!value.检查毒蛇出洞状态())
                {
                    if (value.Player_Job == 1)
                    {
                        //24.0 EVIAS 削弱被攻击者的反伤
                        double ruo = 反气功系统.计算反气功弱化值(player, value, 2003);
                        if ((double)RNG.Next(1, 100) <= value.人反伤几率 - ruo)
                        {
                            int num2 = 攻击力;
                            if (num2 <= 0)
                            {
                                num2 = 1;
                            }
                            if ((double)RNG.Next(1, 100) <= value.刀_升天二气功_穷途末路)
                            {
                                value.显示大字(value.人物全服ID, 19);
                                num2 *= 2;
                            }
                            if (player.Player_Job == 9 && (double)RNG.Next(1, 110) <= player.谭_升天三气功_以柔克刚 + player.升天五式_惊涛骇浪)
                            {
                                player.显示大字(player.人物全服ID, 700);
                                num2 = 0;
                            }
                            if (player.Player_Job == 12)
                            {
                                num2 -= (int)((double)num2 * player.卢风郎反弹无效);
                            }
                            player.发送反伤攻击数据(num2, value.人物全服ID);
                            if (num2 <= 0)
                            {
                                num2 = 1;
                            }
                            if (value.人物坐标_地图 == 45001)
                            {
                                World.极限比武台服务.OnAttackSuccess(value, num2);
                            }
                            player.人物_HP -= num2;
                            if (player.人物_HP <= 0)
                            {
                                player.自动攻击Stop();
                                value.武勋系统(value, player);
                                player.PK死亡 = true;
                                player.人物_HP = 0;
                                player.最终致命一击 = num2;
                                if (player.人物坐标_地图 == 45001)
                                {
                                    World.极限比武台服务.OnKillSuccess(value, player);
                                    return;
                                }
                                player.人物死亡(player.人物全服ID);
                            }
                        }
                    }
                    else if (value.Player_Job == 2)
                    {
                        if ((double)RNG.Next(1, 100) <= value.剑_升天三气功_火凤临朝 && value.人物_HP <= 0)
                        {
                            value.人物_HP = 10;
                            value.显示大字(value.人物全服ID, 322);
                        }
                    }
                    else if (value.Player_Job == 7)
                    {
                        //24.0 EVIAS 削弱被攻击者的三潭映月
                        double ruo = 反气功系统.计算反气功弱化值(player, value, 2014);
                        if ((double)RNG.Next(1, 100) <= value.琴师_升天二气功_三潭映月 - 5.0 - ruo)
                        {
                            value.显示大字(value.人物全服ID, 391);
                            int num3 = 攻击力;
                            if (num3 <= 0)
                            {
                                num3 = 1;
                            }
                            if (value.人物坐标_地图 == 45001)
                            {
                                World.极限比武台服务.OnAttackSuccess(value, num3);
                            }
                            if (player.Player_Job == 12)
                            {
                                num3 -= (int)((double)num3 * player.卢风郎反弹无效);
                            }
                            player.人物_HP -= num3;
                            if (player.人物_HP <= 0)
                            {
                                player.自动攻击Stop();
                                value.武勋系统(value, player);
                                player.PK死亡 = true;
                                player.人物_HP = 0;
                                player.最终致命一击 = num3;
                                if (player.人物坐标_地图 == 45001)
                                {
                                    World.极限比武台服务.OnKillSuccess(value, player);
                                    return;
                                }
                                player.人物死亡(player.人物全服ID);
                            }
                        }
                    }
                    else if (value.Player_Job == 8)
                    {
                        if ((double)RNG.Next(1, 110) <= value.韩_追骨吸元)
                        {
                            int num4 = (int)((double)攻击力 * value.韩_追骨吸元 * 0.01);
                            if (num4 <= 0)
                            {
                                num4 = 1;
                            }
                            if (player.Player_Job == 9 && (double)RNG.Next(1, 100) < player.谭_升天三气功_以柔克刚 + player.升天五式_惊涛骇浪)
                            {
                                player.显示大字(player.人物全服ID, 700);
                                num4 = 0;
                            }
                            if (player.Player_Job == 12)
                            {
                                num4 -= (int)((double)num4 * player.卢风郎反弹无效);
                            }
                            value.加血(num4);
                            if (value.人物坐标_地图 == 45001)
                            {
                                World.极限比武台服务.OnAttackSuccess(value, num4);
                            }
                            player.人物_HP -= num4;
                            if (player.人物_HP <= 0)
                            {
                                player.自动攻击Stop();
                                value.武勋系统(value, player);
                                player.PK死亡 = true;
                                player.人物_HP = 0;
                                player.最终致命一击 = num4;
                                if (player.人物坐标_地图 == 45001)
                                {
                                    World.极限比武台服务.OnKillSuccess(value, player);
                                    return;
                                }
                                player.人物死亡(player.人物全服ID);
                            }
                        }
                    }
                    else if (value.Player_Job == 9 && (double)RNG.Next(1, 100) <= value.谭_升天三气功_火凤临朝 && value.人物_HP <= 0)
                    {
                        value.人物_HP = 10;
                        value.显示大字(value.人物全服ID, 322);
                    }
                }
                if (player.锁定人物几率 > 0 && RNG.Next(1, 100) <= player.锁定人物几率)
                {
                    if (value.异常状态 != null)
                    {
                        if (!value.GetAbnormalState(17))
                        {
                            double num5 = 2000.0;
                            异常状态类 value2 = new 异常状态类(value, (int)num5, 17, 0.0);
                            value.异常状态.TryAdd(17, value2);
                            value.人物锁定 = true;
                        }
                    }
                    else
                    {
                        value.异常状态 = new ConcurrentDictionary<int, 异常状态类>();
                        double num6 = 2000.0;
                        异常状态类 value3 = new 异常状态类(value, (int)num6, 17, 0.0);
                        value.异常状态.TryAdd(17, value3);
                        value.人物锁定 = true;
                    }
                }
                player.攻击列表.Clear();
                if (value.人物_HP <= 0)
                {
                    player.自动攻击Stop();
                    value.PK死亡 = true;
                    value.人物_HP = 0;
                    value.最终致命一击 = 攻击力;
                    if (player.人物坐标_地图 == 45001)
                    {
                        World.极限比武台服务.OnKillSuccess(player, value);
                    }
                    else
                    {
                        value.人物死亡(player.人物全服ID);
                    }
                    if (player.神女异常状态.ContainsKey(40) && value.人物全服ID == player.神女虚弱ID)
                    {
                        player.人物_HP -= (int)((double)player.人物最大_HP * 0.15);
                        if (player.人物_HP <= 1)
                        {
                            player.人物_HP = 1;
                        }
                        player.更新HP_MP_SP();
                    }
                    else if (player.神女异常状态.ContainsKey(41) && value.人物全服ID == player.神女虚弱ID)
                    {
                        player.人物_HP -= (int)((double)player.人物最大_HP * 0.3);
                        if (player.人物_HP <= 1)
                        {
                            player.人物_HP = 1;
                        }
                        player.更新HP_MP_SP();
                    }
                    if (player.人物坐标_地图 != 7001 && player.人物坐标_地图 != 7101)
                    {
                        if (player.人物坐标_地图 == 41001)
                        {
                            if (World.仙魔大战进程 == 3)
                            {
                                if (player.UserNip != value.UserNip || World.同IP势力战不计分 == 0)
                                {
                                    if (player.检查玩家是否在仙魔大战区域内(player))
                                    {
                                        if (player.检查玩家是否在仙魔大战区域内(value))
                                        {
                                            value.仙魔大战死亡数++;
                                            player.仙魔大战杀人数++;
                                            if (World.仙魔Top.TryGetValue(player.UserName, out var value4))
                                            {
                                                value4.杀人数++;
                                            }
                                            else
                                            {
                                                value4 = new 仙魔大战top();
                                                value4.人物名 = player.UserName;
                                                value4.等级 = player.Player_Level;
                                                value4.职业 = player.Player_Job;
                                                value4.势力 = ((!(player.仙魔大战派别 == "仙族")) ? "魔族" : "仙族");
                                                value4.帮派 = player.帮派名字;
                                                value4.杀人数 = 1;
                                                value4.死亡数 = 0;
                                                World.仙魔Top.TryAdd(player.UserName, value4);
                                            }
                                            if (World.仙魔Top.TryGetValue(value.UserName, out var value5))
                                            {
                                                value5.死亡数++;
                                            }
                                            else
                                            {
                                                仙魔大战top 仙魔大战top2 = new 仙魔大战top();
                                                仙魔大战top2.人物名 = value.UserName;
                                                仙魔大战top2.等级 = value.Player_Level;
                                                仙魔大战top2.职业 = value.Player_Job;
                                                value4.势力 = ((!(player.仙魔大战派别 == "仙族")) ? "魔族" : "仙族");
                                                仙魔大战top2.帮派 = value.帮派名字;
                                                仙魔大战top2.杀人数 = 0;
                                                仙魔大战top2.死亡数 = 1;
                                                World.仙魔Top.TryAdd(value.UserName, 仙魔大战top2);
                                            }
                                            if (player.仙魔大战派别 == "仙族")
                                            {
                                                World.仙魔大战正分数 += (int)((double)value.Player_Level * 0.15);
                                            }
                                            else if (player.仙魔大战派别 == "魔族")
                                            {
                                                World.仙魔大战邪分数 += (int)((double)value.Player_Level * 0.15);
                                            }
                                            player.系统提示("当前仙魔大战杀人数[" + player.仙魔大战杀人数 + "]。");
                                            value.系统提示("当前仙魔大战死亡数[" + value.仙魔大战死亡数 + "]。");
                                        }
                                        else
                                        {
                                            player.系统提示("对方已超出仙魔大战对战范围,不能获得分数。");
                                        }
                                    }
                                    else
                                    {
                                        player.系统提示("你已超出仙魔大战对战范围,不能获得分数。");
                                    }
                                }
                                else
                                {
                                    player.系统提示("杀死同IP玩家不能获得分数。");
                                }
                            }
                        }
                        else if (player.人物坐标_地图 == 801)
                        {
                            if (World.势力战进程 == 3)
                            {
                                if (player.UserNip != value.UserNip || World.同IP势力战不计分 == 0)
                                {
                                    if (player.检查玩家是否在势力战区域内(player))
                                    {
                                        if (player.检查玩家是否在势力战区域内(value))
                                        {
                                            value.势力战死亡数++;
                                            player.势力战杀人数++;
                                            int num7 = player.Player_Level - value.Player_Level + 20;
                                            if (num7 < 10)
                                            {
                                                num7 = 10;
                                            }
                                            if (player.Player_Zx == 1)
                                            {
                                                World.势力战正分数 += num7;
                                            }
                                            else
                                            {
                                                World.势力战邪分数 += num7;
                                            }
                                            if (World.EventTop.TryGetValue(player.UserName, out var value6))
                                            {
                                                EventTopClass eventTopClass = value6;
                                                eventTopClass.杀人数++;
                                                eventTopClass = value6;
                                                eventTopClass.玩家杀人分数 += num7;
                                            }
                                            else
                                            {
                                                value6 = new EventTopClass();
                                                value6.人物名 = player.UserName;
                                                value6.等级 = player.Player_Level;
                                                value6.职业 = player.Player_Job;
                                                value6.势力 = player.Player_Zx;
                                                value6.帮派 = player.帮派名字;
                                                value6.杀人数 = 1;
                                                value6.死亡数 = 0;
                                                World.EventTop.TryAdd(player.UserName, value6);
                                            }
                                            if (World.EventTop.TryGetValue(value.UserName, out var value7))
                                            {
                                                EventTopClass eventTopClass2 = value7;
                                                eventTopClass2.死亡数++;
                                            }
                                            else
                                            {
                                                EventTopClass eventTopClass3 = new EventTopClass();
                                                eventTopClass3.人物名 = value.UserName;
                                                eventTopClass3.等级 = value.Player_Level;
                                                eventTopClass3.职业 = value.Player_Job;
                                                eventTopClass3.势力 = value.Player_Zx;
                                                eventTopClass3.帮派 = value.帮派名字;
                                                eventTopClass3.杀人数 = 0;
                                                eventTopClass3.死亡数 = 1;
                                                World.EventTop.TryAdd(value.UserName, eventTopClass3);
                                            }
                                            player.系统提示("当前势力战杀人数[" + player.势力战杀人数 + "]。");
                                            value.系统提示("当前势力战死亡数[" + value.势力战死亡数 + "]。");
                                        }
                                        else
                                        {
                                            player.系统提示("对方已超出势力战对战范围,不能获得分数。");
                                        }
                                    }
                                    else
                                    {
                                        player.系统提示("你已超出势力战对战范围,不能获得分数。");
                                    }
                                }
                                else
                                {
                                    player.系统提示("杀死同IP玩家不能获得分数。");
                                }
                            }
                        }
                        else if (player.人物坐标_地图 == 8001)
                        {
                            player.武林杀人数++;
                            if (value.武林杀人数 >= 3)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战终结了：" + value.UserName + ",的" + value.武林杀人数 + "连杀！");
                            }
                            else if (player.武林杀人数 == 1)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成单杀!");
                            }
                            else if (player.武林杀人数 == 2)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成双杀!");
                            }
                            else if (player.武林杀人数 == 3)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成三杀!");
                            }
                            else if (player.武林杀人数 == 4)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成四杀!");
                            }
                            else if (player.武林杀人数 == 5)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成五杀!");
                            }
                            else if (player.武林杀人数 == 6)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成六杀!");
                            }
                            else if (player.武林杀人数 >= 7)
                            {
                                World.全局提示("武林提示", 10, "英勇武士：" + player.UserName + "在武林血战击败了：" + value.UserName + ",完成超神!");
                            }
                        }
                        else if (player.人物坐标_地图 == 2301 && World.Eve90进程 == 3)
                        {
                            if (player.检查玩家是否在对练区内(player))
                            {
                                if (value.检查玩家是否在对练区内(value))
                                {
                                    player.PVP分数++;
                                }
                                else
                                {
                                    value.系统提示("超出对战范围,不能获得分数。");
                                }
                            }
                            else
                            {
                                player.系统提示("超出对战范围,不能获得分数。");
                            }
                        }
                        else if (player.人物坐标_地图 == 7301 && World.新门战进程 == 2)
                        {
                            if (player.检查玩家是否在帮战区内(player) && player.检查玩家是否在帮战区内(value))
                            {
                                foreach (帮战Class value10 in World.帮战list.Values)
                                {
                                    if (value10.申请帮派ID == player.帮派Id)
                                    {
                                        value10.当前分数++;
                                    }
                                }
                            }
                            else
                            {
                                value.系统提示("超出对战范围,不能获得分数。");
                            }
                        }
                        else
                        {
                            if (player.人物坐标_地图 != 801 && player.人物坐标_地图 != 42001 && player.人物坐标_地图 != 41001 && player.人物坐标_地图 != 7301)
                            {
                                if (value.人物善恶 < 0)
                                {
                                    value.人物善恶 += 250;
                                    if (player.人物善恶 < 0 || player.Player_Zx != value.Player_Zx || player.帮派名字 != value.帮派名字)
                                    {
                                        player.人物善恶 += 100;
                                    }
                                }
                                else if (player.人物善恶 < 0)
                                {
                                    if (player.Player_Zx != value.Player_Zx || player.帮派名字 != value.帮派名字)
                                    {
                                        player.人物善恶 += 100;
                                    }
                                    else
                                    {
                                        player.人物善恶 -= 500;
                                    }
                                }
                                else if (player.Player_Zx == value.Player_Zx || player.帮派名字 == value.帮派名字)
                                {
                                    player.人物善恶 -= 500;
                                }
                                else
                                {
                                    player.人物善恶 += 100;
                                }
                                string text = player.UserName + "在" + World.服务器ID + "线" + 坐标Class.getname(player.人物坐标_地图) + "[" + (int)player.人物坐标_X + "," + (int)player.人物坐标_Y + "]击杀：" + value.UserName + "不服来战！";
                                World.conn.发送("PK提示|" + 22 + "|" + text);
                                if (World.是否开启武勋系统 == 1)
                                {
                                    player.武勋系统(player, value);
                                }
                            }
                            if (player.人物坐标_地图 == 101 && World.比武泡点进程 == 1)
                            {
                                if (player.检查玩家是否在挂机双倍区域(player))
                                {
                                    if (value.检查玩家是否在挂机双倍区域(value))
                                    {
                                        if (World.比武泡点Top.TryGetValue(player.UserName, out var value8))
                                        {
                                            value8.杀人数++;
                                            player.比武追加经验值 = (double)value8.杀人数 * 0.02;
                                            player.系统提示("你当前比武泡点分数为[" + value8.杀人数 + "],当前追加[" + player.比武追加经验值 + "]倍,请继续杀人.", 2, "系统提示");
                                            RxjhClass.Set个人荣誉数据(4, value8.人物名, value8.职业, value8.等级, value8.势力, value8.帮派, string.Empty, 1, World.分区编号);
                                        }
                                        else
                                        {
                                            value8 = new 比武泡点TopClass();
                                            value8.人物名 = player.UserName;
                                            value8.等级 = player.Player_Level;
                                            value8.势力 = player.Player_Zx;
                                            value8.帮派 = player.帮派名字;
                                            value8.职业 = player.Player_Job;
                                            value8.杀人数 = 1;
                                            player.系统提示("你当前比武泡点分数为[" + value8.杀人数 + "]请继续杀人。", 2, "系统提示");
                                            RxjhClass.Set个人荣誉数据(4, value8.人物名, value8.职业, value8.等级, value8.势力, value8.帮派, string.Empty, 1, World.分区编号);
                                            World.比武泡点Top.TryAdd(player.UserName, value8);
                                        }
                                    }
                                    else
                                    {
                                        value.系统提示("超出比武泡点范围或没武勋,不能获得分数.");
                                    }
                                }
                                else
                                {
                                    player.系统提示("超出比武泡点范围或没武勋,不能获得分数。");
                                }
                            }
                            if (player.人物坐标_地图 == 2351 && World.大乱斗进程 == 1)
                            {
                                if (World.大乱斗Top.TryGetValue(player.UserName, out var value9))
                                {
                                    value9.杀人数++;
                                    player.系统提示("你当前大乱斗分数为[" + value9.杀人数 + "],请继续杀人.", 2, "系统提示");
                                }
                                else
                                {
                                    value9 = new 大乱斗TopClass();
                                    value9.人物名 = player.UserName;
                                    value9.等级 = player.Player_Level;
                                    value9.势力 = player.Player_Zx;
                                    value9.帮派 = player.帮派名字;
                                    value9.职业 = player.Player_Job;
                                    value9.杀人数 = 1;
                                    player.系统提示("你当前大乱斗分数为[" + value9.杀人数 + "]请继续杀人。", 2, "系统提示");
                                    World.大乱斗Top.TryAdd(player.UserName, value9);
                                }
                            }
                            value.更新武功和状态();
                            player.更新武功和状态();
                        }
                    }
                }
                else if ((uint)(攻击类型 - 112) <= 1u || (uint)(攻击类型 - 126) <= 8u || 攻击类型 == 136)
                {
                    player.物理攻击人物ID = 人物ID;
                    player.自动攻击Stop();
                    player.自动攻击Start(800.0);
                }
                player.更新HP_MP_SP();
                value.更新HP_MP_SP();
            }
            catch (Exception ex)
            {
                player.攻击列表.Clear();
                player.报错次数阀值++;
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "攻击确认人物[" + player.报错次数阀值 + "]-[" + player.UserName + "]" + ex.Message);
                }
            }
        }

        public static void 攻击确认怪物(Players player, 攻击类 攻击类2, int 全服ID, int 人物ID, int 攻击模式)
        {
            int num = 0;
            try
            {
                if (player.人物善恶 < 0)
                {
                    player.人物善恶 += RNG.Next(50, 200);
                }
                if (World.打怪掉耐久度 == 1)
                {
                    int num2 = 0;
                    if (RNG.Next(1, 1000) <= 10)
                    {
                        num2 = 1;
                        if (player.装备栏已穿装备[0].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[0].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[0].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[0].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[1].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[1].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[1].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[1].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[2].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[2].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[2].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[2].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[3].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[3].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[3].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[3].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[4].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[4].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[4].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[4].FLD_FJ_NJ = 0;
                            }
                        }
                        if (player.装备栏已穿装备[5].FLD_FJ_NJ > 0)
                        {
                            player.装备栏已穿装备[5].FLD_FJ_NJ -= num2;
                            if (player.装备栏已穿装备[5].FLD_FJ_NJ < 0)
                            {
                                player.装备栏已穿装备[5].FLD_FJ_NJ = 0;
                            }
                        }
                    }
                }
                num = 1;
                if (攻击类2.武功ID == 0)
                {
                    num = 2;
                    if (MapClass.GetnpcTemplate(player.人物坐标_地图).TryGetValue(攻击类2.人物ID, out var value))
                    {
                        num = 3;
                        if (全服ID == player.人物全服ID)
                        {
                            if (value.Rxjh_HP > 攻击类2.攻击力)
                            {
                                value.Play_Add(player, 攻击类2.攻击力);
                            }
                            else
                            {
                                value.Play_Add(player, 攻击类2.攻击力);
                            }
                        }
                        num = 4;
                        if (value.Rxjh_HP > 攻击类2.攻击力)
                        {
                            value.Rxjh_HP -= 攻击类2.攻击力;
                            if (value.ContainsKeyInAbnormalState(44))
                            {
                                value.阎王爆累计伤害 += 攻击类2.攻击力;
                            }
                        }
                        else
                        {
                            value.Rxjh_HP = 0;
                            if (value.ContainsKeyInAbnormalState(44))
                            {
                                value.阎王爆累计伤害 = 0;
                            }
                        }
                        num = 6;
                        if (value.Rxjh_HP <= 0 && !value.NPC死亡)
                        {
                            num = 7;
                            player.吸魂(人物ID);
                            value.势力战打怪得分(player, 攻击类2.攻击力);
                            value.发送死亡数据(player.人物全服ID);
                            player.攻击列表.Clear();
                        }
                        else
                        {
                            num = 8;
                            if (value.自动攻击 != null && !value.自动攻击.Enabled)
                            {
                                value.自动移动.Enabled = false;
                                value.自动攻击.Enabled = true;
                            }
                            num = 9;
                            if (攻击模式 < 150)
                            {
                                player.物理攻击人物ID = 人物ID;
                                player.自动攻击Stop();
                                num = 10;
                                player.自动攻击Start(800.0);
                            }
                            else
                            {
                                player.攻击列表.Clear();
                            }
                        }
                        num = 11;
                        player.攻击列表.Clear();
                    }
                    player.更新HP_MP_SP();
                    return;
                }
                num = 12;
                if (!World.TBL_KONGFU.TryGetValue(攻击类2.武功ID, out var value2))
                {
                    return;
                }
                num = 13;
                player.自动攻击Stop();
                num = 14;
                if ((player.Player_Job != 6 || value2.FLD_PID != 801303) && MapClass.GetnpcTemplate(player.人物坐标_地图).TryGetValue(攻击类2.人物ID, out var value3))
                {
                    num = 15;
                    int num3 = value2.FLD_TYPE;
                    if (value2.FLD_INDEX == 16 && value2.FLD_武功类型 == 2 && value2.FLD_攻击数量 == 1)
                    {
                        num3 = ((player.夫妻武功攻击数量 > 1) ? 4 : 0);
                    }
                    if (num3 != 4 && !player.触发流星漫天 && !player.触发杀星义气虎 && !player.触发杀星义气杀)
                    {
                        num = 16;
                        if (全服ID == player.人物全服ID)
                        {
                            if (value3.Rxjh_HP > 攻击类2.攻击力)
                            {
                                value3.Play_Add(player, 攻击类2.攻击力);
                            }
                            else
                            {
                                value3.Play_Add(player, 攻击类2.攻击力);
                            }
                        }
                        num = 17;
                        if (value3.Rxjh_HP > 攻击类2.攻击力)
                        {
                            value3.Rxjh_HP -= 攻击类2.攻击力;
                            if (value3.ContainsKeyInAbnormalState(44))
                            {
                                value3.阎王爆累计伤害 += 攻击类2.攻击力;
                            }
                        }
                        else
                        {
                            value3.Rxjh_HP = 0;
                            if (value3.ContainsKeyInAbnormalState(44))
                            {
                                value3.阎王爆累计伤害 = 0;
                            }
                        }
                        num = 19;
                        if (value3.Rxjh_HP <= 0 && !value3.NPC死亡)
                        {
                            num = 20;
                            player.吸魂(人物ID);
                            value3.势力战打怪得分(player, 攻击类2.攻击力);
                            player.计算绝命技加成(value2, value3);
                            value3.发送死亡数据(player.人物全服ID);
                            player.攻击列表.Clear();
                        }
                        else
                        {
                            num = 21;
                            if (value3.自动攻击 != null && !value3.自动攻击.Enabled)
                            {
                                value3.自动移动.Enabled = false;
                                value3.自动攻击.Enabled = true;
                            }
                            num = 22;
                            if (player.武功连击记数器 > 0)
                            {
                                player.自动攻击Stop();
                                num = 23;
                                攻击系统.魔法攻击(player, 攻击类2.武功ID, 人物ID);
                            }
                            else
                            {
                                player.攻击列表.Clear();
                            }
                        }
                    }
                    else
                    {
                        num = 24;
                        player.触发流星漫天 = false;
                        player.触发绝影射魂 = false;
                        player.触发杀星义气杀 = false;
                        player.触发杀星义气虎 = false;
                        num = 25;
                        if (value3.Rxjh_HP > 攻击类2.攻击力)
                        {
                            if (value3.ContainsKeyInAbnormalState(44))
                            {
                                value3.阎王爆累计伤害 += 攻击类2.攻击力;
                            }
                            value3.Play_Add(player, 攻击类2.攻击力);
                            value3.Rxjh_HP -= 攻击类2.攻击力;
                        }
                        else
                        {
                            if (value3.ContainsKeyInAbnormalState(44))
                            {
                                value3.阎王爆累计伤害 = 0;
                            }
                            value3.Play_Add(player, 攻击类2.攻击力);
                            value3.Rxjh_HP = 0;
                        }
                        num = 27;
                        if (value3.Rxjh_HP <= 0 && !value3.NPC死亡)
                        {
                            num = 28;
                            player.吸魂(人物ID);
                            value3.势力战打怪得分(player, 攻击类2.攻击力);
                            value3.发送死亡数据(player.人物全服ID);
                            player.武功连击记数器 = 0;
                            num = 29;
                            foreach (群攻击类 item in 攻击类2.群攻)
                            {
                                if (item.人物ID == value3.FLD_INDEX)
                                {
                                    攻击类2.群攻.Remove(item);
                                    break;
                                }
                            }
                        }
                        else
                        {
                            num = 30;
                            if (value3.自动攻击 != null && !value3.自动攻击.Enabled)
                            {
                                value3.自动移动.Enabled = false;
                                value3.自动攻击.Enabled = true;
                            }
                            num = 31;
                            if (player.武功连击记数器 > 0)
                            {
                                player.自动攻击Stop();
                                魔法攻击(player, 攻击类2.武功ID, 人物ID);
                            }
                            else
                            {
                                num = 32;
                                player.攻击列表.Clear();
                            }
                        }
                        foreach (群攻击类 item2 in 攻击类2.群攻)
                        {
                            num = 33;
                            if (!player.NpcList.TryGetValue(item2.人物ID, out var value4) || value4.FLD_INDEX == value3.FLD_INDEX)
                            {
                                continue;
                            }
                            num = 34;
                            if (value4.Rxjh_HP > item2.攻击力)
                            {
                                if (value4.ContainsKeyInAbnormalState(44))
                                {
                                    value4.阎王爆累计伤害 += 攻击类2.攻击力;
                                }
                                value4.Play_Add(player, 攻击类2.攻击力);
                                value4.Rxjh_HP -= item2.攻击力;
                            }
                            else
                            {
                                if (value4.ContainsKeyInAbnormalState(44))
                                {
                                    value4.阎王爆累计伤害 = 0;
                                }
                                value4.Play_Add(player, 攻击类2.攻击力);
                                value4.Rxjh_HP = 0;
                            }
                            num = 36;
                            if (value4.Rxjh_HP <= 0 && !value4.NPC死亡)
                            {
                                num = 37;
                                player.吸魂(人物ID);
                                value4.势力战打怪得分(player, 攻击类2.攻击力);
                                value4.发送死亡数据(player.人物全服ID);
                                player.攻击列表.Clear();
                                continue;
                            }
                            if (value4.自动攻击 != null && !value4.自动攻击.Enabled)
                            {
                                num = 38;
                                value4.自动移动.Enabled = false;
                                value4.自动攻击.Enabled = true;
                            }
                            if (player.武功连击记数器 > 0)
                            {
                                num = 39;
                                player.自动攻击Stop();
                                num = 40;
                                攻击系统.魔法攻击(player, 攻击类2.武功ID, 人物ID);
                            }
                            else
                            {
                                player.攻击列表.Clear();
                            }
                        }
                    }
                    player.攻击列表.Clear();
                }
                player.更新HP_MP_SP();
            }
            catch (Exception ex)
            {
                player.报错次数阀值++;
                if (World.是否开启票红字 == 1)
                {
                    Form1.WriteLine(1, "攻击怪物确认[" + num + "]出错:" + player.UserName + "/" + player.报错次数阀值 + "/" + ex.StackTrace);
                }
            }
        }

        public static void 使用辅助技能(Players player, 武功类 value3, Players value4, int 武功ID, int 人物ID)
        {
            if (player.人物_MP < value3.FLD_MP)
            {
                player.魔法不足提示();
                player.发送激活技能数据(value3.FLD_PID, 2);
                return;
            }
            switch (武功ID)
            {
                case 501201:
                    {
                        player.魔法使用(value3.FLD_MP);
                        double num2 = (110.0 + player.医_妙手回春) * (1.0 + player.医_九天真气 * 10.0);
                        if ((double)RNG.Next(1, 100) <= player.医_升天二气功_万物回春)
                        {
                            num2 *= 2.0;
                            player.显示大字(player.人物全服ID, 150);
                        }
                        value4.加血((int)num2);
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        value4.更新HP_MP_SP();
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501202:
                    {
                        player.魔法使用(value3.FLD_MP);
                        double num25 = (140.0 + player.医_妙手回春) * (1.0 + player.医_九天真气 * 10.0);
                        if ((double)RNG.Next(1, 100) <= player.医_升天二气功_万物回春)
                        {
                            num25 *= 2.0;
                            player.显示大字(player.人物全服ID, 150);
                        }
                        value4.加血((int)num25);
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        value4.更新HP_MP_SP();
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501203:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        player.魔法使用(value3.FLD_MP);
                        double num16 = (160.0 + player.医_妙手回春) * (1.0 + player.医_九天真气 * 10.0);
                        if ((double)RNG.Next(1, 100) <= player.医_升天二气功_万物回春)
                        {
                            num16 *= 2.0;
                            player.显示大字(player.人物全服ID, 150);
                        }
                        double num17 = 0.0;
                        double num18 = 0.0;
                        double num19 = 0.0;
                        double num20 = 0.0;
                        if (player.医_狂意护体 > 0.0)
                        {
                            if (player.医_狂意护体 - player.医_无中生有 > 0.0)
                            {
                                num19 = player.医_狂意护体 - player.医_无中生有;
                            }
                            num17 = num19 * 0.005;
                            num18 = player.医_狂意护体 * 0.5;
                            num20 = player.医_狂意护体;
                        }
                        if (player.组队id != 0)
                        {
                            if (World.W组队.TryGetValue(player.组队id, out var value29))
                            {
                                int 增加血量 = (int)num16;
                                if (value4.人物全服ID == player.人物全服ID)
                                {
                                    foreach (Players value62 in value29.组队列表.Values)
                                    {
                                        if (value62 != null && player.查找范围玩家(World.群体辅助组队范围, value62))
                                        {
                                            if ((double)new Random().Next(1, 100) < num20 && !value62.追加状态列表.ContainsKey(700014))
                                            {
                                                value62.人物_SP += value62.人物最大_SP / 20;
                                                value62.更新HP_MP_SP();
                                            }
                                            if ((double)new Random().Next(1, 100) < num18 && !value62.追加状态列表.ContainsKey(700350))
                                            {
                                                value62.显示大字(value62.人物全服ID, 350);
                                                value62.状态效果(BitConverter.GetBytes(700350), 1, 363000);
                                                追加状态类 value30 = new 追加状态类(value62, 363000, 700350, 0);
                                                value62.狂意护体加成 = num17;
                                                value62.追加状态列表.TryAdd(700350, value30);
                                                value62.addFLD_追加百分比_防御(value62.狂意护体加成);
                                                value62.更新武功和状态();
                                            }
                                        }
                                    }
                                    player.发送医生群疗数据(人物ID, value29, 增加血量, 武功ID, value3);
                                }
                                else
                                {
                                    value4.加血((int)num16);
                                    value4.更新HP_MP_SP();
                                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                                }
                            }
                            else
                            {
                                value4.加血((int)num16);
                                value4.更新HP_MP_SP();
                                发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            }
                        }
                        else
                        {
                            value4.加血((int)num16);
                            if (value4.人物全服ID != player.人物全服ID)
                            {
                                value4.更新HP_MP_SP();
                            }
                            player.更新HP_MP_SP();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        break;
                    }
                case 401301:
                    {
                        组队Class value58;
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                        }
                        else if (World.W组队.TryGetValue(player.组队id, out value58) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value63 in value58.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value63))
                                {
                                    if (value63.GetAddState(401301))
                                    {
                                        value63.追加状态列表[401301].时间结束事件();
                                    }
                                    player.魔法使用(value3.FLD_MP);
                                    追加状态类 value59 = new 追加状态类(value63, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                                    value63.追加状态列表.TryAdd(武功ID, value59);
                                    value63.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                                    value63.FLD_人物_武功攻击力增加百分比 += 0.1;
                                    player.更新HP_MP_SP();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value58, 0, 401301, value3);
                        }
                        else
                        {
                            if (value4.GetAddState(401301))
                            {
                                value4.追加状态列表[401301].时间结束事件();
                            }
                            player.魔法使用(value3.FLD_MP);
                            追加状态类 value60 = new 追加状态类(value4, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                            value4.追加状态列表.TryAdd(武功ID, value60);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                            value4.FLD_人物_武功攻击力增加百分比 += 0.1;
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            player.更新HP_MP_SP();
                        }
                        break;
                    }
                case 401302:
                    {
                        组队Class value34;
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                        }
                        else if (World.W组队.TryGetValue(player.组队id, out value34) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value64 in value34.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value64))
                                {
                                    if (value64.GetAddState(401302))
                                    {
                                        value64.追加状态列表[401302].时间结束事件();
                                    }
                                    player.魔法使用(value3.FLD_MP);
                                    追加状态类 value35 = new 追加状态类(value64, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                                    value64.追加状态列表.TryAdd(武功ID, value35);
                                    value64.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                                    value64.FLD_人物_武功防御力增加百分比 += 0.1;
                                    player.更新HP_MP_SP();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value34, 0, 401302, value3);
                        }
                        else
                        {
                            if (value4.GetAddState(401302))
                            {
                                value4.追加状态列表[401302].时间结束事件();
                            }
                            player.魔法使用(value3.FLD_MP);
                            追加状态类 value36 = new 追加状态类(value4, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                            value4.追加状态列表.TryAdd(武功ID, value36);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                            value4.FLD_人物_武功防御力增加百分比 += 0.1;
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            player.更新HP_MP_SP();
                        }
                        break;
                    }
                case 401303:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        if (value4.GetAddState(401303))
                        {
                            value4.追加状态列表[401303].时间结束事件();
                        }
                        player.魔法使用(value3.FLD_MP);
                        追加状态类 value48 = new 追加状态类(value4, 185000 + (int)player.弓_回流真气, 武功ID, 0);
                        value4.追加状态列表.TryAdd(武功ID, value48);
                        value4.状态效果(BitConverter.GetBytes(武功ID), 1, 185000 + (int)player.弓_回流真气);
                        int player_Job = value4.Player_Job;
                        int num33 = player_Job;
                        if (num33 == 4 && value4.弓_致命绝杀 > 0.0)
                        {
                            value4.弓_致命绝杀 += 10.0;
                        }
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 401201:
                    player.魔法使用(value3.FLD_MP);
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    player.更新HP_MP_SP();
                    break;
                case 401202:
                    {
                        组队Class value49;
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                        }
                        else if (World.W组队.TryGetValue(player.组队id, out value49) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value65 in value49.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value65))
                                {
                                    if (value65.GetAddState(401202))
                                    {
                                        value65.追加状态列表[401202].时间结束事件();
                                    }
                                    player.魔法使用(value3.FLD_MP);
                                    追加状态类 value50 = new 追加状态类(value65, 18000 + (int)player.弓_回流真气, 武功ID, 0);
                                    value65.追加状态列表.TryAdd(武功ID, value50);
                                    value65.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                                    value65.FLD_人物_追加_命中 += 40;
                                    value65.FLD_人物_追加_回避 -= 20;
                                    value65.更新武功和状态();
                                    player.更新HP_MP_SP();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value49, 0, 401202, value3);
                        }
                        else
                        {
                            if (value4.GetAddState(401202))
                            {
                                value4.追加状态列表[401202].时间结束事件();
                            }
                            player.魔法使用(value3.FLD_MP);
                            追加状态类 value51 = new 追加状态类(value4, 18000 + (int)player.弓_回流真气, 武功ID, 0);
                            value4.追加状态列表.TryAdd(武功ID, value51);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                            value4.FLD_人物_追加_命中 += 40;
                            value4.FLD_人物_追加_回避 -= 20;
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            player.更新HP_MP_SP();
                        }
                        break;
                    }
                case 401203:
                    {
                        组队Class value13;
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                        }
                        else if (World.W组队.TryGetValue(player.组队id, out value13) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value66 in value13.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value66))
                                {
                                    if (value66.GetAddState(401203))
                                    {
                                        value66.追加状态列表[401203].时间结束事件();
                                    }
                                    player.魔法使用(value3.FLD_MP);
                                    追加状态类 value14 = new 追加状态类(value66, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                                    value66.追加状态列表.TryAdd(武功ID, value14);
                                    value66.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                                    value66.FLD_人物_追加_命中 -= 20;
                                    value66.FLD_人物_追加_回避 += 40;
                                    value66.更新武功和状态();
                                    player.更新HP_MP_SP();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value13, 0, 401203, value3);
                        }
                        else
                        {
                            if (value4.GetAddState(401203))
                            {
                                value4.追加状态列表[401203].时间结束事件();
                            }
                            player.魔法使用(value3.FLD_MP);
                            追加状态类 value15 = new 追加状态类(value4, 180000 + (int)player.弓_回流真气, 武功ID, 0);
                            value4.追加状态列表.TryAdd(武功ID, value15);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, 180000 + (int)player.弓_回流真气);
                            value4.FLD_人物_追加_命中 -= 20;
                            value4.FLD_人物_追加_回避 += 40;
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            player.更新HP_MP_SP();
                        }
                        break;
                    }
                case 501302:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        if (value4.追加状态列表 != null)
                        {
                            if (value4.GetAddState(501502))
                            {
                                value4.追加状态列表[501502].时间结束事件();
                            }
                            if (value4.GetAddState(501302))
                            {
                                value4.追加状态列表[501302].时间结束事件();
                            }
                            if (value4.GetAddState(501303))
                            {
                                value4.追加状态列表[501303].时间结束事件();
                            }
                        }
                        player.魔法使用(value3.FLD_MP);
                        double num21 = 180000.0 + player.医_吸星大法;
                        double num22 = 0.05;
                        if (player.医_九天真气 > 0.0)
                        {
                            num22 += player.医_九天真气;
                        }
                        追加状态类 value31 = new 追加状态类(value4, (int)num21, 武功ID, 0, num22);
                        value4.追加状态列表.TryAdd(武功ID, value31);
                        value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num21);
                        value4.addFLD_追加百分比_防御(num22);
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        player.更新HP_MP_SP();
                        value4.更新武功和状态();
                        break;
                    }
                case 501303:
                case 501502:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        double num14 = 600000.0 + player.医_吸星大法;
                        double num15 = 0.1;
                        if (player.医_九天真气 > 0.0)
                        {
                            num15 += player.医_九天真气;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value25) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value67 in value25.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value67))
                                {
                                    if (value67.GetAddState(501502))
                                    {
                                        value67.追加状态列表[501502].时间结束事件();
                                    }
                                    if (value67.GetAddState(501302))
                                    {
                                        value67.追加状态列表[501302].时间结束事件();
                                    }
                                    if (value67.GetAddState(501303))
                                    {
                                        value67.追加状态列表[501303].时间结束事件();
                                    }
                                    追加状态类 value26 = new 追加状态类(value67, (int)num14, 武功ID, 0, num15);
                                    value67.追加状态列表.TryAdd(武功ID, value26);
                                    value67.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num14);
                                    value67.addFLD_追加百分比_防御(num15);
                                    value67.更新武功和状态();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value25, 0, 武功ID, value3);
                        }
                        else
                        {
                            if (value4.追加状态列表 != null)
                            {
                                if (value4.GetAddState(501502))
                                {
                                    value4.追加状态列表[501502].时间结束事件();
                                }
                                if (value4.GetAddState(501302))
                                {
                                    value4.追加状态列表[501302].时间结束事件();
                                }
                                if (value4.GetAddState(501303))
                                {
                                    value4.追加状态列表[501303].时间结束事件();
                                }
                            }
                            追加状态类 value27 = new 追加状态类(value4, (int)num14, 武功ID, 0, num15);
                            value4.追加状态列表.TryAdd(武功ID, value27);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num14);
                            value4.addFLD_追加百分比_防御(num15);
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501301:
                case 501501:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        double num26 = 600000.0 + player.医_吸星大法;
                        double num27 = 0.1;
                        if (player.医_九天真气 > 0.0)
                        {
                            num27 += player.医_九天真气;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value43) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value68 in value43.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value68))
                                {
                                    if (value68.GetAddState(501501))
                                    {
                                        value68.追加状态列表[501501].时间结束事件();
                                    }
                                    if (value68.GetAddState(501301))
                                    {
                                        value68.追加状态列表[501301].时间结束事件();
                                    }
                                    追加状态类 value44 = new 追加状态类(value68, (int)num26, 武功ID, 0, num27);
                                    value68.追加状态列表.TryAdd(武功ID, value44);
                                    value68.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num26);
                                    value68.addFLD_追加百分比_攻击(num27);
                                    value68.更新武功和状态();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value43, 0, 武功ID, value3);
                        }
                        else
                        {
                            if (value4.追加状态列表 != null)
                            {
                                if (value4.GetAddState(501501))
                                {
                                    value4.追加状态列表[501501].时间结束事件();
                                }
                                if (value4.GetAddState(501301))
                                {
                                    value4.追加状态列表[501301].时间结束事件();
                                }
                            }
                            追加状态类 value45 = new 追加状态类(value4, (int)num26, 武功ID, 0, num27);
                            value4.追加状态列表.TryAdd(武功ID, value45);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num26);
                            value4.addFLD_追加百分比_攻击(num27);
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 601201:
                    {
                        if (value4.GetAddState(601201))
                        {
                            value4.追加状态列表[601201].时间结束事件();
                        }
                        追加状态类 value56 = new 追加状态类(value4, 1800000, 武功ID, 0);
                        value4.追加状态列表.TryAdd(武功ID, value56);
                        value4.状态效果(BitConverter.GetBytes(武功ID), 1, 1800000);
                        player.FLD_夫妻辅助_追加_防具_属性 = 1;
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        value4.计算人物装备数据();
                        value4.更新武功和状态();
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501403:
                case 501601:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        double num9 = 600000.0 + player.医_吸星大法;
                        double num10 = 0.1;
                        if (player.医_九天真气 > 0.0)
                        {
                            num10 += player.医_九天真气;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value16) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value69 in value16.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value69))
                                {
                                    if (value69.GetAddState(501601))
                                    {
                                        value69.追加状态列表[501601].时间结束事件();
                                    }
                                    if (value69.GetAddState(501403))
                                    {
                                        value69.追加状态列表[501403].时间结束事件();
                                    }
                                    追加状态类 value17 = new 追加状态类(value69, (int)num9, 武功ID, 0, num10);
                                    value69.追加状态列表.TryAdd(武功ID, value17);
                                    value69.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num9);
                                    value69.FLD_追加百分比_HP上限 += num10;
                                    value69.更新HP_MP_SP();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value16, 0, 武功ID, value3);
                        }
                        else
                        {
                            if (value4.追加状态列表 != null)
                            {
                                if (value4.GetAddState(501601))
                                {
                                    value4.追加状态列表[501601].时间结束事件();
                                }
                                if (value4.GetAddState(501403))
                                {
                                    value4.追加状态列表[501403].时间结束事件();
                                }
                            }
                            追加状态类 value18 = new 追加状态类(value4, (int)num9, 武功ID, 0, num10);
                            value4.追加状态列表.TryAdd(武功ID, value18);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num9);
                            value4.FLD_追加百分比_HP上限 += num10;
                            value4.更新HP_MP_SP();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501401:
                case 501602:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        double num11 = 600000.0 + player.医_吸星大法;
                        double num12 = 0.1;
                        if (player.医_九天真气 > 0.0)
                        {
                            num12 += player.医_九天真气;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value19) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value70 in value19.组队列表.Values)
                            {
                                if (player.查找范围玩家(World.群体辅助组队范围, value70))
                                {
                                    if (value70.GetAddState(501602))
                                    {
                                        value70.追加状态列表[501602].时间结束事件();
                                    }
                                    if (value70.GetAddState(501401))
                                    {
                                        value70.追加状态列表[501401].时间结束事件();
                                    }
                                    追加状态类 value20 = new 追加状态类(value70, (int)num11, 武功ID, 0, num12);
                                    value70.追加状态列表.TryAdd(武功ID, value20);
                                    value70.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num11);
                                    value70.FLD_追加百分比_命中 += num12;
                                    value70.更新武功和状态();
                                }
                            }
                            player.发送医生群体辅助数据(人物ID, value19, 0, 武功ID, value3);
                        }
                        else
                        {
                            if (value4.追加状态列表 != null)
                            {
                                if (value4.GetAddState(501602))
                                {
                                    value4.追加状态列表[501602].时间结束事件();
                                }
                                if (value4.GetAddState(501401))
                                {
                                    value4.追加状态列表[501401].时间结束事件();
                                }
                            }
                            追加状态类 value21 = new 追加状态类(value4, (int)num11, 武功ID, 0, num12);
                            value4.追加状态列表.TryAdd(武功ID, value21);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num11);
                            value4.FLD_追加百分比_命中 += num12;
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501402:
                case 501603:
                    {
                        if (player.Player_WuXun < -25000)
                        {
                            player.系统提示("你武勋为负数无法使用.....");
                            break;
                        }
                        double num34 = 600000.0 + player.医_吸星大法;
                        double num35 = 0.1;
                        if (player.医_九天真气 > 0.0)
                        {
                            num35 += player.医_九天真气;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value52) && 人物ID == player.人物全服ID)
                        {
                            foreach (Players value71 in value52.组队列表.Values)
                            {
                                if (!player.查找范围玩家(World.群体辅助组队范围, value71))
                                {
                                    continue;
                                }
                                if (value71.追加状态列表 != null)
                                {
                                    if (value71.GetAddState(501603))
                                    {
                                        value71.追加状态列表[501603].时间结束事件();
                                    }
                                    if (value71.GetAddState(501402))
                                    {
                                        value71.追加状态列表[501402].时间结束事件();
                                    }
                                }
                                追加状态类 value53 = new 追加状态类(value71, (int)num34, 武功ID, 0, num35);
                                value71.追加状态列表.TryAdd(武功ID, value53);
                                value71.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num34);
                                value71.FLD_人物_追加百分比_回避 += num35;
                                value71.更新武功和状态();
                            }
                            player.发送医生群体辅助数据(人物ID, value52, 0, 武功ID, value3);
                        }
                        else
                        {
                            if (value4.追加状态列表 != null)
                            {
                                if (value4.GetAddState(501603))
                                {
                                    value4.追加状态列表[501603].时间结束事件();
                                }
                                if (value4.GetAddState(501402))
                                {
                                    value4.追加状态列表[501402].时间结束事件();
                                }
                            }
                            追加状态类 value54 = new 追加状态类(value4, (int)num34, 武功ID, 0, num35);
                            value4.追加状态列表.TryAdd(武功ID, value54);
                            value4.状态效果(BitConverter.GetBytes(武功ID), 1, (int)num34);
                            value4.FLD_人物_追加百分比_回避 += num35;
                            value4.更新武功和状态();
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 601202:
                    {
                        if (value4.GetAddState(601202))
                        {
                            value4.追加状态列表[601202].时间结束事件();
                        }
                        追加状态类 value9 = new 追加状态类(value4, 1800000, 武功ID, 0);
                        value4.追加状态列表.TryAdd(武功ID, value9);
                        value4.状态效果(BitConverter.GetBytes(武功ID), 1, 1800000);
                        player.FLD_夫妻辅助_追加_武器_属性 = 1;
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        value4.计算人物装备数据();
                        value4.更新武功和状态();
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 3000401:
                    if (player.人物全服ID == 人物ID)
                    {
                        if (player.人物_MP < value3.FLD_MP)
                        {
                            player.魔法不足提示();
                            player.发送激活技能数据(value3.FLD_PID, 2);
                        }
                        else
                        {
                            player.魔法使用(value3.FLD_MP);
                            发送攻击人物数据(player, 人物ID, 武功ID, 1, 126, player.人物_HP, 0, 0);
                            player.更新HP_MP_SP();
                        }
                    }
                    break;
                case 4000101:
                case 4000401:
                case 4000501:
                case 4000601:
                case 4000701:
                case 4000801:
                case 4000901:
                case 4002101:
                case 4002201:
                case 4002301:
                case 4002401:
                case 4002501:
                case 4006101:
                    if (player.人物全服ID == 人物ID)
                    {
                        player.魔法使用(value3.FLD_MP);
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 126, player.人物_HP, 0, 0);
                        if (player.人物_AP < player.人物最大_AP)
                        {
                            player.人物_AP += value3.FLD_AT;
                        }
                        player.更新HP_MP_SP();
                    }
                    break;
                case 6002101:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos1 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos1)
                    {
                        if (!value4.神女异常状态.ContainsKey(35))
                        {
                            double num28 = value4.Player_Level * 2;
                            int num29 = 60000;
                            num29 -= (int)((double)num29 * value4.谭花灵电光朝露);
                            神女异常状态类 value46 = new 神女异常状态类(value4, num29, 34, 0.0, num28);
                            value4.神女异常状态.TryAdd(34, value46);
                            value4.FLD_神女_减少_攻击 = (int)num28;
                            value4.FLD_神女_减少_防御 = (int)num28;
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002102:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos2 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos2)
                    {
                        if (!value4.神女异常状态.ContainsKey(34) || !value4.神女异常状态.ContainsKey(35))
                        {
                            double num5 = value4.Player_Level * 2;
                            double num6 = value4.Player_Level;
                            int num7 = 60000;
                            num7 -= (int)((double)num7 * value4.谭花灵电光朝露);
                            神女异常状态类 value10 = new 神女异常状态类(value4, num7, 35, 0.0, num5);
                            value4.神女异常状态.TryAdd(35, value10);
                            value4.FLD_神女_减少_攻击 = (int)num5;
                            value4.FLD_神女_减少_防御 = (int)num5;
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            if (!player.神女异常状态.ContainsKey(52))
                            {
                                神女异常状态类 value11 = new 神女异常状态类(player, num7, 52, 0.0, num6);
                                player.神女异常状态.TryAdd(52, value11);
                                player.FLD_神女_追加_攻击 = (int)num6;
                                player.FLD_神女_追加_防御 = (int)num6;
                            }
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002103:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos3 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos3)
                    {
                        if (!value4.神女异常状态.ContainsKey(36))
                        {
                            神女异常状态类 value28 = new 神女异常状态类(value4, 60000, 36, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(36, value28);
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002104:
                    player.魔法使用(value3.FLD_MP);
                    if (!value4.神女异常状态.ContainsKey(37))
                    {
                        if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(34))
                        {
                            value4.神女异常状态[34].时间结束事件();
                        }
                        if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(35))
                        {
                            value4.神女异常状态[35].时间结束事件();
                        }
                        神女异常状态类 value8 = new 神女异常状态类(value4, 60000, 37, 0.0, 0.0);
                        value4.神女异常状态.TryAdd(37, value8);
                        value4.更新HP_MP_SP();
                        value4.更新武功和状态();
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    }
                    player.更新HP_MP_SP();
                    break;
                case 6002105:
                    {
                        if (player.组队id != 0 && World.W组队.TryGetValue(player.组队id, out var value40) && value4.人物全服ID == player.人物全服ID)
                        {
                            foreach (Players value72 in value40.组队列表.Values)
                            {
                                if (value72 != null && player.查找范围玩家(World.组队范围距离, value72) && !value72.神女异常状态.ContainsKey(38))
                                {
                                    if (value72.神女异常状态 != null && value72.神女异常状态.ContainsKey(34))
                                    {
                                        value72.神女异常状态[34].时间结束事件();
                                    }
                                    if (value72.神女异常状态 != null && value72.神女异常状态.ContainsKey(35))
                                    {
                                        value72.神女异常状态[35].时间结束事件();
                                    }
                                    神女异常状态类 value41 = new 神女异常状态类(value72, 60000, 38, 0.0, 0.0);
                                    value72.神女异常状态.TryAdd(38, value41);
                                    value72.更新HP_MP_SP();
                                    value72.更新武功和状态();
                                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                                }
                            }
                        }
                        player.魔法使用(value3.FLD_MP);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 6002106:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos4 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos4)
                    {
                        if (!value4.神女异常状态.ContainsKey(39))
                        {
                            神女异常状态类 value57 = new 神女异常状态类(value4, 6000, 39, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(39, value57);
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            value4.超负荷等级 = player.武功新[1, value3.FLD_INDEX].武功_等级;
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    player.更新HP_MP_SP();
                    break;
                case 6002301:
                    player.魔法使用(value3.FLD_MP);
                    if (!value4.神女异常状态.ContainsKey(46))
                    {
                        神女异常状态类 value39 = new 神女异常状态类(value4, 60000, 46, 0.0, 0.0);
                        value4.神女异常状态.TryAdd(46, value39);
                        value4.更新HP_MP_SP();
                        value4.更新武功和状态();
                        player.更新HP_MP_SP();
                        player.更新武功和状态();
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002302:
                    {
                        player.魔法使用(value3.FLD_MP);
                        if (player.组队id == 0)
                        {
                            break;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value23) && value4.人物全服ID == player.人物全服ID)
                        {
                            foreach (Players value73 in value23.组队列表.Values)
                            {
                                if (value73 != null && player.查找范围玩家(World.组队范围距离, value73) && !value73.神女异常状态.ContainsKey(46))
                                {
                                    神女异常状态类 value24 = new 神女异常状态类(value73, 60000, 46, 0.0, 0.0);
                                    value73.神女异常状态.TryAdd(46, value24);
                                    value73.更新HP_MP_SP();
                                    value73.更新武功和状态();
                                    player.更新HP_MP_SP();
                                    player.更新武功和状态();
                                }
                            }
                        }
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        break;
                    }
                case 6002303:
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos5 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos5)
                    {
                        int num36 = ((player.武功新[1, value3.FLD_INDEX].武功_等级 == 3) ? 7000 : ((player.武功新[1, value3.FLD_INDEX].武功_等级 != 2) ? 3000 : 5000));
                        num36 -= (int)((double)num36 * value4.谭花灵电光朝露);
                        if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(49))
                        {
                            value4.神女异常状态[49].时间结束事件();
                        }
                        if (!value4.神女异常状态.ContainsKey(48))
                        {
                            神女异常状态类 value55 = new 神女异常状态类(value4, num36, 48, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(48, value55);
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    player.魔法使用(value3.FLD_MP);
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002304:
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos6 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos6)
                    {
                        int num4 = ((player.武功新[1, value3.FLD_INDEX].武功_等级 == 3) ? 7000 : ((player.武功新[1, value3.FLD_INDEX].武功_等级 != 2) ? 3000 : 5000));
                        num4 -= (int)((double)num4 * value4.谭花灵电光朝露);
                        if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(48))
                        {
                            value4.神女异常状态[48].时间结束事件();
                        }
                        if (!value4.神女异常状态.ContainsKey(49))
                        {
                            神女异常状态类 value7 = new 神女异常状态类(value4, num4, 49, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(49, value7);
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    player.魔法使用(value3.FLD_MP);
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002305:
                    if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(51))
                    {
                        value4.神女异常状态[51].时间结束事件();
                    }
                    if (!value4.神女异常状态.ContainsKey(50))
                    {
                        int 时间 = ((player.武功新[1, value3.FLD_INDEX].武功_等级 == 3) ? 7000 : ((player.武功新[1, value3.FLD_INDEX].武功_等级 != 2) ? 3000 : 5000));
                        神女异常状态类 value42 = new 神女异常状态类(value4, 时间, 50, 0.0, 0.0);
                        value4.神女异常状态.TryAdd(50, value42);
                        value4.更新HP_MP_SP();
                        value4.更新武功和状态();
                        player.更新HP_MP_SP();
                        player.更新武功和状态();
                    }
                    player.魔法使用(value3.FLD_MP);
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002306:
                    {
                        player.魔法使用(value3.FLD_MP);
                        if (player.组队id == 0)
                        {
                            break;
                        }
                        if (World.W组队.TryGetValue(player.组队id, out var value37) && value4.人物全服ID == player.人物全服ID)
                        {
                            foreach (Players value74 in value37.组队列表.Values)
                            {
                                if (value74 != null && player.查找范围玩家(World.组队范围距离, value74))
                                {
                                    if (value74.神女异常状态 != null && value74.神女异常状态.ContainsKey(50))
                                    {
                                        value74.神女异常状态[50].时间结束事件();
                                    }
                                    if (!value74.神女异常状态.ContainsKey(51))
                                    {
                                        神女异常状态类 value38 = new 神女异常状态类(value74, 3000, 51, 0.0, 0.0);
                                        value74.神女异常状态.TryAdd(51, value38);
                                        value74.更新HP_MP_SP();
                                        value74.更新武功和状态();
                                        player.更新HP_MP_SP();
                                        player.更新武功和状态();
                                    }
                                }
                            }
                        }
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        break;
                    }
                case 6002201:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos7 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos7)
                    {
                        if (value4.神女异常状态 != null && value4.神女异常状态.ContainsKey(41))
                        {
                            value4.神女异常状态[41].时间结束事件();
                        }
                        if (!value4.神女异常状态.ContainsKey(40))
                        {
                            int num37 = ((player.武功新[1, value3.FLD_INDEX].武功_等级 == 3) ? 7000 : ((player.武功新[1, value3.FLD_INDEX].武功_等级 != 2) ? 3000 : 5000));
                            num37 -= (int)((double)num37 * value4.谭花灵电光朝露);
                            神女异常状态类 value61 = new 神女异常状态类(value4, num37, 40, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(40, value61);
                            value4.神女虚弱ID = player.人物全服ID;
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002202:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos8 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos8)
                    {
                        if (!value4.神女异常状态.ContainsKey(41))
                        {
                            int num24 = ((player.武功新[1, value3.FLD_INDEX].武功_等级 == 3) ? 7000 : ((player.武功新[1, value3.FLD_INDEX].武功_等级 != 2) ? 3000 : 5000));
                            num24 -= (int)((double)num24 * value4.谭花灵电光朝露);
                            神女异常状态类 value33 = new 神女异常状态类(value4, num24, 41, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(41, value33);
                            value4.神女虚弱ID = player.人物全服ID;
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002203:
                    player.魔法使用(value3.FLD_MP);
                    if (人物ID == player.人物全服ID)
                    {
                        player.系统提示("只能对别人施放");
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                    {
                        value4.显示大字(value4.人物全服ID, 582);
                        break;
                    }
                    if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                    {
                        value4.显示大字(value4.人物全服ID, 571);
                        break;
                    }
                    //24.0 EVIAS 削弱攻击者的黑花集中
                    double ruos9 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                    if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos9)
                    {
                        if (!player.神女异常状态.ContainsKey(42))
                        {
                            int num3;
                            if (player.武功新[1, value3.FLD_INDEX].武功_等级 == 3)
                            {
                                num3 = 9000;
                                value4.属性封印 = 0.07;
                            }
                            else if (player.武功新[1, value3.FLD_INDEX].武功_等级 == 2)
                            {
                                num3 = 7000;
                                value4.属性封印 = 0.05;
                            }
                            else
                            {
                                num3 = 5000;
                                value4.属性封印 = 0.03;
                            }
                            num3 -= (int)((double)num3 * value4.谭花灵电光朝露);
                            神女异常状态类 value6 = new 神女异常状态类(value4, num3, 42, 0.0, 0.0);
                            player.神女异常状态.TryAdd(42, value6);
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                    }
                    else
                    {
                        player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 6002204:
                    {
                        player.魔法使用(value3.FLD_MP);
                        if (人物ID == player.人物全服ID)
                        {
                            player.系统提示("只能对别人施放");
                            break;
                        }
                        if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                        {
                            value4.显示大字(value4.人物全服ID, 582);
                            break;
                        }
                        if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                        {
                            value4.显示大字(value4.人物全服ID, 571);
                            break;
                        }
                        int num30 = 10000;
                        num30 -= (int)((double)num30 * value4.谭花灵电光朝露);
                        if (!value4.神女异常状态.ContainsKey(43))
                        {
                            神女异常状态类 value47 = new 神女异常状态类(value4, num30, 43, 0.0, 0.0);
                            value4.神女异常状态.TryAdd(43, value47);
                            value4.神女虚弱ID = player.人物全服ID;
                            value4.更新HP_MP_SP();
                            value4.更新武功和状态();
                            float num31 = value4.人物坐标_X;
                            float num32 = value4.人物坐标_Y;
                            Random random = new Random();
                            num31 = random.Next((int)num31 - 5, (int)num31 + 5);
                            num32 = random.Next((int)num32 - 5, (int)num32 + 5);
                            value4.混乱状态移动(num31, num32);
                            player.更新HP_MP_SP();
                            player.更新武功和状态();
                        }
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        break;
                    }
                case 6002205:
                    {
                        player.魔法使用(value3.FLD_MP);
                        if (人物ID == player.人物全服ID)
                        {
                            player.系统提示("只能对别人施放");
                            break;
                        }
                        if ((double)RNG.Next(1, 100) <= value4.神女抗击身法)
                        {
                            value4.显示大字(value4.人物全服ID, 582);
                            break;
                        }
                        if ((double)RNG.Next(1, 100) <= value4.剑百毒不侵)
                        {
                            value4.显示大字(value4.人物全服ID, 571);
                            break;
                        }
                        int num23 = 5000;
                        num23 -= (int)((double)num23 * value4.谭花灵电光朝露);
                        //24.0 EVIAS 削弱攻击者的黑花集中
                        double ruos10 = 反气功系统.计算反气功弱化值(value4, player, 2025);
                        if ((double)RNG.Next(1, 100) <= 60.0 + player.神女黑花集中 - ruos10)
                        {
                            if (!value4.神女异常状态.ContainsKey(44))
                            {
                                神女异常状态类 value32 = new 神女异常状态类(value4, num23, 44, 0.0, 0.0);
                                value4.神女异常状态.TryAdd(44, value32);
                                value4.神女虚弱ID = player.人物全服ID;
                                value4.阎王爆累计伤害 = 0.0;
                                value4.更新HP_MP_SP();
                                value4.更新武功和状态();
                                发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                            }
                        }
                        else
                        {
                            player.系统提示("对[" + value4.UserName + "]使用异常状态失败");
                        }
                        player.更新HP_MP_SP();
                        break;
                    }
                case 501701:
                    player.魔法使用(value3.FLD_MP);
                    if (value4.检查烈日炎炎状态())
                    {
                        value4.追加状态列表[1008001169].时间结束事件();
                    }
                    if ((double)RNG.Next(1, 100) < player.升天四式_望梅添花)
                    {
                        if (value4.追加状态列表.ContainsKey(1008001174))
                        {
                            value4.追加状态列表[1008001174].时间结束事件();
                        }
                        double num13 = 5000.0;
                        追加状态类 value22 = new 追加状态类(value4, (int)num13, 1008001174, 0);
                        value4.追加状态列表.TryAdd(1008001174, value22);
                        value4.状态效果(BitConverter.GetBytes(1008001174), 1, (int)num13);
                        value4.人物追加最大_HP += 1000;
                        value4.更新HP_MP_SP();
                        value4.显示大字(人物ID, 354);
                        player.显示大字(人物ID, 354);
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
                case 901501:
                    {
                        player.魔法使用(value3.FLD_MP);
                        if (value4.检查毒蛇出洞状态())
                        {
                            value4.追加状态列表[1008001170].时间结束事件();
                        }
                        if (value4.GetAddState(901501))
                        {
                            value4.追加状态列表[901501].时间结束事件();
                        }
                        int num8 = 3000;
                        追加状态类 value12 = new 追加状态类(value4, num8, 901501, 0);
                        value4.追加状态列表.TryAdd(901501, value12);
                        value4.状态效果(BitConverter.GetBytes(901501), 1, num8);
                        value4.addFLD_追加百分比_防御(0.1);
                        发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                        player.更新HP_MP_SP();
                        break;
                    }
                case 6001201:
                    player.魔法使用(value3.FLD_MP);
                    if (value4.检查烈日炎炎状态())
                    {
                        value4.追加状态列表[1008001169].时间结束事件();
                    }
                    if ((double)RNG.Next(1, 100) < player.升天四式_望梅添花)
                    {
                        if (value4.追加状态列表.ContainsKey(1008001174))
                        {
                            value4.追加状态列表[1008001174].时间结束事件();
                        }
                        double num = 5000.0;
                        追加状态类 value5 = new 追加状态类(value4, (int)num, 1008001174, 0);
                        value4.追加状态列表.TryAdd(1008001174, value5);
                        value4.状态效果(BitConverter.GetBytes(1008001174), 1, (int)num);
                        value4.人物追加最大_HP += 1000;
                        value4.更新HP_MP_SP();
                        value4.显示大字(人物ID, 614);
                        player.显示大字(人物ID, 614);
                    }
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 0, value4.人物_HP, 0, 0);
                    break;
            }
        }

        public static void 攻击计算(Players player, 武功类 武功, NpcClass npcTemp, int 武功ID, int 人物ID, int 临时武功)
        {
            int num = 0;
            try
            {
                Random random = new Random();
                num = 1;
                if (武功ID == 3000401 && !npcTemp.ContainsKeyInAbnormalState(4))
                {
                    double num2 = 3000.0;
                    异常状态类 value = new 异常状态类(npcTemp, player.人物全服ID, (int)num2, 4, 0.0);
                    npcTemp.异常状态.TryAdd(4, value);
                    发送攻击人物数据(player, 人物ID, 武功ID, 1, 126, npcTemp.Rxjh_HP, 0, 0);
                    return;
                }
                double num3 = npcTemp.FLD_DF * (World.怪物防御百分比 - npcTemp.FLD_JSDF);
                double num4 = player.FLD_人物基本_攻击;

                //24.0 EVIAS 灵兽特殊效果 20250724 
                bool 灵兽无视防御 = false;

                if (player.人物灵兽 != null && player.人物灵兽.FLD_LEVEL >= 15)
                {
                    if (player.人物灵兽.武功新[1, 4] != null && player.人物灵兽.武功新[1, 4].FLD_PID == 1401)
                    {
                        var 灵兽武功1 = player.人物灵兽.武功新[1, 4]; 
                        double 基础概率 = 1.6;                   //基础概率1.6%
                        double 等级加成 = 灵兽武功1.武功_等级 * 0.4; //每级增加0.4%
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            灵兽无视防御 = true;
                            player.显示大字(player.人物灵兽.全服ID, 1401); 
                        }
                    }

                    if (player.人物灵兽.武功新[1, 5] != null && player.人物灵兽.武功新[1, 5].FLD_PID == 1402)
                    {
                        var 灵兽武功2 = player.人物灵兽.武功新[1, 5]; 
                        double 基础概率 = 0.9;                   // 基础0.9%概率 
                        double 等级加成 = 灵兽武功2.武功_等级 * 0.1; // 每级增加0.1%
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            player.显示大字(player.人物灵兽.全服ID, 1402);
                        }
                    }

                    if (player.人物灵兽.武功新[1, 6] != null && player.人物灵兽.武功新[1, 6].FLD_PID == 1403)
                    {
                        var 灵兽武功3 = player.人物灵兽.武功新[1, 6]; 
                        double 基础概率 = 1.3;                   // 基础1.3%概率 
                        double 等级加成 = 灵兽武功3.武功_等级 * 0.2; // 每级增加0.2%
                        double 触发概率 = 基础概率 + 等级加成;
                        if (RNG.Next(1, 101) <= 触发概率)
                        {
                            player.显示大字(player.人物灵兽.全服ID, 1403);
                        }
                    }
                }

                if (灵兽无视防御)
                {
                    num3 *= 0.3; // 无视70%，保留30%防御
                }

                if (player.陵劲淬砺 > 0.0)
                {
                    player.武器属性提升 = 0;
                    if (player.陵劲淬砺 >= (double)RNG.Next(1, 100))
                    {
                        num4 += num4 * ((double)player.装备栏已穿装备[3].物品属性阶段数 * 0.005 * 2.0);
                        player.武器属性提升 = 2;
                        player.显示大字(player.人物全服ID, 1011);
                    }
                }
                if (player.FLD_装备_追加_降低百分比防御 + player.武勋降低百分比防御 > 0.0 && !灵兽无视防御)
                {
                    num3 *= 1.0 - player.FLD_装备_追加_降低百分比防御 - player.武勋降低百分比防御;
                }
                if (npcTemp.FLD_BOSS == 0 && player.中级附魂_复仇 != 0 && RNG.Next(1, 100) <= player.中级附魂_复仇)
                {
                    player.显示大字(player.人物全服ID, 401);
                    int num5 = 0;
                    num5 = ((npcTemp.Level <= 130) ? (npcTemp.Max_Rxjh_HP / 3) : 20000);
                    npcTemp.发送复仇显示伤害血量(num5);
                    npcTemp.Rxjh_HP -= num5;
                    npcTemp.Play_Add(player, num5);
                    if (npcTemp.Rxjh_HP <= 0 && !npcTemp.NPC死亡)
                    {
                        npcTemp.发送死亡数据(player.人物全服ID);
                    }
                }
                num = 2;
                if (player.Player_Job == 1)
                {
                    if ((double)RNG.Next(1, 110) <= player.破甲几率)
                    {
                        num3 *= player.得到气功加成值(1, 5, 2);
                        player.显示大字(player.人物全服ID, 16);
                    }
                }
                else if (player.Player_Job == 2)
                {
                    double num6 = player.剑_无坚不摧 + player.剑_乘胜追击;
                    double num7 = RNG.Next(1, 100);
                    if (num7 < player.剑_无坚不摧)
                    {
                        player.显示大字(人物ID, 120);
                        num3 *= 0.5;
                    }
                    else if (num7 < num6)
                    {
                        if (player.剑_乘胜追击 > 0.5) player.剑_乘胜追击 = 0.5;
                        player.显示大字(人物ID, 120);
                        num3 *= player.得到气功加成值(2, 9, 2) - player.剑_乘胜追击 * 0.01;
                    }
                }
                else if (player.Player_Job == 8 && (double)RNG.Next(1, 110) <= player.破甲几率)
                {
                    num3 *= player.得到气功加成值(8, 7, 2);
                    player.显示大字(player.人物全服ID, 16);
                }
                double num8 = num4;
                int num9 = 0;
                num = 3;
                if (武功.FLD_武功类型 == 3)
                {
                    num9 = 武功.FLD_AT + (player.武功新[武功.FLD_武功类型, 武功.FLD_INDEX].武功_等级 - 1) * 武功.FLD_每级加危害;
                }
                else if (武功.FLD_武功类型 == 2)
                {
                    num9 = player.夫妻武功攻击力;
                }
                else if (武功.FLD_每级危害.Length <= 0)
                {
                    num9 = ((player.Player_Job == 10 && 临时武功 != 0) ? ((int)((double)(num9 + player.Player_Level * 10) * (1.0 + player.拳师_水火一体))) : ((武功.FLD_PID == player.师傅数据.STWG1 || 武功.FLD_PID == player.师傅数据.STWG2 || 武功.FLD_PID == player.师傅数据.STWG2) ? (武功.FLD_AT * (10 - player.师傅数据.STLEVEL) / 10) : ((武功.FLD_武功类型 != 2) ? player.计算升天武功威力(武功) : player.夫妻武功攻击力)));
                }
                else
                {
                    int at = 武功.GetAt(武功.FLD_PID, player.武功新[武功.FLD_武功类型, 武功.FLD_INDEX].武功_等级);
                    if (at > 0) num9 = at;
                }
                num = 4;
                if (player.Player_Job == 11)
                {
                    if (player.梅_玄武危化 > 0.0) num9 = (int)((double)num9 * (1.0 + player.梅_玄武危化));
                    if (player.怒点 >= 3)
                    {
                        player.怒点 = 0;
                        if (player.梅_愤怒爆发 > 0.0) { num9 = (int)((double)num9 * (1.0 + player.梅_愤怒爆发)); player.显示大字(player.人物全服ID, 802); }
                        player.更新HP_MP_SP();
                    }
                }
                double num10 = (num8 - num3 + (double)player.FLD_人物基本_命中) * 1.5 + (double)num9 * (1.0 + player.FLD_装备_武功攻击力增加百分比 + player.FLD_装备_武功攻击力石头百分比 + player.FLD_人物_武功攻击力增加百分比 + player.FLD_人物_气功_武功攻击力增加百分比 + player.武器武功攻击力百分比 + player.FLD_宠物_追加_武功攻击) + (double)player.FLD_人物基本_命中 * 0.25;
                if (player.爆毒状态 > 0.0)
                {
                    num10 += num10 * player.爆毒状态; player.爆毒状态 = 0.0;
                }
                if (num10 <= 0.0)
                {
                    double num11 = ((player.FLD_装备_追加_伤害值 < 0) ? 0.0 : ((double)player.FLD_装备_追加_伤害值));
                    if (num11 <= 1.0) num11 = RNG.Next(1, 5);
                    攻击计算完成(player, 人物ID, 武功ID, (int)num11, 0, npcTemp.Rxjh_HP, 0);
                    return;
                }
                num = 5;
                if (player.离线挂机打怪模式 != 1) { player.组队升天四气功触发(player); }
                if (player.Player_Job == 1)
                {
                    if ((double)RNG.Next(1, 100) <= player.真武绝击)
                    {
                        player.显示大字(player.人物全服ID, 17);
                        num10 *= player.得到气功加成值(1, 7, 2);
                    }
                    if ((double)RNG.Next(1, 100) <= player.暗影绝杀)
                    {
                        player.显示大字(player.人物全服ID, 18);
                        num10 *= player.得到气功加成值(1, 9, 2);
                    }
                    if ((double)RNG.Next(1, 120) <= player.刀_梵音破镜 + player.升天五式_龙魂附体)
                    {
                        double num12 = player.得到气功加成值(1, 10, 2);
                        if (player.刀_升天三气功_火龙之火 > 0.0)
                        {
                            num12 += num12 * player.刀_升天三气功_火龙之火;
                        }
                        player.显示大字(player.人物全服ID, 312);
                        num10 = (int)(num10 * (1.0 + num12));
                    }
                    if (!player.追加状态列表.ContainsKey(700310) && (double)RNG.Next(1, 110) <= player.刀_升天一气功_遁出逆境)
                    {
                        player.显示大字(player.人物全服ID, 310);
                        if (player.追加状态列表 != null && player.GetAddState(700310))
                        {
                            player.追加状态列表[700310].时间结束事件();
                        }
                        追加状态类 追加状态类2 = new 追加状态类(player, 10000, 700310, 0);
                        player.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
                        player.addFLD_追加百分比_防御(0.1);
                        player.FLD_人物_气功_武功攻击力增加百分比 += 0.3;
                        player.状态效果(BitConverter.GetBytes(700310), 1, 10000);
                        player.更新武功和状态();
                    }
                }
                else if (player.Player_Job == 2)
                {
                    if ((double)RNG.Next(1, 110) < player.剑_怒海狂澜)
                    {
                        num10 *= player.得到气功加成值(2, 7, 2);
                        player.显示大字(player.人物全服ID, 82);
                    }
                    if (player.剑_破天一剑 != 0.0)
                    {
                        num10 *= 1.0 + player.剑_破天一剑;
                    }
                    if ((double)RNG.Next(1, 100) <= player.升天五式_惊天动地)
                    {
                        num10 *= 1.4;
                        player.显示大字(player.人物全服ID, 1015);
                    }
                }
                else if (player.Player_Job == 3)
                {
                    double num13 = 0.4;
                    double num14 = player.枪_升天一气功_破甲刺魂;
                    double num15 = RNG.Next(1, 100);
                    if (武功.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5)
                    {
                        if (player.怒)
                        {
                            num14 += player.枪_升天二气功_以退为进;
                            num13 += player.枪_升天二气功_以退为进 * 0.01;
                        }
                        if (num15 <= num14)
                        {
                            num10 += (double)player.FLD_装备_追加_防御 * num13;
                            player.显示大字(player.人物全服ID, 330);
                        }
                    }
                    if ((double)RNG.Next(1, 100) <= player.枪_怒意之吼)
                    {
                        double num16 = 1.2;
                        player.显示大字(player.人物全服ID, 332);
                        if (player.怒)
                        {
                            num16 = 1.2 + player.枪_升天三气功_怒意之火;
                        }
                        num10 *= num16;
                    }
                    if (!player.怒 && (double)RNG.Next(1, 100) <= player.升天五式_灭世狂舞)
                    {
                        num10 *= 1.2;
                        player.显示大字(player.人物全服ID, 1016);
                    }
                }
                else if (player.Player_Job == 4)
                {
                    num10 += player.弓_锐利之箭 + 1.0;
                    if ((double)RNG.Next(1, 100) <= player.弓_无明暗矢)
                    {
                        num10 *= 1.05 + player.弓_无明暗矢 * 0.01;
                        player.显示大字(player.人物全服ID, 49);
                        player.触发魔法无明暗矢 = true;
                    }
                    if ((double)RNG.Next(1, 100) <= player.弓_升天三气功_天外三矢)
                    {
                        player.显示大字(player.人物全服ID, 342);
                        if (武功.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5)
                        {
                            num10 *= 1.25;
                        }
                    }
                    if (武功.FLD_武功类型 == 3 && (double)RNG.Next(1, 100) <= player.弓_升天一气功_绝影射魂)
                    {
                        player.显示大字(player.人物全服ID, 340);
                        player.触发绝影射魂 = true;
                    }
                    if (武功.FLD_TYPE == 4)
                    {
                        int num17 = RNG.Next(1, 110);
                        int num18 = RNG.Next(1, 120);
                        if ((double)num17 <= player.弓_心神凝聚)
                        {
                            player.弓群攻触发心神 = true;
                            num10 *= 1.65;
                        }
                        if ((double)num18 <= player.弓_致命绝杀)
                        {
                            num10 *= player.得到气功加成值(4, 11, 2);
                            player.显示大字(player.人物全服ID, 140);
                            player.触发弓箭致命绝杀 = true;
                        }
                    }
                    if ((double)RNG.Next(1, 100) <= player.升天五式_千里一击)
                    {
                        num10 *= 1.0 + player.升天五式_千里一击 * 0.01;
                        player.显示大字(player.人物全服ID, 1017);
                    }
                }
                else if (player.Player_Job == 5)
                {
                    num10 *= 1.0 + player.医_长攻击力 * player.得到气功加成值(5, 5, 1);
                    if ((double)RNG.Next(1, 100) <= player.真武绝击)
                    {
                        player.显示大字(player.人物全服ID, 17);
                        num10 *= player.得到气功加成值(5, 7, 2);
                    }
                    double num19 = 0.0;
                    if (player.医_无中生有 - player.医_狂意护体 > 0.0)
                    {
                        num19 = player.医_无中生有 - player.医_狂意护体;
                    }
                    double num20 = num19 * 0.01;
                    if ((double)RNG.Next(1, 100) <= player.医_无中生有)
                    {
                        player.显示大字(player.人物全服ID, 351);
                        if (武功.FLD_武功类型 == 3 && player.夫妻武功攻击等级 >= 5 && (double)RNG.Next(1, 100) <= player.医_升天三气功_明镜止水)
                        {
                            player.显示大字(player.人物全服ID, 352);
                            num20 *= 2.0;
                        }
                        if (num20 > 0.0)
                        {
                            num10 += (double)(int)(num10 * num20);
                        }
                    }
                }
                else if (player.Player_Job == 6)
                {
                    try
                    {
                        if (武功.FLD_PID == 801303)
                        {
                            if ((int)DateTime.Now.Subtract(player.Pktime801303).TotalSeconds < 30)
                            {
                                return;
                            }
                            player.Pktime801303 = DateTime.Now;
                            num10 = 0.0;
                            player.怒气++;
                            if (player.怒气 > 5)
                            {
                                player.怒气 = 5;
                            }
                        }
                        else if (武功.FLD_INDEX == 1 || 武功.FLD_INDEX == 5 || 武功.FLD_INDEX == 9 || 武功.FLD_INDEX == 13 || 武功.FLD_INDEX == 17 || 武功.FLD_INDEX == 21 || 武功.FLD_INDEX == 25)
                        {
                            if (player.怒气 > 0)
                            {
                                double fLD_JSDF = 0.005;
                                switch (武功.FLD_INDEX)
                                {
                                    case 1:
                                        fLD_JSDF = 0.01;
                                        break;
                                    case 5:
                                        fLD_JSDF = 0.014;
                                        break;
                                    case 9:
                                        fLD_JSDF = 0.018;
                                        break;
                                    case 13:
                                        fLD_JSDF = 0.022;
                                        break;
                                    case 17:
                                        fLD_JSDF = 0.026;
                                        break;
                                    case 21:
                                        fLD_JSDF = 0.03;
                                        break;
                                    case 25:
                                        fLD_JSDF = 0.04;
                                        break;
                                }
                                if (!npcTemp.ContainsKeyInAbnormalState(9))
                                {
                                    double num21 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 value2 = new 异常状态类(npcTemp, player.人物全服ID, (int)num21, 9, 0.0);
                                    npcTemp.异常状态.TryAdd(9, value2);
                                    npcTemp.FLD_JSDF = fLD_JSDF;
                                }
                                if (!npcTemp.ContainsKeyInAbnormalState(11))
                                {
                                    double num22 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 value3 = new 异常状态类(npcTemp, player.人物全服ID, (int)num22, 11, 0.0);
                                    npcTemp.异常状态.TryAdd(11, value3);
                                }
                                player.怒气--;
                            }
                        }
                        else if (武功.FLD_INDEX == 2 || 武功.FLD_INDEX == 6 || 武功.FLD_INDEX == 10 || 武功.FLD_INDEX == 14 || 武功.FLD_INDEX == 18 || 武功.FLD_INDEX == 22 || 武功.FLD_INDEX == 26)
                        {
                            if (player.怒气 > 0)
                            {
                                double num23 = 0.005;
                                switch (武功.FLD_INDEX)
                                {
                                    case 2:
                                        num23 = 0.01;
                                        break;
                                    case 6:
                                        num23 = 0.014;
                                        break;
                                    case 10:
                                        num23 = 0.018;
                                        break;
                                    case 14:
                                        num23 = 0.022;
                                        break;
                                    case 18:
                                        num23 = 0.026;
                                        break;
                                    case 22:
                                        num23 = 0.03;
                                        break;
                                    case 26:
                                        num23 = 0.04;
                                        break;
                                }
                                if (!npcTemp.ContainsKeyInAbnormalState(9))
                                {
                                    double num24 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 value4 = new 异常状态类(npcTemp, player.人物全服ID, (int)num24, 9, 0.0);
                                    npcTemp.异常状态.TryAdd(9, value4);
                                    npcTemp.FLD_JSDF = num23;
                                }
                                if (!npcTemp.ContainsKeyInAbnormalState(10))
                                {
                                    double num25 = 10000.0 + player.刺_千蛛万手;
                                    异常状态类 异常状态类2 = new 异常状态类(player, npcTemp, player.人物全服ID, (int)num25, 10, (int)(num10 * num23));
                                    异常状态类2.异常状态类出血怪物(player, num10 * num23);
                                    npcTemp.异常状态.TryAdd(10, 异常状态类2);
                                }
                                player.怒气--;
                            }
                        }
                        else if ((武功.FLD_INDEX == 3 || 武功.FLD_INDEX == 7 || 武功.FLD_INDEX == 11 || 武功.FLD_INDEX == 15 || 武功.FLD_INDEX == 19 || 武功.FLD_INDEX == 23 || 武功.FLD_INDEX == 27) && player.怒气 > 0)
                        {
                            num10 += num10 * 0.3 * (double)player.怒气;
                            player.显示大字(player.人物全服ID, 80);
                            player.怒气 = 0;
                        }
                    }
                    catch (Exception)
                    {
                    }
                }
                else if (player.Player_Job == 7)
                {
                    num = 6;
                    if (!player.检查和弦状态())
                    {
                        if ((double)RNG.Next(1, 100) < player.琴师_梅花三弄)
                        {
                            player.显示大字(人物ID, 87);
                            int num26 = RNG.Next(1, 100);
                            int num27;
                            if (num26 <= 33)
                            {
                                num27 = 900401;
                                player.琴师状态 = 16;
                            }
                            else if (num26 >= 33 && num26 <= 66)
                            {
                                num27 = 900402;
                                player.琴师状态 = 32;
                            }
                            else
                            {
                                num27 = 900403;
                                player.琴师状态 = 64;
                            }
                            追加状态类 value5 = new 追加状态类(player, 10000, num27, 0);
                            player.追加状态列表.TryAdd(num27, value5);
                            player.状态效果(BitConverter.GetBytes(num27), 1, 10000);
                            player.更新人物数据(player);
                        }
                    }
                    else if (player.追加状态列表.ContainsKey(900402) && 武功.FLD_TYPE != 4)
                    {
                        double num28 = player.琴_七和弦_状态效果;
                        if ((double)RNG.Next(1, 100) < player.琴师_鸾凤和鸣 + player.琴师_升天一气功_飞花点翠)
                        {
                            player.显示大字(人物ID, 88);
                            num28 = player.琴_七和弦_状态效果 * 2.0 * (1.0 + player.琴师_升天一气功_飞花点翠加成);
                        }
                        num10 *= 1.0 + num28 * player.琴师_升天三气功_子夜秋歌;
                    }
                    if ((double)RNG.Next(1, 100) <= player.升天五式_龙爪纤指手)
                    {
                        player.显示大字(player.人物全服ID, 1019);
                        num10 *= 1.2;
                    }
                }
                else if (player.Player_Job == 8)
                {
                    double num29 = 0.0;
                    double num30 = 1.0;
                    if (武功.FLD_INDEX == 29 || 武功.FLD_INDEX == 30 || 武功.FLD_INDEX == 31)
                    {
                        num30 = 2.0;
                    }
                    if ((double)RNG.Next(1, 100) <= player.韩_天魔极血概率 * num30)
                    {
                        player.显示大字(player.人物全服ID, 20252);
                        num29 = player.韩_升天二气功_天魔护体;
                        num10 *= 1.8;
                        if (player.触发天魔极血)
                        {
                            num10 += player.韩飞官_天魔狂血攻击力;
                            player.韩飞官_天魔狂血攻击力 = 0.0;
                            player.触发天魔极血 = false;
                        }
                    }
                    if ((double)RNG.Next(1, 100) <= player.真武绝击)
                    {
                        player.显示大字(player.人物全服ID, 17);
                        num10 *= player.得到气功加成值(8, 8, 2);
                    }
                    if ((double)RNG.Next(1, 100) <= player.暗影绝杀 + num29)
                    {
                        player.显示大字(player.人物全服ID, 18);
                        num10 *= player.得到气功加成值(8, 11, 2);
                    }
                    if ((double)RNG.Next(1, 110) <= player.韩_升天一气功_行风弄舞 + num29)
                    {
                        player.显示大字(player.人物全服ID, 600);
                        num10 *= 1.25;
                    }
                    else if ((double)RNG.Next(1, 120) <= player.韩_天魔狂血概率 * num30)
                       
                    {
                        player.显示大字(player.人物全服ID, 252);
                        num10 *= 1.4;
                        player.韩飞官_天魔狂血攻击力 = num10 * (0.4 * num30 + player.升天五式_天魔之力);
                        player.触发天魔极血 = true;
                    }
                }
                else if (player.Player_Job == 9)
                {
                    if (武功ID == 2000401)
                    {
                        double num31 = 4000.0;
                        if (npcTemp.异常状态 != null && !npcTemp.ContainsKeyInAbnormalState(8))
                        {
                            异常状态类 value6 = new 异常状态类(npcTemp, player.人物全服ID, (int)num31, 8, 1.0);
                            npcTemp.异常状态.TryAdd(8, value6);
                        }
                    }
                    if ((double)RNG.Next(1, 100) < player.谭_怒海狂澜)
                    {
                        num10 *= player.得到气功加成值(9, 10, 2);
                        player.显示大字(player.人物全服ID, 82);
                    }
                }
                else if (player.Player_Job == 10)
                {
                    if (临时武功 == 3000109)
                    {
                        num10 *= 1.45;
                    }
                    if (临时武功 != 0)
                    {
                        if ((double)RNG.Next(1, 100) <= player.拳师_会心一击)
                        {
                            player.显示大字(player.人物全服ID, 557);
                            num10 *= 1.0 + player.拳师_会心一击威力;
                        }
                        if ((double)RNG.Next(1, 100) <= player.拳_升天一气功_夺命连环)
                        {
                            player.显示大字(player.人物全服ID, 561);
                            num10 *= 1.45;
                        }
                    }
                    else if ((double)RNG.Next(1, 100) <= player.拳师_会心一击)
                    {
                        player.显示大字(player.人物全服ID, 557);
                        num10 *= 1.0 + player.拳师_会心一击威力 / 2.0;
                    }
                    num10 *= 1.0 + player.拳师_磨杵成针;
                }
                else if (player.Player_Job == 11)
                {
                    if ((double)RNG.Next(1, 100) <= player.梅_玄武强击 * player.得到气功加成值(player.Player_Job, 6, 1))
                    {
                        num10 *= 1.0 + player.梅_玄武强击 * player.得到气功加成值(player.Player_Job, 6, 2);
                    }
                    if ((double)RNG.Next(1, 100) <= player.梅_升天一气功_玄武雷电)
                    {
                        num10 *= 1.4;
                        player.显示大字(player.人物全服ID, 803);
                    }
                    if (player.人物坐标_地图 != 7301 && (double)RNG.Next(1, 100) <= player.梅_升天二气功_玄武诅咒)
                    {
                        num10 += (double)player.人物最大_HP * 0.2;
                        player.显示大字(player.人物全服ID, 806);
                    }
                }
                else if (player.Player_Job == 12)
                {
                    int num32 = RNG.Next(1, 100);
                    if (player.流星漫天 + player.升天五式_破空坠星 >= (double)RNG.Next(1, 100))
                    {
                        player.触发流星漫天 = true;
                        player.显示大字(player.人物全服ID, 1005);
                        if (player.技冠群雄 >= (double)RNG.Next(1, 100))
                        {
                            player.显示大字(player.人物全服ID, 1013);
                            num10 *= 2.0;
                        }
                    }
                    if ((double)num32 <= player.真武绝击)
                    {
                        player.显示大字(player.人物全服ID, 17);
                        num10 *= player.得到气功加成值(12, 6, 2);
                    }
                }
                else if (player.Player_Job == 13)
                {
                    num10 *= 1.0 + player.神女长功击力 * 2.0;
                    int num33 = RNG.Next(1, 100);
                    if (player.神女杀星义虎 >= (double)RNG.Next(1, 100) && !player.触发杀星义气杀)
                    {
                        player.触发杀星义气虎 = true;
                        player.显示大字(player.人物全服ID, 1027);
                        num10 *= 1.15 + player.神女杀星义气;
                    }
                    if ((double)num33 <= player.神女杀星义杀 && !player.触发杀星义气虎)
                    {
                        player.触发杀星义气杀 = true;
                        player.显示大字(player.人物全服ID, 1026);
                        num10 *= 1.25 + player.神女杀星义气;
                    }
                    if (player.神女真武绝击 >= (double)RNG.Next(1, 100))
                    {
                        num10 *= player.得到气功加成值(13, 10, 2);
                        player.显示大字(player.人物全服ID, 17);
                    }
                    if (player.神女蛊毒解除 >= (double)RNG.Next(1, 100))
                    {
                        num10 *= 1.1;
                        player.显示大字(player.人物全服ID, 1025);
                    }
                }
                if (player.神女怒)
                {
                    player.显示大字(player.人物全服ID, 17);
                    num10 *= 1.4;
                }
                num = 7;
                double num34 = RNG.Next((int)num10 - 15, (int)num10 + 15) + player.装备追加对怪攻击 + player.药品追加对怪攻击 + player.讨伐追加对怪攻击;
                if (num34 <= 0.0)
                {
                    num34 = 0.0;
                }
                else
                {
                    if (player.Player_Job == 2)
                    {
                        if ((double)RNG.Next(1, 110) <= player.剑_冲冠一怒 && !player.怒)
                        {
                            player.显示大字(player.人物全服ID, 29);
                            player.人物_SP += (int)((double)player.人物_SP * player.剑_冲冠一怒 * 0.005);
                        }
                        if ((double)RNG.Next(1, 100) <= player.剑_移花接木)
                        {
                            player.显示大字(player.人物全服ID, 26);
                            player.加血((int)(num34 * 0.5));
                        }
                    }
                    if (player.Player_Job == 9)
                    {
                        if ((double)RNG.Next(1, 110) <= player.谭_冲冠一怒 && !player.怒)
                        {
                            player.显示大字(player.人物全服ID, 29);
                            player.人物_SP += (int)((double)player.人物_SP * player.谭_冲冠一怒 * 0.005);
                        }
                        if ((double)RNG.Next(1, 100) <= player.谭_移花接木)
                        {
                            player.显示大字(player.人物全服ID, 26);
                            player.加血((int)(num34 * 0.5));
                        }
                    }
                }
                num = 8;
                if (num34 <= 1.0)
                {
                    num34 = RNG.Next(1, 5);
                }
                else if (player.中级附魂_愤怒 != 0 && RNG.Next(1, 100) <= player.中级附魂_愤怒)
                {
                    num34 *= 1.2;
                    player.显示大字(player.人物全服ID, 404);
                }
                if (num34 > 1.0)
                {
                    num34 *= 获得技能对怪伤害(player.Player_Job, player.Player_Job_leve) + player.人物对怪物总伤害;
                }
                if (player.人物灵兽 != null)
                {
                    int num35 = 0;
                    if (player.人物灵兽.武功新[0, 3] != null)
                    {
                        num35 = 200;
                    }
                    else if (player.人物灵兽.武功新[0, 2] != null)
                    {
                        num35 = 150;
                    }
                    else if (player.人物灵兽.武功新[0, 1] != null)
                    {
                        num35 = 100;
                    }

                    num34 += (double)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num35);
                }
                if (player.FLD_装备_追加_伤害值 > 0)
                {
                    num34 += (double)player.FLD_装备_追加_伤害值;
                }
                攻击计算完成(player, 人物ID, 武功ID, (int)num34, 0, npcTemp.Rxjh_HP, 0);
            }
            catch (Exception ex)
            {
                RxjhClass.HandleGameException(ex, player, "攻击计算", $"步骤: {num}, 武功ID: {武功ID}, 目标: {人物ID}");
                if (World.是否开启票红字 == 1)
                {
                    player.系统提示("你攻击计算出错,请联系客服处理");
                    Form1.WriteLine(1, "攻击计算丨" + num + "丨" + player.UserName + "|" + player.报错次数阀值 + "丨");
                }
            }
        }

        public static void 攻击计算完成(Players player, int 人物ID, int 武功ID, int 攻击力, int 攻击类型, int 最后血量, int 障力吸收)
        {
            int num = 0;
            try
            {
                num = 1;
                if (player.设置固定伤害 > 0)
                {
                    攻击力 = player.设置固定伤害;
                    player.设置固定伤害 = 0;
                }
                NpcClass npc = MapClass.GetNpc(player.人物坐标_地图, 人物ID);
                if (人物ID >= 10000)
                {
                    if (World.讨伐战副本 != null && npc != null && npc.FLD_PID == 16556)
                    {
                        num = 2;
                        player.讨伐副本伤害排名(npc, 攻击力 / 100);
                    }
                    if (World.攻城 != null && npc != null && npc.FLD_PID == 16435)
                    {
                        攻击力 = (int)((double)player.FLD_人物基本_攻击 * (5.0 + player.人物对怪物总伤害));
                    }
                    if (World.世界boss != null && npc != null && npc.Rxjh_Map == World.世界BOSS出现地图 && npc.FLD_PID == World.世界BOSS怪物ID)
                    {
                        攻击力 = ((player.FLD_人物基本_攻击 < 200) ? 1 : (player.FLD_人物基本_攻击 / 50));
                    }
                    if (npc != null)
                    {
                        if (npc.FLD_SFYJ == 1 && player.Player_Level - npc.Level > World.击杀BOSS等级差)
                        {
                            攻击力 = 10;
                        }
                        if (npc.FLD_BOSS == 1)
                        {
                            npc.取最大攻击伤害者()?.攻击BOSS特效();
                        }
                    }
                }
                if (攻击力 <= 0)
                {
                    攻击力 = 0;
                }
                num = 3;
                player.上次攻击人物ID = 人物ID;
                int 宠物攻击力 = 0;
                int num2 = 0;
                if (player.人物灵兽 != null)
                {
                    if (player.人物灵兽.武功新[0, 3] != null)
                    {
                        num2 = 200;
                    }
                    else if (player.人物灵兽.武功新[0, 2] != null)
                    {
                        num2 = 150;
                    }
                    else if (player.人物灵兽.武功新[0, 1] != null)
                    {
                        num2 = 100;
                    }
                }
                if (World.TBL_KONGFU.TryGetValue(武功ID, out var value))
                {
                    num = 4;
                    int num3 = value.FLD_TYPE;
                    if (value.FLD_INDEX == 16 && value.FLD_武功类型 == 2 && value.FLD_攻击数量 == 1)
                    {
                        num3 = ((player.夫妻武功攻击数量 > 1) ? 4 : 0);
                    }
                    if (人物ID >= 10000)
                    {
                        攻击类 攻击类2;
                        if (num3 == 4 || player.触发流星漫天 || player.触发杀星义气虎 || player.触发杀星义气杀)
                        {
                            int num4 = value.FLD_攻击数量;
                            if (player.触发流星漫天)
                            {
                                num4 = 5;
                            }
                            if (player.触发杀星义气虎)
                            {
                                num4 = 5;
                            }
                            if (player.触发杀星义气杀)
                            {
                                num4 = 1;
                            }
                            if (player.触发绝影射魂)
                            {
                                num4 = 5;
                            }
                            if (value.FLD_INDEX == 16 && value.FLD_武功类型 == 2 && value.FLD_攻击数量 == 1)
                            {
                                num4 = player.夫妻武功攻击数量;
                            }
                            if (player.Player_Job == 1 || player.Player_Job == 8)
                            {
                                攻击力 = (int)((double)攻击力 * (1.0 + player.流光乱舞));
                            }
                            if (player.Player_Job == 7 && player.追加状态列表.ContainsKey(900403))
                            {
                                double num5 = player.琴_九和弦_状态效果 * player.琴师_升天三气功_子夜秋歌;
                                double num6 = player.琴_九和弦_群攻数量;
                                int fLD_攻击数量 = value.FLD_攻击数量;
                                if ((double)RNG.Next(1, 100) < player.琴师_鸾凤和鸣 + player.琴师_升天一气功_飞花点翠)
                                {
                                    player.显示大字(人物ID, 88);
                                    num5 = num5 * 2.0 * (1.0 + player.琴师_升天一气功_飞花点翠加成);
                                    num6 = num6 * 2.0 * (1.0 + player.琴师_升天一气功_飞花点翠加成);
                                    num4 = (int)((double)fLD_攻击数量 + num6);
                                    攻击力 = (int)((double)攻击力 * (1.0 + num5));
                                }
                                else
                                {
                                    num4 += (int)num6;
                                    攻击力 = (int)((double)攻击力 * (1.0 + num5));
                                }
                            }
                            else if (player.Player_Job == 13)
                            {
                                攻击力 = (int)((double)攻击力 * (1.0 + player.神女神力激发));
                            }
                            攻击类2 = ((player.Player_Job == 13 && player.触发杀星义气虎) ? new 攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT, value.FLD_TYPE, player.触发杀星义气虎) : ((player.Player_Job != 13 || !player.触发杀星义气杀) ? new 攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT, value.FLD_TYPE, player.触发流星漫天) : new 攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT, value.FLD_TYPE, player.触发杀星义气杀)));
                            if (npc == null)
                            {
                                return;
                            }
                            num = 5;
                            攻击类2.群攻.Add(new 群攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT)
                            {
                                剩余血量 = npc.Rxjh_HP,
                                总血量 = npc.Max_Rxjh_HP
                            });
                            List<NpcClass> list = npc.群攻查找范围Npc2(player, num4);
                            bool 灵甲爆发触发 = false; //24.0 EVIAS 枪客灵甲爆发 
                            if (player.Player_Job == 3 && player.枪客_灵甲爆发 > 0)
                            {
                                if ((double)RNG.Next(1, 100) <= player.枪客_灵甲爆发)
                                {
                                    player.显示大字(player.人物全服ID, 37); 
                                    灵甲爆发触发 = true;
                                }
                            }
                            int num7;
                            if (灵甲爆发触发)
                            {
                                num7 = (int)(攻击力 * 0.8);  // 灵甲爆发：80%伤害
                            }
                            else
                            {
                                num7 = (int)(攻击力 * 0.5);  // 普通：50%伤害
                            }
                            Random random = new Random();
                            if (list != null && list.Count > 0)
                            {
                                foreach (NpcClass item in list)
                                {
                                    num7 = random.Next(num7 - 15, num7 + 15);
                                    攻击类2.群攻.Add(new 群攻击类(item.FLD_INDEX, 武功ID, num7, value.FLD_EFFERT)
                                    {
                                        剩余血量 = item.Rxjh_HP,
                                        总血量 = item.Max_Rxjh_HP
                                    });
                                    if (攻击类2.群攻.Count >= num4)
                                    {
                                        break;
                                    }
                                }
                            }
                            if (player.人物灵兽 != null)
                            {
                                宠物攻击力 = (int)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num2);
                            }
                            num = 6;
                            发送群攻攻击数据(player, 攻击类2.群攻, 人物ID, 武功ID, 攻击力, value.FLD_EFFERT, 宠物攻击力);
                        }
                        else
                        {
                            if (npc == null)
                            {
                                return;
                            }
                            if (player.人物灵兽 != null)
                            {
                                //24.0 EVIAS 灵兽攻击力计算 20250724 
                                宠物攻击力 = (int)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num2);
                            }
                            攻击类2 = new 攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT);
                            num = 7;
                            发送攻击人物数据(player, 人物ID, 武功ID, 攻击力, value.FLD_EFFERT, 最后血量, 障力吸收, 宠物攻击力);
                        }
                        num = 8;
                        player.攻击列表.Clear();
                        player.攻击确认次数 = 1;
                        using (new Lock(player.攻击列表, "攻击列表"))
                        {
                            player.攻击列表.Add(攻击类2);
                        }
                        num = 9;
                        player.SendPack(player.人物全服ID, 人物ID, 攻击类型, value.FLD_PID, 1000);
                        if (player.Player_Job == 7)
                        {
                            player.打怪触发琴异常(攻击类2);
                        }
                        return;
                    }
                    if (player.触发流星漫天 || player.触发杀星义气虎 || player.触发杀星义气杀 || num3 == 4)
                    {
                        int num8 = value.FLD_攻击数量;
                        if (player.触发流星漫天)
                        {
                            num8 = 5;
                        }
                        if (player.触发杀星义气虎)
                        {
                            num8 = 5;
                        }
                        if (player.触发杀星义气杀)
                        {
                            num8 = 1;
                        }
                        if (player.人物灵兽 != null)
                        {
                            //24.0 EVIAS 灵兽攻击力计算 20250724 
                            宠物攻击力 = (int)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num2);
                        }
                        num = 10;
                        攻击类 攻击类3 = new 攻击类(player.人物全服ID, 武功ID, 攻击力, value.FLD_EFFERT, value.FLD_TYPE, false);
                        Players players = World.检查玩家世界ID(人物ID);
                        if (players != null)
                        {
                            攻击类3.群攻.Add(new 群攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT)
                            {
                                剩余血量 = players.人物_HP,
                                总血量 = players.人物最大_HP
                            });
                            List<Players> list2 = player.群攻查找范围RW2(players, num8);

                            
                            bool 灵甲爆发触发 = false; //24.0 EVIAS 枪客灵甲爆发
                            if (player.Player_Job == 3 && player.枪客_灵甲爆发 > 0)
                            {
                                if ((double)RNG.Next(1, 100) <= player.枪客_灵甲爆发)
                                {
                                    player.显示大字(player.人物全服ID, 37); 
                                    灵甲爆发触发 = true;
                                }
                            }
                            int num9;
                            if (灵甲爆发触发)
                            {
                                num9 = (int)(攻击力 * 0.8);  // 灵甲爆发：80%伤害
                            }
                            else
                            {
                                num9 = (int)(攻击力 * 0.5);  // 普通：50%伤害
                            }
                            Random random2 = new Random();
                            if (list2 != null && list2.Count > 0)
                            {
                                foreach (Players item2 in list2)
                                {
                                    num9 = random2.Next(num9 - 15, num9 + 15);
                                    num = 11;
                                    攻击类3.群攻.Add(new 群攻击类(item2.人物全服ID, 武功ID, num9, value.FLD_EFFERT)
                                    {
                                        剩余血量 = item2.人物_HP,
                                        总血量 = item2.人物最大_HP
                                    });
                                    if (攻击类3.群攻.Count >= num8)
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                        if (player.Player_Job == 4)
                        {
                            武功ID = 400001;
                        }
                        num = 12;
                        发送群攻攻击数据(player, 攻击类3.群攻, 人物ID, 武功ID, 攻击力, value.FLD_EFFERT, 宠物攻击力);
                        player.攻击列表.Clear();
                    }
                    else
                    {
                        if (player.人物灵兽 != null)
                        {
                            //24.0 EVIAS 灵兽攻击力计算 20250724
                            宠物攻击力 = (int)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num2);
                        }
                        num = 13;
                        发送攻击人物数据(player, 人物ID, 武功ID, 攻击力, value.FLD_EFFERT, 最后血量, 障力吸收, 宠物攻击力);
                        if (World.开启卡技能 == 0)
                        {
                            player.攻击列表.Clear();
                        }
                    }
                    player.攻击确认次数 = 1;
                    using (new Lock(player.攻击列表, "攻击列表"))
                    {
                        num = 14;
                        player.攻击列表.Add(new 攻击类(人物ID, 武功ID, 攻击力, value.FLD_EFFERT));
                    }
                    num = 15;
                    player.SendPack(player.人物全服ID, 人物ID, 攻击类型, value.FLD_PID, 1000);
                    return;
                }
                num = 16;
                NpcClass npc2 = MapClass.GetNpc(player.人物坐标_地图, 人物ID);
                if (player.人物灵兽 != null)
                {
                    //24.0 EVIAS 灵兽攻击力计算 20250724 
                    宠物攻击力 = (int)(player.人物灵兽.灵兽基本攻击 + player.人物灵兽.灵兽基本命中 + num2);
                }
                player.攻击确认次数 = 1;
                if (人物ID >= 10000 && player.中级附魂_复仇 != 0 && new Random().Next(1, 100) <= player.中级附魂_复仇 && npc2.FLD_BOSS == 0)
                {
                    player.显示大字(人物ID, 401);
                    int num10 = 0;
                    num10 = ((npc2.Level <= 130) ? ((int)((double)npc2.Rxjh_HP * 0.3)) : 20000);
                    攻击力 += num10;
                }
                if (攻击类型 == 128 || 攻击类型 == 129 || 攻击类型 == 130 || 攻击类型 == 131 || 攻击类型 == 132 || 攻击类型 == 133 || 攻击类型 == 136)
                {
                    player.攻击确认次数 = 3;
                }
                num = 17;

                //24.0 EVIAS 灵兽出血效果 20250722 
                if (人物ID >= 10000 && npc2 != null && player.人物灵兽 != null && player.人物灵兽.FLD_LEVEL >= 15 &&
                    player.人物灵兽.武功新[1, 5] != null && player.人物灵兽.武功新[1, 5].FLD_PID == 1402)
                {
                    if (!npc2.ContainsKeyInAbnormalState(55))
                    {
                        double 出血伤害 = player.人物灵兽.灵兽基本攻击; // 每秒造成灵兽攻击力的伤害
                        异常状态类 出血状态 = new 异常状态类(player, npc2, player.人物全服ID, 10000, 55, 出血伤害); // 10秒出血，使用传统出血状态图标ID 55 
                        出血状态.异常状态类出血怪物(player, 出血伤害);
                        npc2.异常状态.TryAdd(55, 出血状态);

                    }
                }

                发送攻击人物数据(player, 人物ID, 武功ID, 攻击力, 攻击类型, 最后血量, 障力吸收, 宠物攻击力);
                player.攻击列表.Clear();
                using (new Lock(player.攻击列表, "攻击列表"))
                {
                    player.攻击列表.Add(new 攻击类(人物ID, 武功ID, 攻击力, 攻击类型));
                }
                int num11;
                switch (player.Player_Job)
                {
                    case 4:
                        num11 = 1000;
                        break;
                    case 3:
                    case 5:
                        num11 = 950;
                        break;
                    case 6:
                        num11 = 1000;
                        break;
                    case 7:
                        num11 = 800;
                        break;
                    default:
                        num11 = 700;
                        break;
                    case 11:
                        num11 = 1000;
                        break;
                    case 12:
                        num11 = 950;
                        break;
                    case 13:
                        num11 = 950;
                        break;
                }
                if (player.Player_Job == 6)
                {
                    num11 = ((!player.GetAddState(801201)) ? 500 : 500);
                    if (攻击类型 == 129 || 攻击类型 == 130 || 攻击类型 == 131 || 攻击类型 == 132 || 攻击类型 == 133)
                    {
                        num11 = 500;
                    }
                }
                if (player.装备栏已穿装备[3].Get物品ID == 0)
                {
                    num11 = 720;
                }
                num = 18;
                player.SendPack(player.人物全服ID, 人物ID, 攻击类型, 0, num11);
            }
            catch (Exception ex)
            {
                // 2025-0617 EVIAS 改进异常处理
                RxjhClass.HandleGameException(ex, player, "攻击计算完成", $"步骤: {num}, 武功ID: {武功ID}, 目标: {人物ID}, 攻击力: {攻击力}");
                player.拳师连击控制 = 0;
                player.攻击列表.Clear();
                if (World.是否开启票红字 == 1)
                {
                    player.系统提示("你攻击计算完成出错,请联系客服处理");
                    Form1.WriteLine(1, "攻击计算完成" + player.人物全服ID + "|" + num + " | " + 武功ID + " | " + 人物ID + " | " + player.UserName + " | " + player.报错次数阀值 + " | " + ex.Message);
                }
            }
        }

        public static void 发送攻击人物数据(Players Play, int 攻击对象人物全服ID, int 武功ID, int 攻击力, int 攻击类型, int 最后血量, int 障力吸收, int 宠物攻击力)
        {
            try
            {
                Random random = new Random(World.GetRandomSeed());
                if (World.TBL_KONGFU.TryGetValue(武功ID, out var value))
                {
                    using (发包类 发包类 = new 发包类())
                    {
                        发包类.Write4(攻击对象人物全服ID);
                        发包类.Write2(1);
                        if (Play.Player_Job != 4 && Play.Player_Job != 11)
                        {
                            if (Play.装备栏已穿装备[12].Get物品数量 > 0)
                            {
                                发包类.Write2(100);
                            }
                            else if (武功ID == 3000401)
                            {
                                发包类.Write2(0);
                            }
                            else if (value.FLD_武功类型 == 1 && Play.人物全服ID == 攻击对象人物全服ID)
                            {
                                发包类.Write2(0);
                            }
                            else
                            {
                                发包类.Write2(武功ID);
                            }
                        }
                        else
                        {
                            发包类.Write2(100);
                        }
                        if (武功ID == 3000401)
                        {
                            发包类.Write4(0);
                        }
                        else
                        {
                            发包类.Write4(攻击力);
                        }
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(障力吸收);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(武功ID);
                        if (Play.Player_Job == 4 && value.FLD_TYPE == 4 && Play.弓群攻触发心神)
                        {
                            发包类.Write4(136);
                            Play.弓群攻触发心神 = false;
                        }
                        else
                        {
                            发包类.Write4(value.FLD_EFFERT);
                        }
                        发包类.Write(Play.人物坐标_X);
                        发包类.Write(15f);
                        发包类.Write(Play.人物坐标_Y);
                        if (武功ID == 3000401)
                        {
                            if (攻击对象人物全服ID > 10000)
                            {
                                发包类.Write(4);
                            }
                            else
                            {
                                发包类.Write(0);
                            }
                        }
                        else
                        {
                            发包类.Write(value.FLD_武功类型);
                        }
                        if (武功ID == 3000401)
                        {
                            if (攻击对象人物全服ID > 10000)
                            {
                                发包类.Write(1);
                            }
                            else
                            {
                                发包类.Write(0);
                            }
                        }
                        else
                        {
                            发包类.Write(1);
                        }
                        if (Play.Player_Job == 4)
                        {
                            if (Play.触发弓箭致命绝杀)
                            {
                                发包类.Write(120);
                                Play.触发弓箭致命绝杀 = false;
                            }
                            else if (Play.触发魔法无明暗矢)
                            {
                                发包类.Write(100);
                                Play.触发魔法无明暗矢 = false;
                            }
                            else
                            {
                                发包类.Write(0);
                            }
                        }
                        else if (Play.Player_Job == 11)
                        {
                            发包类.Write(100);
                        }
                        else
                        {
                            发包类.Write(0);
                        }
                        发包类.Write4(最后血量);
                        if (武功ID == 3000401)
                        {
                            发包类.Write4(0);
                        }
                        else if (攻击力 < 1)
                        {
                            发包类.Write4(1);
                        }
                        else
                        {
                            发包类.Write4(0);
                        }
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        if (攻击对象人物全服ID > 10000 && 宠物攻击力 > 0)
                        {
                            发包类.Write4(宠物攻击力);
                        }
                        else
                        {
                            发包类.Write4(-1);
                        }
                        if (Play.Player_Job == 9)
                        {
                            if (攻击力 >= 1 && value.FLD_武功类型 != 2)
                            {
                                double num = RNG.Next(1, 120);
                                if (攻击对象人物全服ID > 10000)
                                {
                                    发包类.Write4(0);
                                }
                                else if (Play.谭_纵横无双 > 0.0)
                                {
                                    if (num <= Play.谭_招式新法 + Play.谭_纵横无双)
                                    {
                                        int num2 = RNG.Next(2, 6);
                                        if (num2 > 2)
                                        {
                                            Play.触发天地回流 = true;
                                            发包类.Write4(3);
                                        }
                                        else
                                        {
                                            Play.触发缩影步 = true;
                                            发包类.Write4(num2);
                                        }
                                    }
                                    else
                                    {
                                        发包类.Write4(0);
                                    }
                                }
                                else
                                {
                                    发包类.Write4(0);
                                }
                            }
                            else
                            {
                                发包类.Write4(0);
                            }
                        }
                        else
                        {
                            发包类.Write4(0);
                        }
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        发包类.Write4(0);
                        if (Play.Player_Job == 10)
                        {
                            发包类.Write(Play.拳师连击控制);
                        }
                        else
                        {
                            发包类.Write(1);
                        }
                        发包类.Write(Play.连续攻击确认次数);
                        发包类.Write2(Play.当前杀怪数量);
                        发包类.Write2(0);
                        if (Play.Client != null)
                        {
                            Play.Client.SendPak(发包类, 2560, Play.人物全服ID);
                        }
                        Play.发送当前范围广播数据(发包类, 2560, Play.人物全服ID);
                        return;
                    }
                }
                int value2 = 1;
                int num3 = 攻击力;
                int num4 = 0;
                int num5 = 0;
                int num6 = 0;
                int value3 = 0;
                switch (攻击类型)
                {
                    default:
                        if (Play.Player_Job == 6)
                        {
                            num3 = 0;
                            num4 = 0;
                            if (攻击力 != 0)
                            {
                                num3 = new Random().Next(攻击力 / 2 - 10, 攻击力 / 2 + 10);
                                num4 = 攻击力 - num3;
                            }
                            value2 = 2;
                        }
                        break;
                    case 130:
                    case 131:
                    case 132:
                    case 133:
                        if (攻击力 != 0)
                        {
                            Random random4 = new Random();
                            num3 = random4.Next(攻击力 / 5 - 10, 攻击力 / 5 + 10);
                            num4 = random4.Next((攻击力 - num3) / 4 - 10, (攻击力 - num3) / 4 + 10);
                            num5 = random4.Next((攻击力 - num3 - num4) / 3 - 10, (攻击力 - num3 - num4) / 3 + 10);
                            num6 = random4.Next((攻击力 - num3 - num4 - num5) / 2 - 10, (攻击力 - num3 - num4 - num5) / 2 + 10);
                            value3 = 攻击力 - num3 - num4 - num5 - num6;
                            value2 = 5;
                        }
                        break;
                    case 128:
                    case 129:
                        if (Play.Player_Job == 6)
                        {
                            if (攻击力 != 0)
                            {
                                Random random2 = new Random();
                                num3 = random2.Next(攻击力 / 5 - 10, 攻击力 / 5 + 10);
                                num4 = random2.Next((攻击力 - num3) / 4 - 10, (攻击力 - num3) / 4 + 10);
                                num5 = random2.Next((攻击力 - num3 - num4) / 3 - 10, (攻击力 - num3 - num4) / 3 + 10);
                                num6 = random2.Next((攻击力 - num3 - num4 - num5) / 2 - 10, (攻击力 - num3 - num4 - num5) / 2 + 10);
                                value3 = 攻击力 - num3 - num4 - num5 - num6;
                                value2 = 5;
                            }
                        }
                        else
                        {
                            Random random3 = new Random();
                            num3 = random3.Next(攻击力 / 3 - 10, 攻击力 / 3 + 10);
                            num4 = random3.Next((攻击力 - num3) / 2 - 10, (攻击力 - num3) / 2 + 10);
                            num5 = 攻击力 - num3 - num4;
                            value2 = 3;
                        }
                        break;
                }
                using 发包类 发包类2 = new 发包类();
                发包类2.Write4(攻击对象人物全服ID);
                发包类2.Write2(1);
                if (Play.Player_Job != 4 && Play.Player_Job != 11)
                {
                    if (Play.装备栏已穿装备[12].Get物品数量 > 0)
                    {
                        发包类2.Write2(100);
                    }
                    else
                    {
                        发包类2.Write2(武功ID);
                    }
                }
                else
                {
                    发包类2.Write2(100);
                }
                发包类2.Write4(num3);
                发包类2.Write4(num4);
                发包类2.Write4(num5);
                发包类2.Write4(num6);
                发包类2.Write4(value3);
                发包类2.Write4(障力吸收);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(武功ID);
                发包类2.Write4(攻击类型);
                发包类2.Write(Play.人物坐标_X);
                发包类2.Write(15f);
                发包类2.Write(Play.人物坐标_Y);
                发包类2.Write(0);
                发包类2.Write(value2);
                if (Play.Player_Job == 4)
                {
                    if (Play.触发弓箭致命绝杀)
                    {
                        发包类2.Write(120);
                        Play.触发弓箭致命绝杀 = false;
                    }
                    else if (Play.触发物理无明暗矢)
                    {
                        发包类2.Write(100);
                        Play.触发物理无明暗矢 = false;
                    }
                    else
                    {
                        发包类2.Write(0);
                    }
                }
                else if (Play.Player_Job == 11)
                {
                    发包类2.Write(100);
                }
                else
                {
                    发包类2.Write(0);
                }
                发包类2.Write4(最后血量);
                if (攻击力 == 0)
                {
                    发包类2.Write4(1);
                }
                else
                {
                    发包类2.Write4(0);
                }
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                if (攻击对象人物全服ID > 10000)
                {
                    if (宠物攻击力 != 0)
                    {
                        if (random.Next(0, 100) < 80)
                        {
                            发包类2.Write4(宠物攻击力);
                        }
                        else
                        {
                            发包类2.Write4(0);
                        }
                    }
                    else
                    {
                        发包类2.Write4(宠物攻击力);
                    }
                }
                else
                {
                    发包类2.Write4(-1);
                }
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write4(0);
                发包类2.Write(1);
                发包类2.Write(Play.连续攻击确认次数);
                发包类2.Write2(Play.当前杀怪数量);
                发包类2.Write2(0);
                if (Play.Client != null)
                {
                    Play.Client.SendPak(发包类2, 2560, Play.人物全服ID);
                }
                Play.发送当前范围广播数据(发包类2, 2560, Play.人物全服ID);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "发送攻击人物数据 出错 [" + Play.Userid + "][" + Play.UserName + "] " + ex.Message);
            }
        }

        public static void 发送群攻攻击数据(Players player, List<群攻击类> 群攻击, int NPC全服ID, int 武功ID, int 攻击力, int 攻击类型, int 宠物攻击力)
        {
            try
            {
                武功类 武功类2 = World.TBL_KONGFU[武功ID];
                using 发包类 发包类 = new 发包类();
                bool flag = player.弓群攻触发心神;
                发包类.Write4(NPC全服ID);
                发包类.Write2(武功ID);
                发包类.Write4(群攻击.Count);
                发包类.Write4(武功ID);
                if (player.Player_Job == 4 && 武功类2.FLD_TYPE == 4 && player.弓群攻触发心神)
                {
                    发包类.Write4(武功类2.FLD_EFFERT);
                    player.弓群攻触发心神 = false;
                }
                else
                {
                    发包类.Write4(武功类2.FLD_EFFERT);
                }
                发包类.Write(player.人物坐标_X);
                发包类.Write(15f);
                发包类.Write(player.人物坐标_Y);
                发包类.Write4(0);
                发包类.Write4(0);
                for (int i = 0; i < 4; i++)
                {
                    发包类.Write4(0);
                    发包类.Write4(0);
                }
                if (player.Player_Job == 4 || player.Player_Job == 11)
                {
                    if (BitConverter.ToInt32(player.装备栏已穿装备[12].物品ID, 0) != 0)
                    {
                        int num = BitConverter.ToInt32(player.装备栏已穿装备[12].物品数量, 0);
                        player.装备栏已穿装备[12].物品数量 = BitConverter.GetBytes(num);
                        if (num == 0)
                        {
                            player.装备栏已穿装备[12].物品_byte = new byte[World.数据库单个物品大小];
                        }
                        发包类.Write2(100);
                    }
                    else
                    {
                        发包类.Write2(0);
                    }
                }
                else if (player.装备栏已穿装备[12].Get物品数量 > 0)
                {
                    发包类.Write2(100);
                }
                else
                {
                    发包类.Write2(0);
                }
                发包类.Write2(0);
                发包类 发包类2 = new 发包类();
                发包类 发包类3 = new 发包类();
                发包类 发包类4 = new 发包类();
                发包类 发包类5 = new 发包类();
                发包类 发包类6 = new 发包类();
                发包类 发包类7 = new 发包类();
                发包类 发包类8 = new 发包类();
                foreach (群攻击类 item in 群攻击)
                {
                    发包类2.Write4(item.人物ID);
                    发包类3.Write4(item.攻击力);
                    发包类4.Write1(0);
                    发包类5.Write4(0);
                    if (flag)
                    {
                        发包类6.Write2(8);
                    }
                    else
                    {
                        发包类6.Write2(0);
                    }
                    if (player.触发弓箭致命绝杀)
                    {
                        发包类7.Write2(120);
                    }
                    else if (player.触发魔法无明暗矢)
                    {
                        发包类7.Write2(100);
                    }
                    else
                    {
                        发包类7.Write2(0);
                    }
                    发包类8.Write4(item.剩余血量);
                }
                for (int j = 0; j < 20 - 群攻击.Count; j++)
                {
                    发包类2.Write4(0);
                    发包类3.Write4(0);
                    发包类4.Write1(0);
                    发包类5.Write4(0);
                    发包类6.Write2(0);
                    发包类7.Write2(0);
                    发包类8.Write4(0);
                }
                byte[] array = 发包类2.ToArray3();
                发包类.Write(array, 0, array.Length);
                byte[] array2 = 发包类3.ToArray3();
                发包类.Write(array2, 0, array2.Length);
                byte[] array3 = 发包类4.ToArray3();
                发包类.Write(array3, 0, array3.Length);
                byte[] array4 = 发包类5.ToArray3();
                发包类.Write(array4, 0, array4.Length);
                byte[] array5 = 发包类6.ToArray3();
                发包类.Write(array5, 0, array5.Length);
                byte[] array6 = 发包类7.ToArray3();
                发包类.Write(array6, 0, array6.Length);
                byte[] array7 = 发包类8.ToArray3();
                发包类.Write(array7, 0, array7.Length);
                if (player.Player_Job == 4)
                {
                    player.触发弓箭致命绝杀 = false;
                    player.触发魔法无明暗矢 = false;
                }
                if (宠物攻击力 != 0)
                {
                    if (new Random(World.GetRandomSeed()).Next(0, 100) < 80)
                    {
                        发包类.Write4(宠物攻击力);
                    }
                    else
                    {
                        发包类.Write4(0);
                    }
                }
                else
                {
                    发包类.Write4(宠物攻击力);
                }
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write4(0);
                发包类.Write1(1);
                if (player.触发流星漫天)
                {
                    发包类.Write1(1);
                }
                else if (player.触发杀星义气杀)
                {
                    发包类.Write1(3);
                }
                else if (player.触发杀星义气虎)
                {
                    发包类.Write1(4);
                }
                else
                {
                    发包类.Write1(0);
                }
                发包类.Write2(0);
                发包类.ToArray3();
                if (player.Client != null)
                {
                    player.Client.SendPak(发包类, 12032, player.人物全服ID);
                }
                player.发送当前范围广播数据(发包类, 12032, player.人物全服ID);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "发送群攻攻击数据 出错 [" + player.Userid + "][" + player.UserName + "] " + ex.Message);
            }
        }

        public static int 判断是否触发绝命技(Players player, int 人物ID, int 武功ID)
        {
            武功类 武功类2 = World.TBL_KONGFU[武功ID];
            if (player.装备栏已穿装备[3].Get物品ID == 0)
            {
                return -1;
            }
            if (人物ID < 10000)
            {
                return -1;
            }
            if (!MapClass.GetnpcTemplate(player.人物坐标_地图).TryGetValue(人物ID, out var value))
            {
                return -1;
            }
            if (value.FLD_BOSS != 0)
            {
                return -1;
            }
            if (武功类2.FLD_TYPE == 5 && value.Level > 69)
            {
                return -1;
            }
            if (武功类2.FLD_TYPE == 6 && value.Level > 99)
            {
                return -1;
            }
            if (value.Max_Rxjh_HP <= value.Rxjh_HP * 2)
            {
                return -1;
            }
            return 0;
        }

        public static double 获得物理对怪伤害(int 职业, int 几转)
        {
            foreach (职业系数类 value in World.职业系数数据.Values)
            {
                if (value.FLD_职业 == 职业 && value.FLD_几转 == 几转)
                {
                    return value.物理对怪伤害;
                }
            }
            return 1.0;
        }

        public static double 获得物理对人伤害(int 职业, int 几转)
        {
            foreach (职业系数类 value in World.职业系数数据.Values)
            {
                if (value.FLD_职业 == 职业 && value.FLD_几转 == 几转)
                {
                    return value.物理对人伤害;
                }
            }
            return 1.0;
        }

        public static double 获得技能对怪伤害(int 职业, int 几转)
        {
            foreach (职业系数类 value in World.职业系数数据.Values)
            {
                if (value.FLD_职业 == 职业 && value.FLD_几转 == 几转)
                {
                    return value.技能对怪伤害;
                }
            }
            return 1.0;
        }

        public static double 获得技能对人伤害(int 职业, int 几转)
        {
            foreach (职业系数类 value in World.职业系数数据.Values)
            {
                if (value.FLD_职业 == 职业 && value.FLD_几转 == 几转)
                {
                    return value.技能对人伤害;
                }
            }
            return 1.0;
        }

    }
} 
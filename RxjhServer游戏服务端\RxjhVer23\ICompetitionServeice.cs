namespace RxjhVer23;

public interface ICompetitionServeice
{
	bool IsCompetitionStarted();

	void StartCompetition();

	void EndCompetition();

	bool IsAttackAllowed(ICompetitionPlayer player);

	void OnProcessFullPacket(ICompetitionPlayer player, byte[] fullPacket);

	void OnExitGame(ICompetitionPlayer player);

	void OnLeaveField(ICompetitionPlayer player);

	void OnAttackSuccess(ICompetitionPlayer player, int actualDamage);

	void OnKillSuccess(ICompetitionPlayer player, ICompetitionPlayer killedPlayer);
}

using System.Collections.Generic;
using System.Data.SqlClient;

namespace RxjhVer23;

internal class RxjhWebShopItem : INewWebShopSaleItem
{
	public int FLD_SALE_ID { get; set; }

	public int FLD_ITEM_ID { get; set; }

	public string FLD_ITEM_NAME { get; set; } = "";


	public string FLD_ITEM_DESC { get; set; } = "";


	public int FLD_SALE_PRICE { get; set; }

	public int FLD_SALE_PRICE_TYPE { get; set; }

	public int FLD_SALE_TYPE1 { get; set; }

	public int FLD_SALE_TYPE2 { get; set; }

	public int FLD_ITEM_MAGIC { get; set; }

	public int FLD_ITEM_MAGIC1 { get; set; }

	public int FLD_ITEM_MAGIC2 { get; set; }

	public int FLD_ITEM_MAGIC3 { get; set; }

	public int FLD_ITEM_MAGIC4 { get; set; }

	public int FLD_INTERMEDIATE_SOUL_ATTRIBUTE { get; set; }

	public int FLD_SOUL_AWAKENING_STAGE { get; set; }

	public int FLD_EVOLUTION_LEVEL { get; set; }

	public int FLD_FOUR_GODS_POWER { get; set; }

	public int FLD_IS_BOUND { get; set; }

	public int FLD_AVAILABLE_DAYS { get; set; }

	internal static List<INewWebShopSaleItem> GetSaleItems(string connectionString)
	{
		string cmdText = "SELECT * FROM RXJH_WEB_SHOP_ITEM";
		List<INewWebShopSaleItem> list = new List<INewWebShopSaleItem>();
		using SqlConnection sqlConnection = new SqlConnection(connectionString);
		SqlCommand sqlCommand = new SqlCommand(cmdText, sqlConnection);
		sqlConnection.Open();
		SqlDataReader sqlDataReader = sqlCommand.ExecuteReader();
		while (sqlDataReader.Read())
		{
			RxjhWebShopItem item = new RxjhWebShopItem
			{
				FLD_SALE_ID = (int)sqlDataReader["FLD_SALE_ID"],
				FLD_ITEM_ID = (int)sqlDataReader["FLD_ITEM_ID"],
				FLD_ITEM_NAME = (string)sqlDataReader["FLD_ITEM_NAME"],
				FLD_ITEM_DESC = (string)sqlDataReader["FLD_ITEM_DESC"],
				FLD_SALE_PRICE = (int)sqlDataReader["FLD_SALE_PRICE"],
				FLD_SALE_PRICE_TYPE = (int)sqlDataReader["FLD_SALE_PRICE_TYPE"],
				FLD_SALE_TYPE1 = (int)sqlDataReader["FLD_SALE_TYPE1"],
				FLD_SALE_TYPE2 = (int)sqlDataReader["FLD_SALE_TYPE2"],
				FLD_ITEM_MAGIC = (int)sqlDataReader["FLD_ITEM_MAGIC"],
				FLD_ITEM_MAGIC1 = (int)sqlDataReader["FLD_ITEM_MAGIC1"],
				FLD_ITEM_MAGIC2 = (int)sqlDataReader["FLD_ITEM_MAGIC2"],
				FLD_ITEM_MAGIC3 = (int)sqlDataReader["FLD_ITEM_MAGIC3"],
				FLD_ITEM_MAGIC4 = (int)sqlDataReader["FLD_ITEM_MAGIC4"],
				FLD_INTERMEDIATE_SOUL_ATTRIBUTE = (int)sqlDataReader["FLD_INTERMEDIATE_SOUL_ATTRIBUTE"],
				FLD_SOUL_AWAKENING_STAGE = (int)sqlDataReader["FLD_SOUL_AWAKENING_STAGE"],
				FLD_EVOLUTION_LEVEL = (int)sqlDataReader["FLD_EVOLUTION_LEVEL"],
				FLD_FOUR_GODS_POWER = (int)sqlDataReader["FLD_FOUR_GODS_POWER"],
				FLD_IS_BOUND = (int)sqlDataReader["FLD_IS_BOUND"],
				FLD_AVAILABLE_DAYS = (int)sqlDataReader["FLD_AVAILABLE_DAYS"]
			};
			list.Add(item);
		}
		return list;
	}
}

namespace RxjhServer;

public class ItmeClass
{
	private int _FLD_QUESTITEM;

	private int _FLD_PID;

	private int _FLD_RESIDE1;

	private int _FLD_RESIDE2;

	private int _FLD_SEX;

	private int _FLD_DF;

	private int _FLD_LEVEL;

	private int _FLD_JOB_LEVEL;

	private int _FLD_ZX;

	private int _FLD_AT;

	private int _FLD_YCHP;

	private int _FLD_YCAT;

	private int _FLD_YCDF;

	private int _FLD_YCQG;

	private int _FLD_ATBFB;

	private int _FLD_DFBFB;

	private int _FLD_YCJY;

	private int _FLD_AT_Max;

	private int _FLD_RECYCLE_MONEY;

	private int _FLD_SALE_MONEY;

	private int _FLD_SIDE;

	private int _FLD_TYPE;

	private int _FLD_WEIGHT;

	private string _NAME;

	private string _DES;

	private string _回收;

	private int _FLD_MAGIC0;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private double _FLD_NEED_MONEY;

	private int _FLD_NEED_FIGHTEXP;

	private int _FLD_INTEGRATION;

	private int _FLD_SERIES;

	private int _FLD_DEAL_LOCK;

	private int _FLD_HEAD_WEAR;

	private int _FLD_UP_LEVEL;

	private int _FLD_LOCK;

	private int _FLD_XW;

	private int _FLD_XWJD;

	private int _FLD_NJ;

	private int _FLD_SELL_TYPE;

	public int FLD_QUESTITEM
	{
		get
		{
			return _FLD_QUESTITEM;
		}
		set
		{
			_FLD_QUESTITEM = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public int FLD_RESIDE1
	{
		get
		{
			return _FLD_RESIDE1;
		}
		set
		{
			_FLD_RESIDE1 = value;
		}
	}

	public int FLD_RESIDE2
	{
		get
		{
			return _FLD_RESIDE2;
		}
		set
		{
			_FLD_RESIDE2 = value;
		}
	}

	public int FLD_SEX
	{
		get
		{
			return _FLD_SEX;
		}
		set
		{
			_FLD_SEX = value;
		}
	}

	public int FLD_DF
	{
		get
		{
			return _FLD_DF;
		}
		set
		{
			_FLD_DF = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_JOB_LEVEL
	{
		get
		{
			return _FLD_JOB_LEVEL;
		}
		set
		{
			_FLD_JOB_LEVEL = value;
		}
	}

	public int FLD_ZX
	{
		get
		{
			return _FLD_ZX;
		}
		set
		{
			_FLD_ZX = value;
		}
	}

	public int FLD_AT
	{
		get
		{
			return _FLD_AT;
		}
		set
		{
			_FLD_AT = value;
		}
	}

	public int FLD_YCHP
	{
		get
		{
			return _FLD_YCHP;
		}
		set
		{
			_FLD_YCHP = value;
		}
	}

	public int FLD_YCAT
	{
		get
		{
			return _FLD_YCAT;
		}
		set
		{
			_FLD_YCAT = value;
		}
	}

	public int FLD_YCDF
	{
		get
		{
			return _FLD_YCDF;
		}
		set
		{
			_FLD_YCDF = value;
		}
	}

	public int FLD_YCQG
	{
		get
		{
			return _FLD_YCQG;
		}
		set
		{
			_FLD_YCQG = value;
		}
	}

	public int FLD_ATBFB
	{
		get
		{
			return _FLD_ATBFB;
		}
		set
		{
			_FLD_ATBFB = value;
		}
	}

	public int FLD_DFBFB
	{
		get
		{
			return _FLD_DFBFB;
		}
		set
		{
			_FLD_DFBFB = value;
		}
	}

	public int FLD_YCJY
	{
		get
		{
			return _FLD_YCJY;
		}
		set
		{
			_FLD_YCJY = value;
		}
	}

	public int FLD_AT_Max
	{
		get
		{
			return _FLD_AT_Max;
		}
		set
		{
			_FLD_AT_Max = value;
		}
	}

	public int FLD_RECYCLE_MONEY
	{
		get
		{
			return _FLD_RECYCLE_MONEY;
		}
		set
		{
			_FLD_RECYCLE_MONEY = value;
		}
	}

	public int FLD_SALE_MONEY
	{
		get
		{
			return _FLD_SALE_MONEY;
		}
		set
		{
			_FLD_SALE_MONEY = value;
		}
	}

	public int FLD_SIDE
	{
		get
		{
			return _FLD_SIDE;
		}
		set
		{
			_FLD_SIDE = value;
		}
	}

	public int FLD_TYPE
	{
		get
		{
			return _FLD_TYPE;
		}
		set
		{
			_FLD_TYPE = value;
		}
	}

	public int FLD_WEIGHT
	{
		get
		{
			return _FLD_WEIGHT;
		}
		set
		{
			_FLD_WEIGHT = value;
		}
	}

	public string ItmeNAME
	{
		get
		{
			return _NAME;
		}
		set
		{
			_NAME = value;
		}
	}

	public string ItmeDES
	{
		get
		{
			return _DES;
		}
		set
		{
			_DES = value;
		}
	}

	public string 回收字段
	{
		get
		{
			return _回收;
		}
		set
		{
			_回收 = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return _FLD_MAGIC0;
		}
		set
		{
			_FLD_MAGIC0 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public double FLD_NEED_MONEY
	{
		get
		{
			return _FLD_NEED_MONEY;
		}
		set
		{
			_FLD_NEED_MONEY = value;
		}
	}

	public int FLD_NEED_FIGHTEXP
	{
		get
		{
			return _FLD_NEED_FIGHTEXP;
		}
		set
		{
			_FLD_NEED_FIGHTEXP = value;
		}
	}

	public int FLD_INTEGRATION
	{
		get
		{
			return _FLD_INTEGRATION;
		}
		set
		{
			_FLD_INTEGRATION = value;
		}
	}

	public int FLD_SERIES
	{
		get
		{
			return _FLD_SERIES;
		}
		set
		{
			_FLD_SERIES = value;
		}
	}

	public int FLD_DEAL_LOCK
	{
		get
		{
			return _FLD_DEAL_LOCK;
		}
		set
		{
			_FLD_DEAL_LOCK = value;
		}
	}

	public int FLD_HEAD_WEAR
	{
		get
		{
			return _FLD_HEAD_WEAR;
		}
		set
		{
			_FLD_HEAD_WEAR = value;
		}
	}

	public int FLD_UP_LEVEL
	{
		get
		{
			return _FLD_UP_LEVEL;
		}
		set
		{
			_FLD_UP_LEVEL = value;
		}
	}

	public int FLD_LOCK
	{
		get
		{
			return _FLD_LOCK;
		}
		set
		{
			_FLD_LOCK = value;
		}
	}

	public int FLD_XW
	{
		get
		{
			return _FLD_XW;
		}
		set
		{
			_FLD_XW = value;
		}
	}

	public int FLD_XWJD
	{
		get
		{
			return _FLD_XWJD;
		}
		set
		{
			_FLD_XWJD = value;
		}
	}

	public int FLD_NJ
	{
		get
		{
			return _FLD_NJ;
		}
		set
		{
			_FLD_NJ = value;
		}
	}

	public int FLD_SELL_TYPE
	{
		get
		{
			return _FLD_SELL_TYPE;
		}
		set
		{
			_FLD_SELL_TYPE = value;
		}
	}

	public static ItmeClass GetItme(string name)
	{
		foreach (ItmeClass value in World.Itme.Values)
		{
			if (value.ItmeNAME == name)
			{
				return value;
			}
		}
		return null;
	}

	public static string 得到物品名称(int id)
	{
		ItmeClass value;
		return (!World.Itme.TryGetValue(id, out value)) ? string.Empty : value.ItmeNAME;
	}

	public static ItmeClass GetItmeID(int id)
	{
		ItmeClass value;
		return (!World.Itme.TryGetValue(id, out value)) ? null : value;
	}
}

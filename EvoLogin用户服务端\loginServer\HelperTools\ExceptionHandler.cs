using System;
using System.Diagnostics;
using System.Text;

namespace loginServer.HelperTools
{
    // 2025-0618 EVIAS 统一异常处理类，提供详细的错误日志和安全的错误响应
    public static class ExceptionHandler
    {
        // 登录相关异常
        public class LoginException : Exception
        {
            public int ErrorCode { get; }
            public string UserFriendlyMessage { get; }
            
            public LoginException(int errorCode, string message, string userFriendlyMessage = null) : base(message)
            {
                ErrorCode = errorCode;
                UserFriendlyMessage = userFriendlyMessage ?? "登录失败，请重试";
            }
        }
        
        // 数据库相关异常
        public class DatabaseException : Exception
        {
            public string Operation { get; }
            
            public DatabaseException(string operation, string message, Exception innerException = null) 
                : base($"数据库操作失败 [{operation}]: {message}", innerException)
            {
                Operation = operation;
            }
        }
        
        // 网络相关异常
        public class NetworkException : Exception
        {
            public string ConnectionInfo { get; }
            
            public NetworkException(string connectionInfo, string message, Exception innerException = null) 
                : base($"网络连接失败 [{connectionInfo}]: {message}", innerException)
            {
                ConnectionInfo = connectionInfo;
            }
        }
        
        // 处理登录异常
        public static byte[] HandleLoginException(Exception ex, string userId = null, string operation = null)
        {
            try
            {
                string logMessage = BuildLogMessage(ex, userId, operation);
                
                switch (ex)
                {
                    case LoginException loginEx:
                        Form1.WriteLine(2, $"登录异常[{loginEx.ErrorCode}]: {logMessage}");
                        return CreateErrorResponse(loginEx.UserFriendlyMessage);
                        
                    case DatabaseException dbEx:
                        Form1.WriteLine(1, $"数据库异常: {logMessage}");
                        return CreateErrorResponse("系统繁忙，请稍后重试");
                        
                    case NetworkException netEx:
                        Form1.WriteLine(1, $"网络异常: {logMessage}");
                        return CreateErrorResponse("网络连接异常，请检查网络");
                        
                    default:
                        Form1.WriteLine(1, $"未知异常: {logMessage}");
                        return CreateErrorResponse("系统错误，请联系管理员");
                }
            }
            catch (Exception handlerEx)
            {
                // 异常处理器本身出错时的兜底处理
                Debug.WriteLine($"异常处理器错误: {handlerEx.Message}");
                Form1.WriteLine(1, $"异常处理器错误: {handlerEx.Message}");
                return CreateErrorResponse("系统错误");
            }
        }
        
        // 处理数据库异常
        public static void HandleDatabaseException(Exception ex, string operation, string context = null)
        {
            try
            {
                string logMessage = $"数据库操作失败 - 操作: {operation}";
                if (!string.IsNullOrEmpty(context))
                    logMessage += $", 上下文: {context}";
                
                logMessage += $", 错误: {ex.Message}";
                
                if (ex.InnerException != null)
                    logMessage += $", 内部错误: {ex.InnerException.Message}";
                
                Form1.WriteLine(1, logMessage);
                
                // 记录详细的堆栈信息用于调试
                Debug.WriteLine($"数据库异常详情: {ex}");
            }
            catch (Exception handlerEx)
            {
                Debug.WriteLine($"数据库异常处理器错误: {handlerEx.Message}");
            }
        }
        
        // 处理网络异常
        public static void HandleNetworkException(Exception ex, string connectionInfo, string operation)
        {
            try
            {
                string logMessage = $"网络操作失败 - 连接: {connectionInfo}, 操作: {operation}, 错误: {ex.Message}";
                
                if (ex.InnerException != null)
                    logMessage += $", 内部错误: {ex.InnerException.Message}";
                
                Form1.WriteLine(1, logMessage);
                
                // 记录详细的堆栈信息用于调试
                Debug.WriteLine($"网络异常详情: {ex}");
            }
            catch (Exception handlerEx)
            {
                Debug.WriteLine($"网络异常处理器错误: {handlerEx.Message}");
            }
        }
        
        // 安全的异常日志记录（不泄露敏感信息）
        public static void LogSecureException(Exception ex, string operation, string userId = null)
        {
            try
            {
                var logBuilder = new StringBuilder();
                logBuilder.AppendLine($"操作: {operation}");
                
                if (!string.IsNullOrEmpty(userId))
                    logBuilder.AppendLine($"用户: {MaskSensitiveInfo(userId)}");
                
                logBuilder.AppendLine($"异常类型: {ex.GetType().Name}");
                logBuilder.AppendLine($"错误消息: {ex.Message}");
                
                // 只在调试模式下记录完整堆栈
                #if DEBUG
                logBuilder.AppendLine($"堆栈跟踪: {ex.StackTrace}");
                #endif
                
                Form1.WriteLine(1, logBuilder.ToString());
            }
            catch (Exception handlerEx)
            {
                Debug.WriteLine($"安全异常日志记录器错误: {handlerEx.Message}");
            }
        }
        
        // 构建日志消息
        private static string BuildLogMessage(Exception ex, string userId, string operation)
        {
            var builder = new StringBuilder();
            
            if (!string.IsNullOrEmpty(operation))
                builder.Append($"操作[{operation}] ");
            
            if (!string.IsNullOrEmpty(userId))
                builder.Append($"用户[{MaskSensitiveInfo(userId)}] ");
            
            builder.Append($"异常: {ex.Message}");
            
            return builder.ToString();
        }
        
        // 创建错误响应包
        private static byte[] CreateErrorResponse(string message)
        {
            try
            {
                // 使用现有的SetMsg方法格式
                byte[] header = Converter.hexStringToByte("0180040017000300");
                byte[] messageBytes = Encoding.Default.GetBytes(message);
                byte[] response = new byte[header.Length + messageBytes.Length + 1];
                
                Buffer.BlockCopy(header, 0, response, 0, header.Length);
                Buffer.BlockCopy(messageBytes, 0, response, 8, messageBytes.Length);
                Buffer.BlockCopy(BitConverter.GetBytes(messageBytes.Length + 1), 0, response, 2, 2);
                Buffer.BlockCopy(BitConverter.GetBytes(messageBytes.Length), 0, response, 6, 2);
                
                return response;
            }
            catch
            {
                // 如果创建响应包失败，返回最基本的错误包
                return Encoding.Default.GetBytes("ERROR");
            }
        }
        
        // 脱敏处理敏感信息
        private static string MaskSensitiveInfo(string info)
        {
            if (string.IsNullOrEmpty(info) || info.Length <= 3)
                return "***";
            
            return info.Substring(0, 2) + "***" + info.Substring(info.Length - 1);
        }
        
        // 验证异常是否为预期的业务异常
        public static bool IsBusinessException(Exception ex)
        {
            return ex is LoginException || 
                   ex is ArgumentException || 
                   ex is InvalidOperationException;
        }
        
        // 获取用户友好的错误消息
        public static string GetUserFriendlyMessage(Exception ex)
        {
            switch (ex)
            {
                case LoginException loginEx:
                    return loginEx.UserFriendlyMessage;
                case ArgumentException:
                    return "输入参数无效，请检查后重试";
                case TimeoutException:
                    return "操作超时，请稍后重试";
                case UnauthorizedAccessException:
                    return "权限不足，请联系管理员";
                default:
                    return "系统繁忙，请稍后重试";
            }
        }
    }
}

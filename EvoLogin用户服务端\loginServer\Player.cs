using System;
using System.Collections.Generic;
using System.Text;
using loginServer.DbClss;
using loginServer.HelperTools;

namespace loginServer;

public class Player
{
	public string uidd;

	public int loginprocess;

	public NetState Client;

	public Player(NetState client)
	{
		uidd = "";
		Client = client;
	}

	public void ManagePacket(byte[] data, int length)
	{
		try
		{
			// 2025-0618 EVIAS 添加数据包长度验证
			if (data == null || length < 2)
			{
				throw new HelperTools.ExceptionHandler.LoginException(400, "无效的数据包", "数据包格式错误");
			}

			byte[] array = new byte[4];
			Buffer.BlockCopy(data, 0, array, 0, 2);
			int packetType = BitConverter.ToInt32(array, 0);

			switch (packetType)
			{
			case 32768:
				得到ID(data, length);
				break;
			case 32778:
				版本验证(data, length);
				break;
			case 32780:
				选定服务器列表(data, length);
				break;
			case 32790:
				得到服务器列表(data, length);
				break;
			default:
				Form1.WriteLine(2, $"未知数据包类型: {packetType}, 客户端: {Client?.ToString()}");
				break;
			}
		}
		catch (Exception ex)
		{
			// 2025-0618 EVIAS 使用统一异常处理
			byte[] errorResponse = HelperTools.ExceptionHandler.HandleLoginException(ex, uidd, "ManagePacket");
			Client?.Send(errorResponse, errorResponse.Length);

			// 只有在严重错误时才断开连接
			if (!HelperTools.ExceptionHandler.IsBusinessException(ex))
			{
				Client?.Dispose();
				Client = null;
			}
		}
	}

	public byte[] SetMsg(string msg)
	{
		byte[] array = Converter.hexStringToByte("0180040017000300");
		byte[] bytes = Encoding.Default.GetBytes(msg);
		byte[] array2 = new byte[array.Length + bytes.Length + 1];
		Buffer.BlockCopy(array, 0, array2, 0, array.Length);
		Buffer.BlockCopy(bytes, 0, array2, 8, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length + 1), 0, array2, 2, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(bytes.Length), 0, array2, 6, 2);
		return array2;
	}

	public void 得到ID(byte[] data, int length)
	{
		// 检查服务器维护状态
		if (World.维护中)
		{
			string 提示信息 = string.IsNullOrEmpty(World.维护提示信息) ? "服务器维护中，请稍后再试" : World.维护提示信息;
			byte[] 维护提示包 = SetMsg(提示信息);
			Client.Send(维护提示包, 维护提示包.Length);
			Form1.WriteLine(2, $"维护期间拒绝登录：[{Client.ToString()}] 提示：{提示信息}");
			return;
		}

		bool flag = false;
		byte[] array = Converter.hexStringToByte("018004000A000300");
		try
		{
			string text = RxjhClass.GetUserIpadds(Client.ToString()).Replace("  ", "").Trim();
			if (World.封地区 != "")
			{
				bool flag2 = false;
				string[] array2 = World.封地区.Split(',');
				for (int i = 0; i < array2.Length; i++)
				{
					if (text.IndexOf(array2[i]) == -1)
					{
						continue;
					}
					if (World.解地区 != "")
					{
						string 解地区 = World.解地区;
						char[] separator = new char[1] { ',' };
						string[] array3 = 解地区.Split(separator);
						string[] array4 = array3;
						foreach (string value in array4)
						{
							if (text.IndexOf(value) != -1)
							{
								flag2 = true;
							}
						}
					}
					if (!flag2)
					{
						byte[] array5 = SetMsg("该地区已经被禁止登陆,请联系客服处理");
						Form1.WriteLine(1, text + "用户登陆[" + Client.ToString() + "]被封" + array2[i]);
						Client.Send(array5, array5.Length);
						return;
					}
				}
			}
			if (World.封IP段 != "")
			{
				string[] array6 = World.封IP段.Split(',');
				for (int k = 0; k < array6.Length; k++)
				{
					if (Client.ToString().Remove(6) == array6[k].Remove(6))
					{
						byte[] array7 = SetMsg("你的IP段已被管理员停封");
						Form1.WriteLine(1, text + "用户登陆[" + Client.ToString() + "]IP段被封" + array6[k].Remove(6) + "开头");
						Client.Send(array7, array7.Length);
						return;
					}
				}
			}
			if (RxjhClass.GetUserIP(Client.ToString()) == -1)
			{
				byte[] array8 = SetMsg("你的IP已被管理员停封");
				Form1.WriteLine(2, "用户登陆[" + Client.ToString() + "]被封");
				Client.Send(array8, array8.Length);
				return;
			}
			string text2;
			try
			{
				byte[] array9 = new byte[data[4]];
				Buffer.BlockCopy(data, 6, array9, 0, array9.Length);
				text2 = Encoding.Default.GetString(array9).Trim();
			}
			catch (Exception ex)
			{
				HelperTools.ExceptionHandler.LogSecureException(ex, "解析用户ID", uidd);
				byte[] array10 = SetMsg("系统错误,请联系客服人员1");
				Client.Send(array10, array10.Length);
				return;
			}
			string text3;
			try
			{
				byte[] array11 = new byte[data[6 + data[4]]];
				Buffer.BlockCopy(data, 8 + data[4], array11, 0, array11.Length);
				text3 = Encoding.Default.GetString(array11).Trim();
			}
			catch (Exception ex2)
			{
				// 2025-0618 EVIAS 改进异常处理，记录详细信息
				HelperTools.ExceptionHandler.LogSecureException(ex2, "解析用户密码", uidd);
				byte[] array12 = SetMsg("系统错误,请联系客服人员2");
				Client.Send(array12, array12.Length);
				return;
			}
			if (RxjhClass.DAXIE(text2) == -1)
			{
				byte[] array13 = SetMsg("检查到你用复制注入软件\r\n请重新登录游戏");
				Client.Send(array13, array13.Length);
				return;
			}
			if (RxjhClass.ReplaceComma(text2) == -1)
			{
				byte[] array14 = SetMsg("检查到你输入了非法字符,请重新填写");
				Client.Send(array14, array14.Length);
				return;
			}
			if (World.KillList.TryGetValue(text2, out var value2) && value2.conn >= 3)
			{
				Form1.WriteLine(2, "用户登陆[" + text2 + "]-密码错误多次");
				byte[] array15 = SetMsg("帐号密码错误多次，请5分钟以后再试。");
				Client.Send(array15, array15.Length);
				return;
			}
			if (World.KillList.TryGetValue(Client.ToString(), out value2) && value2.conn >= 3)
			{
				Form1.WriteLine(2, "用户登陆IP[" + Client.ToString() + "]-多次输入不存在的帐号");
				byte[] array16 = SetMsg("帐号不存在多次，请5分钟以后再试。");
				Client.Send(array16, array16.Length);
				return;
			}
			int num = -2;
			try
			{
				num = RxjhClass.GetUserId(text2, text3, Client.ToString());
			}
			catch (Exception ex3)
			{
				Form1.WriteLine(1, Converter.ToString(data));
				Form1.WriteLine(1, ex3.Message);
			}
			switch (num)
			{
			case -3:
			{
				byte[] array22 = SetMsg("帐号已锁定登陆IP地址\r\n请到网站[帐号管理]处解锁\r\n" + World.注册网站地址);
				Form1.WriteLine(2, "用户登陆[" + text2 + "]账号已受保护");
				Client.Send(array22, array22.Length);
				return;
			}
			case -2:
			{
				byte[] array17 = SetMsg("系统错误,请联系客服人员3");
				Client.Send(array17, array17.Length);
				return;
			}
			case -1:
			{
				if (World.KillList.TryGetValue(Client.ToString(), out var value3))
				{
					if (value3.conn < 3)
					{
						value3.conn++;
						byte[] array18 = SetMsg("账号不存在。");
						Form1.WriteLine(2, "用户登陆IP[" + Client.ToString() + "]账号不存在-2");
						Client.Send(array18, array18.Length);
					}
					else
					{
						value3.time = DateTime.Now.AddMinutes(5.0);
						Form1.WriteLine(2, "用户登陆IP[" + Client.ToString() + "]账号不存在-3");
						byte[] array19 = SetMsg("你输入的帐号不存在多次，请5分钟以后再试。");
						Client.Send(array19, array19.Length);
					}
				}
				else
				{
					World.KillList.TryAdd(Client.ToString(), new KillClass
					{
						UserId = Client.ToString(),
						conn = 1
					});
					byte[] array20 = SetMsg("账号不存在。");
					Form1.WriteLine(2, "用户登陆IP[" + Client.ToString() + "]账号不存在-1");
					Client.Send(array20, array20.Length);
				}
				return;
			}
			case 0:
			{
				if (World.KillList.TryGetValue(text2, out var value4))
				{
					if (value4.conn < 3)
					{
						value4.conn++;
						Form1.WriteLine(2, "用户登陆[" + text2 + "]-[" + text3 + "]密码错误");
						Client.Send(array, array.Length);
					}
					else
					{
						value4.time = DateTime.Now.AddMinutes(5.0);
						Form1.WriteLine(2, "用户登陆[" + text2 + "]-[" + text3 + "]密码错误");
						byte[] array21 = SetMsg("你输入的帐号密码错误多次，请5分钟以后再试。");
						Client.Send(array21, array21.Length);
					}
				}
				else
				{
					World.KillList.TryAdd(text2, new KillClass
					{
						UserId = text2,
						conn = 1
					});
					Form1.WriteLine(2, "用户登陆[" + text2 + "]-[" + text3 + "]密码错误");
					Client.Send(array, array.Length);
				}
				return;
			}
			}
			if (num > 0)
			{
				byte[] array23 = SetMsg("帐号已被停封\r\n距离解封还有" + num / 24 + "天" + (double)(num % 24) + "小时!");
				Form1.WriteLine(2, "用户登陆[" + text2 + "]账号已被停封");
				Client.Send(array23, array23.Length);
				return;
			}
			if (num != -99)
			{
				return;
			}
			KillClass value5;
			if (World.KillList.ContainsKey(text2))
			{
				World.KillList.TryRemove(text2, out value5);
			}
			if (World.KillList.ContainsKey(Client.ToString()))
			{
				World.KillList.TryRemove(Client.ToString(), out value5);
			}
			byte[] array24 = new byte[11 + text2.Length + text2.Length + 3];
			byte[] array25 = Converter.hexStringToByte("01800C0000003300");
			try
			{
				Buffer.BlockCopy(array25, 0, array24, 0, array25.Length);
			}
			catch (Exception ex4)
			{
				byte[] array26 = SetMsg("系统错误,请联系客服人员4");
				Form1.WriteLine(1, "getid_bySend  1错误" + array24.Length + "  " + array25.Length + ex4.Message);
				Client.Send(array26, array26.Length);
				return;
			}
			byte[] array27 = new byte[0];
			byte[] bytes;
			try
			{
				Buffer.BlockCopy(BitConverter.GetBytes(text2.Length), 0, array24, 8, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(text2.Length), 0, array24, 8 + text2.Length + 3, 1);
				Buffer.BlockCopy(BitConverter.GetBytes(7 + text2.Length + text2.Length + 2), 0, array24, 2, 1);
				bytes = Encoding.Default.GetBytes(text2);
			}
			catch (Exception ex5)
			{
				byte[] array28 = SetMsg("系统错误,请联系客服人员5");
				Form1.WriteLine(1, "getid_bySend  2错误" + array24.Length + "  " + array27.Length + ex5.Message);
				Client.Send(array28, array28.Length);
				return;
			}
			try
			{
				Buffer.BlockCopy(bytes, 0, array24, 10, bytes.Length);
				Buffer.BlockCopy(bytes, 0, array24, 10 + text2.Length + 3, bytes.Length);
			}
			catch (Exception ex6)
			{
				byte[] array29 = SetMsg("系统错误,请联系客服人员6");
				Form1.WriteLine(1, "getid_bySend  错误" + array24.Length + "  " + bytes.Length + ex6.Message);
				Client.Send(array29, array29.Length);
				return;
			}
			try
			{
				string[] array30 = World.分区号.Split(';');
				if (array30.Length > 1)
				{
					for (int l = 0; l < array30.Length; l++)
					{
						if (RxjhClass.得到分区ID(text2) == array30[l])
						{
							flag = true;
							break;
						}
					}
				}
			}
			catch
			{
				byte[] array31 = SetMsg("系统错误,未设置正确的登陆分区,请联系管理员7");
				Client.Send(array31, array31.Length);
				return;
			}
			if (!flag)
			{
				byte[] array32 = SetMsg("帐号未激活或进错分区,请联系管理员8");
				Client.Send(array32, array32.Length);
				return;
			}
			if (!World.登录玩家(text2, Client.ToString()))
			{
				byte[] array33 = SetMsg("账号已经在线,请下线后再顶号");
				Client.Send(array33, array33.Length);
				return;
			}
			Client.Send(array24, array24.Length);
			uidd = text2;
			World.踢出非本线ID(text2);
			RxjhClass.更新登陆IP(text2, Client.ToString());
			var player = HelperTools.ConcurrentPlayerManager.GetPlayer(text2);
			if (player != null)
			{
				player.UserIp = Client.ToString();
			}
			loginprocess = 1;
			Form1.WriteLine(2, "用户登陆[" + text2 + "]-[" + text3 + "]登陆成功  " + text + "  " + Client.ToString());
		}
		catch (Exception ex7)
		{
			byte[] errorResponse = HelperTools.ExceptionHandler.HandleLoginException(ex7, uidd, "得到ID");
			Client?.Send(errorResponse, errorResponse.Length);
		}
	}

	public void 版本验证(byte[] data, int length)
	{
		byte[] array = Converter.hexStringToByte("0B8128004C4F47494E5F5345525645525F3030000159424F6E6C696E652D2D31323535363334313137005546");
		Client.Send(array, array.Length);
	}

	public void 选定服务器列表(byte[] data, int length)
	{
		try
		{
			if (RxjhClass.DAXIE(uidd) == -1)
			{
				if (World.KillList.TryGetValue(uidd, out var value3))
				{
					if (value3.move < 3)
					{
						value3.move++;
						byte[] array10 = SetMsg("检查到你使用非法复制软件\r\n连续3次使用非法注入封永久\r\n请重新登录游戏(2)");
						Client.Send(array10, array10.Length);
					}
					else
					{
						// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
						SecureDBA.UpdateAccountStatus(uidd, 99999, "非法注入永久封号", 0);
						byte[] array11 = SetMsg("检查到你使用非法复制软件\r\n连续3次使用非法注入封永久\r\n请重新登录游戏(3)");
						Client.Send(array11, array11.Length);
					}
				}
				else
				{
					World.KillList.TryAdd(uidd, new KillClass
					{
						UserId = uidd,
						move = 1
					});
					byte[] array12 = SetMsg("检查到你使用非法复制软件\r\n连续3次使用非法注入封永久\r\n请重新登录游戏(1)");
					Client.Send(array12, array12.Length);
				}
				return;
			}
			playerS playerS2 = World.查询玩家(uidd);
			if (playerS2 != null)
			{
				if (playerS2.LoginOut)
				{
					byte[] array13 = SetMsg("已踢出离线或卡号人物\r\n请重新登录游戏");
					Client.Send(array13, array13.Length);
					return;
				}
				int num = data[4];
				int num2 = data[8];
				string[] array29 = World.分区号.Split(';');
				switch (num)
				{
				case 1:
					if (array29[0] != RxjhClass.得到分区ID(uidd))
					{
						byte[] array31 = SetMsg("帐号未进错分区,请联系" + array29[0] + "区管理员");
						Client.Send(array31, array31.Length);
						return;
					}
					break;
				case 2:
					if (array29[1] != RxjhClass.得到分区ID(uidd))
					{
						byte[] array30 = SetMsg("帐号未进错分区,请联系" + array29[1] + "区管理员");
						Client.Send(array30, array30.Length);
						return;
					}
					break;
				}
				ServerClass serverClass = World.ServerList[num - 1];
				SockClient playerHandler = World.查询服务器(serverClass.ServerList[num2 - 1].ServerZId.ToString());
				if (RxjhClass.得到进线符(uidd) != "1" && num2 == World.vip线路)
				{
					byte[] array32 = SetMsg("只有使用了vip进线符的用户才能连接");
					Client.Send(array32, array32.Length);
					return;
				}
				if (serverClass.ServerList[num2 - 1].线路状态 == 0)
				{
					byte[] array33 = SetMsg("此线路未开放^请选择其他线路进入游戏..");
					Client.Send(array33, array33.Length);
					return;
				}
				if (serverClass.ServerList[num2 - 1].线路状态 == 1)
				{
					byte[] array34 = SetMsg("此线路已经满员^请选择其他线路进入游戏..");
					Client.Send(array34, array34.Length);
					return;
				}
				if (playerHandler != null && playerHandler.当前在线 >= playerHandler.最大在线)
				{
					byte[] array35 = SetMsg("此线路已经满员^请选择其他线路进入游戏..");
					Client.Send(array35, array35.Length);
					return;
				}
				byte[] bytes = Encoding.Default.GetBytes(serverClass.ServerList[num2 - 1].SERVER_IP);
				byte[] bytes2 = BitConverter.GetBytes(int.Parse(serverClass.ServerList[num2 - 1].SERVER_PORT));
				byte[] bytes3 = Encoding.Default.GetBytes(serverClass.ServerList[num2 - 1].SERVER_NAME);
				byte[] bytes4 = BitConverter.GetBytes(bytes.Length);
				byte[] bytes5 = BitConverter.GetBytes(bytes3.Length);
				byte[] array36 = new byte[6 + bytes.Length + bytes2.Length + bytes3.Length + 2 + bytes3.Length];
				array36[0] = 100;
				array36[1] = 128;
				array36[4] = bytes4[0];
				Buffer.BlockCopy(BitConverter.GetBytes(array36.Length - 4), 0, array36, 2, 2);
				Buffer.BlockCopy(bytes, 0, array36, 6, bytes.Length);
				Buffer.BlockCopy(bytes2, 0, array36, 6 + bytes.Length, bytes2.Length);
				Buffer.BlockCopy(bytes3, 0, array36, array36.Length - bytes3.Length - bytes3.Length - 2, bytes3.Length);
				Buffer.BlockCopy(bytes3, 0, array36, array36.Length - bytes3.Length, bytes3.Length);
				array36[array36.Length - bytes3.Length - 2] = bytes5[0];
				array36[array36.Length - bytes3.Length - 2 - bytes3.Length - 2] = bytes5[0];
				byte[] array37 = new byte[array36.Length + 4];
				Buffer.BlockCopy(array36, 0, array37, 0, array36.Length);
				Buffer.BlockCopy(BitConverter.GetBytes(serverClass.ServerList[num2 - 1].ServerZId), 0, array37, array37.Length - 2, 2);
				Client.Send(array37, array37.Length);
				playerS2.原服务器序号 = serverClass.ServerList[num2 - 1].ServerId;
				playerS2.原服务器IP = serverClass.ServerList[num2 - 1].SERVER_IP;
				playerS2.原服务器端口 = serverClass.ServerList[num2 - 1].SERVER_PORT;
				playerS2.原服务器ID = serverClass.ServerList[num2 - 1].ServerZId;
				playerS2.银币广场服务器IP = serverClass.ServerList[20].SERVER_IP;
				playerS2.银币广场服务器端口 = serverClass.ServerList[20].SERVER_PORT;
				loginprocess = 3;
			}
			else
			{
				byte[] array38 = SetMsg("已踢出离线或卡号人物\r\n请重新登录游戏");
				Client.Send(array38, array38.Length);
			}
		}
		catch (Exception ex)
		{
			// 2025-0618 EVIAS 改进异常处理，使用统一异常处理器
			byte[] errorResponse = HelperTools.ExceptionHandler.HandleLoginException(ex, uidd, "选定服务器列表");
			Client?.Send(errorResponse, errorResponse.Length);
		}
	}

	public void 得到服务器列表(byte[] data, int length)
	{
		List<byte[]> list = new List<byte[]>();
		int serverCount = World.ServerCount;
		int num = 0;
		for (int i = 0; i < serverCount; i++)
		{
			byte[] array = 得到分区包(i);
			list.Add(array);
			num += array.Length;
		}
		byte[] array2 = new byte[num];
		int num2 = 0;
		for (int j = 0; j < serverCount; j++)
		{
			byte[] array3 = list[j];
			Buffer.BlockCopy(array3, 0, array2, num2, array3.Length);
			num2 += array3.Length;
		}
		byte[] bytes = BitConverter.GetBytes(array2.Length);
		byte[] array4 = new byte[6 + array2.Length];
		Buffer.BlockCopy(bytes, 0, array4, 2, 2);
		array4[0] = 23;
		array4[1] = 128;
		array4[4] = (byte)serverCount;
		array4[5] = 0;
		Buffer.BlockCopy(array2, 0, array4, 6, array2.Length);
		Client.Send(array4, array4.Length);
		loginprocess = 2;
	}

    /*public byte[] 得到分线包(int 分区线路状态, int id, int Zid, string 分线名)
	{
		double num = -1.0;
		double num2 = 1000.0;
		double num4;
		try
		{
			SockClient sockClient = World.查询服务器(Zid.ToString());
			if (sockClient != null)
			{
				num = sockClient.当前在线;
				num2 = sockClient.最大在线;
			}
			if (num != -1.0)
			{
				if (num > 0.0)
				{
					if (num < num2)
					{
						double num3 = num2 / 100.0;
						num4 = num / num3;
					}
					else
					{
						num4 = -1.0;
					}
				}
				else
				{
					num4 = 0.0;
				}
			}
			else
			{
				num4 = 101.0;
			}
		}
		catch
		{
			num4 = 101.0;
		}
		byte[] bytes = Encoding.Default.GetBytes(分线名);
		byte[] bytes2 = BitConverter.GetBytes(id + 1);
		byte[] array;
		if (World.真实线路切换 != 0)
		{
			array = BitConverter.GetBytes((int)num4);
		}
		else
		{
			if (1 == 0)
			{
			}
			byte[] array2 = 分区线路状态 switch
			{
				1 => BitConverter.GetBytes(-1),
				2 => BitConverter.GetBytes(92),
				3 => BitConverter.GetBytes(55),
				4 => BitConverter.GetBytes(22),
				_ => BitConverter.GetBytes(101),
			};
			if (1 == 0)
			{
			}
			array = array2;
		}
		byte[] bytes3 = BitConverter.GetBytes(bytes.Length);
		byte[] array3 = new byte[6 + bytes.Length];
		array3[0] = bytes2[0];
		array3[2] = bytes3[0];
		array3[array3.Length - 2] = array[0];
		Buffer.BlockCopy(bytes, 0, array3, 4, bytes.Length);
		return array3;
	}*/

   
    public byte[] 得到分线包(int 分区线路状态, int id, int Zid, string 分线名) //EVIAS
    {
        double num = -1.0;
        double num2 = 1000.0;
        double num4;
        try
        {
            SockClient sockClient = World.查询服务器(Zid.ToString());
            if (sockClient != null)
            {
                num = sockClient.当前在线;
                num2 = sockClient.最大在线;
            }
            if (num != -1.0)
            {
                if (num > 0.0)
                {
                    if (num < num2)
                    {
                        double num3 = num2 / 100.0;
                        num4 = num / num3; 
                    }
                    else
                    {
                        num4 = -1.0; 
                    }
                }
                else
                {
                    num4 = 0.0; 
                }
            }
            else
            {
                num4 = 101.0; 
            }
        }
        catch
        {
            num4 = 101.0; 
        }

        byte[] bytes = Encoding.Default.GetBytes(分线名);
        byte[] bytes2 = BitConverter.GetBytes(id + 1);
        byte[] array;
        if (World.真实线路切换 != 0)
        {
            int 动态状态;
            if (num4 == 101.0)
            {
                动态状态 = 101; // 未开放
            }
            else if (num4 == -1.0)
            {
                动态状态 = -1; // 满员
            }
            else if (num4 > 80.0)
            {
                动态状态 = 92; // 红
            }
            else if (num4 > 50.0)
            {
                动态状态 = 55; // 黄
            }
            else
            {
                动态状态 = 22; // 绿
            }
            array = BitConverter.GetBytes(动态状态);
        }
        else
        {
            byte[] array2 = 分区线路状态 switch
            {
                1 => BitConverter.GetBytes(-1),
                2 => BitConverter.GetBytes(92),
                3 => BitConverter.GetBytes(55),
                4 => BitConverter.GetBytes(22),
                _ => BitConverter.GetBytes(101),
            };
            array = array2;
        }

        byte[] bytes3 = BitConverter.GetBytes(bytes.Length);
        byte[] array3 = new byte[6 + bytes.Length];
        array3[0] = bytes2[0];
        array3[2] = bytes3[0];
        array3[array3.Length - 2] = array[0];
        Buffer.BlockCopy(bytes, 0, array3, 4, bytes.Length);
        return array3;
    }


    public byte[] 得到分区包(int 分区id)
	{
		ServerClass serverClass = World.ServerList[分区id];
		int num = serverClass.ServerCount;
		if (num > World.显示线路数量)
		{
			num = World.显示线路数量;
		}
		byte[] bytes = Encoding.Default.GetBytes(serverClass.SERVER_NAME);
		int num2 = 0;
		List<byte[]> list = new List<byte[]>();
		for (int i = 0; i < num; i++)
		{
			byte[] array = 得到分线包(serverClass.ServerList[i].线路状态, serverClass.ServerList[i].ServerId, serverClass.ServerList[i].ServerZId, serverClass.ServerList[i].SERVER_NAME);
			num2 += array.Length;
			list.Add(array);
		}
		byte[] array2 = new byte[14 + bytes.Length];
		byte[] array3 = new byte[num2 + array2.Length];
		int num3 = 0;
		for (int j = 0; j < num; j++)
		{
			byte[] array4 = list[j];
			Buffer.BlockCopy(array4, 0, array3, array2.Length + num3, array4.Length);
			num3 += array4.Length;
		}
		byte[] bytes2 = BitConverter.GetBytes(分区id + 1);
		byte[] bytes3 = BitConverter.GetBytes(num);
		byte[] bytes4 = BitConverter.GetBytes(bytes.Length);
		array2[0] = bytes2[0];
		array2[array2.Length - 8] = 1;
		array2[2] = bytes4[0];
		array2[array2.Length - 4] = bytes3[0];
		array2[array2.Length - 6] = 1;
		Buffer.BlockCopy(bytes, 0, array2, 4, bytes.Length);
		Buffer.BlockCopy(array2, 0, array3, 0, array2.Length);
		return array3;
	}
   
}

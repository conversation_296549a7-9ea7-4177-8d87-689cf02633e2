using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer
{
    // 2025-0618 EVIAS 异常管理器界面
    public partial class 异常管理器 : Form
    {
        private ListView listView异常;
        private ComboBox comboBox异常类型;
        private DateTimePicker dateTimePicker开始;
        private DateTimePicker dateTimePicker结束;
        private TextBox textBox搜索;
        private Button button搜索;
        private Button button刷新;
        private Button button导出;
        private Button button清空统计;
        private Label label统计信息;
        private RichTextBox richTextBox详情;
        private Panel panelTop;
        private Label label类型;
        private Label label开始时间;
        private Label label结束时间;
        private Label label搜索;
        private SplitContainer splitContainer;
        private Label label详情;
        private IContainer components;
        private System.Windows.Forms.Timer timer自动刷新;

        public 异常管理器()
        {
            InitializeComponent();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(异常管理器));
            this.panelTop = new System.Windows.Forms.Panel();
            this.label类型 = new System.Windows.Forms.Label();
            this.comboBox异常类型 = new System.Windows.Forms.ComboBox();
            this.label开始时间 = new System.Windows.Forms.Label();
            this.dateTimePicker开始 = new System.Windows.Forms.DateTimePicker();
            this.label结束时间 = new System.Windows.Forms.Label();
            this.dateTimePicker结束 = new System.Windows.Forms.DateTimePicker();
            this.label搜索 = new System.Windows.Forms.Label();
            this.textBox搜索 = new System.Windows.Forms.TextBox();
            this.button搜索 = new System.Windows.Forms.Button();
            this.button刷新 = new System.Windows.Forms.Button();
            this.button导出 = new System.Windows.Forms.Button();
            this.button清空统计 = new System.Windows.Forms.Button();
            this.label统计信息 = new System.Windows.Forms.Label();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.listView异常 = new System.Windows.Forms.ListView();
            this.richTextBox详情 = new System.Windows.Forms.RichTextBox();
            this.label详情 = new System.Windows.Forms.Label();
            this.timer自动刷新 = new System.Windows.Forms.Timer(this.components);
            this.panelTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelTop
            // 
            this.panelTop.BackColor = System.Drawing.Color.LightGray;
            this.panelTop.Controls.Add(this.label类型);
            this.panelTop.Controls.Add(this.comboBox异常类型);
            this.panelTop.Controls.Add(this.label开始时间);
            this.panelTop.Controls.Add(this.dateTimePicker开始);
            this.panelTop.Controls.Add(this.label结束时间);
            this.panelTop.Controls.Add(this.dateTimePicker结束);
            this.panelTop.Controls.Add(this.label搜索);
            this.panelTop.Controls.Add(this.textBox搜索);
            this.panelTop.Controls.Add(this.button搜索);
            this.panelTop.Controls.Add(this.button刷新);
            this.panelTop.Controls.Add(this.button导出);
            this.panelTop.Controls.Add(this.button清空统计);
            this.panelTop.Controls.Add(this.label统计信息);
            this.panelTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelTop.Location = new System.Drawing.Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new System.Drawing.Size(928, 80);
            this.panelTop.TabIndex = 1;
            // 
            // label类型
            // 
            this.label类型.Location = new System.Drawing.Point(10, 15);
            this.label类型.Name = "label类型";
            this.label类型.Size = new System.Drawing.Size(60, 20);
            this.label类型.TabIndex = 0;
            this.label类型.Text = "异常类型:";
            // 
            // comboBox异常类型
            // 
            this.comboBox异常类型.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox异常类型.Location = new System.Drawing.Point(75, 12);
            this.comboBox异常类型.Name = "comboBox异常类型";
            this.comboBox异常类型.Size = new System.Drawing.Size(120, 26);
            this.comboBox异常类型.TabIndex = 1;
            // 
            // label开始时间
            // 
            this.label开始时间.Location = new System.Drawing.Point(210, 15);
            this.label开始时间.Name = "label开始时间";
            this.label开始时间.Size = new System.Drawing.Size(60, 20);
            this.label开始时间.TabIndex = 2;
            this.label开始时间.Text = "开始时间:";
            // 
            // dateTimePicker开始
            // 
            this.dateTimePicker开始.Location = new System.Drawing.Point(275, 12);
            this.dateTimePicker开始.Name = "dateTimePicker开始";
            this.dateTimePicker开始.Size = new System.Drawing.Size(120, 28);
            this.dateTimePicker开始.TabIndex = 3;
            this.dateTimePicker开始.Value = new System.DateTime(2025, 6, 11, 23, 46, 34, 949);
            // 
            // label结束时间
            // 
            this.label结束时间.Location = new System.Drawing.Point(410, 15);
            this.label结束时间.Name = "label结束时间";
            this.label结束时间.Size = new System.Drawing.Size(60, 20);
            this.label结束时间.TabIndex = 4;
            this.label结束时间.Text = "结束时间:";
            // 
            // dateTimePicker结束
            // 
            this.dateTimePicker结束.Location = new System.Drawing.Point(475, 12);
            this.dateTimePicker结束.Name = "dateTimePicker结束";
            this.dateTimePicker结束.Size = new System.Drawing.Size(120, 28);
            this.dateTimePicker结束.TabIndex = 5;
            // 
            // label搜索
            // 
            this.label搜索.Location = new System.Drawing.Point(610, 15);
            this.label搜索.Name = "label搜索";
            this.label搜索.Size = new System.Drawing.Size(50, 20);
            this.label搜索.TabIndex = 6;
            this.label搜索.Text = "关键词:";
            // 
            // textBox搜索
            // 
            this.textBox搜索.ForeColor = System.Drawing.Color.Gray;
            this.textBox搜索.Location = new System.Drawing.Point(665, 12);
            this.textBox搜索.Name = "textBox搜索";
            this.textBox搜索.Size = new System.Drawing.Size(120, 28);
            this.textBox搜索.TabIndex = 7;
            this.textBox搜索.Text = "搜索异常信息...";
            // 
            // button搜索
            // 
            this.button搜索.Location = new System.Drawing.Point(800, 12);
            this.button搜索.Name = "button搜索";
            this.button搜索.Size = new System.Drawing.Size(60, 25);
            this.button搜索.TabIndex = 8;
            this.button搜索.Text = "搜索";
            // 
            // button刷新
            // 
            this.button刷新.Location = new System.Drawing.Point(10, 45);
            this.button刷新.Name = "button刷新";
            this.button刷新.Size = new System.Drawing.Size(60, 25);
            this.button刷新.TabIndex = 9;
            this.button刷新.Text = "刷新";
            // 
            // button导出
            // 
            this.button导出.Location = new System.Drawing.Point(80, 45);
            this.button导出.Name = "button导出";
            this.button导出.Size = new System.Drawing.Size(60, 25);
            this.button导出.TabIndex = 10;
            this.button导出.Text = "导出";
            // 
            // button清空统计
            // 
            this.button清空统计.Location = new System.Drawing.Point(150, 45);
            this.button清空统计.Name = "button清空统计";
            this.button清空统计.Size = new System.Drawing.Size(80, 25);
            this.button清空统计.TabIndex = 11;
            this.button清空统计.Text = "清空统计";
            // 
            // label统计信息
            // 
            this.label统计信息.ForeColor = System.Drawing.Color.DarkBlue;
            this.label统计信息.Location = new System.Drawing.Point(250, 47);
            this.label统计信息.Name = "label统计信息";
            this.label统计信息.Size = new System.Drawing.Size(650, 20);
            this.label统计信息.TabIndex = 12;
            this.label统计信息.Text = "异常统计信息将在这里显示";
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.Location = new System.Drawing.Point(0, 80);
            this.splitContainer.Name = "splitContainer";
            this.splitContainer.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.listView异常);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.richTextBox详情);
            this.splitContainer.Panel2.Controls.Add(this.label详情);
            this.splitContainer.Size = new System.Drawing.Size(928, 514);
            this.splitContainer.SplitterDistance = 364;
            this.splitContainer.TabIndex = 0;
            // 
            // listView异常
            // 
            this.listView异常.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView异常.FullRowSelect = true;
            this.listView异常.GridLines = true;
            this.listView异常.HideSelection = false;
            this.listView异常.Location = new System.Drawing.Point(0, 0);
            this.listView异常.MultiSelect = false;
            this.listView异常.Name = "listView异常";
            this.listView异常.Size = new System.Drawing.Size(928, 364);
            this.listView异常.TabIndex = 0;
            this.listView异常.UseCompatibleStateImageBehavior = false;
            this.listView异常.View = System.Windows.Forms.View.Details;
            // 
            // richTextBox详情
            // 
            this.richTextBox详情.BackColor = System.Drawing.Color.White;
            this.richTextBox详情.Dock = System.Windows.Forms.DockStyle.Fill;
            this.richTextBox详情.Font = new System.Drawing.Font("Consolas", 9F);
            this.richTextBox详情.Location = new System.Drawing.Point(0, 25);
            this.richTextBox详情.Name = "richTextBox详情";
            this.richTextBox详情.ReadOnly = true;
            this.richTextBox详情.Size = new System.Drawing.Size(928, 121);
            this.richTextBox详情.TabIndex = 0;
            this.richTextBox详情.Text = "";
            // 
            // label详情
            // 
            this.label详情.BackColor = System.Drawing.Color.LightBlue;
            this.label详情.Dock = System.Windows.Forms.DockStyle.Top;
            this.label详情.Location = new System.Drawing.Point(0, 0);
            this.label详情.Name = "label详情";
            this.label详情.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.label详情.Size = new System.Drawing.Size(928, 25);
            this.label详情.TabIndex = 1;
            this.label详情.Text = "异常详情:";
            this.label详情.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // timer自动刷新
            // 
            this.timer自动刷新.Enabled = true;
            this.timer自动刷新.Interval = 30000;
            // 
            // 异常管理器
            // 
            this.ClientSize = new System.Drawing.Size(928, 594);
            this.Controls.Add(this.splitContainer);
            this.Controls.Add(this.panelTop);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MinimumSize = new System.Drawing.Size(800, 500);
            this.Name = "异常管理器";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "异常管理器 - EIVAS";
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        // 初始化ListView列
        private void InitializeListViewColumns()
        {
            listView异常.Columns.Clear();
            listView异常.Columns.Add("发生时间", 150, HorizontalAlignment.Left);
            listView异常.Columns.Add("异常类型", 120, HorizontalAlignment.Left);
            listView异常.Columns.Add("操作", 100, HorizontalAlignment.Left);
            listView异常.Columns.Add("玩家", 80, HorizontalAlignment.Left);
            listView异常.Columns.Add("异常消息", 300, HorizontalAlignment.Left);
            listView异常.Columns.Add("次数", 50, HorizontalAlignment.Center);
            listView异常.Columns.Add("最后发生", 150, HorizontalAlignment.Left);
            
            // 绑定选择事件
            listView异常.SelectedIndexChanged += ListView异常_SelectedIndexChanged;
            
            // 绑定按钮事件
            button搜索.Click += Button搜索_Click;
            button刷新.Click += Button刷新_Click;
            button导出.Click += Button导出_Click;
            button清空统计.Click += Button清空统计_Click;
            timer自动刷新.Tick += Timer自动刷新_Tick;
            
            // 绑定搜索框事件
            textBox搜索.Enter += TextBox搜索_Enter;
            textBox搜索.Leave += TextBox搜索_Leave;
        }

        private void InitializeData()
        {
            // 初始化ListView列
            InitializeListViewColumns();
            
            // 初始化异常类型下拉框
            comboBox异常类型.Items.Add("全部");
            comboBox异常类型.Items.Add("游戏逻辑异常");
            comboBox异常类型.Items.Add("数据库异常");
            comboBox异常类型.Items.Add("网络异常");
            comboBox异常类型.Items.Add("界面操作异常");
            comboBox异常类型.Items.Add("系统功能异常");
            comboBox异常类型.Items.Add("定时器异常");
            comboBox异常类型.Items.Add("参数异常");
            comboBox异常类型.Items.Add("空引用异常");
            comboBox异常类型.Items.Add("无效操作异常");
            comboBox异常类型.Items.Add("SQL异常");
            comboBox异常类型.Items.Add("网络连接异常");
            comboBox异常类型.SelectedIndex = 0;

            // 确保异常统计功能已初始化
            try
            {
                RxjhClass.InitializeExceptionStatistics();
            }
            catch (Exception ex)
            {
                richTextBox详情.Text = $"初始化异常统计功能失败: {ex.Message}";
            }

            // 加载异常数据
            LoadExceptionData();
        }

        private void LoadExceptionData()
        {
            try
            {
                listView异常.Items.Clear();
                
                // 获取异常统计数据
                var stats = RxjhClass.GetExceptionStatistics();
                label统计信息.Text = stats.Replace("\n", " | ");

                // 这里可以扩展为从日志文件或数据库加载详细异常数据
                // 目前先显示统计信息
                LoadExceptionFromLogs();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载异常数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadExceptionFromLogs()
        {
            try
            {
                string logsPath = "logs";
                if (!Directory.Exists(logsPath))
                {
                    richTextBox详情.Text = "logs文件夹不存在，请检查日志配置。";
                    return;
                }

                // 读取错误日志文件
                string[] logFiles = Directory.GetFiles(logsPath, "错误日志_*.log");

                if (logFiles.Length == 0)
                {
                    richTextBox详情.Text = "未找到错误日志文件。";
                    return;
                }

                List<ExceptionInfo> exceptions = new List<ExceptionInfo>();

                foreach (string logFile in logFiles.OrderByDescending(f => f))
                {
                    if (exceptions.Count >= 500) break; // 限制显示数量

                    try
                    {
                        string[] lines = File.ReadAllLines(logFile, Encoding.UTF8);

                        foreach (string line in lines.Reverse().Take(200)) // 每个文件最多读取200行
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;

                            var exInfo = ParseLogLine(line, Path.GetFileName(logFile));
                            if (exInfo != null)
                            {
                                exceptions.Add(exInfo);
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // 忽略单个文件读取错误
                        continue;
                    }
                }

                // 显示异常数据
                DisplayExceptions(exceptions);
            }
            catch (Exception ex)
            {
                richTextBox详情.Text = $"读取日志文件失败: {ex.Message}";
            }
        }

        private ExceptionInfo ParseLogLine(string line, string fileName)
        {
            try
            {
                // 解析包含"异常详情"的行
                if (!line.Contains("异常") && !line.Contains("Exception") && !line.Contains("错误"))
                {
                    return null;
                }

                var info = new ExceptionInfo();

                // 尝试提取时间戳
                if (line.Length > 19 && char.IsDigit(line[0]))
                {
                    string timeStr = line.Substring(0, 19);
                    if (DateTime.TryParse(timeStr, out DateTime time))
                    {
                        info.Time = time;
                    }
                }

                // 提取异常类型
                if (line.Contains("Exception"))
                {
                    int exIndex = line.IndexOf("Exception");
                    int startIndex = line.LastIndexOf(' ', exIndex - 1);
                    if (startIndex > 0 && exIndex + 9 < line.Length)
                    {
                        info.ExceptionType = line.Substring(startIndex + 1, exIndex + 9 - startIndex - 1);
                    }
                }

                // 提取操作信息
                if (line.Contains("操作:"))
                {
                    int opIndex = line.IndexOf("操作:");
                    int endIndex = line.IndexOf(",", opIndex);
                    if (endIndex == -1) endIndex = line.IndexOf("\n", opIndex);
                    if (endIndex == -1) endIndex = line.Length;

                    if (opIndex + 3 < endIndex)
                    {
                        info.Operation = line.Substring(opIndex + 3, endIndex - opIndex - 3).Trim();
                    }
                }

                // 提取玩家信息
                if (line.Contains("玩家:"))
                {
                    int playerIndex = line.IndexOf("玩家:");
                    int endIndex = line.IndexOf(",", playerIndex);
                    if (endIndex == -1) endIndex = line.IndexOf("\n", playerIndex);
                    if (endIndex == -1) endIndex = line.Length;

                    if (playerIndex + 3 < endIndex)
                    {
                        info.Player = line.Substring(playerIndex + 3, endIndex - playerIndex - 3).Trim();
                    }
                }

                info.Message = line.Length > 200 ? line.Substring(0, 200) + "..." : line;
                info.FullMessage = line;
                info.FileName = fileName;

                return info;
            }
            catch
            {
                return null;
            }
        }

        // 将英文异常类型转换为中文
        private string ConvertExceptionTypeToChinese(string exceptionType)
        {
            if (string.IsNullOrEmpty(exceptionType))
                return "未知异常";

            // 根据异常类型转换为中文
            if (exceptionType.Contains("ArgumentException"))
                return "参数异常";
            else if (exceptionType.Contains("NullReferenceException"))
                return "空引用异常";
            else if (exceptionType.Contains("InvalidOperationException"))
                return "无效操作异常";
            else if (exceptionType.Contains("SqlException"))
                return "SQL异常";
            else if (exceptionType.Contains("SocketException"))
                return "网络连接异常";
            else if (exceptionType.Contains("TimeoutException"))
                return "超时异常";
            else if (exceptionType.Contains("OutOfMemoryException"))
                return "内存不足异常";
            else if (exceptionType.Contains("StackOverflowException"))
                return "堆栈溢出异常";
            else if (exceptionType.Contains("AccessViolationException"))
                return "访问冲突异常";
            else if (exceptionType.Contains("FileNotFoundException"))
                return "文件未找到异常";
            else if (exceptionType.Contains("DirectoryNotFoundException"))
                return "目录未找到异常";
            else if (exceptionType.Contains("UnauthorizedAccessException"))
                return "未授权访问异常";
            else if (exceptionType.Contains("FormatException"))
                return "格式异常";
            else if (exceptionType.Contains("OverflowException"))
                return "溢出异常";
            else if (exceptionType.Contains("DivideByZeroException"))
                return "除零异常";
            else if (exceptionType.Contains("IndexOutOfRangeException"))
                return "索引超出范围异常";
            else if (exceptionType.Contains("KeyNotFoundException"))
                return "键未找到异常";
            else if (exceptionType.Contains("NotSupportedException"))
                return "不支持操作异常";
            else if (exceptionType.Contains("NotImplementedException"))
                return "未实现异常";
            else if (exceptionType.Contains("ObjectDisposedException"))
                return "对象已释放异常";
            else if (exceptionType.Contains("Exception"))
                return "系统异常";
            
            // 如果包含我们自定义的异常前缀，直接返回中文部分
            if (exceptionType.Contains("游戏逻辑异常") || exceptionType.Contains("数据库异常") || 
                exceptionType.Contains("网络异常") || exceptionType.Contains("界面操作异常") ||
                exceptionType.Contains("系统功能异常") || exceptionType.Contains("定时器异常"))
            {
                return exceptionType;
            }

            return exceptionType; // 如果无法识别，返回原始类型
        }

        private void DisplayExceptions(List<ExceptionInfo> exceptions)
        {
            listView异常.BeginUpdate();
            listView异常.Items.Clear();

            foreach (var ex in exceptions.Take(1000)) // 最多显示1000条
            {
                ListViewItem item = new ListViewItem(ex.Time.ToString("yyyy-MM-dd HH:mm:ss"));
                item.SubItems.Add(ConvertExceptionTypeToChinese(ex.ExceptionType));
                item.SubItems.Add(ex.Operation ?? "未知");
                item.SubItems.Add(ex.Player ?? "未知");
                item.SubItems.Add(ex.Message ?? "");
                item.SubItems.Add("1");
                item.SubItems.Add(ex.Time.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = ex;

                // 根据异常类型设置颜色
                if (ex.ExceptionType?.Contains("Sql") == true)
                {
                    item.BackColor = Color.LightPink;
                }
                else if (ex.ExceptionType?.Contains("Socket") == true)
                {
                    item.BackColor = Color.LightYellow;
                }
                else if (ex.ExceptionType?.Contains("NullReference") == true)
                {
                    item.BackColor = Color.LightCoral;
                }

                listView异常.Items.Add(item);
            }

            listView异常.EndUpdate();
        }

        // 事件处理方法
        private void ListView异常_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listView异常.SelectedItems.Count > 0)
            {
                var selectedItem = listView异常.SelectedItems[0];
                var exInfo = selectedItem.Tag as ExceptionInfo;

                if (exInfo != null)
                {
                    richTextBox详情.Text = $"文件: {exInfo.FileName}\n" +
                                          $"时间: {exInfo.Time:yyyy-MM-dd HH:mm:ss}\n" +
                                          $"异常类型: {exInfo.ExceptionType}\n" +
                                          $"操作: {exInfo.Operation}\n" +
                                          $"玩家: {exInfo.Player}\n" +
                                          $"完整信息:\n{exInfo.FullMessage}";
                }
            }
        }

        private void Button搜索_Click(object sender, EventArgs e)
        {
            LoadFilteredExceptionData();
        }

        private void LoadFilteredExceptionData()
        {
            try
            {
                listView异常.Items.Clear();

                // 获取筛选条件
                string selectedType = comboBox异常类型.SelectedItem?.ToString();
                string searchKeyword = textBox搜索.Text.Trim();
                // 如果是提示文本，则视为空搜索
                if (searchKeyword == "搜索异常信息..." || textBox搜索.ForeColor == Color.Gray)
                {
                    searchKeyword = "";
                }
                DateTime startDate = dateTimePicker开始.Value.Date;
                DateTime endDate = dateTimePicker结束.Value.Date.AddDays(1);

                // 从日志文件加载异常数据
                LoadFilteredExceptionFromLogs(selectedType, searchKeyword, startDate, endDate);

                // 更新统计信息
                var stats = RxjhClass.GetExceptionStatistics();
                label统计信息.Text = $"筛选结果: {listView异常.Items.Count} 条记录 | " + stats.Replace("\n", " | ");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索异常数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadFilteredExceptionFromLogs(string filterType, string keyword, DateTime startDate, DateTime endDate)
        {
            try
            {
                string logsPath = "logs";
                if (!Directory.Exists(logsPath))
                {
                    richTextBox详情.Text = "logs文件夹不存在，请检查日志配置。";
                    return;
                }

                string[] logFiles = Directory.GetFiles(logsPath, "错误日志_*.log");
                List<ExceptionInfo> exceptions = new List<ExceptionInfo>();

                foreach (string logFile in logFiles.OrderByDescending(f => f))
                {
                    if (exceptions.Count >= 1000) break;

                    try
                    {
                        // 检查文件日期是否在范围内
                        FileInfo fileInfo = new FileInfo(logFile);
                        if (fileInfo.CreationTime < startDate || fileInfo.CreationTime > endDate)
                        {
                            continue;
                        }

                        string[] lines = File.ReadAllLines(logFile, Encoding.UTF8);

                        foreach (string line in lines.Reverse().Take(500))
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;

                            var exInfo = ParseLogLine(line, Path.GetFileName(logFile));
                            if (exInfo != null)
                            {
                                // 应用筛选条件
                                bool matchesFilter = true;

                                // 时间筛选
                                if (exInfo.Time < startDate || exInfo.Time > endDate)
                                {
                                    matchesFilter = false;
                                }

                                // 类型筛选
                                if (matchesFilter && filterType != "全部" && !string.IsNullOrEmpty(filterType))
                                {
                                    if (exInfo.ExceptionType?.Contains(filterType.Replace("异常", "")) != true)
                                    {
                                        matchesFilter = false;
                                    }
                                }

                                // 关键词筛选
                                if (matchesFilter && !string.IsNullOrEmpty(keyword))
                                {
                                    if (exInfo.FullMessage?.Contains(keyword) != true &&
                                        exInfo.Player?.Contains(keyword) != true &&
                                        exInfo.Operation?.Contains(keyword) != true)
                                    {
                                        matchesFilter = false;
                                    }
                                }

                                if (matchesFilter)
                                {
                                    exceptions.Add(exInfo);
                                }
                            }
                        }
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }

                DisplayExceptions(exceptions);
            }
            catch (Exception ex)
            {
                richTextBox详情.Text = $"筛选日志文件失败: {ex.Message}";
            }
        }

        private void Button刷新_Click(object sender, EventArgs e)
        {
            LoadExceptionData();
        }

        private void Button导出_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "CSV文件|*.csv|文本文件|*.txt";
                saveDialog.FileName = $"异常报告_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendLine("时间,异常类型,操作,玩家,异常消息");

                    foreach (ListViewItem item in listView异常.Items)
                    {
                        var exInfo = item.Tag as ExceptionInfo;
                        if (exInfo != null)
                        {
                            sb.AppendLine($"{exInfo.Time:yyyy-MM-dd HH:mm:ss},{exInfo.ExceptionType},{exInfo.Operation},{exInfo.Player},\"{exInfo.Message}\"");
                        }
                    }

                    File.WriteAllText(saveDialog.FileName, sb.ToString(), Encoding.UTF8);
                    MessageBox.Show($"异常报告已导出到: {saveDialog.FileName}", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Button清空统计_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要清空异常统计数据吗？此操作不可恢复！\n\n注意：这将删除所有错误日志文件！",
                "确认清空", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    // 清空异常统计数据
                    ClearExceptionStatistics();
                    
                    MessageBox.Show("异常统计已清空", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadExceptionData();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"清空统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        // 清空异常统计数据
        private void ClearExceptionStatistics()
        {
            try
            {
                // 清空内存中的异常统计数据
                RxjhServer.DbClss.RxjhClass.ClearExceptionStatistics();

                // 清空日志文件
                string logsPath = "logs";
                if (Directory.Exists(logsPath))
                {
                    // 删除所有错误日志文件
                    string[] logFiles = Directory.GetFiles(logsPath, "错误日志_*.log");
                    int deletedCount = 0;
                    int failedCount = 0;

                    foreach (string logFile in logFiles)
                    {
                        try
                        {
                            File.Delete(logFile);
                            deletedCount++;
                        }
                        catch (Exception ex)
                        {
                            failedCount++;
                            // 记录无法删除的文件，但继续处理其他文件
                            richTextBox详情.AppendText($"无法删除文件 {Path.GetFileName(logFile)}: {ex.Message}\n");
                        }
                    }

                    if (deletedCount > 0)
                    {
                        richTextBox详情.AppendText($"成功删除 {deletedCount} 个日志文件\n");
                    }
                    if (failedCount > 0)
                    {
                        richTextBox详情.AppendText($"删除失败 {failedCount} 个日志文件\n");
                    }
                }

                // 清空ListView显示
                listView异常.Items.Clear();
                
                // 重置统计信息显示
                label统计信息.Text = "异常统计信息已清空";
            }
            catch (Exception ex)
            {
                throw new Exception($"清空异常统计时发生错误: {ex.Message}");
            }
        }

        private void Timer自动刷新_Tick(object sender, EventArgs e)
        {
            // 自动刷新数据
            LoadExceptionData();
        }

        // 搜索框提示文本处理
        private void TextBox搜索_Enter(object sender, EventArgs e)
        {
            if (textBox搜索.Text == "搜索异常信息..." && textBox搜索.ForeColor == Color.Gray)
            {
                textBox搜索.Text = "";
                textBox搜索.ForeColor = Color.Black;
            }
        }

        private void TextBox搜索_Leave(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(textBox搜索.Text))
            {
                textBox搜索.Text = "搜索异常信息...";
                textBox搜索.ForeColor = Color.Gray;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timer自动刷新?.Stop();
                timer自动刷新?.Dispose();
            }
            base.Dispose(disposing);
        }

        // 异常信息类
        private class ExceptionInfo
        {
            public DateTime Time { get; set; } = DateTime.Now;
            public string ExceptionType { get; set; }
            public string Operation { get; set; }
            public string Player { get; set; }
            public string Message { get; set; }
            public string FullMessage { get; set; }
            public string FileName { get; set; }
        }
    }
}

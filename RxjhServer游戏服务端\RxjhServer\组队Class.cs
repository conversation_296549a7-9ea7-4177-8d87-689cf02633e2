using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class 组队Class : IDisposable
{
	public List<Players> tem = new List<Players>();

	public ConcurrentDictionary<int, Players> 组队列表;

	private ConcurrentDictionary<string, 离线队员> 离线队员列表;

	public string 队长;

	public int 组队id;

	public int 队伍级别;

	public int 限制职业一;

	public int 限制职业二;

	public bool 红包;

	public int 红包时间;

	public int 道具分配规则;

	public int 当前分配;

	public Players 邀请人;

	public Players 离线队长;

	public System.Timers.Timer 自动显示;

	private int 计数;

	public 组队Class(Players 队长_)
	{
		自动显示 = new System.Timers.Timer(2000.0);
		自动显示.Elapsed += 自动显示事件;
		自动显示.AutoReset = true;
		道具分配规则 = 0;
		当前分配 = 0;
		红包 = false;
		红包时间 = 0;
		队长 = 队长_.UserName;
		队伍级别 = 队长_.Player_Level;
		组队列表 = new ConcurrentDictionary<int, Players>();
		组队列表.TryAdd(队长_.人物全服ID, 队长_);
		离线队员列表 = new ConcurrentDictionary<string, 离线队员>();
		离线队长 = 队长_;
	}

	public void Dispose()
	{
		组队Class value;
		try
		{
			if (离线队员列表 != null)
			{
				离线队员列表.Clear();
			}
			if (World.W组队.ContainsKey(组队id))
			{
				World.W组队.TryRemove(组队id, out value);
			}
			if (组队列表 != null)
			{
				foreach (Players value2 in 组队列表.Values)
				{
					value2.解散组队提示();
					value2.组队id = 0;
					value2.组队阶段 = 0;
					value2.夫妻组队中 = false;
					value2.FLD_方天灵盾组队增加百分比 = 0.0;
					value2.FLD_太极神丹组队增加百分比 = 0.0;
					value2.FLD_囧组队增加百分比 = 0.0;
					value2.更新武功和状态();
				}
				组队列表.Clear();
			}
			if (自动显示 != null)
			{
				自动显示.Enabled = false;
				自动显示.Close();
				自动显示.Dispose();
				自动显示 = null;
			}
			邀请人 = null;
			tem = null;
			红包 = false;
			红包时间 = 0;
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "组队系统", "Dispose", "组队资源清理");
			Form1.WriteLine(1, "组队类 Dispose 出错!" + ex.Message);
		}
		finally
		{
			if (tem != null)
			{
				tem.Clear();
				tem = null;
			}
			if (组队列表 != null)
			{
				组队列表.Clear();
				组队列表 = null;
			}
			if (离线队员列表 != null)
			{
				离线队员列表.Clear();
				离线队员列表 = null;
			}
			if (World.W组队.ContainsKey(组队id))
			{
				World.W组队.TryRemove(组队id, out value);
			}
			if (自动显示 != null)
			{
				自动显示.Enabled = false;
				自动显示.Close();
				自动显示.Dispose();
				自动显示 = null;
			}
			tem = null;
			组队列表 = null;
			红包 = false;
			红包时间 = 0;
		}
	}

	private void 自动显示事件(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "组队Class-自动显示事件");
		}
		try
		{
			if (组队列表.Count <= 1)
			{
				Dispose();
				return;
			}
			计数++;
			if (计数 >= 10)
			{
				计数 = 0;
				if (离线队员列表 != null && 离线队员列表.Count > 0)
				{
					List<string> list = new List<string>();
					foreach (离线队员 value4 in 离线队员列表.Values)
					{
						Players players = World.检查玩家name(value4.UserName);
						if (players == null)
						{
							continue;
						}
						if (value4.组队id == 组队id)
						{
							if (离线队长.玩家是否在指定范围内(1000, players))
							{
								if (players.组队阶段 == 0 && 组队列表.Count < 8 && players.组队id == 0)
								{
									byte[] array = Converter.hexStringToByte("AA5528002C0130000600010001002D010000000000000000000000000000000000000000000000000000000055AA");
									Buffer.BlockCopy(BitConverter.GetBytes(离线队长.人物全服ID), 0, array, 4, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 14, 2);
									离线队长.发送组队(array, array.Length);
								}
								else if (players.组队阶段 == 1)
								{
									byte[] array2 = Converter.hexStringToByte("AA5512002C013200040001002C01000000000000000055AA");
									Buffer.BlockCopy(BitConverter.GetBytes(离线队长.人物全服ID), 0, array2, 4, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(离线队长.人物全服ID), 0, array2, 12, 2);
									离线队长.本人取消组队(array2, array2.Length);
								}
								else if (players.组队阶段 == 2 && !list.Contains(players.Userid))
								{
									list.Add(players.Userid);
								}
							}
						}
						else if (!list.Contains(players.Userid))
						{
							list.Add(players.Userid);
						}
					}
					if (list.Count > 0)
					{
						foreach (string item in list)
						{
							if (离线队员列表.ContainsKey(item))
							{
								离线队员列表.TryRemove(item, out var _);
							}
						}
					}
					list.Clear();
				}
			}
			if (红包)
			{
				红包时间 -= 3000;
				if (红包时间 <= 0)
				{
					红包 = false;
					红包时间 = 0;
				}
			}
			else
			{
				红包 = false;
				红包时间 = 0;
			}
			Players value2;
			if (组队列表 != null)
			{
				foreach (Players value5 in 组队列表.Values)
				{
					if (World.allConnectedChars.TryGetValue(value5.人物全服ID, out value2))
					{
						if (value5.组队id == 0)
						{
							退出(value5, 0);
						}
						value5.显示队员();
						if (红包 && 红包时间 > 0)
						{
							if (value5.追加状态列表 != null && !value5.GetAddState(1000000050))
							{
								追加状态类 追加状态类2 = new 追加状态类(value5, 红包时间, 1000000050, 0);
								value5.追加状态列表.TryAdd(追加状态类2.FLD_PID, 追加状态类2);
								value5.状态效果(BitConverter.GetBytes(1000000050), 1, 红包时间);
							}
						}
						else if (value5.追加状态列表 != null && value5.GetAddState(1000000050))
						{
							value5.追加状态列表[1000000050].时间结束事件();
						}
					}
					else if (tem != null && !tem.Contains(value5))
					{
						tem.Add(value5);
					}
				}
			}
			if (tem != null)
			{
				foreach (Players item2 in tem)
				{
					if (组队列表 != null && 组队列表.TryGetValue(item2.人物全服ID, out value2))
					{
						组队列表.TryRemove(item2.人物全服ID, out var _);
						item2.组队id = 0;
						item2.组队阶段 = 0;
					}
				}
			}
			if (tem.Count > 0)
			{
				tem.Clear();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "组队类 自动显示事件 出错!" + ex.Message);
		}
	}

	private void 自动显示事件2(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (组队列表.Count <= 1)
			{
				Dispose();
				return;
			}
			foreach (Players item in tem)
			{
				组队列表.TryRemove(item.人物全服ID, out var _);
			}
			if (tem.Count > 0)
			{
				tem.Clear();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "组队类 自动显示事件 出错!" + ex.Message);
		}
	}

	public void 委任队长(Players 本人, Players 队长类)
	{
		try
		{
			队长 = 队长类.UserName;
			离线队长 = 队长类;
			foreach (Players value in 组队列表.Values)
			{
				value.委任队长提示(本人, 队长类);
				value.显示队员();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "组队类 委任队长 出错!" + ex.Message);
		}
	}

	public void 加入队员提示(Players 队员)
	{
		try
		{
			计算方天灵盾组队加成();
			计算太极神丹组队加成();
			计算囧组队加成();
			if (队员.FLD_情侣 != "")
			{
				foreach (Players value2 in 组队列表.Values)
				{
					if (value2.UserName == 队员.FLD_情侣)
					{
						队员.夫妻组队中 = true;
						value2.夫妻组队中 = true;
						break;
					}
				}
			}
			foreach (Players value3 in 组队列表.Values)
			{
				if (队员 != value3)
				{
					value3.加入组队提示(队员);
					队员.加入组队提示(value3);
				}
				value3.显示队员();
			}
			if (组队列表.Count >= 2)
			{
				自动显示.Enabled = true;
			}
			if (离线队员列表.ContainsKey(队员.Userid))
			{
				离线队员列表.TryRemove(队员.Userid, out var _);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "组队类 加入队员提示 出错!" + ex.Message);
		}
	}

	public void 计算方天灵盾组队加成()
	{
		foreach (Players value in 组队列表.Values)
		{
			value.FLD_方天灵盾组队增加百分比 = 0.0;
		}
		double num = 0.0;
		foreach (Players value2 in 组队列表.Values)
		{
			if (value2.时间药品 != null && value2.时间药品.ContainsKey(1008000195))
			{
				num += 0.01;
			}
		}
		if (num > 0.0)
		{
			num -= 0.01;
		}
		if (num < 0.0)
		{
			num = 0.0;
		}
		if (num > 0.07)
		{
			num = 0.07;
		}
		foreach (Players value3 in 组队列表.Values)
		{
			if (value3.时间药品 != null && value3.时间药品.ContainsKey(1008000195))
			{
				value3.FLD_方天灵盾组队增加百分比 = num;
				value3.更新武功和状态();
			}
		}
	}

	public void 计算太极神丹组队加成()
	{
		foreach (Players value in 组队列表.Values)
		{
			value.FLD_太极神丹组队增加百分比 = 0.0;
		}
		double num = 0.0;
		foreach (Players value2 in 组队列表.Values)
		{
			if (value2.追加状态列表 != null && value2.追加状态列表.ContainsKey(1008000188))
			{
				num += 0.01;
			}
		}
		if (num > 0.0)
		{
			num -= 0.01;
		}
		if (num < 0.0)
		{
			num = 0.0;
		}
		if (num > 0.07)
		{
			num = 0.07;
		}
		foreach (Players value3 in 组队列表.Values)
		{
			if (value3.追加状态列表 != null && value3.追加状态列表.ContainsKey(1008000188))
			{
				value3.FLD_太极神丹组队增加百分比 = num;
				value3.更新武功和状态();
			}
		}
	}

	public void 计算囧组队加成()
	{
		foreach (Players value in 组队列表.Values)
		{
			value.FLD_囧组队增加百分比 = 0.0;
		}
		double num = 0.0;
		foreach (Players value2 in 组队列表.Values)
		{
			if (value2.追加状态列表 != null && value2.追加状态列表.ContainsKey(1008000243))
			{
				num += 0.01;
			}
		}
		if (num > 0.0)
		{
			num -= 0.01;
		}
		if (num < 0.0)
		{
			num = 0.0;
		}
		if (num > 0.07)
		{
			num = 0.07;
		}
		foreach (Players value3 in 组队列表.Values)
		{
			if (value3.追加状态列表 != null && value3.追加状态列表.ContainsKey(1008000243))
			{
				value3.FLD_囧组队增加百分比 = num;
				value3.更新武功和状态();
			}
		}
	}

	public void 退出(Players 队员, int 退出ID)
	{
		try
		{
			队员.FLD_方天灵盾组队增加百分比 = 0.0;
			队员.FLD_太极神丹组队增加百分比 = 0.0;
			队员.FLD_囧组队增加百分比 = 0.0;
			队员.更新武功和状态();
			if (组队列表 != null && 组队列表.ContainsKey(队员.人物全服ID))
			{
				组队列表.TryRemove(队员.人物全服ID, out var _);
				if (退出ID == 1 && 组队列表.Count >= 2 && !离线队员列表.ContainsKey(队员.Userid))
				{
					离线队员列表.TryAdd(队员.Userid, new 离线队员
					{
						组队id = 队员.组队id,
						UserName = 队员.UserName
					});
				}
			}
			if (队员.GetAddState(1000000050))
			{
				队员.追加状态列表[1000000050].时间结束事件();
			}
			if (队员.FLD_情侣 != "")
			{
				队员.夫妻组队中 = false;
				foreach (Players value2 in 组队列表.Values)
				{
					if (value2.UserName == 队员.FLD_情侣)
					{
						value2.夫妻组队中 = false;
						break;
					}
				}
			}
			if (组队列表.Count >= 2)
			{
				if (队长 != 队员.UserName)
				{
					foreach (Players value3 in 组队列表.Values)
					{
						value3.退出组队提示(队员);
						value3.显示队员();
					}
				}
				else
				{
					Players toPlaye = 队员;
					bool flag = true;
					foreach (Players value4 in 组队列表.Values)
					{
						if (flag)
						{
							队长 = value4.UserName;
							toPlaye = value4;
							flag = false;
						}
						value4.委任队长提示(队员, toPlaye);
						value4.退出组队提示(队员);
						value4.显示队员();
					}
				}
				计算方天灵盾组队加成();
				计算太极神丹组队加成();
				计算囧组队加成();
			}
			else
			{
				Dispose();
			}
			队员.本人退出组队提示();
			队员.组队id = 0;
			队员.组队阶段 = 0;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "组队类退出出错!" + ex.Message);
		}
		finally
		{
			队员.组队id = 0;
			队员.组队阶段 = 0;
		}
	}

	public Players 得到对应队员(int key)
	{
		try
		{
			int num = 0;
			foreach (Players value in 组队列表.Values)
			{
				if (key == num)
				{
					return value;
				}
				num++;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "获取对应队员 出错!" + ex.Message);
		}
		return null;
	}
}

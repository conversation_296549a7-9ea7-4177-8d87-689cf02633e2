using System;
using System.Net.Sockets;

namespace loginServer;

public class ATSockClient : IDisposable
{
	public Socket clientSocket;

	private ATRemoveClientDelegate removeFromTheServerList;

	private byte[] dataReceive;

	private bool disposed;

	public ATSockClient(Socket from, ATRemoveClientDelegate rftsl)
	{
		dataReceive = new byte[1024 * World.缓冲区倍数];
		disposed = false;
		removeFromTheServerList = rftsl;
		clientSocket = from;
	}

	public void Dispose()
	{
		if (!disposed)
		{
			disposed = true;
			try
			{
				clientSocket.Shutdown(SocketShutdown.Both);
			}
			catch
			{
			}
			if (clientSocket != null)
			{
				clientSocket.Close();
			}
			clientSocket = null;
			if (removeFromTheServerList != null)
			{
				removeFromTheServerList(this);
			}
		}
	}

	public virtual void OnReceiveData(IAsyncResult ar)
	{
		try
		{
			if (disposed)
			{
				return;
			}
			int num = clientSocket.EndReceive(ar);
			if (num <= 0)
			{
				Dispose();
				return;
			}
			byte[] array = ((World.aaaaaa != 1) ? DataReceived2(dataReceive, num) : ProcessDataReceived(dataReceive, num));
			if (array != null)
			{
				clientSocket.BeginSend(array, 0, array.Length, SocketFlags.None, OnSended, this);
			}
			else
			{
				clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
			}
		}
		catch (Exception ex)
		{
			Console.WriteLine("{0}", ex.Message);
			Dispose();
		}
	}

	public void OnSended(IAsyncResult ar)
	{
		try
		{
			if (!disposed)
			{
				clientSocket.EndSend(ar);
				clientSocket.BeginReceive((ar.AsyncState as ATSockClient).dataReceive, 0, (ar.AsyncState as ATSockClient).dataReceive.Length, SocketFlags.None, OnReceiveData, ar.AsyncState);
			}
		}
		catch
		{
		}
	}

	public virtual byte[] ProcessDataReceived(byte[] data, int length)
	{
		return null;
	}

	public virtual byte[] DataReceived2(byte[] data, int length)
	{
		return null;
	}

	public virtual void Sendbyte(byte[] toSendBuff, int len)
	{
		try
		{
			byte[] array = new byte[len + 6];
			array[0] = 170;
			array[1] = 102;
			Buffer.BlockCopy(BitConverter.GetBytes(len), 0, array, 2, 4);
			Buffer.BlockCopy(toSendBuff, 0, array, 6, len);
			if (clientSocket != null)
			{
				clientSocket.BeginSend(array, 0, len + 6, SocketFlags.None, OnSended2, this);
			}
		}
		catch (SocketException ex)
		{
			Form1.WriteLine(1, "AT帐号服务器 发送攻击确认包出错：" + ex.Message);
		}
	}

	public void OnSended2(IAsyncResult ar)
	{
		try
		{
			if (!disposed)
			{
				clientSocket.EndSend(ar);
			}
		}
		catch (Exception)
		{
		}
	}

	public void Start()
	{
		clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
	}
}

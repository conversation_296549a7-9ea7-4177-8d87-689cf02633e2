using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class ItemEdit : Form
{
	private IContainer components = null;

	private Label label28;

	private Label label27;

	private GroupBox groupBox2;

	private Label label20;

	private Button button1;

	private TextBox textBox14;

	private TextBox textBox21;

	private TextBox textBox22;

	private TextBox textBox23;

	private TextBox textBox24;

	private TextBox textBox25;

	private TextBox textBox26;

	private TextBox textBox13;

	private TextBox textBox12;

	private TextBox textBox11;

	private TextBox textBox10;

	private TextBox textBox9;

	private TextBox textBox8;

	private TextBox textBox7;

	private TextBox textBox6;

	private TextBox textBox5;

	private TextBox textBox4;

	private TextBox textBox3;

	private TextBox textBox2;

	private TextBox textBox1;

	private Label label25;

	private Label label19;

	private Label label18;

	private Label label17;

	private Label label16;

	private Label label15;

	private Label label14;

	private Label label13;

	private Label label12;

	private Label label11;

	private Label label10;

	private Label label9;

	private Label label8;

	private Label label7;

	private Label label6;

	private Label label5;

	private Label label4;

	private Label label3;

	private Label label2;

	private Label label1;

	private GroupBox groupBox1;

	private ListBox listBox1;

	private GroupBox groupBox3;

	private ComboBox comboBox1;

	private Label label22;

	private Button button2;

	private Button button4;

	private Label label40;

	private ComboBox comboBoxITEMTYPE;

	private TextBox textBox30;

	private TextBox textBox29;

	private Label label33;

	private Label label32;

	private TextBox textBox28;

	private TextBox textBox27;

	private TextBox textBox20;

	private TextBox textBox19;

	private TextBox textBox18;

	private TextBox textBox17;

	private TextBox textBox16;

	private Label label31;

	private Label label30;

	private Label label29;

	private Label label26;

	private Label label24;

	private Label label23;

	private Label label21;

	private TextBox textBox15;

	public ItemEdit()
	{
		InitializeComponent();
		label28.Text = "0";
	}

	private void 刷新()
	{
		try
		{
			listBox1.Items.Clear();
			string sqlCommand = "select FLD_PID,FLD_NAME from TBL_XWWL_ITEM";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
				listBox1.Items.Add(keyValuePair);
			}
			label28.Text = dBToDataTable.Rows.Count.ToString();
			dBToDataTable.Dispose();
		}
		catch
		{
		}
	}

	private void listBox1_SelectedValueChanged(object sender, EventArgs e)
	{
		KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBox1.SelectedItem;
		textBox1.Text = keyValuePair.Key;
		textBox3.Text = keyValuePair.Value;
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_PID='" + textBox1.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable.Rows.Count > 0)
		{
			textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
			textBox2.Text = dBToDataTable.Rows[0]["FLD_QUESTITEM"].ToString();
			textBox3.Text = dBToDataTable.Rows[0]["FLD_Name"].ToString();
			textBox4.Text = dBToDataTable.Rows[0]["FLD_NJ"].ToString();
			textBox5.Text = dBToDataTable.Rows[0]["FLD_RESIDE1"].ToString();
			textBox6.Text = dBToDataTable.Rows[0]["FLD_RESIDE2"].ToString();
			textBox7.Text = dBToDataTable.Rows[0]["FLD_SEX"].ToString();
			textBox8.Text = dBToDataTable.Rows[0]["FLD_DF"].ToString();
			textBox9.Text = dBToDataTable.Rows[0]["FLD_AT1"].ToString();
			textBox10.Text = dBToDataTable.Rows[0]["FLD_AT2"].ToString();
			textBox11.Text = dBToDataTable.Rows[0]["FLD_LEVEL"].ToString();
			textBox12.Text = dBToDataTable.Rows[0]["FLD_JOB_LEVEL"].ToString();
			textBox13.Text = dBToDataTable.Rows[0]["FLD_ZX"].ToString();
			textBox14.Text = dBToDataTable.Rows[0]["FLD_DES"].ToString();
			textBox21.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
			textBox22.Text = dBToDataTable.Rows[0]["FLD_WEIGHT"].ToString();
			textBox23.Text = dBToDataTable.Rows[0]["FLD_MONEY"].ToString();
			textBox24.Text = dBToDataTable.Rows[0]["FLD_EL"].ToString();
			textBox25.Text = dBToDataTable.Rows[0]["FLD_WXJD"].ToString();
			textBox26.Text = dBToDataTable.Rows[0]["FLD_WX"].ToString();
			textBox16.Text = dBToDataTable.Rows[0]["FLD_ZBSJ"].ToString();
			textBox17.Text = dBToDataTable.Rows[0]["FLD_YCDF"].ToString();
			textBox18.Text = dBToDataTable.Rows[0]["FLD_YCAT"].ToString();
			textBox19.Text = dBToDataTable.Rows[0]["FLD_YCHP"].ToString();
			textBox20.Text = dBToDataTable.Rows[0]["FLD_YCJY"].ToString();
			textBox27.Text = dBToDataTable.Rows[0]["FLD_ZBHS"].ToString();
			textBox28.Text = dBToDataTable.Rows[0]["FLD_YCQG"].ToString();
			textBox29.Text = dBToDataTable.Rows[0]["FLD_DFBFB"].ToString();
			textBox30.Text = dBToDataTable.Rows[0]["FLD_ATBFB"].ToString();
		}
		dBToDataTable.Dispose();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ItemEdit));
            this.label28 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.textBox30 = new System.Windows.Forms.TextBox();
            this.textBox29 = new System.Windows.Forms.TextBox();
            this.label33 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.textBox28 = new System.Windows.Forms.TextBox();
            this.textBox27 = new System.Windows.Forms.TextBox();
            this.textBox20 = new System.Windows.Forms.TextBox();
            this.textBox19 = new System.Windows.Forms.TextBox();
            this.textBox18 = new System.Windows.Forms.TextBox();
            this.textBox17 = new System.Windows.Forms.TextBox();
            this.textBox16 = new System.Windows.Forms.TextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.textBox14 = new System.Windows.Forms.TextBox();
            this.textBox21 = new System.Windows.Forms.TextBox();
            this.textBox22 = new System.Windows.Forms.TextBox();
            this.textBox23 = new System.Windows.Forms.TextBox();
            this.textBox24 = new System.Windows.Forms.TextBox();
            this.textBox25 = new System.Windows.Forms.TextBox();
            this.textBox26 = new System.Windows.Forms.TextBox();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.textBox12 = new System.Windows.Forms.TextBox();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.textBox10 = new System.Windows.Forms.TextBox();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label40 = new System.Windows.Forms.Label();
            this.comboBoxITEMTYPE = new System.Windows.Forms.ComboBox();
            this.listBox1 = new System.Windows.Forms.ListBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label22 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.button4 = new System.Windows.Forms.Button();
            this.textBox15 = new System.Windows.Forms.TextBox();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.ForeColor = System.Drawing.Color.Red;
            this.label28.Location = new System.Drawing.Point(69, 562);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(47, 12);
            this.label28.TabIndex = 14;
            this.label28.Text = "label28";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(9, 561);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(59, 12);
            this.label27.TabIndex = 13;
            this.label27.Text = "物品总数:";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.textBox30);
            this.groupBox2.Controls.Add(this.textBox29);
            this.groupBox2.Controls.Add(this.label33);
            this.groupBox2.Controls.Add(this.label32);
            this.groupBox2.Controls.Add(this.textBox28);
            this.groupBox2.Controls.Add(this.textBox27);
            this.groupBox2.Controls.Add(this.textBox20);
            this.groupBox2.Controls.Add(this.textBox19);
            this.groupBox2.Controls.Add(this.textBox18);
            this.groupBox2.Controls.Add(this.textBox17);
            this.groupBox2.Controls.Add(this.textBox16);
            this.groupBox2.Controls.Add(this.label31);
            this.groupBox2.Controls.Add(this.label30);
            this.groupBox2.Controls.Add(this.label29);
            this.groupBox2.Controls.Add(this.label26);
            this.groupBox2.Controls.Add(this.label24);
            this.groupBox2.Controls.Add(this.label23);
            this.groupBox2.Controls.Add(this.label21);
            this.groupBox2.Controls.Add(this.label20);
            this.groupBox2.Controls.Add(this.button1);
            this.groupBox2.Controls.Add(this.textBox14);
            this.groupBox2.Controls.Add(this.textBox21);
            this.groupBox2.Controls.Add(this.textBox22);
            this.groupBox2.Controls.Add(this.textBox23);
            this.groupBox2.Controls.Add(this.textBox24);
            this.groupBox2.Controls.Add(this.textBox25);
            this.groupBox2.Controls.Add(this.textBox26);
            this.groupBox2.Controls.Add(this.textBox13);
            this.groupBox2.Controls.Add(this.textBox12);
            this.groupBox2.Controls.Add(this.textBox11);
            this.groupBox2.Controls.Add(this.textBox10);
            this.groupBox2.Controls.Add(this.textBox9);
            this.groupBox2.Controls.Add(this.textBox8);
            this.groupBox2.Controls.Add(this.textBox7);
            this.groupBox2.Controls.Add(this.textBox6);
            this.groupBox2.Controls.Add(this.textBox5);
            this.groupBox2.Controls.Add(this.textBox4);
            this.groupBox2.Controls.Add(this.textBox3);
            this.groupBox2.Controls.Add(this.textBox2);
            this.groupBox2.Controls.Add(this.textBox1);
            this.groupBox2.Controls.Add(this.label25);
            this.groupBox2.Controls.Add(this.label19);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label17);
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.label15);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Location = new System.Drawing.Point(271, 62);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(528, 585);
            this.groupBox2.TabIndex = 12;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "物品属性";
            // 
            // textBox30
            // 
            this.textBox30.Location = new System.Drawing.Point(356, 377);
            this.textBox30.Multiline = true;
            this.textBox30.Name = "textBox30";
            this.textBox30.Size = new System.Drawing.Size(136, 20);
            this.textBox30.TabIndex = 71;
            this.textBox30.TextChanged += new System.EventHandler(this.textBox30_TextChanged);
            // 
            // textBox29
            // 
            this.textBox29.Location = new System.Drawing.Point(356, 351);
            this.textBox29.Multiline = true;
            this.textBox29.Name = "textBox29";
            this.textBox29.Size = new System.Drawing.Size(136, 20);
            this.textBox29.TabIndex = 70;
            this.textBox29.TextChanged += new System.EventHandler(this.textBox29_TextChanged);
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(266, 380);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(71, 12);
            this.label33.TabIndex = 69;
            this.label33.Text = "攻击百分比:";
            this.label33.Click += new System.EventHandler(this.label33_Click);
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(265, 353);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(71, 12);
            this.label32.TabIndex = 68;
            this.label32.Text = "防御百分比:";
            this.label32.Click += new System.EventHandler(this.label32_Click);
            // 
            // textBox28
            // 
            this.textBox28.Location = new System.Drawing.Point(356, 324);
            this.textBox28.Name = "textBox28";
            this.textBox28.Size = new System.Drawing.Size(136, 21);
            this.textBox28.TabIndex = 67;
            this.textBox28.TextChanged += new System.EventHandler(this.textBox28_TextChanged);
            // 
            // textBox27
            // 
            this.textBox27.Location = new System.Drawing.Point(356, 297);
            this.textBox27.Name = "textBox27";
            this.textBox27.Size = new System.Drawing.Size(136, 21);
            this.textBox27.TabIndex = 66;
            this.textBox27.TextChanged += new System.EventHandler(this.textBox27_TextChanged);
            // 
            // textBox20
            // 
            this.textBox20.Location = new System.Drawing.Point(356, 270);
            this.textBox20.Name = "textBox20";
            this.textBox20.Size = new System.Drawing.Size(136, 21);
            this.textBox20.TabIndex = 65;
            this.textBox20.TextChanged += new System.EventHandler(this.textBox20_TextChanged);
            // 
            // textBox19
            // 
            this.textBox19.Location = new System.Drawing.Point(356, 243);
            this.textBox19.Name = "textBox19";
            this.textBox19.Size = new System.Drawing.Size(136, 21);
            this.textBox19.TabIndex = 64;
            this.textBox19.TextChanged += new System.EventHandler(this.textBox19_TextChanged);
            // 
            // textBox18
            // 
            this.textBox18.Location = new System.Drawing.Point(356, 216);
            this.textBox18.Name = "textBox18";
            this.textBox18.Size = new System.Drawing.Size(136, 21);
            this.textBox18.TabIndex = 63;
            this.textBox18.TextChanged += new System.EventHandler(this.textBox18_TextChanged);
            // 
            // textBox17
            // 
            this.textBox17.Location = new System.Drawing.Point(356, 189);
            this.textBox17.Name = "textBox17";
            this.textBox17.Size = new System.Drawing.Size(136, 21);
            this.textBox17.TabIndex = 62;
            this.textBox17.TextChanged += new System.EventHandler(this.textBox17_TextChanged);
            // 
            // textBox16
            // 
            this.textBox16.Location = new System.Drawing.Point(356, 162);
            this.textBox16.Name = "textBox16";
            this.textBox16.Size = new System.Drawing.Size(136, 21);
            this.textBox16.TabIndex = 61;
            this.textBox16.TextChanged += new System.EventHandler(this.textBox16_TextChanged);
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(274, 326);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(59, 12);
            this.label31.TabIndex = 60;
            this.label31.Text = "隐藏气功:";
            this.label31.Click += new System.EventHandler(this.label31_Click);
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(274, 299);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(59, 12);
            this.label30.TabIndex = 59;
            this.label30.Text = "装备回收:";
            this.label30.Click += new System.EventHandler(this.label30_Click);
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(274, 169);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(59, 12);
            this.label29.TabIndex = 58;
            this.label29.Text = "装备介绍:";
            this.label29.Click += new System.EventHandler(this.label29_Click);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(274, 278);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 12);
            this.label26.TabIndex = 57;
            this.label26.Text = "隐藏经验:";
            this.label26.Click += new System.EventHandler(this.label26_Click);
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(274, 251);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(59, 12);
            this.label24.TabIndex = 56;
            this.label24.Text = "隐藏生命:";
            this.label24.Click += new System.EventHandler(this.label24_Click);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(274, 224);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(59, 12);
            this.label23.TabIndex = 55;
            this.label23.Text = "隐藏攻击:";
            this.label23.Click += new System.EventHandler(this.label23_Click);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(274, 197);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(59, 12);
            this.label21.TabIndex = 54;
            this.label21.Text = "隐藏防御:";
            this.label21.Click += new System.EventHandler(this.label21_Click);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(268, 528);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(77, 12);
            this.label20.TabIndex = 53;
            this.label20.Text = "注:120字以内";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(356, 556);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(136, 23);
            this.button1.TabIndex = 52;
            this.button1.Text = "修改(重读物品)";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBox14
            // 
            this.textBox14.Location = new System.Drawing.Point(356, 414);
            this.textBox14.Multiline = true;
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new System.Drawing.Size(136, 136);
            this.textBox14.TabIndex = 51;
            // 
            // textBox21
            // 
            this.textBox21.Location = new System.Drawing.Point(108, 377);
            this.textBox21.Name = "textBox21";
            this.textBox21.Size = new System.Drawing.Size(136, 21);
            this.textBox21.TabIndex = 44;
            // 
            // textBox22
            // 
            this.textBox22.Location = new System.Drawing.Point(356, 135);
            this.textBox22.Name = "textBox22";
            this.textBox22.Size = new System.Drawing.Size(136, 21);
            this.textBox22.TabIndex = 43;
            // 
            // textBox23
            // 
            this.textBox23.Location = new System.Drawing.Point(356, 107);
            this.textBox23.Name = "textBox23";
            this.textBox23.Size = new System.Drawing.Size(136, 21);
            this.textBox23.TabIndex = 42;
            // 
            // textBox24
            // 
            this.textBox24.Location = new System.Drawing.Point(356, 79);
            this.textBox24.Name = "textBox24";
            this.textBox24.Size = new System.Drawing.Size(136, 21);
            this.textBox24.TabIndex = 41;
            // 
            // textBox25
            // 
            this.textBox25.Location = new System.Drawing.Point(356, 51);
            this.textBox25.Name = "textBox25";
            this.textBox25.Size = new System.Drawing.Size(136, 21);
            this.textBox25.TabIndex = 40;
            // 
            // textBox26
            // 
            this.textBox26.Location = new System.Drawing.Point(356, 23);
            this.textBox26.Name = "textBox26";
            this.textBox26.Size = new System.Drawing.Size(136, 21);
            this.textBox26.TabIndex = 39;
            // 
            // textBox13
            // 
            this.textBox13.Location = new System.Drawing.Point(108, 350);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(136, 21);
            this.textBox13.TabIndex = 38;
            // 
            // textBox12
            // 
            this.textBox12.Location = new System.Drawing.Point(108, 323);
            this.textBox12.Name = "textBox12";
            this.textBox12.Size = new System.Drawing.Size(136, 21);
            this.textBox12.TabIndex = 37;
            // 
            // textBox11
            // 
            this.textBox11.Location = new System.Drawing.Point(108, 296);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(136, 21);
            this.textBox11.TabIndex = 36;
            // 
            // textBox10
            // 
            this.textBox10.Location = new System.Drawing.Point(108, 269);
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new System.Drawing.Size(136, 21);
            this.textBox10.TabIndex = 35;
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(108, 242);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(136, 21);
            this.textBox9.TabIndex = 34;
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(108, 215);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(136, 21);
            this.textBox8.TabIndex = 33;
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(108, 188);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(136, 21);
            this.textBox7.TabIndex = 32;
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(108, 161);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(136, 21);
            this.textBox6.TabIndex = 31;
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(108, 134);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(136, 21);
            this.textBox5.TabIndex = 30;
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(108, 107);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(136, 21);
            this.textBox4.TabIndex = 29;
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(108, 80);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(136, 21);
            this.textBox3.TabIndex = 28;
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(108, 53);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(136, 21);
            this.textBox2.TabIndex = 27;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(108, 26);
            this.textBox1.Name = "textBox1";
            this.textBox1.ReadOnly = true;
            this.textBox1.Size = new System.Drawing.Size(136, 21);
            this.textBox1.TabIndex = 26;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(274, 504);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(53, 12);
            this.label25.TabIndex = 24;
            this.label25.Text = "FLD_DES:";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(12, 380);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(59, 12);
            this.label19.TabIndex = 18;
            this.label19.Text = "FLD_TYPE:";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(12, 56);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(89, 12);
            this.label18.TabIndex = 17;
            this.label18.Text = "FLD_QUESTITEM:";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(274, 55);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(59, 12);
            this.label17.TabIndex = 16;
            this.label17.Text = "FLD_WXJD:";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(274, 27);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(47, 12);
            this.label16.TabIndex = 15;
            this.label16.Text = "FLD_WX:";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(274, 82);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(47, 12);
            this.label15.TabIndex = 14;
            this.label15.Text = "FLD_EL:";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(274, 111);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(65, 12);
            this.label14.TabIndex = 13;
            this.label14.Text = "FLD_MONEY:";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(12, 110);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(47, 12);
            this.label13.TabIndex = 12;
            this.label13.Text = "FLD_NJ:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(12, 218);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(47, 12);
            this.label12.TabIndex = 11;
            this.label12.Text = "FLD_DF:";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(12, 272);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(53, 12);
            this.label11.TabIndex = 10;
            this.label11.Text = "FLD_AT2:";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(12, 245);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 9;
            this.label10.Text = "FLD_AT1:";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(274, 140);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(71, 12);
            this.label9.TabIndex = 8;
            this.label9.Text = "FLD_WEIGHT:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(12, 164);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 7;
            this.label8.Text = "FLD_RESIDE2:";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 191);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(53, 12);
            this.label7.TabIndex = 6;
            this.label7.Text = "FLD_SEX:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(12, 326);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 5;
            this.label6.Text = "FLD_JOB_LEVEL:";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(12, 299);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "FLD_LEVEL:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 137);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 12);
            this.label4.TabIndex = 3;
            this.label4.Text = "FLD_RESIDE1:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 353);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(47, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "FLD_ZX:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 83);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "FLD_NAME:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "FLD_PID:";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label40);
            this.groupBox1.Controls.Add(this.comboBoxITEMTYPE);
            this.groupBox1.Controls.Add(this.label28);
            this.groupBox1.Controls.Add(this.label27);
            this.groupBox1.Controls.Add(this.listBox1);
            this.groupBox1.Location = new System.Drawing.Point(12, 62);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(251, 585);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "物品列表";
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(10, 25);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(53, 12);
            this.label40.TabIndex = 41;
            this.label40.Text = "物品类别";
            // 
            // comboBoxITEMTYPE
            // 
            this.comboBoxITEMTYPE.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxITEMTYPE.FormattingEnabled = true;
            this.comboBoxITEMTYPE.Items.AddRange(new object[] {
            "衣服",
            "护手",
            "武器",
            "鞋子",
            "内甲",
            "项链",
            "耳环",
            "戒指",
            "披风",
            "弓箭",
            "门甲",
            "宝宝",
            "石头",
            "宝盒",
            "幸运符",
            "气功书",
            "百宝",
            "武功书",
            "其他"});
            this.comboBoxITEMTYPE.Location = new System.Drawing.Point(67, 21);
            this.comboBoxITEMTYPE.MaxDropDownItems = 20;
            this.comboBoxITEMTYPE.Name = "comboBoxITEMTYPE";
            this.comboBoxITEMTYPE.Size = new System.Drawing.Size(163, 20);
            this.comboBoxITEMTYPE.TabIndex = 42;
            this.comboBoxITEMTYPE.SelectedIndexChanged += new System.EventHandler(this.comboBoxITEMTYPE_SelectedIndexChanged);
            // 
            // listBox1
            // 
            this.listBox1.FormattingEnabled = true;
            this.listBox1.ItemHeight = 12;
            this.listBox1.Location = new System.Drawing.Point(9, 51);
            this.listBox1.Name = "listBox1";
            this.listBox1.Size = new System.Drawing.Size(234, 496);
            this.listBox1.TabIndex = 1;
            this.listBox1.SelectedIndexChanged += new System.EventHandler(this.listBox1_SelectedValueChanged);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.comboBox1);
            this.groupBox3.Controls.Add(this.label22);
            this.groupBox3.Controls.Add(this.button2);
            this.groupBox3.Controls.Add(this.button4);
            this.groupBox3.Controls.Add(this.textBox15);
            this.groupBox3.Location = new System.Drawing.Point(17, 8);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(760, 48);
            this.groupBox3.TabIndex = 22;
            this.groupBox3.TabStop = false;
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "PID",
            "物品名",
            "物品种类",
            "适用职业",
            "适用性别",
            "适用转职"});
            this.comboBox1.Location = new System.Drawing.Point(181, 19);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(121, 20);
            this.comboBox1.TabIndex = 56;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(110, 23);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(65, 12);
            this.label22.TabIndex = 55;
            this.label22.Text = "按条件查找";
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(10, 16);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(92, 23);
            this.button2.TabIndex = 54;
            this.button2.Text = "查看所有物品";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(618, 16);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(67, 23);
            this.button4.TabIndex = 9;
            this.button4.Text = "查找";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // textBox15
            // 
            this.textBox15.Location = new System.Drawing.Point(313, 18);
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new System.Drawing.Size(132, 21);
            this.textBox15.TabIndex = 6;
            // 
            // ItemEdit
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(817, 659);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ItemEdit";
            this.Text = "物品数据库修改";
            this.Load += new System.EventHandler(this.ItemEdit_Load);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (listBox1.Items.Count == 0)
		{
			MessageBox.Show("请先查询数据库!", "提示");
			return;
		}
		if (textBox1.Text == "")
		{
			MessageBox.Show("请先选择要修改的物品!", "提示");
			return;
		}
		if (textBox14.Text.Length > 120)
		{
			MessageBox.Show("装备说明不能超过120个文字!");
			return;
		}
		if (textBox3.Text.Length > 15)
		{
			MessageBox.Show("装备名称不能超过15个文字!");
			return;
		}
		if (textBox16.Text.Length > 15)
		{
			MessageBox.Show("装备介绍不能超过15个文字!");
			return;
		}
		if (textBox27.Text.Length > 15)
		{
			MessageBox.Show("装备回收不能超过15个文字!");
			return;
		}
		try
		{
			string sqlCommand = string.Format("UPDATE TBL_XWWL_ITEM  SET FLD_Name='{1}',FLD_RESIDE1={2}, FLD_RESIDE2={3},FLD_SEX={4},FLD_DF={5},FLD_AT1={6},FLD_AT2={7},FLD_LEVEL={8},FLD_JOB_LEVEL={9},FLD_ZX={10},FLD_WEIGHT={11},FLD_MONEY={12},FLD_WXJD={13},FLD_WX={14},FLD_ZBSJ='{15}',FLD_YCDF={16},FLD_YCAT={17},FLD_YCHP={18},FLD_YCJY={19},FLD_ZBHS='{20}',FLD_YCQG={21},FLD_DFBFB={22},FLD_ATBFB={23} WHERE FLD_PID={0}", int.Parse(textBox1.Text), textBox3.Text, int.Parse(textBox5.Text), int.Parse(textBox6.Text), int.Parse(textBox7.Text), int.Parse(textBox8.Text), int.Parse(textBox9.Text), int.Parse(textBox10.Text), int.Parse(textBox11.Text), int.Parse(textBox12.Text), int.Parse(textBox13.Text), int.Parse(textBox22.Text), int.Parse(textBox23.Text), int.Parse(textBox25.Text), int.Parse(textBox26.Text), textBox16.Text, int.Parse(textBox17.Text), int.Parse(textBox18.Text), int.Parse(textBox19.Text), int.Parse(textBox20.Text), textBox27.Text, int.Parse(textBox28.Text), int.Parse(textBox29.Text), int.Parse(textBox30.Text));
			DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			MessageBox.Show("修改成功!");
		}
		catch (Exception)
		{
			MessageBox.Show("修改出错!");
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
		刷新();
	}

	private void button4_Click(object sender, EventArgs e)
	{
		if (textBox15.Text == "")
		{
			MessageBox.Show("请先输入要查询的内容!", "提示");
			return;
		}
		if (comboBox1.SelectedIndex != -1)
		{
			try
			{
				listBox1.Items.Clear();
				string text = "";
				int result;
				switch (comboBox1.SelectedIndex)
				{
				case 0:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品ID!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) <= 0)
					{
						MessageBox.Show("必须是正整数");
						return;
					}
					int num3 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_PID={num3}";
					break;
				}
				case 1:
					text = "select * from TBL_XWWL_ITEM where FLD_NAME like '%" + textBox15.Text + "%'";
					break;
				case 2:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num4 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_RESIDE2={num4}";
					break;
				}
				case 3:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num2 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_RESIDE1={num2}";
					break;
				}
				case 4:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num5 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_SEX={num5}";
					break;
				}
				case 5:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_JOB_LEVEL={num}";
					break;
				}
				default:
					text = "select * from TBL_XWWL_ITEM";
					break;
				}
				DataTable dBToDataTable = DBA.GetDBToDataTable(text, "PublicDb");
				if (dBToDataTable.Rows.Count > 0)
				{
					for (int i = 0; i < dBToDataTable.Rows.Count; i++)
					{
						KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
						listBox1.Items.Add(keyValuePair);
					}
					label28.Text = dBToDataTable.Rows.Count.ToString();
				}
				else
				{
					MessageBox.Show("无此物品,请检查PID是否正确！", "提示");
				}
				dBToDataTable.Dispose();
				return;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.ToString(), "错误");
				return;
			}
		}
		MessageBox.Show("请选择查询的类型", "提示");
	}

	private void comboBoxITEMTYPE_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = ((ItemDef.MyItem)comboBoxITEMTYPE.SelectedItem).Value.ToString();
		listBox1.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			listBox1.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
	}

	private void ItemEdit_Load(object sender, EventArgs e)
	{
		ItemDef.AddComBoxItemReside2(comboBoxITEMTYPE);
	}

	private void label29_Click(object sender, EventArgs e)
	{
	}

	private void label21_Click(object sender, EventArgs e)
	{
	}

	private void label23_Click(object sender, EventArgs e)
	{
	}

	private void label24_Click(object sender, EventArgs e)
	{
	}

	private void label26_Click(object sender, EventArgs e)
	{
	}

	private void label30_Click(object sender, EventArgs e)
	{
	}

	private void label31_Click(object sender, EventArgs e)
	{
	}

	private void label32_Click(object sender, EventArgs e)
	{
	}

	private void label33_Click(object sender, EventArgs e)
	{
	}

	private void textBox16_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox17_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox18_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox19_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox20_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox27_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox28_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox29_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBox30_TextChanged(object sender, EventArgs e)
	{
	}
}

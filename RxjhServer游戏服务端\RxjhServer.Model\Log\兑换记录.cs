﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log
{
    [Table(Name = "兑换记录", DisableSyncStructure = true)]
    [JsonObject(MemberSerialization.OptIn)]
    public partial class 兑换记录
    {
        [JsonProperty]
        [Column(IsPrimary = true, IsIdentity = true, Name = "ID")]
        public int ID { get; set; }

        [JsonProperty]
        [Column(Name = "FLD_CDK", DbType = "varchar(50) NOT NULL")]
        public string FLD_CDK { get; set; }

        [JsonProperty]
        [Column(Name = "玩家名字", DbType = "varchar(50)")]
        public string 玩家名字 { get; set; }

        [JsonProperty]
        [Column(Name = "玩家帐号", DbType = "varchar(50)")]
        public string 玩家帐号 { get; set; }

        [JsonProperty]
        [Column(Name = "称号")]
        public int? 称号 { get; set; }

        [JsonProperty]
        [Column(Name = "物品编号")]
        public int? 物品编号 { get; set; }

        [JsonProperty]
        [Column(Name = "元宝")]
        public int? 元宝 { get; set; }

        [JsonProperty]
        [Column(Name = "IP地址", DbType = "varchar(50)")]
        public string IP地址 { get; set; }

        [JsonProperty]
        [Column(Name = "是否使用")]
        public int? 是否使用 { get; set; }

        [JsonProperty]
        [Column(Name = "时间", DbType = "smalldatetime", InsertValueSql = "getdate()")]
        public DateTime? 时间 { get; set; }

        [JsonProperty]
        [Column(Name = "说明", DbType = "varchar(50)")]
        public string 说明 { get; set; }

        [JsonProperty]
        [Column(Name = "分区", DbType = "varchar(50)")]
        public string 分区 { get; set; }
    }
}


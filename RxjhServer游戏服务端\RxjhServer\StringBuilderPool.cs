using System;
using System.Collections.Concurrent;
using System.Text;
using System.Threading;

namespace RxjhServer
{
    // 2025-0618 EVIAS StringBuilder对象池，减少字符串操作的内存分配
    public class StringBuilderPool
    {
        private static readonly Lazy<StringBuilderPool> _instance = new(() => new StringBuilderPool());
        public static StringBuilderPool Instance => _instance.Value;

        private readonly ConcurrentQueue<StringBuilder> _pool = new();
        private const int MaxPoolSize = 100;
        private const int DefaultCapacity = 256;
        private const int MaxCapacity = 8192; // 最大容量限制，防止内存泄漏
        
        private long _totalRented = 0;
        private long _totalReturned = 0;
        private long _totalCreated = 0;

        // 租用StringBuilder
        public StringBuilder Rent()
        {
            Interlocked.Increment(ref _totalRented);
            
            if (_pool.TryDequeue(out var sb))
            {
                sb.Clear();
                return sb;
            }
            
            Interlocked.Increment(ref _totalCreated);
            return new StringBuilder(DefaultCapacity);
        }

        // 归还StringBuilder
        public void Return(StringBuilder sb)
        {
            if (sb == null) return;
            
            Interlocked.Increment(ref _totalReturned);
            
            // 如果容量过大，不放回池中，让GC回收
            if (sb.Capacity > MaxCapacity)
            {
                return;
            }
            
            // 如果池已满，不放回
            if (_pool.Count >= MaxPoolSize)
            {
                return;
            }
            
            _pool.Enqueue(sb);
        }

        // 使用StringBuilder执行操作
        public string Build(Action<StringBuilder> buildAction)
        {
            var sb = Rent();
            try
            {
                buildAction(sb);
                return sb.ToString();
            }
            finally
            {
                Return(sb);
            }
        }

        // 获取池统计信息
        public string GetPoolStats()
        {
            return $"StringBuilder池: 当前:{_pool.Count} 租用:{_totalRented} 归还:{_totalReturned} 创建:{_totalCreated}";
        }
    }

    // 2025-0618 EVIAS 高性能字符串工具类
    public static class FastString
    {
        // 高性能字符串拼接
        public static string Concat(params object[] values)
        {
            if (values == null || values.Length == 0)
                return string.Empty;
                
            return StringBuilderPool.Instance.Build(sb =>
            {
                foreach (var value in values)
                {
                    if (value != null)
                    {
                        sb.Append(value);
                    }
                }
            });
        }

        // 格式化字符串
        public static string Format(string format, params object[] args)
        {
            if (string.IsNullOrEmpty(format))
                return string.Empty;
                
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.AppendFormat(format, args);
            });
        }

        // 连接字符串数组
        public static string Join(string separator, params string[] values)
        {
            if (values == null || values.Length == 0)
                return string.Empty;
                
            return StringBuilderPool.Instance.Build(sb =>
            {
                for (int i = 0; i < values.Length; i++)
                {
                    if (i > 0 && !string.IsNullOrEmpty(separator))
                    {
                        sb.Append(separator);
                    }
                    sb.Append(values[i]);
                }
            });
        }

        // 构建日志消息
        public static string BuildLogMessage(int type, string message)
        {
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.Append(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                sb.Append("  ");
                sb.Append(message);
            });
        }

        // 构建错误信息
        public static string BuildErrorInfo(string operation, string playerName, int errorCount, string details = null)
        {
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.Append(operation);
                sb.Append("丨");
                sb.Append(playerName);
                sb.Append("丨错误次数:");
                sb.Append(errorCount);
                if (!string.IsNullOrEmpty(details))
                {
                    sb.Append("丨");
                    sb.Append(details);
                }
            });
        }

        // 构建网络统计信息
        public static string BuildNetworkStats(long received, long sent, int packets)
        {
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.Append("收:");
                sb.Append(FormatBytes(received));
                sb.Append("/s 发:");
                sb.Append(FormatBytes(sent));
                sb.Append("/s 包:");
                sb.Append(packets);
            });
        }

        // 格式化字节数
        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024)
                return bytes + "B";
            else if (bytes < 1024 * 1024)
                return (bytes / 1024) + "KB";
            else
                return (bytes / 1024 / 1024) + "MB";
        }

        // 构建玩家状态信息
        public static string BuildPlayerStats(int connected, int online, int cloud, int fake, int shop)
        {
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.Append("连:");
                sb.Append(connected);
                sb.Append(" 在:");
                sb.Append(online);
                sb.Append(" 云:");
                sb.Append(cloud);
                sb.Append(" 假:");
                sb.Append(fake);
                sb.Append(" 商:");
                sb.Append(shop);
            });
        }
        // 构建运行时间信息 - 2025-0619 EVIAS 改为简写格式节省状态栏空间
        public static string BuildUptime(TimeSpan uptime)
        {
            return StringBuilderPool.Instance.Build(sb =>
            {
                sb.Append("运行:");
                sb.Append(uptime.Days);
                sb.Append("d");
                sb.Append(uptime.Hours);
                sb.Append("h");
                sb.Append(uptime.Minutes);
                sb.Append("m");
                sb.Append(uptime.Seconds);
                sb.Append("s");
            });
        }
    }
}

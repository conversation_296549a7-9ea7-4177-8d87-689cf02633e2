using System.Collections.Generic;
using System.Data.SqlClient;

namespace RxjhVer23;

internal class RxjhWebShopMenu : ICustomMenu
{
	public Dictionary<string, int> MainMenu { get; set; } = new Dictionary<string, int>();


	public Dictionary<int, string> MainMenuCopy { get; set; } = new Dictionary<int, string>();


	public Dictionary<int, string> SubMenu { get; set; } = new Dictionary<int, string>();


	internal static ICustomMenu GetCustomMenus(string connectionString)
	{
		string cmdText = "SELECT MainMenuName, MainMenuID FROM RXJH_WEB_SHOP_MAIN_MENU";
		string cmdText2 = "SELECT SubMenuID, SubMenuName, MainMenuID FROM RXJH_WEB_SHOP_SUB_MENU";
		RxjhWebShopMenu rxjhWebShopMenu = new RxjhWebShopMenu();
		using (SqlConnection sqlConnection = new SqlConnection(connectionString))
		{
			SqlCommand sqlCommand = new SqlCommand(cmdText, sqlConnection);
			SqlCommand sqlCommand2 = new SqlCommand(cmdText2, sqlConnection);
			sqlConnection.Open();
			SqlDataReader sqlDataReader = sqlCommand.ExecuteReader();
			while (sqlDataReader.Read())
			{
				string text = sqlDataReader["MainMenuName"].ToString();
				int num = (int)sqlDataReader["MainMenuID"];
				rxjhWebShopMenu.MainMenu.Add(text, num);
				rxjhWebShopMenu.MainMenuCopy.Add(num, text);
			}
			sqlDataReader.Close();
			SqlDataReader sqlDataReader2 = sqlCommand2.ExecuteReader();
			while (sqlDataReader2.Read())
			{
				int key = (int)sqlDataReader2["SubMenuID"];
				string text2 = sqlDataReader2["SubMenuName"].ToString();
				int key2 = (int)sqlDataReader2["MainMenuID"];
				text2 = rxjhWebShopMenu.MainMenuCopy[key2] + "_" + text2;
				rxjhWebShopMenu.SubMenu.Add(key, text2);
			}
			sqlDataReader2.Close();
		}
		return rxjhWebShopMenu;
	}
}

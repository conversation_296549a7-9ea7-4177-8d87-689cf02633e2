using System;
using System.IO;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace RxjhServer;

public class Aes
{
	public const string Key1 = "P9kR4mH2wX8vN6bQ1cF7tY3jL5aD0sE2";

	public const string Key2 = "T5gB8nK4xM2vR9cL6pH1wS7fA3jD0qY8";

	public static string Decrypt(string key, string code)
	{
		try
		{
			using AesManaged aesManaged = new AesManaged();
			byte[] array = (aesManaged.Key = Encoding.ASCII.GetBytes(key));
			aesManaged.IV = array.Take(16).ToArray();
			byte[] cipherText = Convert.FromBase64String(code);
			return DecryptStringFromBytes_Aes(cipherText, aesManaged.Key, aesManaged.IV);
		}
		catch
		{
			return null;
		}
	}

	public static string DecryptStringFromBytes_Aes(byte[] cipherText, byte[] Key, byte[] IV)
	{
		if (cipherText == null || cipherText.Length == 0)
		{
			return null;
		}
		if (Key == null || Key.Length == 0)
		{
			return null;
		}
		if (IV == null || IV.Length == 0)
		{
			return null;
		}
		string result = null;
		using (AesManaged aesManaged = new AesManaged())
		{
			aesManaged.Key = Key;
			aesManaged.IV = IV;
			ICryptoTransform transform = aesManaged.CreateDecryptor(aesManaged.Key, aesManaged.IV);
			using MemoryStream stream = new MemoryStream(cipherText);
			using CryptoStream stream2 = new CryptoStream(stream, transform, CryptoStreamMode.Read);
			using StreamReader streamReader = new StreamReader(stream2);
			result = streamReader.ReadToEnd();
		}
		return result;
	}

	public static string GetMachineCode()
	{
		return GetCpuId() + GetDiskNo();
	}

	public static string GetDiskNo()
	{
		ManagementClass managementClass = new ManagementClass("Win32_NetworkAdapterConfiguration");
		ManagementObject managementObject = new ManagementObject("win32_logicaldisk.deviceid=\"c:\"");
		managementObject.Get();
		return managementObject.GetPropertyValue("VolumeSerialNumber").ToString();
	}

	public static string GetCpuId()
	{
		string result = null;
		ManagementClass managementClass = new ManagementClass("win32_Processor");
		ManagementObjectCollection instances = managementClass.GetInstances();
		using (ManagementObjectCollection.ManagementObjectEnumerator managementObjectEnumerator = instances.GetEnumerator())
		{
			if (managementObjectEnumerator.MoveNext())
			{
				ManagementObject managementObject = (ManagementObject)managementObjectEnumerator.Current;
				result = managementObject.Properties["Processorid"].Value.ToString();
			}
		}
		return result;
	}

	public static string Encrypt(string plainText, string key)
	{
		try
		{
			using AesManaged aesManaged = new AesManaged();
			byte[] keyBytes = Encoding.ASCII.GetBytes(key);
			aesManaged.Key = keyBytes;
			aesManaged.IV = keyBytes.Take(16).ToArray();

			byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);

			using MemoryStream memoryStream = new MemoryStream();
			using CryptoStream cryptoStream = new CryptoStream(memoryStream, aesManaged.CreateEncryptor(), CryptoStreamMode.Write);
			cryptoStream.Write(plainTextBytes, 0, plainTextBytes.Length);
			cryptoStream.FlushFinalBlock();

			byte[] cipherTextBytes = memoryStream.ToArray();
			return Convert.ToBase64String(cipherTextBytes);
		}
		catch (Exception ex)
		{
            Console.WriteLine($"未预期的错误: {ex.Message}");
            return null;
		}
	}

	public static string GenerateRegistrationCode(string machineCode, DateTime expiryDate, string secretKey, string salt)
	{
		string dataToEncrypt = $"{machineCode}|{expiryDate:yyyy-MM-dd}|{salt}";
		return Encrypt(dataToEncrypt, secretKey);
	}

	public static bool ValidateRegistrationCode(string registrationCode, string machineCode, string secretKey, out DateTime expiryDate)
	{
		expiryDate = DateTime.MinValue;
		
		try
		{
			string decryptedData = Decrypt(secretKey, registrationCode);
			if (string.IsNullOrEmpty(decryptedData)) return false;
			
			string[] parts = decryptedData.Split('|');
			
			if (parts.Length == 4)
			{
				if (!parts[1].Equals(machineCode)) return false;
				
				if (!DateTime.TryParse(parts[2], out expiryDate)) return false;
			}
			else if (parts.Length == 3)
			{
				if (!parts[0].Equals(machineCode)) return false;
				
				if (!DateTime.TryParse(parts[1], out expiryDate)) return false;
			}
			else
			{
				return false;
			}
			
			return DateTime.Now <= expiryDate;
		}
		catch
		{
			return false;
		}
	}

	public static bool ValidateRegistrationCodes(string projectId, string code1, string code2, string machineCode, out DateTime expiryDate)
	{
		expiryDate = DateTime.MinValue;
		
		DateTime expiryDate1, expiryDate2;
		
		bool isValid1 = ValidateRegistrationCode(code1, machineCode, Key1, out expiryDate1);
		bool isValid2 = ValidateRegistrationCode(code2, machineCode, Key2, out expiryDate2);

		if (!isValid1 || !isValid2)
		{
			return false;
		}

		expiryDate = expiryDate1 < expiryDate2 ? expiryDate1 : expiryDate2;
		return true;
	}
}

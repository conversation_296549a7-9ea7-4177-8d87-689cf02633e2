using System;

namespace loginServer;

public class TcpServerEvent
{
	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnSendEventHandler(IntPtr connId, byte[] bytes);

	public delegate <PERSON><PERSON><PERSON><PERSON>ult OnReceiveEventHandler(IntPtr connId, byte[] bytes);

	public delegate <PERSON><PERSON>R<PERSON>ult OnPointerDataReceiveEventHandler(IntPtr connId, IntPtr pData, int length);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnCloseEventHandler(IntPtr connId, SocketOperation enOperation, int errorCode);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnShutdownEventHandler();

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnPrepareListenEventHandler(IntPtr soListen);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnAcceptEventHandler(IntPtr connId, IntPtr pClient);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnHandShakeEventHandler(IntPtr connId);
}

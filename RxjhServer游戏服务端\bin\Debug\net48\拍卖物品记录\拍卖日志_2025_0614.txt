2025/6/14 20:07:09 查询拍卖物品 SQL: Select * from [拍卖物品列表] where ID = 1
2025/6/14 20:07:09 查询结果行数: 1
2025/6/14 20:07:09 成功解析拍品: ID=1, 名称=破仙刀, 状态=待竞拍
2025/6/14 20:07:09 查询拍卖物品 SQL: Select * from [拍卖物品列表] where ID = 1
2025/6/14 20:07:09 查询结果行数: 1
2025/6/14 20:07:09 成功解析拍品: ID=1, 名称=破仙刀, 状态=待竞拍
2025/6/14 20:07:09 购买拍卖装备-2 贰贰 拍卖ID:1 物品ID:100202016 名称:破仙刀 数量:1 价格:10 属性:0 0 0 0 0
2025/6/14 20:15:20 添加拍卖物品成功-2 贰贰 物品ID:100202016 名称:破仙刀 数量:1 一口价:10 起拍价:1
2025/6/14 20:15:29 查询拍卖物品 SQL: Select * from [拍卖物品列表] where ID = 3
2025/6/14 20:15:29 查询结果行数: 1
2025/6/14 20:15:29 成功解析拍品: ID=3, 名称=灵宠 龙, 状态=待竞拍
2025/6/14 20:15:29 参与竞拍成功-2 贰贰 拍品ID:3 竞拍价格:2

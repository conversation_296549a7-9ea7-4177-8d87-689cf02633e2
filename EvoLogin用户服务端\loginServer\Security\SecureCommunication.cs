using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Configuration;
using System.Linq;
using loginServer.DbClss;

namespace loginServer.Security
{
    public class SecureCommunication
    {
        private readonly byte[] _key;
        private readonly byte[] _iv;
        private static readonly byte[] DefaultKey = new byte[] 
        { 
            0x76, 0x61, 0x6C, 0x75, 0x65, 0x73, 0x65, 0x63, 
            0x75, 0x72, 0x69, 0x74, 0x79, 0x6B, 0x65, 0x79,
            0x73, 0x75, 0x70, 0x65, 0x72, 0x73, 0x74, 0x72,
            0x6F, 0x6E, 0x67, 0x76, 0x61, 0x6C, 0x75, 0x65
        };
        private static readonly byte[] DefaultIv = new byte[] 
        { 
            0x72, 0x61, 0x6E, 0x64, 0x6F, 0x6D, 0x69, 0x76, 
            0x76, 0x61, 0x6C, 0x75, 0x65, 0x68, 0x65, 0x72 
        };

        public SecureCommunication()
        {
            _key = DefaultKey;
            _iv = DefaultIv;
        }

        public SecureCommunication(string configKeyName)
        {
            try
            {
                string keyString = Config.IniReadValue("Security", configKeyName);
                if (string.IsNullOrEmpty(keyString))
                {
                    _key = DefaultKey;
                    _iv = DefaultIv;
                    
                    string keyBase64 = Convert.ToBase64String(DefaultKey);
                    string ivBase64 = Convert.ToBase64String(DefaultIv);
                    Config.IniWriteValue("Security", configKeyName, keyBase64);
                    Config.IniWriteValue("Security", configKeyName + "Iv", ivBase64);
                }
                else
                {
                    _key = Convert.FromBase64String(keyString);
                    string ivString = Config.IniReadValue("Security", configKeyName + "Iv");
                    _iv = string.IsNullOrEmpty(ivString) 
                        ? DefaultIv 
                        : Convert.FromBase64String(ivString);
                }
            }
            catch (Exception ex)
            {
                _key = DefaultKey;
                _iv = DefaultIv;
                System.Diagnostics.Debug.WriteLine("加载加密配置出错: " + ex.Message);
            }
        }

        public SecureCommunication(byte[] key, byte[] iv)
        {
            _key = key ?? DefaultKey;
            _iv = iv ?? DefaultIv;
        }

 
        public string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] cipherBytes;

                using (var aesAlg = new AesManaged())
                {
                    aesAlg.Key = _key;
                    aesAlg.IV = _iv;
                    aesAlg.Mode = CipherMode.CBC;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    using var encryptor = aesAlg.CreateEncryptor();
                    using var ms = new MemoryStream();
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        cs.Write(plainBytes, 0, plainBytes.Length);
                        cs.FlushFinalBlock();
                    }

                    cipherBytes = ms.ToArray();
                }

                return Convert.ToBase64String(cipherBytes);
            }
            catch
            {
                return string.Empty;
            }
        }


        public string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                string plainText;

                using (var aesAlg = new AesManaged())
                {
                    aesAlg.Key = _key;
                    aesAlg.IV = _iv;
                    aesAlg.Mode = CipherMode.CBC;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    using var decryptor = aesAlg.CreateDecryptor();
                    using var ms = new MemoryStream(cipherBytes);
                    using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                    using var reader = new StreamReader(cs, Encoding.UTF8);
                    plainText = reader.ReadToEnd();
                }

                return plainText;
            }
            catch
            {

                return cipherText;
            }
        }


        public string DecryptCompatible(string cipherText)
        {
            return Decrypt(cipherText);
        }
    }
} 
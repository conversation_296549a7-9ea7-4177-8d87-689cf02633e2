using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace loginServer;

public class FormServer : Form
{
	private IContainer components = null;

	private ComboBox comboBox1;

	private Label label1;

	private ListView listView1;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader6;

	private ColumnHeader columnHeader7;

	private Button button1;

	public FormServer()
	{
		InitializeComponent();
	}

	private void FormServer_Load(object sender, EventArgs e)
	{
		comboBox1.Items.Clear();
		foreach (ServerClass server in World.ServerList)
		{
			comboBox1.Items.Add(server.SERVER_NAME);
		}
	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		加载服务器线路信息();
	}

	private void ServerList_Load(object sender, EventArgs e)
	{
		comboBox1.Items.Clear();
		foreach (ServerClass server in World.ServerList)
		{
			comboBox1.Items.Add(server.SERVER_NAME);
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		加载服务器线路信息();
	}

    //private void 加载服务器线路信息()
    //{
    //	listView1.Items.Clear();
    //	if (comboBox1.SelectedIndex < 0)
    //	{
    //		return;
    //	}
    //	foreach (ServerXlClass serverXL in World.ServerList[comboBox1.SelectedIndex].ServerList)
    //	{
    //		string text = "";
    //		string text2 = "";
    //		int num = 0;
    //		SockClient playerHandler = World.查询服务器(serverXL.ServerZId.ToString());
    //		string text3;
    //		if (playerHandler != null)
    //		{
    //			num = playerHandler.当前在线;
    //			text3 = playerHandler.最大在线.ToString();
    //			text = num + "/" + text3;
    //		}
    //		else
    //		{
    //			text = "未连接";
    //		}
    //		int 线路状态 = serverXL.线路状态;
    //		bool flag = false;
    //		if (1 == 0)
    //		{
    //		}
    //		string text4 = 线路状态 switch
    //		{
    //			1 => "满员", 
    //			2 => "60%", 
    //			3 => "90%", 
    //			4 => "99%", 
    //			_ => "实际数", 
    //		};
    //		if (1 == 0)
    //		{
    //		}
    //		text3 = text4;
    //		bool flag2 = false;
    //		text2 = text3;
    //		string[] items = new string[7]
    //		{
    //			serverXL.ServerId.ToString(),
    //			serverXL.ServerId.ToString(),
    //			serverXL.SERVER_NAME.ToString(),
    //			serverXL.SERVER_IP.ToString(),
    //			serverXL.SERVER_PORT.ToString(),
    //			text,
    //			text2
    //		};
    //		listView1.Items.Insert(listView1.Items.Count, new ListViewItem(items));
    //	}
    //}

    private void 加载服务器线路信息()
    {
        listView1.Items.Clear();

        if (comboBox1.SelectedIndex < 0 || comboBox1.SelectedIndex >= World.ServerList.Count)
        {
            return;
        }

        var serverGroup = World.ServerList[comboBox1.SelectedIndex];
        if (serverGroup?.ServerList == null) return;

        foreach (ServerXlClass serverXL in serverGroup.ServerList)
        {
            if (serverXL == null) continue;

            string onlineInfo = "未连接";
            string loadStatus = "实际数";
            SockClient playerHandler = World.查询服务器(serverXL.ServerZId.ToString()); // ServerZId是int，直接ToString()

            if (playerHandler != null)
            {
                onlineInfo = $"{playerHandler.当前在线}/{playerHandler.最大在线}";

                loadStatus = serverXL.线路状态 switch
                {
                    1 => "满员",
                    2 => "60%",
                    3 => "90%",
                    4 => "99%",
                    _ => "实际数"
                };
            }

            string[] items = new string[7]
            {
            serverXL.ServerId.ToString(),
            serverXL.ServerId.ToString(),
            serverXL.SERVER_NAME?.ToString() ?? "", 
            serverXL.SERVER_IP?.ToString() ?? "",    
            serverXL.SERVER_PORT.ToString(),         
            onlineInfo,
            loadStatus
            };

            listView1.Items.Add(new ListViewItem(items));
        }
    }

    protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormServer));
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.button1 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(148, 16);
            this.comboBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(211, 26);
            this.comboBox1.TabIndex = 8;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(20, 21);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(116, 18);
            this.label1.TabIndex = 7;
            this.label1.Text = "选择服务器：";
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader7});
            this.listView1.Cursor = System.Windows.Forms.Cursors.Default;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(9, 64);
            this.listView1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(848, 424);
            this.listView1.TabIndex = 6;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "序号";
            this.columnHeader1.Width = 50;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "服务器ID";
            this.columnHeader2.Width = 70;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "线路名";
            this.columnHeader3.Width = 160;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "IP";
            this.columnHeader4.Width = 100;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "端口";
            this.columnHeader5.Width = 50;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "线路实况";
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "线路虚况";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(393, 15);
            this.button1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(112, 34);
            this.button1.TabIndex = 9;
            this.button1.Text = "刷新";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // FormServer
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(866, 506);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.comboBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.listView1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "FormServer";
            this.Text = "FormServer";
            this.Load += new System.EventHandler(this.FormServer_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

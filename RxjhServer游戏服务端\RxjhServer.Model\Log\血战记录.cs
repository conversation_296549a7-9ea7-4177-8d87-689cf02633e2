﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 血战记录 {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int ID { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 比赛结果 { get; set; }

		[JsonProperty]
		public int? 场地代号 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 分区 { get; set; }

		[JsonProperty]
		public int? A获得元宝 { get; set; }

		[JsonProperty]
		public int? A杀人数 { get; set; }

		[JsonProperty]
		public int? A逃跑次数 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string A玩家 { get; set; }

		[JsonProperty]
		public int? B获得元宝 { get; set; }

		[JsonProperty]
		public int? B杀人数 { get; set; }

		[JsonProperty]
		public int? B逃跑次数 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string B玩家 { get; set; }

	}

}

using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Timers;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class Connect
{
	private System.Timers.Timer 自动连接;

	private Socket listenSocket;

	private byte[] dataReceive;

	public Connect()
	{
		dataReceive = new byte[102400];
		自动连接 = new System.Timers.Timer(5000.0);
		自动连接.Elapsed += 自动连接事件;
		自动连接.AutoReset = true;
		自动连接.Enabled = true;
	}

	private void 自动连接事件(object source, ElapsedEventArgs e)
	{
		if (!listenSocket.Connected)
		{
			Sestup();
		}
	}

	public void Sestup()
	{
		try
		{
			IPEndPoint remoteEP = new IPEndPoint(IPAddress.Parse(World.帐号验证服务器IP), World.帐号验证服务器端口);
			listenSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
			listenSocket.BeginConnect(remoteEP, ConnectCallback, listenSocket);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "连接帐号验证服务器出错" + World.帐号验证服务器端口 + "IP" + World.帐号验证服务器IP.ToString() + "丨" + ex.Message);
		}
	}

	public void Dispose()
	{
		if (自动连接 != null)
		{
			自动连接.Enabled = false;
			自动连接.Close();
			自动连接.Dispose();
		}
		try
		{
			listenSocket.Shutdown(SocketShutdown.Both);
		}
		catch
		{
		}
		if (listenSocket != null)
		{
			listenSocket.Close();
		}
		listenSocket = null;
	}

	public void 复查用户登陆()
	{
		try
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (NetState value11 in World.list.Values)
			{
				if (value11.换线中)
				{
					continue;
				}
				string value = "NULL";
				int value2 = 0;
				if (value11.挂机)
				{
					value2 = 1;
				}
				else if (value11.假人)
				{
					value2 = 2;
				}
				else if (value11.云挂机)
				{
					value2 = 3;
				}
				int value3 = 0;
				string value4 = string.Empty;
				string value5 = string.Empty;
				int value6 = 0;
				string value7 = string.Empty;
				string value8 = string.Empty;
				int value9 = 0;
				int value10 = 0;
				Players players = World.检查玩家世界ID(value11.WorldId);
				if (players != null)
				{
					value = players.UserName;
					value3 = players.原服务器序号;
					value4 = players.原服务器IP;
					value5 = players.原服务器端口.ToString();
					value6 = players.原服务器ID;
					value7 = players.银币广场服务器IP;
					value8 = players.银币广场服务器端口.ToString();
					if (players.FLD_VIP == 1)
					{
						value9 = 1;
					}
					value10 = players.Player_Job;
				}
				stringBuilder.Append(value11.Player.Userid);
				stringBuilder.Append("-");
				stringBuilder.Append(value11.ToString());
				stringBuilder.Append("-");
				stringBuilder.Append(value11.绑定帐号);
				stringBuilder.Append("-");
				stringBuilder.Append(value2);
				stringBuilder.Append("-");
				stringBuilder.Append(value);
				stringBuilder.Append("-");
				stringBuilder.Append(value3);
				stringBuilder.Append("-");
				stringBuilder.Append(value4);
				stringBuilder.Append("-");
				stringBuilder.Append(value5);
				stringBuilder.Append("-");
				stringBuilder.Append(value6);
				stringBuilder.Append("-");
				stringBuilder.Append(value7);
				stringBuilder.Append("-");
				stringBuilder.Append(value8);
				stringBuilder.Append("-");
				stringBuilder.Append(value11.WorldId);
				stringBuilder.Append("-");
				stringBuilder.Append(value9);
				stringBuilder.Append("-");
				stringBuilder.Append(value10);
				stringBuilder.Append(",");
			}
			if (stringBuilder.Length > 0)
			{
				stringBuilder.Remove(stringBuilder.Length - 1, 1);
			}
			World.conn.发送("复查用户登陆|" + stringBuilder);
			if (World.AutGC != 0)
			{
				GC.Collect();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "复查用户登陆 错误" + ex.Message);
		}
	}

	private void ConnectCallback(IAsyncResult ar)
	{
		try
		{
			((Socket)ar.AsyncState).EndConnect(ar);
			try
			{
				发送("服务器连接登陆|" + World.服务器ID + "|" + World.最大在线 + "|" + CRC32.GetEXECRC32());
				Form1.WriteLine(2, "帐号服务器连接成功端口：[" + World.帐号验证服务器端口 + "] IP：" + World.帐号验证服务器IP);
				listenSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
				Thread.Sleep(500);
				发送("更新服务器端口|" + World.服务器ID + "|" + World.游戏服务器端口2);
				// 同步维护状态给EvoLogin（重启后默认非维护状态）
				发送("服务器维护状态|0|");
				复查用户登陆();
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "验证服务器ConnectCallback出错：" + ex.Message);
			}
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "帐号服务器连接出错：" + ex2.Message);
		}
	}

	public virtual void OnReceiveData(IAsyncResult ar)
	{
		try
		{
			int num = listenSocket.EndReceive(ar);
			if (num > 0 && listenSocket.Connected)
			{
				ProcessDataReceived(dataReceive, num);
				listenSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "帐号服务器 接收出错：" + ex.Message);
		}
	}

	public void 发送(string msg)
	{
		try
		{
			if (listenSocket != null && listenSocket.Connected)
			{
				byte[] bytes = Encoding.Default.GetBytes(msg);
				Send(bytes, bytes.Length);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证服务器发送出错：" + msg + ex.Message);
		}
	}

	public virtual void Send(byte[] toSendBuff, int len)
	{
		try
		{
			byte[] array = new byte[len + 6];
			array[0] = 204;
			array[1] = 153;
			Buffer.BlockCopy(BitConverter.GetBytes(len), 0, array, 2, 4);
			Buffer.BlockCopy(toSendBuff, 0, array, 6, len);
			listenSocket.BeginSend(array, 0, len + 6, SocketFlags.None, OnSended2, this);
		}
		catch (SocketException ex)
		{
			Form1.WriteLine(1, "帐号服务器      发送出错：" + ex.Message);
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "帐号服务器      发送出错：" + ex2.Message);
		}
	}

	public void OnSended2(IAsyncResult ar)
	{
		try
		{
			listenSocket.EndSend(ar);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "帐号服务器      发送出错：" + ex.Message);
		}
	}

	public void ProcessDataReceived(byte[] data, int length)
	{
		try
		{
			int num = 0;
			if (204 == data[0] && 153 == data[1])
			{
				byte[] array = new byte[4];
				Buffer.BlockCopy(data, 2, array, 0, 4);
				int num2 = BitConverter.ToInt32(array, 0);
				if (length < num2 + 6)
				{
					return;
				}
				while (true)
				{
					byte[] array2 = new byte[num2];
					Buffer.BlockCopy(data, num + 6, array2, 0, num2);
					num += num2 + 6;
					DataReceived(array2, num2);
					if (num < length && data[num] == 204 && data[num + 1] == 153)
					{
						Buffer.BlockCopy(data, num + 2, array, 0, 4);
						num2 = BitConverter.ToInt16(array, 0);
						continue;
					}
					break;
				}
			}
			else
			{
				Form1.WriteLine(1, "LoginServer错包：" + data[0] + "      " + data[1]);
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, "服务器连接", "数据接收处理");
			Form1.WriteLine(1, "出错：" + ex.Message);
		}
	}

	public void DataReceived(byte[] data, int length)
	{
		string @string = Encoding.Default.GetString(data);
		try
		{
			string[] array = @string.Split('|');
			switch (array[0])
			{
			case "OpClient":
				try
				{
					Players players4 = World.检查玩家世界ID(int.Parse(array[1]));
					NetState value3;
					if (players4 != null)
					{
						if (players4.Client != null)
						{
							players4.OpClient(int.Parse(array[2]));
						}
					}
					else if (World.list.TryGetValue(int.Parse(array[1]), out value3))
					{
						if (value3.Player != null)
						{
							value3.Player.OpClient(int.Parse(array[2]));
							break;
						}
						byte[] array3 = Converter.hexStringToByte("AA5512000100BB00040001000000000000000000000055AA");
						Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(array[2])), 0, array3, 10, 2);
						Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(array[1])), 0, array3, 4, 2);
						value3.Send(array3, array3.Length);
					}
					break;
				}
				catch
				{
					break;
				}
			case "帐号服务器断开连接":
				if (listenSocket != null)
				{
					listenSocket.Close();
				}
				break;
			case "PVP":
			{
				for (int i = 1; i < array.Length; i++)
				{
					Players players2 = World.检查玩家name(array[i]);
					if (players2 != null)
					{
						players2.FLD_VIP = 1;
						players2.更新人物数据(players2);
					}
				}
				break;
			}
			case "仙魔大战掉线":
				if (World.仙魔大战掉线玩家 != null && !World.仙魔大战掉线玩家.ContainsKey(array[1]))
				{
					World.仙魔大战掉线玩家.TryAdd(array[1], array[2]);
				}
				break;
			case "移除仙魔大战掉线":
				if (World.仙魔大战掉线玩家 != null && World.仙魔大战掉线玩家.ContainsKey(array[1]))
				{
					World.仙魔大战掉线玩家.TryRemove(array[1], out var _);
				}
				break;
			case "仙魔大战进程":
				World.仙魔大战进程 = int.Parse(array[1]);
				break;
			case "仙魔大战人数":
				World.仙魔大战正人数 = int.Parse(array[1]);
				World.仙魔大战邪人数 = int.Parse(array[2]);
				break;
			case "获取服务器列表":
			{
				Players players3 = World.检查玩家(array[1]);
				if (players3 != null)
				{
					for (int j = 2; j < array.Length - 1; j++)
					{
						players3.更新服务器列表(array[j]);
					}
				}
				break;
			}
			case "用户登陆":
				用户登陆(int.Parse(array[2]), array[1], array[3], array[4], array[5], array[6], array[7], array[8], array[9]);
				break;
			case "更新配置":
				更新配置(array[1], array[2]);
				break;
			case "用户换线登陆":
				Form1.WriteLine(1, "收到LS：" + @string);
				用户换线登陆(int.Parse(array[4]), array[1], array[2], array[3], array[7], array[8], array[9], array[10], array[11], array[12], array[13], array[6], array[14]);
				break;
			case "用户踢出":
				if (array[1].Length != 0)
				{
					用户踢出(int.Parse(array[1]));
				}
				break;
			case "用户踢出ID":
				if (array[1].Length != 0)
				{
					用户踢出ID(array[1]);
				}
				break;
			case "发送公告":
				全线发送公告(int.Parse(array[1]), array[2]);
				break;
			case "狮吼功":
				狮吼功(array[1], array[2], array[3]);
				break;
			case "刷怪掉宝":
				if (int.Parse(World.掉落开盒杀人提示开关[0]) == 1)
				{
					刷怪掉宝(array[1], array[2], array[3]);
				}
				break;
			case "情侣提示":
				if (int.Parse(World.掉落开盒杀人提示开关[4]) == 1)
				{
					情侣提示(array[1], array[2]);
				}
				break;
			case "PK提示":
				if (int.Parse(World.掉落开盒杀人提示开关[2]) == 1)
				{
					PK提示(array[1], array[2]);
				}
				break;
			case "开启宝盒":
				if (int.Parse(World.掉落开盒杀人提示开关[1]) == 1)
				{
					开启宝盒(array[1], array[2], array[3]);
				}
				break;
			case "百宝抽奖":
				if (int.Parse(World.掉落开盒杀人提示开关[5]) == 1)
				{
					百宝抽奖(array[1], array[2], array[3]);
				}
				break;
			case "狮子吼":
			{
				NetState value2;
				if (array[1] == "OK")
				{
					World.发送全服狮子吼消息广播数据(int.Parse(array[2]), array[3], int.Parse(array[4]), array[5], int.Parse(array[6]), int.Parse(array[7]), int.Parse(array[8]));
				}
				else if (World.list.TryGetValue(int.Parse(array[2]), out value2))
				{
					value2.Player.系统提示("狮子吼列队以满请等待.....");
				}
				break;
			}
			case "同盟聊天":
				if (array[1] == "OK")
				{
					World.发送同盟聊天(array[2], array[3], array[4], int.Parse(array[5]));
				}
				break;
			case "传音消息":
				World.发送传音消息(int.Parse(array[1]), array[2], array[3], array[4], int.Parse(array[5]), array[6]);
				break;
			case "帮派消息":
			{
				byte[] array2 = strToToHexByte(array[2]);
				World.发送帮派消息(array[1], array2, array2.Length);
				break;
			}
			case "全线公告":
				全线发送公告(int.Parse(array[1]), array[2]);
				{
					foreach (Players value4 in World.allConnectedChars.Values)
					{
						if (World.是否开启共用银币市场 == 1 && int.Parse(array[1]) != 1)
						{
							if (array[3] == "势力战" && value4.Player_Job_leve >= 2)
							{
								value4.活动邀请银币市场();
							}
							if (array[3] == "攻城战" && value4.宣告攻城 != 0)
							{
								value4.活动邀请银币市场();
							}
							if (array[3] == "仙魔大战" && value4.Player_Job_leve >= 2)
							{
								value4.活动邀请银币市场();
							}
							if (array[3] == "帮派战" && value4.帮派名字 != string.Empty)
							{
								value4.活动邀请银币市场();
							}
							if (array[3] == "武林血战" && value4.Player_Job_leve >= 3)
							{
								value4.武林杀人数 = 0;
								value4.活动邀请银币市场();
							}
						}
						World.活动开启中 = int.Parse(array[1]);
					}
					break;
				}
			case "全线提示":
				if (int.Parse(World.掉落开盒杀人提示开关[3]) == 1)
				{
					全线提示(array[1], array[2], array[3]);
				}
				break;
			case "限制用户":
			{
				Players players = World.检查玩家世界ID(int.Parse(array[1]));
				if (players != null)
				{
					if (array[2] == "0")
					{
						players.LS_降低_暴率百分比 = (double)int.Parse(array[3]) * 0.01;
						players.系统提示("你的暴率被降低了" + players.LS_降低_暴率百分比 * 100.0 + "%.....");
					}
					if (array[2] == "1")
					{
						players.LS_降低_经验百分比 = (double)int.Parse(array[3]) * 0.01;
						players.系统提示("你的经验被降低了" + players.LS_降低_经验百分比 * 100.0 + "%.....");
					}
					if (array[2] == "2")
					{
						players.LS_降低_历练百分比 = (double)int.Parse(array[3]) * 0.01;
						players.系统提示("你的历练被降低了" + players.LS_降低_历练百分比 * 100.0 + "%.....");
					}
					if (array[2] == "3")
					{
						players.LS_降低_金钱百分比 = (double)int.Parse(array[3]) * 0.01;
						players.系统提示("你的金钱被降低了" + players.LS_降低_金钱百分比 * 100.0 + "%.....");
					}
				}
				break;
			}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证服务器接收出错：" + @string + ex.Message);
		}
	}

	private static byte[] strToToHexByte(string hexString)
	{
		hexString = hexString.Replace("      ", string.Empty);
		if (hexString.Length % 2 != 0)
		{
			hexString += "      ";
		}
		byte[] array = new byte[hexString.Length / 2];
		for (int i = 0; i < array.Length; i++)
		{
			array[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
		}
		return array;
	}

	public void 更新配置(string userid, string date)
	{
		Players players = World.检查玩家(userid);
		if (players != null)
		{
			if (players.快捷栏.Contains(1008000044))
			{
				players.人物追加最大_HP += 300;
			}
			if (players.快捷栏.Contains(1008000045))
			{
				players.人物追加最大_MP += 200;
			}
			if (!players.是否更新配置)
			{
				byte[] array = Converter.hexStringToByte(date);
				Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
				players.换线更新配置(array, array.Length);
			}
			players.客户端设置 = date;
			players.更新HP_MP_SP();
		}
	}

	public void 用户登陆(int serverid, string userid, string txt, string 原服务器IP, string 原端口, string 银币IP, string 银币端口, string 服务器序号, string 服务器ID)
	{
		try
		{
			if (World.list.TryGetValue(serverid, out var value))
			{
				value.Player.连接登陆2(userid, txt, 原服务器IP, 原端口, 银币IP, 银币端口, 服务器序号, 服务器ID);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证服务器用户登陆出错：" + ex.Message);
		}
	}

	public void 用户换线登陆(int WorldID, string userid, string 服务器ID, string 人物序号, string 绑定帐号, string 原服务器IP, string 原服务器端口, string 银币广场IP, string 银币广场端口, string 原服务器序号, string 原服务器ID, string 新服务器ID, string 封包登陆)
	{
		try
		{
			if (World.list.TryGetValue(WorldID, out var value))
			{
				value.Player.换线账号登陆(userid, int.Parse(服务器ID), int.Parse(人物序号), WorldID, 绑定帐号, 原服务器IP, 原服务器端口, 银币广场IP, 银币广场端口, 原服务器序号, 原服务器ID, 新服务器ID, 封包登陆);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证用户换线登陆：" + ex.Message);
		}
	}

	public void 狮吼功(string name, string txt, string 线路)
	{
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				value?.系统提示(txt, 21, name + "「" + 线路 + "线」");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送狮吼功出错：" + ex.Message);
		}
	}

	public void 刷怪掉宝(string name, string txt, string 线路)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[0];
			int type = int.Parse(World.掉落开盒杀人提示颜色[0]);
            World.掉落全局提示("掉落提示「" + 线路 + "线」", type, txt);

		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送刷怪掉宝出错：" + ex.Message);
		}
	}

	public void 全线提示(string name, string txt, string 线路)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[3];
			int type = int.Parse(World.掉落开盒杀人提示颜色[3]);
			World.全局提示(text ?? "", type, txt);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送刷怪掉宝出错：" + ex.Message);
		}
	}

	public void 情侣提示(string name, string txt)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[4];
			int type = int.Parse(World.掉落开盒杀人提示颜色[4]);
			World.全局提示(text ?? "", type, txt);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送情侣提示出错：" + ex.Message);
		}
	}

	public void PK提示(string name, string txt)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[2];
			int type = int.Parse(World.掉落开盒杀人提示颜色[2]);
			World.PK全局提示(text ?? "", type, txt);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送PK提示出错：" + ex.Message);
		}
	}

	public void 开启宝盒(string name, string txt, string 线路)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[1];
			int type = int.Parse(World.掉落开盒杀人提示颜色[1]);
			World.开箱全局提示(text ?? "", type, txt);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送开启宝盒出错：" + ex.Message);
		}
	}

	public void 百宝抽奖(string name, string txt, string 线路)
	{
		try
		{
			string text = World.掉落开盒杀人提示语言[5];
			int type = int.Parse(World.掉落开盒杀人提示颜色[5]);
			World.开箱全局提示(text ?? "", type, txt);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送百宝抽奖提示出错：" + ex.Message);
		}
	}

	public void 全线发送公告(int ggid, string txt)
	{
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (!value.Client.挂机 && value != null)
				{
					switch (ggid)
					{
					case 0:
						value.系统公告(txt);
						break;
					case 1:
						value.横幅公告(txt);
						break;
					case 2:
						value.系统提示(txt, 10, "系统提示");
						break;
					case 3:
						value.系统提示(txt, 6, "公告");
						break;
					}
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "发送公告出错：" + ex.Message);
		}
	}

	public void 用户踢出(int WorldID)
	{
		try
		{
			Players players = World.检查玩家世界ID(WorldID);
			string empty = string.Empty;
			string empty2 = string.Empty;
			string empty3 = string.Empty;
			if (players != null)
			{
				empty = players.Userid;
				empty2 = players.UserName;
				empty3 = players.Client.ToString();
				if (players.Client.假人)
				{
					players.Client.DisposedOffline();
					World.假人数量--;
					if (World.假人数量 < 0)
					{
						World.假人数量 = 0;
					}
				}
				if (players.Client.云挂机)
				{
					players.Client.DisposedOffline();
					World.云挂机数量--;
					if (World.云挂机数量 < 0)
					{
						World.云挂机数量 = 0;
					}
				}
				if (players.Client.挂机)
				{
					players.Client.DisposedOffline();
					World.离线数量--;
					if (World.离线数量 < 0)
					{
						World.离线数量 = 0;
					}
				}
				else
				{
					players.Client.Dispose();
				}
				Form1.WriteLine(3, "1用户踢出 [" + empty + "]-[" + empty2 + "]" + empty3);
			}
			if (World.list.TryGetValue(WorldID, out var value))
			{
				value.delWorldIdd();
				value.Dispose();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证服务器用户踢出出错：" + WorldID + " " + ex.Message);
		}
	}

	public void 用户踢出ID(string userid)
	{
		try
		{
			Players players = World.检查玩家(userid);
			string empty = string.Empty;
			string empty2 = string.Empty;
			string empty3 = string.Empty;
			if (players == null)
			{
				return;
			}
			string userid2 = players.Userid;
			string userName = players.UserName;
			players.Client.ToString();
			if (players.Client.假人)
			{
				players.Client.DisposedOffline();
				World.假人数量--;
				if (World.假人数量 < 0)
				{
					World.假人数量 = 0;
				}
			}
			if (players.Client.云挂机)
			{
				players.Client.DisposedOffline();
				World.云挂机数量--;
				if (World.云挂机数量 < 0)
				{
					World.云挂机数量 = 0;
				}
			}
			if (players.Client.挂机)
			{
				players.Client.DisposedOffline();
				World.离线数量--;
				if (World.离线数量 < 0)
				{
					World.离线数量 = 0;
				}
			}
			else
			{
				players.OpClient(1);
				players.kickidlog("玩家重复登陆 - 用户踢出ID()");
				players.Logout(World.服务器ID + "线退出1");
				players.Client.Dispose();
			}
			World.conn.发送("踢出玩家ID|" + World.服务器ID + "|" + userid);
			Form1.WriteLine(3, "玩家重复登陆-用户踢出ID [" + players.Userid + "]-[" + players.UserName + "]" + players.Client.ToString());
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "验证服务器用户踢出出错：" + userid + " " + ex.Message);
		}
	}
}

using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form2 : Form
{
	private ListView listView2;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private ColumnHeader columnHeader8;

	private ColumnHeader columnHeader10;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader14;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader15;

	private ColumnHeader columnHeader16;

	private ColumnHeader columnHeader17;

	public Form2()
	{
		InitializeComponent();
	}

	public static void FlushAll0()
	{
		FlushAll9();
		Thread.Sleep(1000);
		FlushAll7();
		Thread.Sleep(1000);
		FlushAll6();
	}

	public static void FlushAl23()
	{
		FlushAl20();
		Thread.Sleep(1000);
		FlushAl21();
		Thread.Sleep(1000);
		FlushAl22();
	}

	public static void FlushAll()
	{
		FlushAll10();
		Thread.Sleep(1000);
		FlushAll11();
		Thread.Sleep(1000);
	}

	public static void FlushAll1()
	{
		int num = 0;
		Form1.WriteLine(55, "删除人物数据开始");
		DataTable dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_Char");
		if (dBToDataTable != null)
		{
			Form1.WriteLine(100, "共有人物数据" + dBToDataTable.Rows.Count);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				if ((int)DBA.GetDBValue_3(string.Format("select count(*) from TBL_ACCOUNT where FLD_ID='{0}'", dBToDataTable.Rows[i]["FLD_ID"]), "rxjhaccount") >= 1)
				{
					continue;
				}
				num++;
				Form1.WriteLine(100, "删除人物[" + dBToDataTable.Rows[i]["FLD_ID"].ToString() + "] [" + dBToDataTable.Rows[i]["FLD_NAME"]?.ToString() + "]");
				DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Char WHERE FLD_NAME  ='{0}'", dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
				DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_warehouse WHERE FLD_NAME  ='{0}'", dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
				DataTable dBToDataTable2 = DBA.GetDBToDataTable(string.Format("select * from TBL_XWWL_GuildMember WHERE FLD_NAME ='{0}'", dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
				if (dBToDataTable2 != null)
				{
					if (dBToDataTable2.Rows.Count > 0)
					{
						if (dBToDataTable2.Rows[0]["leve"].ToString() == "6")
						{
							Form1.WriteLine(100, "删除帮派" + dBToDataTable2.Rows[0]["G_Name"]);
							DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'", dBToDataTable2.Rows[0]["G_Name"].ToString()));
							DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'", dBToDataTable2.Rows[0]["G_Name"].ToString()));
						}
						else
						{
							DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE FLD_NAME  ='{0}'", dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
						}
					}
					dBToDataTable2.Dispose();
				}
				dBToDataTable2.Dispose();
			}
			dBToDataTable.Dispose();
		}
		Thread.Sleep(10);
		Form1.WriteLine(55, "删除人物数据完成" + num);
	}

	public static void FlushAll2()
	{
		int num = 0;
		Form1.WriteLine(55, "删除人物仓库数据开始");
		DataTable dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_warehouse");
		if (dBToDataTable != null)
		{
			Form1.WriteLine(55, "共有人物数据" + dBToDataTable.Rows.Count);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DataTable dBToDataTable2 = DBA.GetDBToDataTable(string.Format("select * from TBL_XWWL_Char where FLD_NAME='{0}'", dBToDataTable.Rows[i]["FLD_NAME"]));
				if (dBToDataTable2.Rows.Count < 1)
				{
					num++;
					Form1.WriteLine(55, "删除人物仓库[" + dBToDataTable.Rows[i]["FLD_ID"].ToString() + "] [" + dBToDataTable.Rows[i]["FLD_NAME"]?.ToString() + "]");
					DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_warehouse WHERE FLD_ID='{0}'and FLD_NAME  ='{1}'", dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
				}
				else if (dBToDataTable2.Rows[0]["FLD_ID"].ToString() != dBToDataTable.Rows[i]["FLD_ID"].ToString())
				{
					num++;
					Form1.WriteLine(55, "删除人物仓库[" + dBToDataTable.Rows[i]["FLD_ID"].ToString() + "] [" + dBToDataTable.Rows[i]["FLD_NAME"]?.ToString() + "]");
					DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_warehouse WHERE FLD_ID='{0}'and FLD_NAME  ='{1}'", dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
				}
				dBToDataTable2.Dispose();
			}
			dBToDataTable.Dispose();
		}
		Thread.Sleep(10);
		Form1.WriteLine(55, "删除人物仓库数据完成" + num);
	}

	public static void FlushAll3()
	{
		int num = 0;
		Form1.WriteLine(55, "删除宗合仓库开始");
		DataTable dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_PublicWarehouse");
		if (dBToDataTable != null)
		{
			Form1.WriteLine(55, "共有宗合仓库数据" + dBToDataTable.Rows.Count);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				if ((int)DBA.GetDBValue_3(string.Format("select count(*) from TBL_ACCOUNT where FLD_ID='{0}'", dBToDataTable.Rows[i]["FLD_ID"]), "rxjhaccount") < 1)
				{
					num++;
					Form1.WriteLine(55, "删除宗合仓库[" + dBToDataTable.Rows[i]["FLD_ID"].ToString() + "]");
					DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_PublicWarehouse WHERE FLD_ID  ='{0}'", dBToDataTable.Rows[i]["FLD_ID"].ToString()));
				}
			}
			dBToDataTable.Dispose();
		}
		Thread.Sleep(10);
		Form1.WriteLine(55, "删除宗合仓库完成" + num);
	}

	public static void FlushAll4()
	{
		int num = 0;
		Form1.WriteLine(55, "删除帮派数据开始");
		DataTable dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_Guild");
		if (dBToDataTable != null)
		{
			Form1.WriteLine(55, "共有人物数据" + dBToDataTable.Rows.Count);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DataTable dBToDataTable2 = DBA.GetDBToDataTable(string.Format("select * from TBL_XWWL_Char where FLD_NAME='{0}'", dBToDataTable.Rows[i]["G_Master"]));
				if (dBToDataTable2.Rows.Count < 1)
				{
					num++;
					Form1.WriteLine(55, "删除帮派[" + dBToDataTable.Rows[i]["G_Name"].ToString() + "] [" + dBToDataTable.Rows[i]["G_Master"]?.ToString() + "]");
					DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'", dBToDataTable.Rows[i]["G_Name"].ToString()));
					DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'", dBToDataTable.Rows[i]["G_Name"].ToString()));
				}
				dBToDataTable2.Dispose();
			}
			dBToDataTable.Dispose();
		}
		Thread.Sleep(10);
		Form1.WriteLine(55, "删除帮派数据完成" + num);
		FlushAll5();
	}

	public static void FlushAll5()
	{
		int num = 0;
		Form1.WriteLine(55, "删除帮派数据开始");
		DataTable dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_GuildMember");
		if (dBToDataTable != null)
		{
			Form1.WriteLine(55, "共有帮派数据" + dBToDataTable.Rows.Count);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DataTable dBToDataTable2 = DBA.GetDBToDataTable(string.Format("select * from TBL_XWWL_Char where FLD_NAME='{0}'", dBToDataTable.Rows[i]["FLD_NAME"]));
				if (dBToDataTable2.Rows.Count < 1)
				{
					num++;
					if (dBToDataTable.Rows[0]["leve"].ToString() == "6")
					{
						Form1.WriteLine(55, "删除帮派" + dBToDataTable.Rows[i]["G_Name"]);
						DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'", dBToDataTable.Rows[0]["G_Name"].ToString()));
						DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'", dBToDataTable.Rows[0]["G_Name"].ToString()));
					}
					else
					{
						Form1.WriteLine(55, "删除帮派" + dBToDataTable.Rows[i]["G_Name"]?.ToString() + "    " + dBToDataTable.Rows[i]["FLD_NAME"].ToString());
						DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE FLD_NAME  ='{0}'", dBToDataTable.Rows[i]["FLD_NAME"].ToString()));
					}
				}
				dBToDataTable2.Dispose();
			}
			dBToDataTable.Dispose();
		}
		Form1.WriteLine(55, "删除帮派数据完成" + num);
	}

	public static void FlushAll6()
	{
		string sqlCommand = string.Format("DELETE FROM 血战记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand2 = string.Format("DELETE FROM 传书记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand3 = string.Format("DELETE FROM 仙魔记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand4 = string.Format("DELETE FROM 百宝记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand5 = string.Format("DELETE FROM 帮战记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand6 = string.Format("DELETE FROM 登陆记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand7 = string.Format("DELETE FROM 掉落记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand8 = string.Format("DELETE FROM 合成记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand9 = string.Format("DELETE FROM 进化记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand10 = string.Format("DELETE FROM 卡号记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand11 = string.Format("DELETE FROM 开盒记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand12 = string.Format("DELETE FROM 商店记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand13 = string.Format("DELETE FROM 门战记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand14 = string.Format("DELETE FROM 物品记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand15 = string.Format("DELETE FROM 药品记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand16 = string.Format("DELETE FROM 元宝记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand17 = string.Format("DELETE FROM 首爆记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand18 = string.Format("DELETE FROM 仓库记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand19 = string.Format("DELETE FROM 复制记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand20 = string.Format("DELETE FROM 锁定记录 where 分区='" + World.分区编号 + "'");
		string sqlCommand21 = string.Format("DELETE FROM 武勋记录 where 分区='" + World.分区编号 + "'");
        string sqlCommand22 = string.Format("DELETE FROM 清理记录 where 分区='" + World.分区编号 + "'");
        DBA.ExeSqlCommand(sqlCommand, "GameLog");
		DBA.ExeSqlCommand(sqlCommand2, "GameLog");
		DBA.ExeSqlCommand(sqlCommand3, "GameLog");
		DBA.ExeSqlCommand(sqlCommand4, "GameLog");
		DBA.ExeSqlCommand(sqlCommand5, "GameLog");
		DBA.ExeSqlCommand(sqlCommand6, "GameLog");
		DBA.ExeSqlCommand(sqlCommand7, "GameLog");
		DBA.ExeSqlCommand(sqlCommand8, "GameLog");
		DBA.ExeSqlCommand(sqlCommand9, "GameLog");
		DBA.ExeSqlCommand(sqlCommand10, "GameLog");
		DBA.ExeSqlCommand(sqlCommand11, "GameLog");
		DBA.ExeSqlCommand(sqlCommand12, "GameLog");
		DBA.ExeSqlCommand(sqlCommand13, "GameLog");
		DBA.ExeSqlCommand(sqlCommand14, "GameLog");
		DBA.ExeSqlCommand(sqlCommand15, "GameLog");
		DBA.ExeSqlCommand(sqlCommand16, "GameLog");
		DBA.ExeSqlCommand(sqlCommand17, "GameLog");
		DBA.ExeSqlCommand(sqlCommand18, "GameLog");
		DBA.ExeSqlCommand(sqlCommand19, "GameLog");
		DBA.ExeSqlCommand(sqlCommand20, "GameLog");
		DBA.ExeSqlCommand(sqlCommand21, "GameLog");
        DBA.ExeSqlCommand(sqlCommand22, "GameLog");
        Thread.Sleep(10);
		Form1.WriteLine(55, "数据库其他记录清楚完成");
	}

	public static void FlushAll7()
	{
		string sqlCommand = string.Format("DELETE FROM  TBL_XWWL_Char where FLD_FQID='" + World.分区编号 + "'");
		string sqlCommand2 = string.Format("DELETE FROM  TBL_XWWL_Cw where 分区='" + World.分区编号 + "'");
		string sqlCommand3 = string.Format("DELETE FROM  TBL_XWWL_CWarehouse where 分区='" + World.分区编号 + "'");
		string sqlCommand4 = string.Format("DELETE FROM  每日任务 where 分区='" + World.分区编号 + "'");
		string sqlCommand5 = string.Format("DELETE FROM  TBL_XWWL_Guild where 分区='" + World.分区编号 + "'");
		string sqlCommand6 = string.Format("DELETE FROM  TBL_XWWL_GuildMember where 分区='" + World.分区编号 + "'");
		string sqlCommand7 = string.Format("DELETE FROM  TBL_XWWL_LWarehouse where 分区='" + World.分区编号 + "'");
		string sqlCommand8 = string.Format("DELETE FROM TBL_XWWL_PublicWarehouse where 分区='" + World.分区编号 + "'");
		string sqlCommand9 = string.Format("DELETE FROM  TBL_XWWL_Warehouse where 分区='" + World.分区编号 + "'");
		string sqlCommand10 = string.Format("DELETE FROM   岳父玫瑰排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand11 = string.Format("DELETE FROM   荣誉系统排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand12 = string.Format("DELETE FROM   荣誉比武排行 where 分区信息='" + World.分区编号 + "'");
		string sqlCommand13 = string.Format("DELETE FROM   TBL_师徒数据 where 分区='" + World.分区编号 + "'");
		string sqlCommand14 = string.Format("DELETE FROM   荣誉乱斗排行 where 分区信息='" + World.分区编号 + "'");
		string sqlCommand15 = string.Format("DELETE FROM   荣誉门派排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand16 = string.Format("DELETE FROM   荣誉势力排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand17 = string.Format("DELETE FROM   荣誉讨伐排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand18 = string.Format("DELETE FROM   荣誉武林排行 where FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand19 = string.Format("DELETE FROM   荣誉仙魔排行 where 分区信息='" + World.分区编号 + "'");
		string sqlCommand20 = string.Format("DELETE FROM   充值排名 where 分区信息='" + World.分区编号 + "'");
		string sqlCommand21 = string.Format("DELETE FROM   天魔神宫信息 where 分区='" + World.分区编号 + "'");
		string sqlCommand22 = string.Format("DELETE FROM   兑换记录 where 分区='" + World.分区编号 + "'");
		DBA.ExeSqlCommand(sqlCommand, "GameServer");
		DBA.ExeSqlCommand(sqlCommand2, "GameServer");
		DBA.ExeSqlCommand(sqlCommand3, "GameServer");
		DBA.ExeSqlCommand(sqlCommand4, "GameServer");
		DBA.ExeSqlCommand(sqlCommand5, "GameServer");
		DBA.ExeSqlCommand(sqlCommand6, "GameServer");
		DBA.ExeSqlCommand(sqlCommand7, "GameServer");
		DBA.ExeSqlCommand(sqlCommand8, "GameServer");
		DBA.ExeSqlCommand(sqlCommand9, "GameServer");
		DBA.ExeSqlCommand(sqlCommand10, "GameServer");
		DBA.ExeSqlCommand(sqlCommand11, "GameServer");
		DBA.ExeSqlCommand(sqlCommand12, "GameServer");
		DBA.ExeSqlCommand(sqlCommand13, "GameServer");
		DBA.ExeSqlCommand(sqlCommand14, "GameServer");
		DBA.ExeSqlCommand(sqlCommand15, "GameServer");
		DBA.ExeSqlCommand(sqlCommand16, "GameServer");
		DBA.ExeSqlCommand(sqlCommand17, "GameServer");
		DBA.ExeSqlCommand(sqlCommand18, "GameServer");
		DBA.ExeSqlCommand(sqlCommand19, "GameServer");
		DBA.ExeSqlCommand(sqlCommand20, "GameServer");
		DBA.ExeSqlCommand(sqlCommand21, "GameServer");
		DBA.ExeSqlCommand(sqlCommand22, "GameLog");
		Thread.Sleep(10);
		Form1.WriteLine(55, "所有分区数据库清楚完成");
	}

	public static void FlushAll8()
	{
		string sqlCommand = string.Format("DELETE FROM 百宝记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
		string sqlCommand2 = string.Format("DELETE FROM 掉落记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand3 = string.Format("DELETE FROM 登陆记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
		string sqlCommand4 = string.Format("DELETE FROM 合成记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME, getdate()) > " + World.记录保存天数);
		string sqlCommand5 = string.Format("DELETE FROM 商店记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand6 = string.Format("DELETE FROM 物品记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
		string sqlCommand7 = string.Format("DELETE FROM 药品记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand8 = string.Format("DELETE FROM 元宝记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
		string sqlCommand9 = string.Format("DELETE FROM 开盒记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand10 = string.Format("DELETE FROM 进化记录 WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand11 = string.Format("DELETE FROM 卡号记录 WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand12 = string.Format("DELETE FROM 仓库记录 WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand13 = string.Format("DELETE FROM 传书记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,传书时间,getdate())>" + World.传书保存天数);
		string sqlCommand14 = string.Format("DELETE FROM 复制记录 WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,FLD_TIME,getdate())>" + World.记录保存天数);
		string sqlCommand15 = string.Format("DELETE FROM 锁定记录 WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
		string sqlCommand16 = string.Format("DELETE FROM 武勋记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
        string sqlCommand17 = string.Format("DELETE FROM 清理记录  WHERE 分区='" + World.分区编号 + "' and  DateDiff(dd,时间,getdate())>" + World.记录保存天数);
        DBA.ExeSqlCommand(sqlCommand, "GameLog");
		DBA.ExeSqlCommand(sqlCommand2, "GameLog");
		DBA.ExeSqlCommand(sqlCommand3, "GameLog");
		DBA.ExeSqlCommand(sqlCommand4, "GameLog");
		DBA.ExeSqlCommand(sqlCommand5, "GameLog");
		DBA.ExeSqlCommand(sqlCommand6, "GameLog");
		DBA.ExeSqlCommand(sqlCommand7, "GameLog");
		DBA.ExeSqlCommand(sqlCommand8, "GameLog");
		DBA.ExeSqlCommand(sqlCommand9, "GameLog");
		DBA.ExeSqlCommand(sqlCommand10, "GameLog");
		DBA.ExeSqlCommand(sqlCommand11, "GameLog");
		DBA.ExeSqlCommand(sqlCommand13, "GameLog");
		DBA.ExeSqlCommand(sqlCommand12, "GameLog");
		DBA.ExeSqlCommand(sqlCommand14, "GameLog");
		DBA.ExeSqlCommand(sqlCommand15, "GameLog");
		DBA.ExeSqlCommand(sqlCommand16, "GameLog");
        DBA.ExeSqlCommand(sqlCommand17, "GameLog");
        Thread.Sleep(10);
		Form1.WriteLine(55, "数据库全部记录表清除" + World.记录保存天数 + "天前完成");
	}

	public static void FlushAll9()
	{
		string sqlCommand = string.Format("DELETE FROM TBL_ACCOUNT where FLD_FQ='" + World.分区编号 + "'");
		DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
		Thread.Sleep(10);
		Form1.WriteLine(55, "所有分区账号库清楚完成");
	}

	public static void FlushAll10()
	{
		string sqlCommand = string.Format("UPDATE TBL_ACCOUNT SET FLD_FQ='" + World.分区编号 + "'");
		DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
		Thread.Sleep(10);
		Form1.WriteLine(55, "所有分区账号库合并完成");
	}

	public static void FlushAll11()
	{
		string sqlCommand = string.Format("UPDATE TBL_XWWL_Char SET FLD_FQID='" + World.分区编号 + "'");
		string sqlCommand2 = string.Format("UPDATE TBL_XWWL_Cw SET 分区='" + World.分区编号 + "'");
		string sqlCommand3 = string.Format("UPDATE TBL_XWWL_CWarehouse SET 分区='" + World.分区编号 + "'");
		string sqlCommand4 = string.Format("UPDATE 每日任务 SET 分区='" + World.分区编号 + "'");
		string sqlCommand5 = string.Format("UPDATE TBL_XWWL_Guild SET 分区='" + World.分区编号 + "'");
		string sqlCommand6 = string.Format("UPDATE TBL_XWWL_GuildMember SET 分区='" + World.分区编号 + "'");
		string sqlCommand7 = string.Format("UPDATE TBL_XWWL_LWarehouse SET 分区='" + World.分区编号 + "'");
		string sqlCommand8 = string.Format("UPDATE TBL_XWWL_PublicWarehouse SET 分区='" + World.分区编号 + "'");
		string sqlCommand9 = string.Format("UPDATE TBL_XWWL_Warehouse SET 分区='" + World.分区编号 + "'");
		string sqlCommand10 = string.Format("UPDATE  岳父玫瑰排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand11 = string.Format("UPDATE  荣誉系统排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand12 = string.Format("UPDATE  荣誉比武排行 SET 分区信息='" + World.分区编号 + "'");
		string sqlCommand13 = string.Format("UPDATE TBL_师徒数据 SET 分区='" + World.分区编号 + "'");
		string sqlCommand14 = string.Format("UPDATE  荣誉乱斗排行 SET 分区信息='" + World.分区编号 + "'");
		string sqlCommand15 = string.Format("UPDATE  荣誉门派排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand16 = string.Format("UPDATE  荣誉势力排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand17 = string.Format("UPDATE  荣誉讨伐排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand18 = string.Format("UPDATE  荣誉武林排行 SET FLD_FQ='" + World.分区编号 + "'");
		string sqlCommand19 = string.Format("UPDATE  荣誉仙魔排行 SET 分区信息='" + World.分区编号 + "'");
		string sqlCommand20 = string.Format("UPDATE  充值排名 SET 分区信息='" + World.分区编号 + "'");
		DBA.ExeSqlCommand(sqlCommand, "GameServer");
		DBA.ExeSqlCommand(sqlCommand2, "GameServer");
		DBA.ExeSqlCommand(sqlCommand3, "GameServer");
		DBA.ExeSqlCommand(sqlCommand4, "GameServer");
		DBA.ExeSqlCommand(sqlCommand5, "GameServer");
		DBA.ExeSqlCommand(sqlCommand6, "GameServer");
		DBA.ExeSqlCommand(sqlCommand7, "GameServer");
		DBA.ExeSqlCommand(sqlCommand8, "GameServer");
		DBA.ExeSqlCommand(sqlCommand9, "GameServer");
		DBA.ExeSqlCommand(sqlCommand10, "GameServer");
		DBA.ExeSqlCommand(sqlCommand11, "GameServer");
		DBA.ExeSqlCommand(sqlCommand12, "GameServer");
		DBA.ExeSqlCommand(sqlCommand13, "GameServer");
		DBA.ExeSqlCommand(sqlCommand14, "GameServer");
		DBA.ExeSqlCommand(sqlCommand15, "GameServer");
		DBA.ExeSqlCommand(sqlCommand16, "GameServer");
		DBA.ExeSqlCommand(sqlCommand17, "GameServer");
		DBA.ExeSqlCommand(sqlCommand18, "GameServer");
		DBA.ExeSqlCommand(sqlCommand19, "GameServer");
		DBA.ExeSqlCommand(sqlCommand20, "GameServer");
		Form1.WriteLine(55, "所有分区数据库合并完成");
	}

	public static void FlushAl20()
	{
		string sqlCommand = "TRUNCATE TABLE TBL_ACCOUNT";
		string sqlCommand2 = "TRUNCATE TABLE 封IP表";
		string sqlCommand3 = "TRUNCATE TABLE 限制IP多开表";
		DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
		DBA.ExeSqlCommand(sqlCommand2, "rxjhaccount");
		DBA.ExeSqlCommand(sqlCommand3, "rxjhaccount");
		Thread.Sleep(10);
		Form1.WriteLine(55, "所有分区账号库初始化删除完成");
	}

	public static void FlushAl21()
	{
		string sqlCommand = "TRUNCATE TABLE  TBL_XWWL_Char";
		string sqlCommand2 = "TRUNCATE TABLE  TBL_XWWL_Cw";
		string sqlCommand3 = "TRUNCATE TABLE  TBL_XWWL_CWarehouse";
		string sqlCommand4 = "TRUNCATE TABLE  每日任务";
		string sqlCommand5 = "TRUNCATE TABLE  TBL_XWWL_Guild";
		string sqlCommand6 = "TRUNCATE TABLE  TBL_XWWL_GuildMember";
		string sqlCommand7 = "TRUNCATE TABLE  TBL_XWWL_LWarehouse";
		string sqlCommand8 = "TRUNCATE TABLE  TBL_XWWL_PublicWarehouse";
		string sqlCommand9 = "TRUNCATE TABLE  TBL_XWWL_Warehouse";
		string sqlCommand10 = "TRUNCATE TABLE   岳父玫瑰排行";
		string sqlCommand11 = "TRUNCATE TABLE   荣誉系统排行";
		string sqlCommand12 = "TRUNCATE TABLE   荣誉比武排行";
		string sqlCommand13 = "TRUNCATE TABLE   TBL_师徒数据";
		string sqlCommand14 = "TRUNCATE TABLE   荣誉乱斗排行";
		string sqlCommand15 = "TRUNCATE TABLE   荣誉门派排行";
		string sqlCommand16 = "TRUNCATE TABLE   荣誉势力排行";
		string sqlCommand17 = "TRUNCATE TABLE   荣誉讨伐排行";
		string sqlCommand18 = "TRUNCATE TABLE   荣誉武林排行";
		string sqlCommand19 = "TRUNCATE TABLE   荣誉仙魔排行";
		string sqlCommand20 = "TRUNCATE TABLE   充值排名";
		string sqlCommand21 = "TRUNCATE TABLE   天魔神宫信息";
		DBA.ExeSqlCommand(sqlCommand, "GameServer");
		DBA.ExeSqlCommand(sqlCommand2, "GameServer");
		DBA.ExeSqlCommand(sqlCommand3, "GameServer");
		DBA.ExeSqlCommand(sqlCommand4, "GameServer");
		DBA.ExeSqlCommand(sqlCommand5, "GameServer");
		DBA.ExeSqlCommand(sqlCommand6, "GameServer");
		DBA.ExeSqlCommand(sqlCommand7, "GameServer");
		DBA.ExeSqlCommand(sqlCommand8, "GameServer");
		DBA.ExeSqlCommand(sqlCommand9, "GameServer");
		DBA.ExeSqlCommand(sqlCommand10, "GameServer");
		DBA.ExeSqlCommand(sqlCommand11, "GameServer");
		DBA.ExeSqlCommand(sqlCommand12, "GameServer");
		DBA.ExeSqlCommand(sqlCommand13, "GameServer");
		DBA.ExeSqlCommand(sqlCommand14, "GameServer");
		DBA.ExeSqlCommand(sqlCommand15, "GameServer");
		DBA.ExeSqlCommand(sqlCommand16, "GameServer");
		DBA.ExeSqlCommand(sqlCommand17, "GameServer");
		DBA.ExeSqlCommand(sqlCommand18, "GameServer");
		DBA.ExeSqlCommand(sqlCommand19, "GameServer");
		DBA.ExeSqlCommand(sqlCommand20, "GameServer");
		DBA.ExeSqlCommand(sqlCommand21, "GameServer");
		DBA.ExeSqlCommand("TRUNCATE TABLE XWWL_GameServerInfo");
		DBA.ExeSqlCommand("INSERT INTO [XWWL_GameServerInfo]([serverid],[ItemCount]) VALUES (1 ,1)");
		Thread.Sleep(10);
		Form1.WriteLine(55, "所有分区数据库初始化删除完成");
	}

	public static void FlushAl22()
	{
		string sqlCommand = "TRUNCATE TABLE 血战记录";
		string sqlCommand2 = "TRUNCATE TABLE 传书记录";
		string sqlCommand3 = "TRUNCATE TABLE 仙魔记录";
		string sqlCommand4 = "TRUNCATE TABLE 百宝记录";
		string sqlCommand5 = "TRUNCATE TABLE 帮战记录";
		string sqlCommand6 = "TRUNCATE TABLE 登陆记录";
		string sqlCommand7 = "TRUNCATE TABLE 掉落记录";
		string sqlCommand8 = "TRUNCATE TABLE 合成记录";
		string sqlCommand9 = "TRUNCATE TABLE 进化记录";
		string sqlCommand10 = "TRUNCATE TABLE 卡号记录";
		string sqlCommand11 = "TRUNCATE TABLE 开盒记录";
		string sqlCommand12 = "TRUNCATE TABLE 商店记录";
		string sqlCommand13 = "TRUNCATE TABLE 门战记录";
		string sqlCommand14 = "TRUNCATE TABLE 物品记录";
		string sqlCommand15 = "TRUNCATE TABLE 药品记录";
		string sqlCommand16 = "TRUNCATE TABLE 元宝记录";
		string sqlCommand17 = "TRUNCATE TABLE 首爆记录";
		string sqlCommand18 = "TRUNCATE TABLE 仓库记录";
		string sqlCommand19 = "TRUNCATE TABLE 复制记录";
		string sqlCommand20 = "TRUNCATE TABLE 锁定记录";
		string sqlCommand21 = "TRUNCATE TABLE 武勋记录";
		string sqlCommand22 = "TRUNCATE TABLE 兑换记录";
        string sqlCommand23 = "TRUNCATE TABLE 清理记录";
        DBA.ExeSqlCommand(sqlCommand, "GameLog");
		DBA.ExeSqlCommand(sqlCommand2, "GameLog");
		DBA.ExeSqlCommand(sqlCommand3, "GameLog");
		DBA.ExeSqlCommand(sqlCommand4, "GameLog");
		DBA.ExeSqlCommand(sqlCommand5, "GameLog");
		DBA.ExeSqlCommand(sqlCommand6, "GameLog");
		DBA.ExeSqlCommand(sqlCommand7, "GameLog");
		DBA.ExeSqlCommand(sqlCommand8, "GameLog");
		DBA.ExeSqlCommand(sqlCommand9, "GameLog");
		DBA.ExeSqlCommand(sqlCommand10, "GameLog");
		DBA.ExeSqlCommand(sqlCommand11, "GameLog");
		DBA.ExeSqlCommand(sqlCommand12, "GameLog");
		DBA.ExeSqlCommand(sqlCommand13, "GameLog");
		DBA.ExeSqlCommand(sqlCommand14, "GameLog");
		DBA.ExeSqlCommand(sqlCommand15, "GameLog");
		DBA.ExeSqlCommand(sqlCommand16, "GameLog");
		DBA.ExeSqlCommand(sqlCommand17, "GameLog");
		DBA.ExeSqlCommand(sqlCommand18, "GameLog");
		DBA.ExeSqlCommand(sqlCommand19, "GameLog");
		DBA.ExeSqlCommand(sqlCommand20, "GameLog");
		DBA.ExeSqlCommand(sqlCommand21, "GameLog");
		DBA.ExeSqlCommand(sqlCommand22, "GameLog");
        DBA.ExeSqlCommand(sqlCommand23, "GameLog");
        Thread.Sleep(10);
		Form1.WriteLine(55, "数据库其他记录初始化删除完成");
	}

	public static void 加载充值排名()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM TBL_XWWL_Char where FLD_FQID='" + World.分区编号 + "' and FLD_CZPH !=0  Order By FLD_CZPH Desc"), "GameServer");
		if (dBToDataTable == null || dBToDataTable.Rows.Count <= 0)
		{
			return;
		}
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			if (i == 0)
			{
				DataTable dBToDataTable2 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=31 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable2.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(31, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第一名★", 101, 350, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(31, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 1)
			{
				DataTable dBToDataTable3 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=32 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable3.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(32, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第二名★", 101, 330, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(32, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 2)
			{
				DataTable dBToDataTable4 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=33 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable4.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(33, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第三名★", 101, 310, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(33, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 3)
			{
				DataTable dBToDataTable5 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=34 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable5.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(34, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第四名★", 101, 290, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(34, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 4)
			{
				DataTable dBToDataTable6 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=35 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable6.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(35, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第五名★", 101, 270, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(35, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 5)
			{
				DataTable dBToDataTable7 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=36 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable7.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(36, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第六名★", 101, 250, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(36, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 6)
			{
				DataTable dBToDataTable8 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=37 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable8.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(37, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第七名★", 101, 230, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(37, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 7)
			{
				DataTable dBToDataTable9 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=38 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable9.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(38, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第八名★", 101, 210, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(38, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 8)
			{
				DataTable dBToDataTable10 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=39 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable10.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(39, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第九名★", 101, 190, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(39, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
			if (i == 9)
			{
				DataTable dBToDataTable11 = DBA.GetDBToDataTable(string.Format("SELECT * FROM 充值排名 where 全局id=40 and 分区信息= '" + World.分区编号 + "' "), "GameServer");
				if (dBToDataTable11.Rows.Count <= 0)
				{
					RxjhClass.写入充值排名(40, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), "★充值第十名★", 101, 170, 1347, (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
				else
				{
					RxjhClass.修改充值排名(40, dBToDataTable.Rows[i]["FLD_ID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString(), (int)dBToDataTable.Rows[i]["FLD_CZPH"], World.分区编号);
				}
			}
		}
		Thread.Sleep(100);
		Form1.WriteLine(2, "加载充值排名数据完成" + dBToDataTable.Rows.Count);
	}

	private void Form2_Load(object sender, EventArgs e)
	{
		listView2.ListViewItemSorter = new ListViewColumnSorter();
		listView2.ColumnClick += ListViewHelper.ListView_ColumnClick;
		foreach (地面物品类 value in World.ItmeTeM.Values)
		{
			try
			{
				string[] array = new string[17];
				try
				{
					array[0] = BitConverter.ToInt64(value.物品.物品全局ID, 0).ToString();
					array[1] = BitConverter.ToInt32(value.物品.物品ID, 0).ToString();
					array[2] = value.物品.得到物品名称();
					array[3] = value.Rxjh_Map.ToString();
					array[4] = value.Rxjh_X.ToString();
					array[5] = value.Rxjh_Y.ToString();
					array[6] = value.物品.FLD_MAGIC0.ToString();
					array[7] = value.物品.FLD_MAGIC1.ToString();
					array[8] = value.物品.FLD_MAGIC2.ToString();
					array[9] = value.物品.FLD_MAGIC3.ToString();
					array[10] = value.物品.FLD_MAGIC4.ToString();
					array[11] = value.物品优先权.UserName;
				}
				catch
				{
					array[0] = BitConverter.ToInt64(value.物品.物品全局ID, 0).ToString();
					string[] array2 = array;
					string text = (array2[1] = BitConverter.ToInt32(value.物品.物品ID, 0).ToString());
					array[2] = value.物品.得到物品名称();
					string[] array3 = array;
					string text2 = (array3[3] = value.Rxjh_Map.ToString());
					string[] array4 = array;
					string text3 = (array4[4] = value.Rxjh_X.ToString());
					string[] array5 = array;
					string text4 = (array5[5] = value.Rxjh_Y.ToString());
					string[] array6 = array;
					string text5 = (array6[6] = value.物品.FLD_MAGIC0.ToString());
					string[] array7 = array;
					string text6 = (array7[7] = value.物品.FLD_MAGIC1.ToString());
					string[] array8 = array;
					string text7 = (array8[8] = value.物品.FLD_MAGIC2.ToString());
					string[] array9 = array;
					string text8 = (array9[9] = value.物品.FLD_MAGIC3.ToString());
					string[] array10 = array;
					string text9 = (array10[10] = value.物品.FLD_MAGIC4.ToString());
				}
				listView2.Items.Insert(listView2.Items.Count, new ListViewItem(array));
			}
			catch
			{
			}
		}
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form2));
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader15 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader16 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader17 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.SuspendLayout();
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader8,
            this.columnHeader10,
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader14,
            this.columnHeader13,
            this.columnHeader15,
            this.columnHeader16,
            this.columnHeader17});
            this.listView2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView2.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(0, 0);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(769, 384);
            this.listView2.TabIndex = 3;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "全局ID";
            this.columnHeader2.Width = 65;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "物品ID";
            this.columnHeader3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.columnHeader3.Width = 79;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "物品名";
            this.columnHeader4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.columnHeader4.Width = 98;
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "地图";
            this.columnHeader8.Width = 42;
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "X";
            this.columnHeader10.Width = 61;
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "Y";
            this.columnHeader11.Width = 61;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "属性0";
            this.columnHeader12.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "属性1";
            this.columnHeader14.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "属性2";
            this.columnHeader13.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // columnHeader15
            // 
            this.columnHeader15.Text = "属性3";
            this.columnHeader15.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // columnHeader16
            // 
            this.columnHeader16.Text = "属性4";
            this.columnHeader16.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // columnHeader17
            // 
            this.columnHeader17.Text = "优先";
            this.columnHeader17.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.columnHeader17.Width = 56;
            // 
            // Form2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(769, 384);
            this.Controls.Add(this.listView2);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "Form2";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "地面物品";
            this.Load += new System.EventHandler(this.Form2_Load);
            this.ResumeLayout(false);

	}
}

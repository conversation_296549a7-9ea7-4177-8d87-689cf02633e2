﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer
{
    public class 服务器维护类
    {
        private System.Timers.Timer 维护倒计时;
        private DateTime 停机时间;
        private int 倒计时分钟 = 5; // 默认5分钟
        private int 维护时长 = 15; // 默认15分钟
        private bool _维护中 = false;

        // 公共属性：允许外部检查维护状态
        public bool 维护中
        {
            get { return _维护中; }
        }

        // 公共属性：允许外部检查是否在倒计时中
        public bool 维护倒计时中
        {
            get { return 维护倒计时?.Enabled ?? false; }
        }

        public 服务器维护类()
        {
            维护倒计时 = new System.Timers.Timer(60000); // 每分钟执行一次
            维护倒计时.Elapsed += 发送维护公告;
        }

        public void 发送维护倒计时(int 剩余分钟)
        {
            try
            {
                int 剩余秒数 = 剩余分钟 * 60;

                foreach (Players player in World.allConnectedChars.Values)
                {
                    if (player != null && player.Client != null)
                    {
                        player.发送其他活动开始倒计时(剩余秒数);
                    }
                }
            }
            catch (Exception ex)
            {
                RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "服务器维护", "发送维护倒计时", $"剩余分钟: {剩余分钟}");
                Form1.WriteLine(1, "发送维护倒计时出错：" + ex.Message);
                维护日志("发送维护倒计时出错：" + ex.Message);
            }
        }


        private void 维护日志(string 日志内容)
        {
            try
            {
                if (!Directory.Exists("logs"))
                {
                    Directory.CreateDirectory("logs");
                }
                using (StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\服务器维护_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read)))
                {
                    streamWriter.Write(DateTime.Now.ToString() + " " + 日志内容 + "\r\n");
                }
            }
            catch
            {
                
            }
        }

        public bool 开始维护(int 时长 = 15, int 倒计时 = 5)
        {
            try
            {
                倒计时分钟 = 倒计时;
                停机时间 = DateTime.Now.AddMinutes(倒计时分钟);
                维护时长 = 时长;
                string 公告内容 = $"{倒计时分钟}分钟后服务器将进行例行维护，请大家及时下线，本次维护预计{维护时长}分钟";
                World.发送特殊公告(公告内容, 6, "公告");

                发送维护倒计时(倒计时分钟);

                维护倒计时.Enabled = true;

                Form1.WriteLine(2, $"服务器维护程序已启动，将在{倒计时分钟}分钟后执行维护");
                维护日志($"服务器维护程序已启动，将在{倒计时分钟}分钟后执行维护");

                return true;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "开始维护出错：" + ex.Message);
                维护日志("开始维护出错：" + ex.Message);
                return false;
            }
        }

        private void 发送维护公告(object sender, ElapsedEventArgs e)
        {
            try
            {
                int 剩余分钟 = (int)停机时间.Subtract(DateTime.Now).TotalMinutes;

                if (剩余分钟 <= 0)
                {
                    维护倒计时.Enabled = false;
                    执行维护();
                    return;
                }

                string 公告内容 = $"{剩余分钟}分钟后服务器将进行例行维护，请大家及时下线，本次维护预计{维护时长}分钟";
                World.发送特殊公告(公告内容, 6, "公告");

                发送维护倒计时(剩余分钟);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "发送维护公告出错：" + ex.Message);
                维护日志("发送维护公告出错：" + ex.Message);
            }
        }

        private void 执行维护()
        {
            try
            {
                _维护中 = true;

                string 公告内容 = "服务器即将进行维护，所有玩家将被强制下线";
                World.发送特殊公告(公告内容, 6, "公告");

                Thread.Sleep(2000);

                存档所有玩家();

                断开所有玩家连接();

                更新账号在线状态();

                Form1.WriteLine(2, $"服务器维护完成，预计{维护时长}分钟后恢复");
                维护日志($"服务器维护完成，预计{维护时长}分钟后恢复，维护开始时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "执行维护出错：" + ex.Message);
                维护日志("执行维护出错：" + ex.Message);
            }
        }

        public bool 立即维护()
        {
            try
            {
                // 通知EvoLogin进入维护状态
                if (World.conn != null)
                {
                    World.conn.发送("服务器维护状态|1|服务器维护中，请稍后再试");
                    Form1.WriteLine(2, "已通知EvoLogin进入维护状态");
                }

                string 公告内容 = "服务器即将进行维护，所有玩家将被强制下线";
                World.发送特殊公告(公告内容, 6, "公告");

                维护日志("开始执行立即维护操作...");

                Thread.Sleep(5000);

                存档所有玩家();

                断开所有玩家连接();

                更新账号在线状态();

                _维护中 = true;

                Form1.WriteLine(2, "服务器已进入立即维护状态");
                维护日志($"服务器已进入立即维护状态，维护开始时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}，预计维护时长：{维护时长}分钟");
                return true;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "立即维护出错：" + ex.Message);
                维护日志("立即维护出错：" + ex.Message);
                return false;
            }
        }

        private void 存档所有玩家()
        {
            try
            {
                Form1.WriteLine(2, "开始保存所有玩家数据...");
                维护日志("开始保存所有玩家数据...");

                List<Players> players = new List<Players>();
                foreach (Players player in World.allConnectedChars.Values)
                {
                    players.Add(player);
                }

                int 成功数量 = 0;
                int 失败数量 = 0;

                foreach (Players player in players)
                {
                    try
                    {
                        player.保存人物的数据();
                        成功数量++;
                    }
                    catch (Exception ex)
                    {
                        失败数量++;
                        Form1.WriteLine(1, $"保存玩家[{player.UserName}]数据出错：{ex.Message}");
                        维护日志($"保存玩家[{player.UserName}]数据出错：{ex.Message}");
                    }
                }

                Form1.WriteLine(8, $"所有玩家数据保存完成，总共{players.Count}人，成功：{成功数量}，失败：{失败数量}");
                维护日志($"所有玩家数据保存完成，总共{players.Count}人，成功：{成功数量}，失败：{失败数量}");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "存档所有玩家出错：" + ex.Message);
                维护日志("存档所有玩家出错：" + ex.Message);
            }
        }

        private void 断开所有玩家连接()
        {
            try
            {
                Form1.WriteLine(2, "开始断开所有玩家连接...");
                维护日志("开始断开所有玩家连接...");

                List<Players> players = new List<Players>();
                foreach (Players player in World.allConnectedChars.Values)
                {
                    players.Add(player);
                }

                int 成功数量 = 0;
                int 失败数量 = 0;

                foreach (Players player in players)
                {
                    try
                    {
                        if (player.Client != null)
                        {
                            // 记录玩家登出信息
                            RxjhClass.登陆记录(UserId: player.Userid,
                                        UserName: player.UserName,
                                        UserIp: player.zastcoginip,
                                        类型: "维护退出",
                                        分区: World.服务器ID.ToString());

                            // 执行完整的下线流程
                            if (!player.退出中)
                            {
                                player.Logout(World.服务器ID + "线维护退出");
                            }

                            // 处理特殊状态的玩家
                            if (player.Client.挂机)
                            {
                                player.Client.DisposedOffline();
                                World.离线数量--;
                                if (World.离线数量 < 0) World.离线数量 = 0;
                            }
                            else if (player.Client.假人)
                            {
                                player.Client.DisposedOffline();
                                World.假人数量--;
                                if (World.假人数量 < 0) World.假人数量 = 0;
                            }
                            else if (player.Client.云挂机)
                            {
                                player.Client.DisposedOffline();
                                World.云挂机数量--;
                                if (World.云挂机数量 < 0) World.云挂机数量 = 0;
                            }
                            else
                            {
                                // 正常在线玩家
                                player.Client.Dispose();
                            }

                            成功数量++;
                        }
                    }
                    catch (Exception ex)
                    {
                        失败数量++;
                        Form1.WriteLine(1, $"断开玩家[{player.UserName}]连接出错：{ex.Message}");
                        维护日志($"断开玩家[{player.UserName}]连接出错：{ex.Message}");
                    }
                }

                Form1.WriteLine(8, $"所有玩家连接已断开，总共{players.Count}人，成功：{成功数量}，失败：{失败数量}");
                维护日志($"所有玩家连接已断开，总共{players.Count}人，成功：{成功数量}，失败：{失败数量}");

                // 清理残留的连接和数据
                清理残留连接();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "断开所有玩家连接出错：" + ex.Message);
                维护日志("断开所有玩家连接出错：" + ex.Message);
            }
        }

        private void 更新账号在线状态()
        {
            try
            {
                Form1.WriteLine(2, "开始更新数据库账号在线状态...");
                维护日志("开始更新数据库账号在线状态...");

                string 更新账号SQL = "UPDATE TBL_ACCOUNT SET FLD_ONLINE=0, 账号是否在线=0, 随机时间在线=0 WHERE FLD_ONLINE=1 OR 账号是否在线=1 OR 随机时间在线=1";
                int 账号更新数量 = DBA.ExeSqlCommand(更新账号SQL, "rxjhaccount");

                string 更新角色SQL = "UPDATE TBL_XWWL_Char SET 角色是否在线=0 WHERE 角色是否在线=1";
                int 角色更新数量 = DBA.ExeSqlCommand(更新角色SQL, "GameServer");

                Form1.WriteLine(8, $"更新账号在线状态完成，账号表更新{账号更新数量}条，角色表更新{角色更新数量}条");
                维护日志($"更新账号在线状态完成，账号表更新{账号更新数量}条，角色表更新{角色更新数量}条");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "更新账号在线状态出错：" + ex.Message);
                维护日志("更新账号在线状态出错：" + ex.Message);
            }
        }

        private void 清理残留连接()
        {
            try
            {
                Form1.WriteLine(2, "开始清理残留连接和数据...");
                维护日志("开始清理残留连接和数据...");

                // 清理World.allConnectedChars中的残留数据
                int 清理数量 = 0;
                var 残留玩家 = new List<int>();

                foreach (var kvp in World.allConnectedChars)
                {
                    残留玩家.Add(kvp.Key);
                }

                foreach (int worldId in 残留玩家)
                {
                    if (World.allConnectedChars.TryRemove(worldId, out _))
                    {
                        清理数量++;
                    }
                }

                // 清理World.list中的残留连接
                int 连接清理数量 = 0;
                var 残留连接 = new List<int>();

                foreach (var kvp in World.list)
                {
                    残留连接.Add(kvp.Key);
                }

                foreach (int worldId in 残留连接)
                {
                    if (World.list.TryGetValue(worldId, out var netState))
                    {
                        try
                        {
                            netState?.Dispose();
                            连接清理数量++;
                        }
                        catch (Exception ex)
                        {
                            Form1.WriteLine(1, $"清理连接[{worldId}]出错：{ex.Message}");
                        }
                    }
                }

                Form1.WriteLine(8, $"残留数据清理完成，玩家数据：{清理数量}，网络连接：{连接清理数量}");
                维护日志($"残留数据清理完成，玩家数据：{清理数量}，网络连接：{连接清理数量}");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "清理残留连接出错：" + ex.Message);
                维护日志("清理残留连接出错：" + ex.Message);
            }
        }

        public bool 取消维护()
        {
            try
            {
                if (维护倒计时.Enabled)
                {
                    维护倒计时.Enabled = false;
                    World.发送特殊公告("服务器维护已取消", 6, "公告");
                    Form1.WriteLine(2, "服务器维护已取消");
                    维护日志($"服务器维护已取消，取消时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                    return true;
                }
                else if (_维护中)
                {
                    _维护中 = false;

                    // 通知EvoLogin退出维护状态
                    if (World.conn != null)
                    {
                        World.conn.发送("服务器维护状态|0|");
                        Form1.WriteLine(2, "已通知EvoLogin退出维护状态");
                    }

                    Form1.WriteLine(2, "服务器维护状态已重置");
                    维护日志($"服务器维护状态已重置，时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");

                    return true;
                }
                else
                {
                    Form1.WriteLine(2, "没有正在进行的维护任务可取消");
                    维护日志("没有正在进行的维护任务可取消");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "取消维护出错：" + ex.Message);
                维护日志("取消维护出错：" + ex.Message);
                return false;
            }
        }
    }
}
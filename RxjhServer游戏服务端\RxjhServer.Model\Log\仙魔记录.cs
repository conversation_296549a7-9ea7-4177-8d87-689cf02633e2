﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 仙魔记录 {

		[JsonProperty, Column(DbType = "varchar(50)")]
		public string 分区 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)")]
		public string FLD_名字 { get; set; }

		[JsonProperty]
		public int? FLD_在线 { get; set; } = 0;

		[JsonProperty, Column(DbType = "varchar(255)")]
		public string FLD_种族 { get; set; }

	}

}

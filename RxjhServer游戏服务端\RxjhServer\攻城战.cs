using System;
using System.Threading;
using System.Timers;
using RxjhServer;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

public class 攻城战 : IDisposable
{
	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	private System.Timers.Timer timer_2;

	private System.Timers.Timer timer_3;

	private DateTime 准备时间;

	private DateTime 预备时间;

	private DateTime 进行时间;

	private DateTime 进程总时间;

	public bool 火龙之力释放;

	public static DateTime 当前进程结束时间;

	public string 攻城前天魔神宫占领者;

	public NpcClass 守城雕像;

	public 攻城战()
	{
		守城雕像 = null;
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "新攻城战开始");
			}
			World.天魔神宫大门是否死亡 = 0;
			World.天魔神宫东门是否死亡 = 0;
			进程总时间 = DateTime.Now;
			准备时间 = DateTime.Now.AddMinutes(1.0);
			当前进程结束时间 = 准备时间;
			World.攻城战进程 = 1;
			timer_0 = new System.Timers.Timer(10000.0);
			timer_0.Elapsed += 时间结束事件1;
			timer_0.Enabled = true;
			timer_0.AutoReset = true;
			NpcClass npcClass = World.AddNpc(16430, -430f, -393f, 42001, 0, 0, 一次性怪: true, 600);
			if (npcClass != null)
			{
				npcClass.Max_Rxjh_HP += World.城门强化等级 * 250000;
				npcClass.Rxjh_HP = npcClass.Max_Rxjh_HP;
				npcClass.FLD_DF += World.城门强化等级 * 50;
			}
			NpcClass npcClass2 = World.AddNpc(16431, 50f, 468f, 42001, 1, 0, 一次性怪: true, 600);
			if (npcClass2 != null)
			{
				npcClass2.Max_Rxjh_HP += World.城门强化等级 * 250000;
				npcClass2.Rxjh_HP = npcClass2.Max_Rxjh_HP;
				npcClass2.FLD_DF += World.城门强化等级 * 50;
			}
			守城雕像 = World.AddNpc(16435, -436f, 530f, 42001, 0, 0, 一次性怪: true, 600);
			攻城前天魔神宫占领者 = World.天魔神宫占领者;
			if (World.势力战开始时向其它线广播 == 1)
			{
				if (World.服务器ID == 28)
				{
					string text = World.攻城战预备时间 + "分钟后将在银币广场,开启攻城战.请要参加的玩家前往银币广场参加";
					World.conn.发送("全线公告|" + 3 + "|" + text + "|攻城战");
				}
				else
				{
					string text2 = World.攻城战预备时间 + "分钟后将在" + World.服务器ID + "线,开启攻城战.请要参加的玩家前往" + World.服务器ID + "线参加";
					World.conn.发送("全线公告|" + 3 + "|" + text2 + "|攻城战");
				}
			}
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.宣告攻城 == 1 && value.帮派名字 != "")
				{
					天魔神宫.发出攻城战准备开始第一次通知(value);
				}
			}
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "攻城战", "攻城战开始", "攻城战初始化");
			Form1.WriteLine(1, "攻城战开始 出错：" + ex.StackTrace);
		}
	}

	public void 时间结束事件1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "攻城战_时间结束事件1");
		}
		try
		{
			int num = (int)准备时间.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			World.攻城战进程 = 2;
			num = 0;
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
			预备时间 = DateTime.Now.AddMinutes(World.攻城战预备时间);
			当前进程结束时间 = 预备时间;
			timer_1 = new System.Timers.Timer(10000.0);
			timer_1.Elapsed += 时间结束事件2;
			timer_1.Enabled = true;
			timer_1.AutoReset = true;
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 42001)
				{
					value.发送攻城战剩余时间(180);
				}
				else if (value.宣告攻城 == 1 && value.帮派名字 != "")
				{
					天魔神宫.发出攻城战准备开始第二次通知(value);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "攻城战 时间结束事件1 出错：" + ex.StackTrace);
		}
	}

	public void 时间结束事件2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "攻城战_时间结束事件2");
		}
		try
		{
			int num = (int)预备时间.Subtract(DateTime.Now).TotalSeconds;
			if (World.攻城战进程 == 3)
			{
				num = 0;
			}
			if (num > 0)
			{
				return;
			}
			World.攻城战进程 = 3;
			num = 0;
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
			进行时间 = DateTime.Now.AddMinutes(World.攻城战时长);
			当前进程结束时间 = 进行时间;
			timer_2 = new System.Timers.Timer(10000.0);
			timer_2.Elapsed += 时间结束事件3;
			timer_2.Enabled = true;
			timer_2.AutoReset = true;
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 42001)
				{
					value.切换PK模式(2);
					value.发送攻城战剩余时间(World.攻城战时长 * 60);
					if (value.门派联盟盟主 == World.天魔神宫占领者)
					{
						value.移动(-437f, 89f, 15f, 42001);
					}
					else
					{
						value.移动(-430f, -660f, 15f, 42001);
					}
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "攻城战 时间结束事件2 出错：" + ex.StackTrace);
		}
	}

	public void 时间结束事件3(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "攻城战_时间结束事件3");
		}
		try
		{
			int num = (int)进行时间.Subtract(DateTime.Now).TotalSeconds;
			if (World.攻城战进程 == 2)
			{
				num = 0;
			}
			if (DateTime.Now.Subtract(进程总时间).TotalSeconds > 3600.0)
			{
				num = 0;
				World.发送特殊公告("天魔神宫攻城战时间超过1小时，强制结束攻城战。", 6, "公告");
			}
			if (num > 0)
			{
				return;
			}
			num = 0;
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
			if (World.攻城战进程 == 3)
			{
				if (World.天魔神宫占领者 != "")
				{
					if (World.天魔神宫占领者 == 攻城前天魔神宫占领者)
					{
						RxjhClass.更新天魔神宫信息(World.天魔神宫占领者, World.初始占领日期.ToString(), 0, World.分区编号);
						foreach (Players value in World.allConnectedChars.Values)
						{
							if (value.人物坐标_地图 == 42001)
							{
								value.发送守城成功消息(9, World.天魔神宫占领者);
							}
						}
					}
					else
					{
						World.初始占领日期 = int.Parse(Converter.DateTimeToString(DateTime.Now));
						RxjhClass.更新天魔神宫信息(World.天魔神宫占领者, Converter.DateTimeToString(DateTime.Now), 0, World.分区编号);
						foreach (Players value2 in World.allConnectedChars.Values)
						{
							if (value2.人物坐标_地图 == 42001)
							{
								value2.发送守城成功消息(11, World.天魔神宫占领者);
								if (value2.门派联盟盟主 == 攻城前天魔神宫占领者)
								{
									天魔神宫.发送攻城相关BUFF(value2, 是否消失: false);
								}
								else if (value2.门派联盟盟主 == World.天魔神宫占领者)
								{
									天魔神宫.发送攻城相关BUFF(value2, 是否消失: false);
								}
							}
						}
					}
				}
				foreach (Players value3 in World.allConnectedChars.Values)
				{
					if (value3.人物坐标_地图 != 42001)
					{
						continue;
					}
					if (value3.人物坐标_地图 == 42001)
					{
						if (value3.门派联盟盟主 == World.天魔神宫占领者)
						{
							Thread.Sleep(100);
							value3.活动奖励系统(3);
						}
						else
						{
							Thread.Sleep(100);
							value3.活动奖励系统(4);
						}
					}
					value3.系统提示("1分钟后将传送出去", 10, "系统提示");
				}
				World.攻城战进程 = 4;
				timer_3 = new System.Timers.Timer(60000.0);
				timer_3.Elapsed += 时间结束事件4;
				timer_3.Enabled = true;
				timer_3.AutoReset = true;
			}
			else
			{
				if (World.攻城战进程 != 2)
				{
					return;
				}
				World.AddNpc(16430, -430f, -393f, 42001, 0, 0, 一次性怪: true, 600);
				World.AddNpc(16431, 50f, 468f, 42001, 1, 0, 一次性怪: true, 600);
				守城雕像 = World.AddNpc(16435, -436f, 530f, 42001, 0, 0, 一次性怪: true, 600);
				World.攻城战进程 = 3;
				World.天魔神宫大门是否死亡 = 0;
				World.天魔神宫东门是否死亡 = 0;
				进行时间 = DateTime.Now.AddMinutes(10.0);
				当前进程结束时间 = 进行时间;
				timer_2 = new System.Timers.Timer(10000.0);
				timer_2.Elapsed += 时间结束事件3;
				timer_2.Enabled = true;
				timer_2.AutoReset = true;
				{
					foreach (Players value4 in World.allConnectedChars.Values)
					{
						if (value4.人物坐标_地图 == 42001)
						{
							if (value4.人物PK模式 != 2)
							{
								value4.切换PK模式(2);
							}
							value4.发送攻城战剩余时间(600);
							if (value4.门派联盟盟主 == World.天魔神宫占领者)
							{
								value4.死亡移动(-437f, 89f, 15f, 42001);
							}
							else
							{
								value4.死亡移动(-430f, -660f, 15f, 42001);
							}
						}
					}
					return;
				}
			}
		}
		catch (Exception ex)
		{
			World.攻城.Dispose();
			Form1.WriteLine(1, "攻城战 时间结束事件3 出错：" + ex.StackTrace);
		}
	}

	public void 时间结束事件4(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "攻城战_时间结束事件4");
		}
		try
		{
			timer_3.Enabled = false;
			timer_3.Close();
			timer_3.Dispose();
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 42001)
				{
					value.移动(10f, 10f, 15f, 1201);
				}
			}
			Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "攻城战 时间结束事件4 出错：" + ex.StackTrace);
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "攻城战-Dispose");
		}
		World.攻城战进程 = 0;
		if (timer_0 != null)
		{
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
		}
		if (timer_1 != null)
		{
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
		}
		if (timer_2 != null)
		{
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
		}
		if (timer_3 != null)
		{
			timer_3.Enabled = false;
			timer_3.Close();
			timer_3.Dispose();
		}
		string text = "攻城战已经结束";
		World.conn.发送("全线公告|" + 1 + "|" + text + "|攻城战");
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.人物坐标_地图 == 42001)
			{
				value.切换PK模式(0);
				value.移动(10f, 10f, 15f, 1201);
			}
		}
		World.攻城 = null;
		World.delNpc(42001, 16430);
		World.delNpc(42001, 16431);
		World.delNpc(42001, 16435);
	}
}

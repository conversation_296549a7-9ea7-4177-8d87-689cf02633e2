using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class 神女异常状态类 : IDisposable
{
	public System.Timers.Timer npcyd;

	public System.Timers.Timer yczt;

	public DateTime time;

	public Players Play;

	public NpcClass Npc;

	private int _FLD_PID;

	private double _FLD_NUM;

	private double _伤害;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_NUM
	{
		get
		{
			return _FLD_NUM;
		}
		set
		{
			_FLD_NUM = value;
		}
	}

	private double 伤害
	{
		get
		{
			return _伤害;
		}
		set
		{
			_伤害 = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-Dispose");
		}
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (yczt != null)
		{
			yczt.Enabled = false;
			yczt.Close();
			yczt.Dispose();
			yczt = null;
		}
		Play = null;
		Npc = null;
	}

	public 神女异常状态类(Players Play_, int 时间, int 异常ID, double 异常数量, double 伤害)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-NEW");
		}
		FLD_PID = 异常ID;
		FLD_NUM = 异常数量;
		this.伤害 = 伤害;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Play = Play_;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 神女时间结束事件;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000);
	}

	public void 神女时间结束事件(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "时间结束事件1");
		}
		时间结束事件();
	}

	public void 时间结束事件()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (Play == null || Play.Client.挂机)
		{
			Dispose();
			return;
		}
		if (!Play.退出中 && Play.Client.Running)
		{
			try
			{
				switch (FLD_PID)
				{
				case 52:
					Play.FLD_神女_追加_攻击 = 0;
					Play.FLD_神女_追加_防御 = 0;
					Play.更新武功和状态();
					break;
				case 34:
					Play.FLD_神女_减少_攻击 = 0;
					Play.FLD_神女_减少_防御 = 0;
					Play.更新武功和状态();
					break;
				case 35:
					Play.FLD_神女_减少_攻击 = 0;
					Play.FLD_神女_减少_防御 = 0;
					Play.更新武功和状态();
					break;
				case 42:
					Play.属性封印 = 0.0;
					Play.更新武功和状态();
					break;
				case 44:
					Play.触发人物阎王爆();
					break;
				case 882:
					Play.FLD_负数武勋伤害减少 = 0.0;
					Play.更新HP_MP_SP();
					break;
				case 242:
					Play.FLD_人物_追加_攻击 -= 15;
					if (Play.FLD_人物_追加_攻击 < 0)
					{
						Play.FLD_人物_追加_攻击 = 0;
					}
					Play.FLD_人物_追加_防御 -= 15;
					if (Play.FLD_人物_追加_防御 < 0)
					{
						Play.FLD_人物_追加_防御 = 0;
					}
					Play.人物追加最大_HP -= 300;
					Play.人物追加最大_MP -= 300;
					Play.FLD_人物_追加_经验百分比 -= 0.2;
					Play.FLD_结婚礼物_追加_属性石 = 0;
					Play.更新武功和状态();
					Play.更新HP_MP_SP();
					break;
				}
				Play.神女异常状态.TryRemove(FLD_PID, out var _);
				神女状态效果(FLD_PID);
				Dispose();
				return;
			}
			catch (Exception ex)
			{
				RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "神女异常状态类", "时间结束事件", $"玩家ID: {FLD_PID}");
				Form1.WriteLine(1, "神女异常状态类 时间结束事件出错：[" + FLD_PID + "]" + ex);
				return;
			}
			finally
			{
				Dispose();
			}
		}
		if (Play.神女异常状态 != null)
		{
			Play.神女异常状态.Clear();
		}
		Dispose();
	}

	public void 神女状态效果(int 异常ID)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-状态效果");
		}
		byte[] array = Converter.hexStringToByte("AA553E00250040153800000000002500000034000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
		if (Play != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send(array, array.Length);
			}
			Play.发送当前范围广播数据(array, array.Length);
		}
	}

	public void 状态效果(int 异常ID, int 开关, int 异常数量, int 时间)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-状态效果");
		}
		byte[] array = Converter.hexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
		if (Play != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send多包(array, array.Length);
			}
			Play.发送当前范围广播数据多包(array, array.Length);
		}
		else if (Npc != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
			Npc.广播数据(array, array.Length);
		}
	}
}

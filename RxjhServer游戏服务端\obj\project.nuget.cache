{"version": 2, "dgSpecHash": "Wgm3F5iZ+CA=", "success": true, "projectFilePath": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\freesql\\3.5.202\\freesql.3.5.202.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\freesql.provider.sqlserverforsystem\\3.5.202\\freesql.provider.sqlserverforsystem.3.5.202.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hpsocket.net\\6.0.3.1\\hpsocket.net.6.0.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\keralua\\1.4.4\\keralua.1.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.1\\microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlua\\1.7.5\\nlua.1.7.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\9.0.6\\system.collections.immutable.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.nrbf\\9.0.6\\system.formats.nrbf.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\9.0.6\\system.reflection.metadata.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.extensions\\9.0.6\\system.resources.extensions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512"], "logs": []}
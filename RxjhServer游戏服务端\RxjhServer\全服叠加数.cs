using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 全服叠加数 : Form
{
	public class ListBoxItem
	{
		public string ItemText;

		public int ItemValue;

		public ListBoxItem(string text, int val)
		{
			ItemText = text;
			ItemValue = val;
		}
	}

	private IContainer components = new Container();

	private TabPage tabPage3;

	private GroupBox groupBox4;

	private Button buttonSeeZBItem;

	private ListView listViewItem1;

	private ColumnHeader columnHeader_0;

	private ColumnHeader columnHeader_1;

	private ColumnHeader columnHeader_2;

	private ColumnHeader columnHeader_3;

	private ColumnHeader columnHeader_8;

	private ColumnHeader columnHeader_4;

	private ColumnHeader columnHeader_5;

	private ColumnHeader columnHeader_6;

	private Button buttonSeePackItem;

	private Button button1;

	private TabControl tabControl1;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 删除数据;

	private ToolStripMenuItem 绑定物品;

	private ToolStripMenuItem 解绑物品;

	public TextBox textBoxKey;

	private Label label34;

	public int 序列号 = 0;

	public 全服叠加数()
	{
		InitializeComponent();
		序列号 = 0;
		listViewItem1.ContextMenuStrip = contextMenuStrip1;
		contextMenuStrip1.Closed += contextMenuStrip1_Closed;
	}

	private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
	{
		listViewItem1.ContextMenuStrip = contextMenuStrip1;
	}

	private void buttonSeePackItem_Click(object sender, EventArgs e)
	{
		序列号 = 1;
		listViewItem1.Items.Clear();
		foreach (Players value in World.allConnectedChars.Values)
		{
			for (int i = 0; i < value.装备栏包裹.Length; i++)
			{
				物品类 物品类2 = value.装备栏包裹[i];
				if (物品类2 != null && 物品类2.Get物品数量 >= int.Parse(textBoxKey.Text) && 禁止物品(物品类2.Get物品ID))
				{
					string[] array = new string[9];
					try
					{
						array[0] = i.ToString();
						array[1] = 物品类2.Get物品ID.ToString();
						array[2] = 物品类2.得到物品名称();
						array[3] = 物品类2.Get物品数量.ToString();
						array[4] = 物品类2.Get物品全局ID.ToString();
						array[5] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
						array[6] = ((!物品类2.物品绑定) ? "无" : "绑定中");
						array[7] = value.UserName.ToString();
					}
					catch (Exception ex)
					{
						// 2025-0617 EVIAS 改进异常处理
						RxjhClass.HandleGameException(ex, value, "查看背包物品", $"物品位置: {i}, 物品ID: {物品类2?.Get物品ID}");
					}
					listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
				}
			}
		}
	}

	private void buttonSeeZBItem_Click(object sender, EventArgs e)
	{
		序列号 = 2;
		listViewItem1.Items.Clear();
		foreach (Players value in World.allConnectedChars.Values)
		{
			for (int i = 0; i < value.个人仓库.Length; i++)
			{
				物品类 物品类2 = value.个人仓库[i];
				if (物品类2 != null && 物品类2.Get物品数量 >= int.Parse(textBoxKey.Text) && 禁止物品(物品类2.Get物品ID))
				{
					string[] array = new string[9];
					try
					{
						array[0] = i.ToString();
						array[1] = 物品类2.Get物品ID.ToString();
						array[2] = 物品类2.得到物品名称();
						array[3] = 物品类2.Get物品数量.ToString();
						array[4] = 物品类2.Get物品全局ID.ToString();
						array[5] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
						array[6] = ((!物品类2.物品绑定) ? "无" : "绑定中");
						array[7] = value.UserName.ToString();
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, "奥里给14" + ex.StackTrace);
					}
					listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
				}
			}
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		序列号 = 3;
		listViewItem1.Items.Clear();
		foreach (Players value in World.allConnectedChars.Values)
		{
			for (int i = 0; i < value.公共仓库.Length; i++)
			{
				物品类 物品类2 = value.公共仓库[i];
				if (物品类2 != null && 物品类2.Get物品数量 >= int.Parse(textBoxKey.Text) && 禁止物品(物品类2.Get物品ID))
				{
					string[] array = new string[9];
					try
					{
						array[0] = i.ToString();
						array[1] = 物品类2.Get物品ID.ToString();
						array[2] = 物品类2.得到物品名称();
						array[3] = 物品类2.Get物品数量.ToString();
						array[4] = 物品类2.Get物品全局ID.ToString();
						array[5] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
						array[6] = ((!物品类2.物品绑定) ? "无" : "绑定中");
						array[7] = value.UserName.ToString();
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, "奥里给15" + ex.StackTrace);
					}
					listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
				}
			}
		}
	}

	public bool 禁止物品(long 物品)
	{
		if (物品 > 0)
		{
			switch (物品)
			{
			case 1007000007L:
			case 1008000007L:
			case 1008000008L:
			case 1008000044L:
			case 1008000045L:
			case 1008000055L:
			case 1008000077L:
			case 1008000088L:
				return false;
			}
		}
		return true;
	}

	private void 删除物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定删除玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
		Players players = World.检查玩家name(listViewItem1.SelectedItems[0].SubItems[7].Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化装备篮包裹();
				break;
			case 2:
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化个人仓库();
				break;
			case 3:
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化综合仓库();
				break;
			}
			players.保存人物的数据();
			MessageBox.Show("删除玩家物品完成");
		}
	}

	private void 绑定人物物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定绑定玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
		Players players = World.检查玩家name(listViewItem1.SelectedItems[0].SubItems[7].Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
			{
				byte[] array3 = new byte[60];
				players.装备栏包裹[num].物品绑定 = true;
				players.装备栏包裹[num].锁定 = true;
				byte[] 物品ID3 = players.装备栏包裹[num].物品ID;
				byte[] 物品全局ID3 = players.装备栏包裹[num].物品全局ID;
				Buffer.BlockCopy(players.装备栏包裹[num].物品_byte, 16, array3, 0, 60);
				int get物品数量3 = players.装备栏包裹[num].Get物品数量;
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定(物品全局ID3, 物品ID3, num, BitConverter.GetBytes(get物品数量3), array3, 绑定: true);
				players.初始化装备篮包裹();
				break;
			}
			case 2:
			{
				byte[] array2 = new byte[60];
				players.个人仓库[num].物品绑定 = true;
				players.个人仓库[num].锁定 = true;
				byte[] 物品ID2 = players.个人仓库[num].物品ID;
				byte[] 物品全局ID2 = players.个人仓库[num].物品全局ID;
				Buffer.BlockCopy(players.个人仓库[num].物品_byte, 16, array2, 0, 60);
				int get物品数量2 = players.个人仓库[num].Get物品数量;
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定个人仓库(物品全局ID2, 物品ID2, num, BitConverter.GetBytes(get物品数量2), array2, 绑定: true);
				players.初始化个人仓库();
				break;
			}
			case 3:
			{
				byte[] array = new byte[60];
				players.公共仓库[num].物品绑定 = true;
				players.公共仓库[num].锁定 = true;
				byte[] 物品ID = players.公共仓库[num].物品ID;
				byte[] 物品全局ID = players.公共仓库[num].物品全局ID;
				Buffer.BlockCopy(players.公共仓库[num].物品_byte, 16, array, 0, 60);
				int get物品数量 = players.公共仓库[num].Get物品数量;
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定综合仓库(物品全局ID, 物品ID, num, BitConverter.GetBytes(get物品数量), array, 绑定: true);
				players.初始化综合仓库();
				break;
			}
			}
			players.保存人物的数据();
			MessageBox.Show("绑定玩家物品完成");
		}
	}

	private void 解绑人物物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定解绑玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
		Players players = World.检查玩家name(listViewItem1.SelectedItems[0].SubItems[7].Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
			{
				byte[] array3 = new byte[60];
				players.装备栏包裹[num].物品绑定 = false;
				players.装备栏包裹[num].锁定 = false;
				byte[] 物品ID3 = players.装备栏包裹[num].物品ID;
				byte[] 物品全局ID3 = players.装备栏包裹[num].物品全局ID;
				Buffer.BlockCopy(players.装备栏包裹[num].物品_byte, 16, array3, 0, 60);
				int get物品数量3 = players.装备栏包裹[num].Get物品数量;
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定(物品全局ID3, 物品ID3, num, BitConverter.GetBytes(get物品数量3), array3, 绑定: false);
				players.初始化装备篮包裹();
				break;
			}
			case 2:
			{
				byte[] array2 = new byte[60];
				players.个人仓库[num].物品绑定 = false;
				players.个人仓库[num].锁定 = false;
				byte[] 物品ID2 = players.个人仓库[num].物品ID;
				byte[] 物品全局ID2 = players.个人仓库[num].物品全局ID;
				Buffer.BlockCopy(players.个人仓库[num].物品_byte, 16, array2, 0, 60);
				int get物品数量2 = players.个人仓库[num].Get物品数量;
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定个人仓库(物品全局ID2, 物品ID2, num, BitConverter.GetBytes(get物品数量2), array2, 绑定: false);
				players.初始化个人仓库();
				break;
			}
			case 3:
			{
				byte[] array = new byte[60];
				players.公共仓库[num].物品绑定 = false;
				players.公共仓库[num].锁定 = false;
				byte[] 物品ID = players.公共仓库[num].物品ID;
				byte[] 物品全局ID = players.公共仓库[num].物品全局ID;
				Buffer.BlockCopy(players.公共仓库[num].物品_byte, 16, array, 0, 60);
				int get物品数量 = players.公共仓库[num].Get物品数量;
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定综合仓库(物品全局ID, 物品ID, num, BitConverter.GetBytes(get物品数量), array, 绑定: false);
				players.初始化综合仓库();
				break;
			}
			}
			players.保存人物的数据();
			MessageBox.Show("解绑玩家物品完成");
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(全服叠加数));
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.button1 = new System.Windows.Forms.Button();
            this.buttonSeeZBItem = new System.Windows.Forms.Button();
            this.listViewItem1 = new System.Windows.Forms.ListView();
            this.columnHeader_0 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.buttonSeePackItem = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.删除数据 = new System.Windows.Forms.ToolStripMenuItem();
            this.绑定物品 = new System.Windows.Forms.ToolStripMenuItem();
            this.解绑物品 = new System.Windows.Forms.ToolStripMenuItem();
            this.textBoxKey = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.tabPage3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox4);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(646, 475);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "全服叠加";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.button1);
            this.groupBox4.Controls.Add(this.buttonSeeZBItem);
            this.groupBox4.Controls.Add(this.listViewItem1);
            this.groupBox4.Controls.Add(this.buttonSeePackItem);
            this.groupBox4.Location = new System.Drawing.Point(10, 6);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(631, 464);
            this.groupBox4.TabIndex = 0;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "右键/删除/绑定/解绑";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(173, 15);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 7;
            this.button1.Text = "综合仓库叠加";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // buttonSeeZBItem
            // 
            this.buttonSeeZBItem.Location = new System.Drawing.Point(92, 15);
            this.buttonSeeZBItem.Name = "buttonSeeZBItem";
            this.buttonSeeZBItem.Size = new System.Drawing.Size(75, 23);
            this.buttonSeeZBItem.TabIndex = 6;
            this.buttonSeeZBItem.Text = "个人仓库叠加";
            this.buttonSeeZBItem.UseVisualStyleBackColor = true;
            this.buttonSeeZBItem.Click += new System.EventHandler(this.buttonSeeZBItem_Click);
            // 
            // listViewItem1
            // 
            this.listViewItem1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listViewItem1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader_0,
            this.columnHeader_1,
            this.columnHeader_2,
            this.columnHeader_3,
            this.columnHeader_8,
            this.columnHeader_4,
            this.columnHeader_5,
            this.columnHeader_6});
            this.listViewItem1.ForeColor = System.Drawing.SystemColors.WindowText;
            this.listViewItem1.FullRowSelect = true;
            this.listViewItem1.GridLines = true;
            this.listViewItem1.HideSelection = false;
            this.listViewItem1.Location = new System.Drawing.Point(6, 44);
            this.listViewItem1.Name = "listViewItem1";
            this.listViewItem1.Size = new System.Drawing.Size(619, 414);
            this.listViewItem1.TabIndex = 5;
            this.listViewItem1.UseCompatibleStateImageBehavior = false;
            this.listViewItem1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader_0
            // 
            this.columnHeader_0.Text = "位置";
            this.columnHeader_0.Width = 36;
            // 
            // columnHeader_1
            // 
            this.columnHeader_1.Text = "PID";
            this.columnHeader_1.Width = 80;
            // 
            // columnHeader_2
            // 
            this.columnHeader_2.Text = "名字";
            this.columnHeader_2.Width = 100;
            // 
            // columnHeader_3
            // 
            this.columnHeader_3.Text = "物品数量";
            this.columnHeader_3.Width = 80;
            // 
            // columnHeader_8
            // 
            this.columnHeader_8.Text = "全局ID";
            this.columnHeader_8.Width = 80;
            // 
            // columnHeader_4
            // 
            this.columnHeader_4.Text = "物品来源";
            this.columnHeader_4.Width = 80;
            // 
            // columnHeader_5
            // 
            this.columnHeader_5.Text = "是否绑定";
            this.columnHeader_5.Width = 80;
            // 
            // columnHeader_6
            // 
            this.columnHeader_6.Text = "角色名字";
            this.columnHeader_6.Width = 80;
            // 
            // buttonSeePackItem
            // 
            this.buttonSeePackItem.Location = new System.Drawing.Point(11, 15);
            this.buttonSeePackItem.Name = "buttonSeePackItem";
            this.buttonSeePackItem.Size = new System.Drawing.Size(75, 23);
            this.buttonSeePackItem.TabIndex = 4;
            this.buttonSeePackItem.Text = "个人背包叠加";
            this.buttonSeePackItem.UseVisualStyleBackColor = true;
            this.buttonSeePackItem.Click += new System.EventHandler(this.buttonSeePackItem_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Location = new System.Drawing.Point(7, 6);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(654, 501);
            this.tabControl1.TabIndex = 63;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.删除数据,
            this.绑定物品,
            this.解绑物品});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(125, 70);
            // 
            // 删除数据
            // 
            this.删除数据.Name = "删除数据";
            this.删除数据.Size = new System.Drawing.Size(124, 22);
            this.删除数据.Text = "删除物品";
            this.删除数据.Click += new System.EventHandler(this.删除物品);
            // 
            // 绑定物品
            // 
            this.绑定物品.Name = "绑定物品";
            this.绑定物品.Size = new System.Drawing.Size(124, 22);
            this.绑定物品.Text = "绑定物品";
            this.绑定物品.Click += new System.EventHandler(this.绑定人物物品);
            // 
            // 解绑物品
            // 
            this.解绑物品.Name = "解绑物品";
            this.解绑物品.Size = new System.Drawing.Size(124, 22);
            this.解绑物品.Text = "解绑物品";
            this.解绑物品.Click += new System.EventHandler(this.解绑人物物品);
            // 
            // textBoxKey
            // 
            this.textBoxKey.Location = new System.Drawing.Point(66, 513);
            this.textBoxKey.Name = "textBoxKey";
            this.textBoxKey.Size = new System.Drawing.Size(63, 21);
            this.textBoxKey.TabIndex = 145;
            this.textBoxKey.Text = "100";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(19, 518);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(41, 12);
            this.label34.TabIndex = 146;
            this.label34.Text = "叠加数";
            // 
            // 全服叠加数
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(671, 547);
            this.Controls.Add(this.label34);
            this.Controls.Add(this.textBoxKey);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "全服叠加数";
            this.Text = "全服叠加数";
            this.tabPage3.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

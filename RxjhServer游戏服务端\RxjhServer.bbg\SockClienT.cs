using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace RxjhServer.bbg;

public class SockClienT
{
	public Socket clientSocket;

	private byte[] dataReceive;

	private bool disposed;

	private MemoryStream ms;

	private RemoveClientDelegateE removeFromTheServerList;

	public event MessageeDelegaterE OnSockMessage;

	public SockClienT(Socket from, RemoveClientDelegateE rftsl)
	{
		dataReceive = new byte[1500];
		disposed = false;
		ms = new MemoryStream();
		removeFromTheServerList = rftsl;
		clientSocket = from;
	}

	public void Dispose()
	{
		if (disposed)
		{
			return;
		}
		disposed = true;
		try
		{
			if (removeFromTheServerList != null)
			{
				removeFromTheServerList(this);
			}
			clientSocket.Shutdown(SocketShutdown.Both);
		}
		catch
		{
		}
		if (clientSocket != null)
		{
			clientSocket.Close();
		}
		clientSocket = null;
	}

	public virtual void OnReceiveData(IAsyncResult ar)
	{
		try
		{
			if (!disposed)
			{
				int num = clientSocket.EndReceive(ar);
				if (num <= 0)
				{
					Dispose();
					return;
				}
				ProcessDataReceived(dataReceive, num);
				Dispose();
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	

	public void OnSended2(IAsyncResult ar)
	{
		try
		{
			if (!disposed)
			{
				clientSocket.EndSend(ar);
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public virtual void ProcessDataReceived(byte[] data, int length)
	{
	}

	public virtual void Sendd(string str)
	{
		byte[] bytes = Encoding.Default.GetBytes(str);
		Send(bytes, bytes.Length);
	}

	

		public virtual void Send(byte[] toSendBuff, int len)
		{
			try
			{
				if (!disposed)
				{
					byte[] array = new byte[len + 6];
					array[0] = 170;
					array[1] = 136;
					Buffer.BlockCopy(BitConverter.GetBytes(len), 0, array, 2, 4);
					Buffer.BlockCopy(toSendBuff, 0, array, 6, len);
					clientSocket.BeginSend(array, 0, array.Length, SocketFlags.None, OnSended2, this);
				}
			}
			catch (Exception)
			{
				Dispose();
			}
		}

	public void Start()
	{
		clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
	}

	public void RaiseMessageEvent(string Msg)
	{
		if (this.OnSockMessage != null)
		{
			this.OnSockMessage(Msg, this);
		}
	}
}

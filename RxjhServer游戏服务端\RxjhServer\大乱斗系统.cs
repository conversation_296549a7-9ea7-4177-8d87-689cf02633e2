using System;
using System.Data;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 大乱斗系统 : IDisposable
{
	private System.Timers.Timer 时间1;

	private System.Timers.Timer 时间2;

	private DateTime kssj;

	private int kssjint;

	private DateTime kssj1;

	private int kssjint1;

	public 大乱斗系统()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "大乱斗错误1");
			}
			World.大乱斗Top.Clear();
			kssj = DateTime.Now.AddMinutes(World.大乱斗倒计时);
			时间1 = new System.Timers.Timer(10000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "大乱斗系统", "初始化", "创建大乱斗定时器");
		}
	}

	public void 时间结束事件1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "大乱斗错误3");
		}
		try
		{
			int num = (kssjint = (int)kssj.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 != 2351)
				{
					value.发送其他活动开始倒计时(num);
					value.系统提示("请速去泫勃派西大宝找神秘商人NPC进入大乱斗", 10, "系统提示");
					value.系统提示("请速去泫勃派西大宝找神秘商人NPC进入大乱斗", 3, "系统提示");
					value.系统提示("请速去泫勃派西大宝找神秘商人NPC进入大乱斗", 7, "系统提示");
				}
			}
			if (kssjint <= 0)
			{
				时间1.Enabled = false;
				时间1.Close();
				时间1.Dispose();
				World.大乱斗进程 = 1;
				kssj1 = DateTime.Now.AddMinutes(World.大乱斗总时间);
				时间2 = new System.Timers.Timer(60000.0);
				时间2.Elapsed += 时间结束事件2;
				时间2.Enabled = true;
				时间2.AutoReset = true;
				时间结束事件2(null, null);
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "大乱斗系统", "时间结束事件1", "倒计时和玩家通知处理");
		}
	}

	public void 时间结束事件2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "大乱斗错误4");
		}
		try
		{
			int num = (kssjint1 = (int)kssj1.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 2351)
				{
					value.发送其他活动开始倒计时(num);
				}
			}
			if (kssjint1 > 0)
			{
				return;
			}
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
			foreach (大乱斗TopClass value2 in World.大乱斗Top.Values)
			{
				string text = "正";
				text = ((value2.势力 != 1) ? "邪" : "正");
				DBA.ExeSqlCommand($"INSERT INTO 荣誉乱斗排行 (人物名,帮派,势力,等级,杀人数,分区信息)values('{value2.人物名}','{value2.帮派}','{text}',{value2.等级},{value2.杀人数},'{World.分区编号}')");
			}
			string sqlCommand = string.Format("Select TOP 10 * from 荣誉乱斗排行 where 分区信息='" + World.分区编号 + "'  Order By 杀人数 Desc");
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "GameServer");
			if (dBToDataTable != null)
			{
				if (dBToDataTable.Rows.Count > 0)
				{
					for (int i = 0; i < dBToDataTable.Rows.Count; i++)
					{
						foreach (Players value3 in World.allConnectedChars.Values)
						{
							if (value3.UserName == dBToDataTable.Rows[i]["人物名"].ToString())
							{
								value3.大乱斗奖励(i);
							}
						}
					}
				}
				dBToDataTable.Dispose();
			}
			foreach (Players value4 in World.allConnectedChars.Values)
			{
				if (value4.人物坐标_地图 == 2351)
				{
					value4.活动奖励系统(9);
				}
			}
			Dispose();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleTimerException(ex, "大乱斗系统", "时间结束事件2", "结算和奖励发放处理");
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "大乱斗错误5");
		}
		World.大乱斗进程 = 0;
		World.发送特殊公告("大乱斗点动已结束，期待下次大乱斗！", 10, "系统提示");
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		if (时间2 != null)
		{
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
		}
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.人物坐标_地图 == 2351)
			{
				value.移动(170f, 1805f, 15f, 101);
				value.切换PK模式(0);
			}
		}
		if (World.大乱斗Top != null)
		{
			World.大乱斗Top.Clear();
		}
		World.大乱斗 = null;
		Form1.WriteLine(22, "大乱斗活动结束");
	}
}

using System;
using System.Collections.Generic;
using System.Timers;

namespace RxjhServer;

public class 幸运玩家 : IDisposable
{
	private DateTime dateTime_0;

	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	private System.Timers.Timer timer_2;

	public 幸运玩家()
	{
		dateTime_0 = DateTime.Now.AddMinutes(2.0);
		timer_0 = new System.Timers.Timer(10000.0);
		timer_0.Elapsed += 发送幸运奖开始时间公告;
		timer_0.Enabled = true;
		timer_0.AutoReset = true;
		发送幸运奖开始时间公告(null, null);
		timer_1 = new System.Timers.Timer(90000.0);
		timer_1.Elapsed += 幸运奖开始;
		timer_1.Enabled = true;
	}

	public void Dispose()
	{
		if (timer_0 != null)
		{
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
		}
		if (timer_1 != null)
		{
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
		}
		if (timer_2 != null)
		{
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
		}
		World.开启幸运玩家 = null;
		Form1.WriteLine(22, "幸运玩家活动结束");
	}

	public void 发送幸运奖开始时间公告(object sender, ElapsedEventArgs e)
	{
		try
		{
			int num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num == 0)
			{
				return;
			}
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.发送其他活动开始倒计时(num);
				value.系统提示("离幸运奖开启还剩：" + num + "秒.请注意！！", 10, "系统提示");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "幸运奖 时间结束事件1 出错：" + ex);
		}
	}

	public void 幸运奖开始(object sender, ElapsedEventArgs e)
	{
		timer_0.Enabled = false;
		timer_0.Close();
		timer_0.Dispose();
		timer_1.Enabled = false;
		timer_1.Close();
		timer_1.Dispose();
		timer_2 = new System.Timers.Timer(30000.0);
		timer_2.Elapsed += 幸运奖战结束;
		timer_2.Enabled = true;
		List<MonSterClss> list = new List<MonSterClss>();
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				value.系统提示("幸运奖开始30秒，看看你是幸运玩家吗？", 3, "系统提示");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "幸运奖 时间结束事件1 出错：" + ex);
		}
	}

	public void 幸运奖战结束(object sender, ElapsedEventArgs e)
	{
		timer_2.Enabled = false;
		timer_2.Close();
		timer_2.Dispose();
		try
		{
			List<Players> list = new List<Players>();
			foreach (Players value in World.allConnectedChars.Values)
			{
				list.Add(value);
			}
			Players players = list[new Random().Next(0, World.allConnectedChars.Count)];
			string[] array = World.幸运奖奖励.Split(';');
			string[] array2 = World.幸运奖奖励单件物品.Split(';');
			int num = players.得到包裹空位(players);
			players.检察元宝数据(int.Parse(array[0]), 1, "幸运玩家");
			if (num != -1)
			{
				players.增加物品带属性(int.Parse(array2[0]), num, int.Parse(array2[1]), int.Parse(array2[2]), int.Parse(array2[3]), int.Parse(array2[4]), int.Parse(array2[5]), int.Parse(array2[6]), int.Parse(array2[7]), int.Parse(array2[8]), int.Parse(array2[9]), int.Parse(array2[10]), int.Parse(array2[11]));
			}
			else
			{
				players.系统提示("你已经被系统随机选中，成为今天的幸运玩家！", 22, "幸运之神");
				players.系统提示("系统赠送您元宝：" + Convert.ToInt32(array[0]), 22, "幸运之神");
				players.系统提示("系统赠送您武勋：" + Convert.ToInt32(array[1]), 22, "幸运之神");
			}
			players.保存人物的数据();
			Dispose();
			World.发送特殊公告(string.Concat("恭喜玩家：[", players.UserName, "]成为", DateTime.Now, "的幸运玩家。"), 6, "公告");
		}
		catch
		{
		}
	}
}

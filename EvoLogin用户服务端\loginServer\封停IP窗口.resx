﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>248, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAMAEBAAAAEAIABoBAAANgAAACAgAAABACAAqBAAAJ4EAAAwMAAAAQAgAKglAABGFQAAKAAAABAA
        AAAgAAAAAQAgAAAAAAAAAAAAIy4AACMuAAAAAAAAAAAAABUVFQogICCbICAg8yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICDzICAgmxcXFwogICCbICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICCbICAg8yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAjJP8gIyT/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg8yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yArLf8lv+f/Jbnn/yAqLf8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8hQEr/JcT//yS8//8gPUr/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ID1K/yS0//8jrf//IDpK/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yA6Sv8ipf//IZ3//yA3Sv8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8fNUr/IZX//yCN//8gNEr/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/HjJL/x2B//8ffv//HzFK/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yA+S/8Zdv//HW7//x8uSv8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gOD3/Ildj/yFVZP8iZoL/IaT//x14//8fS4H/IEBk/x8+
        Y/8fLD3/ICAg/yAgIP8gICD/ICAg/yAgIP8gJSb/Jsjx/ybO//8lyP//JL/9/yGc/v8gkv//IIn//x+A
        //8ed///HWnx/x8iJv8gICD/ICAg/yAgIP8gICD/ICIi/ySr2f8lwv//JLz//yS2//8iov3/H4P+/x53
        //8dbv//HGT//xxT2f8fICL/ICAg/yAgIP8gICDzICAg/yAgIP8gIiL/ICwx/yArMf8gKzH/ICox/yAp
        Mf8fJjH/HyUx/x8kMf8fICL/ICAg/yAgIP8gICDzICAgmyAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAgmxcXFwogICCbICAg8yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICDzICAgmxgYGArgBwAAgAEAAIABAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAEAAIABAADgBwAAKAAAACAAAABAAAAAAQAgAAAA
        AAAAAAAAIy4AACMuAAAAAAAAAAAAAAAAAAAAAAAAICAgDiAgIIAgICDWICAg+yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg+yAgINYgICCAICAgDgAAAAAAAAAAAAAAACAgICkgICDfICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICDfICAgKQAAAAAgICAOICAg3yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICDfICAgDiAgIIAgICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICCAICAg1iAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        INYgICD7ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gLzP/IC8z/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg+yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/JIqi/ybU/f8m0f3/I4Wi/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yFMV/8m0v//Js7//yXK//8lxv//IUhX/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ImF1/yXK//8lxv//JcL//yS+//8hW3X/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8iX3X/JcL//yS+//8kuv//JLf//yFYdf8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yFcdf8kuv//JLf//yOz//8jr///IVV1/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/IVl1/yOz//8jr///I6v//yKn
        //8gUnX/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8hVnX/I6v//yKn
        //8io///Ip///yBPdf8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yFT
        df8io///Ip///yGb//8hl///IEx1/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/H051/yGb//8hl///IZP//yCP//8gSXX/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8dRnX/IZP//yCP//8gi///IIf//x9Gdf8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/xtBdf8eh///IIf//x+E//8fgP//H0N1/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/HUh3/xl1//8fgP//H3z//x54//8fQHX/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8hXXf/FGr//x11//8edP//HnD//x89
        df8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yFed/8dl///FmH//x1s
        //8daP//Hjp1/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/IVp3/yO0
        //8civ//GF7//xxg//8eN3X/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yApK/8jd4r/JI+n/ySNqP8jjKj/I4qo/yOI
        qP8jlsn/I6v//yKm//8hnf//Hob//yBxyf8gZaj/IGOo/x9gqP8fXaj/H1qn/x9Liv8fJCv/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/JarI/yfV//8m0v//Js///ybM
        //8lyf//Jcb//yS6+/8iov//IZ3//yGZ//8hlP//II///yCL//8fh///H4L//x99//8eef//HnT//x5c
        yP8fICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yA1Ov8m0v//Js///ybM
        //8lyf//Jcb//yXD//8lwP//JLz9/yGc/P8hlP//II///yCL//8fh///H4L//x99//8eef//HnT//x1w
        //8da///HWf//x8oOv8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICkr/yXG
        9/8lyf//Jcb//yXD//8lwP//JL3//yS6//8kuP//I6v7/yCM/f8fh///H4L//x99//8eef//HnT//x1w
        //8da///HWf//xxi//8cXPf/HyMr/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ImB1/yS+9/8lwP//JL3//yS6//8kuP//JLX//yOy//8jr///IqP9/yCJ/f8eef7/HnT//x1w
        //8da///HWf//xxi//8cXv//HFj3/x42df8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD7ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICgr/yA4Qv8gOEL/IDdC/yA3Qv8gNkL/IDZC/yA1Qv8gNUL/IDRC/yAy
        Qf8fLUP/HytC/x8qQv8fKkL/HylC/x8oQv8fIiv/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg+yAg
        INYgICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICDWICAggCAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIIAgICAOICAg3yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICDfICAgDgAAAAAgICApICAg3yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg3yAgICkAAAAAAAAAAAAAAAAgICAOICAggCAgINYgICD7ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD7ICAg1iAgIIAgICAOAAAAAAAAAAD8AAA/4AAAB8AAAAOAAAABgAAAAYAA
        AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAGAAAABgAAAAcAAAAPgAAAH/AAAPygAAAAwAAAAYAAAAAEA
        IAAAAAAAAAAAACMuAAAjLgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAICAgCCAgIF0gICCzICAg5yAg
        IP0gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD9ICAg5yAgILMgICBdICAgCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgICBAHx8f2R8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f2SAgIEAAAAAAAAAAAAAAAAAAAAAAAAAAAB8f
        H1wgICD5Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIPkfHx9cAAAAAAAA
        AAAAAAAAICAgQCAgIPkgICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD5ICAgQAAAAAAgICAIHx8f2R8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f2R8fHwggICBdHx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH10gICCzICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgILMgICDnHx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H+cgICD9Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/ICIi/iFBSP4hQUj/ICIi/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/0gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8hTFX/Jr/i/yfW//8m0///Jbri/yFK
        Vf8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAyNv4my/H/JtP+/ibR
        /v4mzv//Jcv+/iW/8f4gMTb/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yNy
        hv8m0f//Js7//ybM//8lyf//Jcb//yXE//8iaob/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iODn/4my///Jcn+/iXG/v4lxP//JMH+/iS//v4ieZ//Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iOAoP4lxv//JcT+/iTB/v4lv///JLz+/iS6/v4id6D/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iJ9oP4lwf//JL/+/iS8/v4kuv//JLf+/iO1
        /v4idKD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yJ7oP8kvP//JLr//yS3
        //8ktP//I7L//yOv//8hcaD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iJ4
        oP4kt///I7T+/iOy/v4jr///I6z+/iKq/v4hbqD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iJ1oP4jsv//I6/+/iOt/v4jqv//Iqf+/iKl/v4ha6D/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yFyoP8jrP//I6r//yOn//8ipf//IqL//yKg//8haKD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iFvoP4jp///IqX+/iKi/v4ioP//IZ3+/iGa
        /v4gZaD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iFroP4iov//IqD+/iGd
        /v4hmv//IZj+/iGV/v4gYqD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yBn
        oP8inf//IZr//yGY//8hlf//IZL//yCQ//8gX6D/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h5foP4hmP//IZX+/iCS/v4gkP//II3+/iCL/v4gXKD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/hpWoP4hkv//IJD+/iCN/v4gi///H4j+/h+G/v4fWaD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/xlRoP8eiP//IIv//yCI//8ghf//H4P//x+A
        //8fVqD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/hlTov4Zev//H4X+/h+D
        /v4fgP//Hn3+/h57/v4fU6D/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/hxf
        ov4UbP//Hn/+/h9+/v4fe///Hnj+/h52/v4eUKD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yF5ov8TZ///GnH//x54//8edf//HnP//x5x//8eTaD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iN/ov4ah///FGD+/h1x/v4ecf//HW7+/h1r/v4eSqD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iJ8ov4jtv//FWj+/hZf/v4da///HWn+/hxm
        /v4eR6D/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yJ4ov8ktv//Iqn//xZn
        //8XW///HGP//xxh//8eRKD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iJ1
        ov4jsP//I63+/iKo/v4chP//GWb+/htd/v4dQaD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ImZ2/yWy0v8mxuv/Jsbs/ybE7P8mwuz/JcDs/yW/
        7P8lvez/Jbvs/ySy9f8jqv//Iqf//yKk//8iof//IZ7//yGY//8hkPb/IIrs/yCI7P8ghez/IILs/x+A
        7P8ffez/H3rs/x936/8eadL/H0F2/x8gIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4jeIz/Jtb+/ifV//8m0/7+JtH+/ibP
        /v4mzf//Jcv+/iXJ/v4lx///JcX+/iO0+f4ipP//IqH+/iGe/v4hm///IZj+/iGV/v4hkv//II/+/iCM
        /v4gif//H4b+/h+D//8fgP7+Hn3+/h56//8edv7+HnP+/h5HjP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAqLP4mz/f/JtP+/ibS
        //8mz/7+Jc3+/iXL/v4myv//Jcf+/iXF/v4lxP//JMH+/iS4+v4hnv7/IZv+/iGY/v4hlf//IJL+/iCP
        /v4gjP//IIn+/h+G/v4fg///H4D+/h99//8eev7+Hnb+/h5z//8dcf7+HW7+/h1o9/8fJCz+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yFG
        T/8m0v//JtD//ybO//8mzP//Jsr//yXI//8lxv//JcT//yXC//8lwP//Jb7//yS8//8ioPn/IZX//yGS
        //8gj///IIz//yCJ//8ghv//H4P//x+A//8fff//H3r//x52//8edP//HnH//x1u//8da///HWj//x1k
        //8fLk//ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iA0Ov4lzf7/Jcv+/ibK//8lx/7+JcX+/iXD/v4lwf//JL/+/iS+/v4kvP//JLr+/iS4
        /v4jsPv/IZH8/iCM/v4gif//H4b+/h+D/v4fgP//Hn3+/h56/v4edv//HnP+/h5x//8dbv7+HWv+/h1o
        //8cZP7+HGH+/hxe/v8fJzr+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4kl7z/Jcf+/iXG//8lw/7+JMH+/iTA/v4lvv//JLz+/iS6
        /v4kuP//I7b+/iO0/v4ksv//I6j7/iCL/P4fg///H4D+/h59/v4eev//Hnb+/h5z/v4ecf//HW7+/h1r
        //8daP7+HGT+/hxh//8cXv7+HFv+/hxIvP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gLDD/I5O8/yTB/v8lwP//Jb7//yS8
        //8kuv//JLj//yS2//8ktP//JLL//yOw//8jrv//I6z//yKn/f8hkvz/H3/9/x52//8edP//HnH//x1u
        //8da///HWj//x1k//8cYf//HF7//xxb//8bWv7/HEi8/x8kMP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD9Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAy
        Ov8hRFP+IURU/iBEVP4gQ1T/IENU/iBCVP4gQlT/IEFU/iBBVP4gQFT/IEBU/iA/VP4gP1T/ID5U/iA5
        Uf4fNFb/HzFU/h8xVP4fMFT/Hy9U/h8vVP8eLlT+Hi1U/h4tU/8fJjr+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH/0gICDnHx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8fH+cgICCzICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgILMgICBdHx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/h8f
        H10gICAIHx8f2R8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f2R8fHwgAAAAAICAgQCAgIPkgICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAgIP8gICD/ICAg/yAg
        IP8gICD/ICAg/yAgIP8gICD5ICAgQAAAAAAAAAAAAAAAAB8fH1wgICD5Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIPkfHx9cAAAAAAAAAAAAAAAAAAAAAAAAAAAgICBAHx8f2R8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAgIP8fHx/+Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/+Hx8f2SAgIEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAHx8fCB8fH10gICCzHx8f5x8fH/0gICD/Hx8f/iAgIP8fHx/+Hx8f/h8fH/4gICD/Hx8f/h8f
        H/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/h8fH/4gICD/Hx8f/iAg
        IP8fHx/+Hx8f/iAgIP8fHx/+Hx8f/iAgIP8fHx/9Hx8f5yAgILMfHx9dHx8fCAAAAAAAAAAAAAAAAAAA
        AAD/gAAAAf8AAP2u221tvwAA/a7bbW2/AADgAAAAAAcAAO2u221ttwAA7a7bbW23AACAAAAAAAEAAO2u
        221ttwAA7a7bbW23AAAAAAAAAAAAAG2u221ttwAAAAAAAAAAAABtrtttbbcAAG2u221ttwAAba7bbW23
        AAAAAAAAAAAAAG2u221ttwAAba7bbW23AAAAAAAAAAAAAG2u221ttwAAba7bbW23AAAAAAAAAAAAAG2u
        221ttwAAba7bbW23AAAAAAAAAAAAAG2u221ttwAAba7bbW23AAAAAAAAAAAAAG2u221ttwAAba7bbW23
        AAAAAAAAAAAAAG2u221ttwAAAAAAAAAAAABtrtttbbcAAG2u221ttwAAAAAAAAAAAABtrtttbbcAAG2u
        221ttwAAAAAAAAAAAADtrtttbbcAAO2u221ttwAAgAAAAAABAADtrtttbbcAAO2u221ttwAA4AAAAAAH
        AAD9rtttbb8AAP2u221tvwAA/67bbW3/AAA=
</value>
  </data>
</root>
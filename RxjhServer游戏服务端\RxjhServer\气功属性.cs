namespace RxjhServer;

public class 气功属性
{
	private double _刀_连环飞舞;

	private double _韩_天魔狂血概率;

	private double _韩_天魔极血概率;

	private double _韩_追骨吸元;

	private double _韩_火龙问鼎;

	private double _谭_招式新法;

	private double _刀_升天三气功_火龙之火;

	private double _剑_破天一剑;

	private double _枪_升天三气功_怒意之火;

	private double _怪反伤几率;

	private double _人反伤几率;

	private double _破甲几率;

	private double _真武绝击;

	private double _气沉丹田;

	private double _暗影绝杀;

	private double _流光乱舞;

	private double _剑_升天一气功_护身罡气;

	private double _剑_移花接木;

	private double _剑_回柳身法;

	private double _剑_怒海狂澜;

	private double _剑_冲冠一怒;

	private double _剑_无坚不摧;

	private double _枪_运气疗伤;

	private double _拳头_运气疗伤;

	private double _枪_狂神降世;
	
	private double _枪客_灵甲爆发; //24.0 EVIAS 灵甲爆发

	private double _流星漫天;

	private double _枪_转攻为守;

	private double _枪_末日狂舞;

	private double _弓_锐利之箭;

	private double _弓_猎鹰之眼;

	private double _弓_无明暗矢;

	private double _医_运气疗心;

	private double _医_长攻击力;

	private double _医_太极心法;

	private double _医_妙手回春;

	private double _医_神农仙术;

	private double _医_九天真气;

	private double _医_升天二气功_万物回春;

	private double _医_吸星大法;

	private double _刺_荆轲之怒;

	private double _刺_三花聚顶;

	private double _刺_连环飞舞;

	private double _狂风万破;

	private double _剑_连环飞舞;

	private double _枪_连环飞舞;

	private double _弓_回流真气;

	private double _弓_心神凝聚;

	private double _弓_流星三矢;

	private double _弓_流星三矢时间;

	private double _弓_致命绝杀;

	private double _刺_心神凝聚;

	private double _刺_致手绝命;

	private double _刺_先发制人;

	private double _刺_千蛛万手;

	private double _刺_连消带打;

	private double _刺_剑刃乱舞;

	private double _刺_一招残杀;

	private double _刺_升天三气功_无情打击;

	private double _琴师_高山流水;

	private double _琴师_梅花三弄;

	private double _三和弦_状态效果;

	private double _七和弦_状态效果;

	private double _九和弦_状态效果;

	private double _九和弦_群攻数量;

	private double _琴师_鸾凤和鸣;

	private double _琴师_阳明春晓概率;

	private double _琴师_阳明春晓绝望;

	private double _琴师_阳明春晓时间;

	private double _琴师_阳明春晓攻击;

	private double _琴师_潇湘雨夜概率;

	private double _琴师_潇湘雨夜时间;

	private double _琴师_潇湘雨夜防御;

	private double _琴师_潇湘雨夜不安;

	private double _谭_护身罡气;

	private double _谭_移花接木;

	private double _谭_纵横无双;

	private double _谭_回柳身法;

	private double _谭_怒海狂澜;

	private double _谭_冲冠一怒;

	private double _拳师_狂神降世;

	private double _拳师_金刚不坏;

	private double _拳师_转攻为守;

	private double _拳师_水火一体;

	private double _拳师_磨杵成针;

	private double _拳师_会心一击;

	private double _拳师_会心一击威力;

	private double _拳师_末日狂舞;

	private double _梅_障力激活;

	private double _梅_障力运用;

	private double _梅_玄武神功;

	private double _梅_玄武的指点;

	private double _梅_玄武强击;

	private double _梅_玄武危化;

	private double _梅_障力恢复;

	private double _梅_嫉妒的化身;

	private double _梅_愤怒爆发;

	private double _梅_吸血进击;

	private double _韩_升天一气功_行风弄舞;

	private double _韩_升天二气功_天魔护体;

	private double _韩_升天二气功_内息行心;

	private double _刀_升天一气功_遁出逆境;

	private double _剑_乘胜追击;

	private double _枪_升天一气功_破甲刺魂;

	private double _弓_升天一气功_绝影射魂;

	private double _医_狂意护体;

	private double _刺_升天一气功_夜魔缠身;

	private double _升天一气功_运气行心;

	private double _升天一气功_运气疗伤;

	private double _升天一气功_狂风天意;

	private double _琴师_升天一气功_飞花点翠;

	private double _琴师_升天一气功_飞花点翠加成;

	private double _拳_升天一气功_夺命连环;

	private double _拳_升天二气功_电光石火;

	private double _拳_升天三气功_精益求精;

	private double _梅_升天一气功_玄武雷电;

	private double _梅_升天二气功_玄武诅咒;

	private double _梅_升天三气功_杀人鬼;

	private double _刀_升天二气功_穷途末路;

	private double _剑_升天二气功_天地同寿;

	private double _谭_升天二气功_天地同寿;

	private double _枪_升天二气功_以退为进;

	private double _医_无中生有;

	private double _刺_升天二气功_顺水推舟;

	private double _琴师_升天二气功_三潭映月;

	private double _刺_以怒还怒;

	private double _刀_梵音破镜;

	private double _剑_升天三气功_火凤临朝;

	private double _谭_升天三气功_火凤临朝;

	private double _枪_怒意之吼;

	private double _弱点攻破;

	private double _牢不可破;

	private double _陵劲淬砺;

	private double _卢_破血狂风;

	private double _技冠群雄;

	private double _弓_升天三气功_天外三矢;

	private double _医_升天三气功_明镜止水;

	private double _琴师_升天三气功_子夜秋歌;

	private double _谭_升天三气功_以柔克刚;

	private double _升天四式_红月狂风;

	private double _升天四式_即月狂风;

	private double _升天四式_毒蛇出洞;

	private double _升天四式_满月狂风;

	private double _升天四式_烈日炎炎;

	private double _升天四式_望梅添花;

	private double _升天四式_悬丝诊脉;

	private double _升天四式_长虹贯天;

	private double _升天四式_哀鸿遍野;

	private double _升天五式_致残;

	private double _升天五式_龙魂附体;

	private double _升天五式_惊天动地;

	private double _升天五式_灭世狂舞;

	private double _升天五式_千里一击;

	private double _升天五式_形移妖相;

	private double _升天五式_一招杀神;

	private double _升天五式_龙爪纤指手;

	private double _升天五式_天魔之力;

	private double _升天五式_惊涛骇浪;

	private double _升天五式_不死之躯;

	private double _升天五式_魔魂之力;

	private double _升天五式_破空坠星;

	public double _卢_转攻为守;

	public double _全职业气功防御;

	public double _大魔神添加全职业气功防御几率;

	public double _神女运气行心;

	public double _神女太极心法;

	public double _神女神力激发;

	public double _神女神力激发对怪防御力;

	public double _神女杀星义虎;

	public double _神女杀星义杀;

	public double _神女杀星义气;

	public double _神女洗髓易筋;

	public double _神女黑花漫开;

	public double _神女妙手回春;

	public double _神女长功击力;

	public double _神女黑花集中;

	public double _神女真武绝击;

	public double _神女万毒不侵;

	public double _神女愤怒调节;

	public double _神女蛊毒解除;

	public double _神女尸毒爆发;

	private double _阎王爆累计伤害;

	public double _精金百炼;

	public double _血气方刚;

	public double _减少攻击;

	public double _减少罡气;

	public double _刀画龙点睛;

	public double _剑百毒不侵;

	public double _枪寒冰领域;

	public double _弓恶尽矢穷;

	public double _医生云心月性;

	public double _刺客外刚内刚;

	public double _乐师血脉上升;

	public double _韩飞官真气还原;

	public double _谭花灵电光朝露;

	public double _格斗家无障无碍;

	public double _梅柳真化险为夷;

	public double _卢风郎反弹无效;

	public double _神女抗击身法;

	#region 反气功 //24.0 EVIAS 新增反气功系统
	
	private double _反剑_无坚不摧;
	private double _反剑_护身罡气;
	private double _反刀_四两千斤;
	private double _反刀_霸气破甲;
	private double _反枪_即月狂风;
	private double _反枪_怒吼之意;
	private double _反弓_无名暗矢;
	private double _反弓_致命绝杀;
	private double _反医_无中生有;
	private double _反医_云心月性;
	private double _反刺_烈日炎炎;
	private double _反刺_连消带打;
	private double _反乐_鸾凤和鸣;
	private double _反乐_三潭映月;
	private double _反韩_天魔狂血;
	private double _反韩_霸气破甲;
	private double _反谭_纵横无双;
	private double _反谭_招式新法;
	private double _反格_会心一击;
	private double _反格_电光火石;
	private double _反梅_愤怒爆发;
	private double _反梅_玄武雷电;
	private double _反卢_流星漫天;
	private double _反卢_转攻为守;
	private double _反神_尸毒爆发;
    private double _反神_黑花集中;
    private double _反气功_致残;
	#endregion

	public double 反剑_无坚不摧 { get => _反剑_无坚不摧; set => _反剑_无坚不摧 = value; }
	public double 反剑_护身罡气 { get => _反剑_护身罡气; set => _反剑_护身罡气 = value; }
	public double 反刀_四两千斤 { get => _反刀_四两千斤; set => _反刀_四两千斤 = value; }
	public double 反刀_霸气破甲 { get => _反刀_霸气破甲; set => _反刀_霸气破甲 = value; }
	public double 反枪_即月狂风 { get => _反枪_即月狂风; set => _反枪_即月狂风 = value; }
	public double 反枪_怒吼之意 { get => _反枪_怒吼之意; set => _反枪_怒吼之意 = value; }
	public double 反弓_无名暗矢 { get => _反弓_无名暗矢; set => _反弓_无名暗矢 = value; }
	public double 反弓_致命绝杀 { get => _反弓_致命绝杀; set => _反弓_致命绝杀 = value; }
	public double 反医_无中生有 { get => _反医_无中生有; set => _反医_无中生有 = value; }
	public double 反医_云心月性 { get => _反医_云心月性; set => _反医_云心月性 = value; }
	public double 反刺_烈日炎炎 { get => _反刺_烈日炎炎; set => _反刺_烈日炎炎 = value; }
	public double 反刺_连消带打 { get => _反刺_连消带打; set => _反刺_连消带打 = value; }
	public double 反乐_鸾凤和鸣 { get => _反乐_鸾凤和鸣; set => _反乐_鸾凤和鸣 = value; }
	public double 反乐_三潭映月 { get => _反乐_三潭映月; set => _反乐_三潭映月 = value; }
	public double 反韩_天魔狂血 { get => _反韩_天魔狂血; set => _反韩_天魔狂血 = value; }
	public double 反韩_霸气破甲 { get => _反韩_霸气破甲; set => _反韩_霸气破甲 = value; }
	public double 反谭_纵横无双 { get => _反谭_纵横无双; set => _反谭_纵横无双 = value; }
	public double 反谭_招式新法 { get => _反谭_招式新法; set => _反谭_招式新法 = value; }
	public double 反格_会心一击 { get => _反格_会心一击; set => _反格_会心一击 = value; }
	public double 反格_电光火石 { get => _反格_电光火石; set => _反格_电光火石 = value; }
	public double 反梅_愤怒爆发 { get => _反梅_愤怒爆发; set => _反梅_愤怒爆发 = value; }
	public double 反梅_玄武雷电 { get => _反梅_玄武雷电; set => _反梅_玄武雷电 = value; }
	public double 反卢_流星漫天 { get => _反卢_流星漫天; set => _反卢_流星漫天 = value; }
	public double 反卢_转攻为守 { get => _反卢_转攻为守; set => _反卢_转攻为守 = value; }
    public double 反神_黑花集中 { get => _反神_黑花集中; set => _反神_黑花集中 = value; }
    public double 反神_尸毒爆发 { get => _反神_尸毒爆发; set => _反神_尸毒爆发 = value; }
	public double 反气功_致残 { get => _反气功_致残; set => _反气功_致残 = value; }

	public double 刀画龙点睛
	{
		get
		{
			return _刀画龙点睛;
		}
		set
		{
			_刀画龙点睛 = value;
		}
	}

	public double 剑百毒不侵
	{
		get
		{
			return _剑百毒不侵;
		}
		set
		{
			_剑百毒不侵 = value;
		}
	}

	public double 枪寒冰领域
	{
		get
		{
			return _枪寒冰领域;
		}
		set
		{
			_枪寒冰领域 = value;
		}
	}

	public double 弓恶尽矢穷
	{
		get
		{
			return _弓恶尽矢穷;
		}
		set
		{
			_弓恶尽矢穷 = value;
		}
	}

	public double 医生云心月性
	{
		get
		{
			return _医生云心月性;
		}
		set
		{
			_医生云心月性 = value;
		}
	}

	public double 刺客外刚内刚
	{
		get
		{
			return _刺客外刚内刚;
		}
		set
		{
			_刺客外刚内刚 = value;
		}
	}

	public double 乐师血脉上升
	{
		get
		{
			return _乐师血脉上升;
		}
		set
		{
			_乐师血脉上升 = value;
		}
	}

	public double 韩飞官真气还原
	{
		get
		{
			return _韩飞官真气还原;
		}
		set
		{
			_韩飞官真气还原 = value;
		}
	}

	public double 谭花灵电光朝露
	{
		get
		{
			return _谭花灵电光朝露;
		}
		set
		{
			_谭花灵电光朝露 = value;
		}
	}

	public double 格斗家无障无碍
	{
		get
		{
			return _格斗家无障无碍;
		}
		set
		{
			_格斗家无障无碍 = value;
		}
	}

	public double 梅柳真化险为夷
	{
		get
		{
			return _梅柳真化险为夷;
		}
		set
		{
			_梅柳真化险为夷 = value;
		}
	}

	public double 卢风郎反弹无效
	{
		get
		{
			return _卢风郎反弹无效;
		}
		set
		{
			_卢风郎反弹无效 = value;
		}
	}

	public double 神女抗击身法
	{
		get
		{
			return _神女抗击身法;
		}
		set
		{
			_神女抗击身法 = value;
		}
	}

	public double 精金百炼
	{
		get
		{
			return _精金百炼;
		}
		set
		{
			_精金百炼 = value;
		}
	}

	public double 血气方刚
	{
		get
		{
			return _血气方刚;
		}
		set
		{
			_血气方刚 = value;
		}
	}

	public double 减少攻击
	{
		get
		{
			return _减少攻击;
		}
		set
		{
			_减少攻击 = value;
		}
	}

	public double 减少罡气
	{
		get
		{
			return _减少罡气;
		}
		set
		{
			_减少罡气 = value;
		}
	}

	public double 刀_连环飞舞
	{
		get
		{
			return _刀_连环飞舞;
		}
		set
		{
			_刀_连环飞舞 = value;
		}
	}

	public double 韩_天魔狂血概率
	{
		get
		{
			return _韩_天魔狂血概率;
		}
		set
		{
			_韩_天魔狂血概率 = value;
		}
	}

	public double 韩_天魔极血概率
	{
		get
		{
			return _韩_天魔极血概率;
		}
		set
		{
			_韩_天魔极血概率 = value;
		}
	}

	public double 韩_追骨吸元
	{
		get
		{
			return _韩_追骨吸元;
		}
		set
		{
			_韩_追骨吸元 = value;
		}
	}

	public double 韩_火龙问鼎
	{
		get
		{
			return _韩_火龙问鼎;
		}
		set
		{
			_韩_火龙问鼎 = value;
		}
	}

	public double 谭_招式新法
	{
		get
		{
			return _谭_招式新法;
		}
		set
		{
			_谭_招式新法 = value;
		}
	}

	public double 刀_升天三气功_火龙之火
	{
		get
		{
			return _刀_升天三气功_火龙之火;
		}
		set
		{
			_刀_升天三气功_火龙之火 = value;
		}
	}

	public double 剑_破天一剑
	{
		get
		{
			return _剑_破天一剑;
		}
		set
		{
			_剑_破天一剑 = value;
		}
	}

	public double 枪_升天三气功_怒意之火
	{
		get
		{
			return _枪_升天三气功_怒意之火;
		}
		set
		{
			_枪_升天三气功_怒意之火 = value;
		}
	}

	public double 怪反伤几率
	{
		get
		{
			return _怪反伤几率;
		}
		set
		{
			_怪反伤几率 = value;
		}
	}

	public double 人反伤几率
	{
		get
		{
			return _人反伤几率;
		}
		set
		{
			_人反伤几率 = value;
		}
	}

	public double 破甲几率
	{
		get
		{
			return _破甲几率;
		}
		set
		{
			_破甲几率 = value;
		}
	}

	public double 真武绝击
	{
		get
		{
			return _真武绝击;
		}
		set
		{
			_真武绝击 = value;
		}
	}

	public double 气沉丹田
	{
		get
		{
			return _气沉丹田;
		}
		set
		{
			_气沉丹田 = value;
		}
	}

	public double 暗影绝杀
	{
		get
		{
			return _暗影绝杀;
		}
		set
		{
			_暗影绝杀 = value;
		}
	}

	public double 流光乱舞
	{
		get
		{
			return _流光乱舞;
		}
		set
		{
			_流光乱舞 = value;
		}
	}

	public double 剑_升天一气功_护身罡气
	{
		get
		{
			return _剑_升天一气功_护身罡气;
		}
		set
		{
			_剑_升天一气功_护身罡气 = value;
		}
	}

	public double 剑_移花接木
	{
		get
		{
			return _剑_移花接木;
		}
		set
		{
			_剑_移花接木 = value;
		}
	}

	public double 剑_回柳身法
	{
		get
		{
			return _剑_回柳身法;
		}
		set
		{
			_剑_回柳身法 = value;
		}
	}

	public double 剑_怒海狂澜
	{
		get
		{
			return _剑_怒海狂澜;
		}
		set
		{
			_剑_怒海狂澜 = value;
		}
	}

	public double 剑_冲冠一怒
	{
		get
		{
			return _剑_冲冠一怒;
		}
		set
		{
			_剑_冲冠一怒 = value;
		}
	}

	public double 剑_无坚不摧
	{
		get
		{
			return _剑_无坚不摧;
		}
		set
		{
			_剑_无坚不摧 = value;
		}
	}

	public double 枪_运气疗伤
	{
		get
		{
			return _枪_运气疗伤;
		}
		set
		{
			_枪_运气疗伤 = value;
		}
	}

	public double 枪_狂神降世
	{
		get
		{
			return _枪_狂神降世;
		}
		set
		{
			_枪_狂神降世 = value;
		}
	}

	public double 流星漫天
	{
		get
		{
			return _流星漫天;
		}
		set
		{
			_流星漫天 = value;
		}
	}

	public double 卢_转攻为守
	{
		get
		{
			return _卢_转攻为守;
		}
		set
		{
			_卢_转攻为守 = value;
		}
	}

	public double 枪_转攻为守
	{
		get
		{
			return _枪_转攻为守;
		}
		set
		{
			_枪_转攻为守 = value;
		}
	}

	public double 枪_末日狂舞
	{
		get
		{
			return _枪_末日狂舞;
		}
		set
		{
			_枪_末日狂舞 = value;
		}
	}

	public double 弓_锐利之箭
	{
		get
		{
			return _弓_锐利之箭;
		}
		set
		{
			_弓_锐利之箭 = value;
		}
	}

	public double 弓_猎鹰之眼
	{
		get
		{
			return _弓_猎鹰之眼;
		}
		set
		{
			_弓_猎鹰之眼 = value;
		}
	}

	public double 弓_无明暗矢
	{
		get
		{
			return _弓_无明暗矢;
		}
		set
		{
			_弓_无明暗矢 = value;
		}
	}

	public double 医_运气疗心
	{
		get
		{
			return _医_运气疗心;
		}
		set
		{
			_医_运气疗心 = value;
		}
	}

	public double 医_长攻击力
	{
		get
		{
			return _医_长攻击力;
		}
		set
		{
			_医_长攻击力 = value;
		}
	}

	public double 医_太极心法
	{
		get
		{
			return _医_太极心法;
		}
		set
		{
			_医_太极心法 = value;
		}
	}

	public double 医_妙手回春
	{
		get
		{
			return _医_妙手回春;
		}
		set
		{
			_医_妙手回春 = value;
		}
	}

	public double 医_神农仙术
	{
		get
		{
			return _医_神农仙术;
		}
		set
		{
			_医_神农仙术 = value;
		}
	}

	public double 医_九天真气
	{
		get
		{
			return _医_九天真气;
		}
		set
		{
			_医_九天真气 = value;
		}
	}

	public double 医_升天二气功_万物回春
	{
		get
		{
			return _医_升天二气功_万物回春;
		}
		set
		{
			_医_升天二气功_万物回春 = value;
		}
	}

	public double 医_吸星大法
	{
		get
		{
			return _医_吸星大法;
		}
		set
		{
			_医_吸星大法 = value;
		}
	}

	public double 刺_荆轲之怒
	{
		get
		{
			return _刺_荆轲之怒;
		}
		set
		{
			_刺_荆轲之怒 = value;
		}
	}

	public double 刺_三花聚顶
	{
		get
		{
			return _刺_三花聚顶;
		}
		set
		{
			_刺_三花聚顶 = value;
		}
	}

	public double 刺_连环飞舞
	{
		get
		{
			return _刺_连环飞舞;
		}
		set
		{
			_刺_连环飞舞 = value;
		}
	}

	public double 狂风万破
	{
		get
		{
			return _狂风万破;
		}
		set
		{
			_狂风万破 = value;
		}
	}

	public double 剑_连环飞舞
	{
		get
		{
			return _剑_连环飞舞;
		}
		set
		{
			_剑_连环飞舞 = value;
		}
	}

	public double 枪_连环飞舞
	{
		get
		{
			return _枪_连环飞舞;
		}
		set
		{
			_枪_连环飞舞 = value;
		}
	}

	public double 弓_回流真气
	{
		get
		{
			return _弓_回流真气;
		}
		set
		{
			_弓_回流真气 = value;
		}
	}

	public double 弓_心神凝聚
	{
		get
		{
			return _弓_心神凝聚;
		}
		set
		{
			_弓_心神凝聚 = value;
		}
	}

	public double 弓_流星三矢
	{
		get
		{
			return _弓_流星三矢;
		}
		set
		{
			_弓_流星三矢 = value;
		}
	}

	public double 弓_流星三矢时间
	{
		get
		{
			return _弓_流星三矢时间;
		}
		set
		{
			_弓_流星三矢时间 = value;
		}
	}

	public double 弓_致命绝杀
	{
		get
		{
			return _弓_致命绝杀;
		}
		set
		{
			_弓_致命绝杀 = value;
		}
	}

	public double 刺_心神凝聚
	{
		get
		{
			return _刺_心神凝聚;
		}
		set
		{
			_刺_心神凝聚 = value;
		}
	}

	public double 刺_致手绝命
	{
		get
		{
			return _刺_致手绝命;
		}
		set
		{
			_刺_致手绝命 = value;
		}
	}

	public double 刺_先发制人
	{
		get
		{
			return _刺_先发制人;
		}
		set
		{
			_刺_先发制人 = value;
		}
	}

	public double 刺_千蛛万手
	{
		get
		{
			return _刺_千蛛万手;
		}
		set
		{
			_刺_千蛛万手 = value;
		}
	}

	public double 刺_连消带打
	{
		get
		{
			return _刺_连消带打;
		}
		set
		{
			_刺_连消带打 = value;
		}
	}

	public double 刺_剑刃乱舞
	{
		get
		{
			return _刺_剑刃乱舞;
		}
		set
		{
			_刺_剑刃乱舞 = value;
		}
	}

	public double 刺_一招残杀
	{
		get
		{
			return _刺_一招残杀;
		}
		set
		{
			_刺_一招残杀 = value;
		}
	}

	public double 刺_升天三气功_无情打击
	{
		get
		{
			return _刺_升天三气功_无情打击;
		}
		set
		{
			_刺_升天三气功_无情打击 = value;
		}
	}

	public double 琴师_高山流水
	{
		get
		{
			return _琴师_高山流水;
		}
		set
		{
			_琴师_高山流水 = value;
		}
	}

	public double 琴_三和弦_状态效果
	{
		get
		{
			return _三和弦_状态效果;
		}
		set
		{
			_三和弦_状态效果 = value;
		}
	}

	public double 琴_七和弦_状态效果
	{
		get
		{
			return _七和弦_状态效果;
		}
		set
		{
			_七和弦_状态效果 = value;
		}
	}

	public double 琴_九和弦_状态效果
	{
		get
		{
			return _九和弦_状态效果;
		}
		set
		{
			_九和弦_状态效果 = value;
		}
	}

	public double 琴师_梅花三弄
	{
		get
		{
			return _琴师_梅花三弄;
		}
		set
		{
			_琴师_梅花三弄 = value;
		}
	}

	public double 琴师_鸾凤和鸣
	{
		get
		{
			return _琴师_鸾凤和鸣;
		}
		set
		{
			_琴师_鸾凤和鸣 = value;
		}
	}

	public double 琴_九和弦_群攻数量
	{
		get
		{
			return _九和弦_群攻数量;
		}
		set
		{
			_九和弦_群攻数量 = value;
		}
	}

	public double 琴师_阳明春晓概率
	{
		get
		{
			return _琴师_阳明春晓概率;
		}
		set
		{
			_琴师_阳明春晓概率 = value;
		}
	}

	public double 琴师_阳明春晓绝望
	{
		get
		{
			return _琴师_阳明春晓绝望;
		}
		set
		{
			_琴师_阳明春晓绝望 = value;
		}
	}

	public double 琴师_阳明春晓时间
	{
		get
		{
			return _琴师_阳明春晓时间;
		}
		set
		{
			_琴师_阳明春晓时间 = value;
		}
	}

	public double 琴师_阳明春晓攻击
	{
		get
		{
			return _琴师_阳明春晓攻击;
		}
		set
		{
			_琴师_阳明春晓攻击 = value;
		}
	}

	public double 琴师_潇湘雨夜概率
	{
		get
		{
			return _琴师_潇湘雨夜概率;
		}
		set
		{
			_琴师_潇湘雨夜概率 = value;
		}
	}

	public double 琴师_潇湘雨夜时间
	{
		get
		{
			return _琴师_潇湘雨夜时间;
		}
		set
		{
			_琴师_潇湘雨夜时间 = value;
		}
	}

	public double 琴师_潇湘雨夜防御
	{
		get
		{
			return _琴师_潇湘雨夜防御;
		}
		set
		{
			_琴师_潇湘雨夜防御 = value;
		}
	}

	public double 琴师_潇湘雨夜不安
	{
		get
		{
			return _琴师_潇湘雨夜不安;
		}
		set
		{
			_琴师_潇湘雨夜不安 = value;
		}
	}

	public double 谭_护身罡气
	{
		get
		{
			return _谭_护身罡气;
		}
		set
		{
			_谭_护身罡气 = value;
		}
	}

	public double 谭_移花接木
	{
		get
		{
			return _谭_移花接木;
		}
		set
		{
			_谭_移花接木 = value;
		}
	}

	public double 谭_纵横无双
	{
		get
		{
			return _谭_纵横无双;
		}
		set
		{
			_谭_纵横无双 = value;
		}
	}

	public double 谭_回柳身法
	{
		get
		{
			return _谭_回柳身法;
		}
		set
		{
			_谭_回柳身法 = value;
		}
	}

	public double 谭_怒海狂澜
	{
		get
		{
			return _谭_怒海狂澜;
		}
		set
		{
			_谭_怒海狂澜 = value;
		}
	}

	public double 谭_冲冠一怒
	{
		get
		{
			return _谭_冲冠一怒;
		}
		set
		{
			_谭_冲冠一怒 = value;
		}
	}

	public double 拳师_狂神降世
	{
		get
		{
			return _拳师_狂神降世;
		}
		set
		{
			_拳师_狂神降世 = value;
		}
	}

	public double 拳师_金刚不坏
	{
		get
		{
			return _拳师_金刚不坏;
		}
		set
		{
			_拳师_金刚不坏 = value;
		}
	}

	public double 拳师_转攻为守
	{
		get
		{
			return _拳师_转攻为守;
		}
		set
		{
			_拳师_转攻为守 = value;
		}
	}

	public double 拳师_水火一体
	{
		get
		{
			return _拳师_水火一体;
		}
		set
		{
			_拳师_水火一体 = value;
		}
	}

	public double 拳师_会心一击
	{
		get
		{
			return _拳师_会心一击;
		}
		set
		{
			_拳师_会心一击 = value;
		}
	}

	public double 拳师_磨杵成针
	{
		get
		{
			return _拳师_磨杵成针;
		}
		set
		{
			_拳师_磨杵成针 = value;
		}
	}

	public double 拳师_末日狂舞
	{
		get
		{
			return _拳师_末日狂舞;
		}
		set
		{
			_拳师_末日狂舞 = value;
		}
	}

	//24.0 EVIAS 灵甲爆发 2025-07-22
	
	public double 枪客_灵甲爆发
	{
		get
		{
			return _枪客_灵甲爆发;
		}
		set
		{
			_枪客_灵甲爆发 = value;
		}
	}

	public double 拳头_运气疗伤
	{
		get
		{
			return _拳头_运气疗伤;
		}
		set
		{
			_拳头_运气疗伤 = value;
		}
	}

	public double 拳师_会心一击威力
	{
		get
		{
			return _拳师_会心一击威力;
		}
		set
		{
			_拳师_会心一击威力 = value;
		}
	}

	public double 梅_障力激活
	{
		get
		{
			return _梅_障力激活;
		}
		set
		{
			_梅_障力激活 = value;
		}
	}

	public double 梅_障力运用
	{
		get
		{
			return _梅_障力运用;
		}
		set
		{
			_梅_障力运用 = value;
		}
	}

	public double 梅_玄武神功
	{
		get
		{
			return _梅_玄武神功;
		}
		set
		{
			_梅_玄武神功 = value;
		}
	}

	public double 梅_玄武的指点
	{
		get
		{
			return _梅_玄武的指点;
		}
		set
		{
			_梅_玄武的指点 = value;
		}
	}

	public double 梅_玄武强击
	{
		get
		{
			return _梅_玄武强击;
		}
		set
		{
			_梅_玄武强击 = value;
		}
	}

	public double 梅_玄武危化
	{
		get
		{
			return _梅_玄武危化;
		}
		set
		{
			_梅_玄武危化 = value;
		}
	}

	public double 梅_障力恢复
	{
		get
		{
			return _梅_障力恢复;
		}
		set
		{
			_梅_障力恢复 = value;
		}
	}

	public double 梅_嫉妒的化身
	{
		get
		{
			return _梅_嫉妒的化身;
		}
		set
		{
			_梅_嫉妒的化身 = value;
		}
	}

	public double 梅_愤怒爆发
	{
		get
		{
			return _梅_愤怒爆发;
		}
		set
		{
			_梅_愤怒爆发 = value;
		}
	}

	public double 梅_吸血进击
	{
		get
		{
			return _梅_吸血进击;
		}
		set
		{
			_梅_吸血进击 = value;
		}
	}

	public double 韩_升天一气功_行风弄舞
	{
		get
		{
			return _韩_升天一气功_行风弄舞;
		}
		set
		{
			_韩_升天一气功_行风弄舞 = value;
		}
	}

	public double 韩_升天二气功_天魔护体
	{
		get
		{
			return _韩_升天二气功_天魔护体;
		}
		set
		{
			_韩_升天二气功_天魔护体 = value;
		}
	}

	public double 韩_升天二气功_内息行心
	{
		get
		{
			return _韩_升天二气功_内息行心;
		}
		set
		{
			_韩_升天二气功_内息行心 = value;
		}
	}

	public double 刀_升天一气功_遁出逆境
	{
		get
		{
			return _刀_升天一气功_遁出逆境;
		}
		set
		{
			_刀_升天一气功_遁出逆境 = value;
		}
	}

	public double 剑_乘胜追击
	{
		get
		{
			return _剑_乘胜追击;
		}
		set
		{
			_剑_乘胜追击 = value;
		}
	}

	public double 枪_升天一气功_破甲刺魂
	{
		get
		{
			return _枪_升天一气功_破甲刺魂;
		}
		set
		{
			_枪_升天一气功_破甲刺魂 = value;
		}
	}

	public double 弓_升天一气功_绝影射魂
	{
		get
		{
			return _弓_升天一气功_绝影射魂;
		}
		set
		{
			_弓_升天一气功_绝影射魂 = value;
		}
	}

	public double 医_狂意护体
	{
		get
		{
			return _医_狂意护体;
		}
		set
		{
			_医_狂意护体 = value;
		}
	}

	public double 刺_升天一气功_夜魔缠身
	{
		get
		{
			return _刺_升天一气功_夜魔缠身;
		}
		set
		{
			_刺_升天一气功_夜魔缠身 = value;
		}
	}

	public double 升天一气功_运气行心
	{
		get
		{
			return _升天一气功_运气行心;
		}
		set
		{
			_升天一气功_运气行心 = value;
		}
	}

	public double 升天一气功_运气疗伤
	{
		get
		{
			return _升天一气功_运气疗伤;
		}
		set
		{
			_升天一气功_运气疗伤 = value;
		}
	}

	public double 升天一气功_狂风天意
	{
		get
		{
			return _升天一气功_狂风天意;
		}
		set
		{
			_升天一气功_狂风天意 = value;
		}
	}

	public double 琴师_升天一气功_飞花点翠
	{
		get
		{
			return _琴师_升天一气功_飞花点翠;
		}
		set
		{
			_琴师_升天一气功_飞花点翠 = value;
		}
	}

	public double 拳_升天一气功_夺命连环
	{
		get
		{
			return _拳_升天一气功_夺命连环;
		}
		set
		{
			_拳_升天一气功_夺命连环 = value;
		}
	}

	public double 拳_升天二气功_电光石火
	{
		get
		{
			return _拳_升天二气功_电光石火;
		}
		set
		{
			_拳_升天二气功_电光石火 = value;
		}
	}

	public double 拳_升天三气功_精益求精
	{
		get
		{
			return _拳_升天三气功_精益求精;
		}
		set
		{
			_拳_升天三气功_精益求精 = value;
		}
	}

	public double 梅_升天一气功_玄武雷电
	{
		get
		{
			return _梅_升天一气功_玄武雷电;
		}
		set
		{
			_梅_升天一气功_玄武雷电 = value;
		}
	}

	public double 梅_升天二气功_玄武诅咒
	{
		get
		{
			return _梅_升天二气功_玄武诅咒;
		}
		set
		{
			_梅_升天二气功_玄武诅咒 = value;
		}
	}

	public double 梅_升天三气功_杀人鬼
	{
		get
		{
			return _梅_升天三气功_杀人鬼;
		}
		set
		{
			_梅_升天三气功_杀人鬼 = value;
		}
	}

	public double 刀_升天二气功_穷途末路
	{
		get
		{
			return _刀_升天二气功_穷途末路;
		}
		set
		{
			_刀_升天二气功_穷途末路 = value;
		}
	}

	public double 琴师_升天一气功_飞花点翠加成
	{
		get
		{
			return _琴师_升天一气功_飞花点翠加成;
		}
		set
		{
			_琴师_升天一气功_飞花点翠加成 = value;
		}
	}

	public double 剑_升天二气功_天地同寿
	{
		get
		{
			return _剑_升天二气功_天地同寿;
		}
		set
		{
			_剑_升天二气功_天地同寿 = value;
		}
	}

	public double 谭_升天二气功_天地同寿
	{
		get
		{
			return _谭_升天二气功_天地同寿;
		}
		set
		{
			_谭_升天二气功_天地同寿 = value;
		}
	}

	public double 枪_升天二气功_以退为进
	{
		get
		{
			return _枪_升天二气功_以退为进;
		}
		set
		{
			_枪_升天二气功_以退为进 = value;
		}
	}

	public double 医_无中生有
	{
		get
		{
			return _医_无中生有;
		}
		set
		{
			_医_无中生有 = value;
		}
	}

	public double 刺_升天二气功_顺水推舟
	{
		get
		{
			return _刺_升天二气功_顺水推舟;
		}
		set
		{
			_刺_升天二气功_顺水推舟 = value;
		}
	}

	public double 琴师_升天二气功_三潭映月
	{
		get
		{
			return _琴师_升天二气功_三潭映月;
		}
		set
		{
			_琴师_升天二气功_三潭映月 = value;
		}
	}

	public double 刺_以怒还怒
	{
		get
		{
			return _刺_以怒还怒;
		}
		set
		{
			_刺_以怒还怒 = value;
		}
	}

	public double 刀_梵音破镜
	{
		get
		{
			return _刀_梵音破镜;
		}
		set
		{
			_刀_梵音破镜 = value;
		}
	}

	public double 剑_升天三气功_火凤临朝
	{
		get
		{
			return _剑_升天三气功_火凤临朝;
		}
		set
		{
			_剑_升天三气功_火凤临朝 = value;
		}
	}

	public double 谭_升天三气功_火凤临朝
	{
		get
		{
			return _谭_升天三气功_火凤临朝;
		}
		set
		{
			_谭_升天三气功_火凤临朝 = value;
		}
	}

	public double 枪_怒意之吼
	{
		get
		{
			return _枪_怒意之吼;
		}
		set
		{
			_枪_怒意之吼 = value;
		}
	}

	public double 弱点攻破
	{
		get
		{
			return _弱点攻破;
		}
		set
		{
			_弱点攻破 = value;
		}
	}

	public double 牢不可破
	{
		get
		{
			return _牢不可破;
		}
		set
		{
			_牢不可破 = value;
		}
	}

	public double 陵劲淬砺
	{
		get
		{
			return _陵劲淬砺;
		}
		set
		{
			_陵劲淬砺 = value;
		}
	}

	public double 卢_破血狂风
	{
		get
		{
			return _卢_破血狂风;
		}
		set
		{
			_卢_破血狂风 = value;
		}
	}

	public double 技冠群雄
	{
		get
		{
			return _技冠群雄;
		}
		set
		{
			_技冠群雄 = value;
		}
	}

	public double 弓_升天三气功_天外三矢
	{
		get
		{
			return _弓_升天三气功_天外三矢;
		}
		set
		{
			_弓_升天三气功_天外三矢 = value;
		}
	}

	public double 医_升天三气功_明镜止水
	{
		get
		{
			return _医_升天三气功_明镜止水;
		}
		set
		{
			_医_升天三气功_明镜止水 = value;
		}
	}

	public double 琴师_升天三气功_子夜秋歌
	{
		get
		{
			return _琴师_升天三气功_子夜秋歌;
		}
		set
		{
			_琴师_升天三气功_子夜秋歌 = value;
		}
	}

	public double 谭_升天三气功_以柔克刚
	{
		get
		{
			return _谭_升天三气功_以柔克刚;
		}
		set
		{
			_谭_升天三气功_以柔克刚 = value;
		}
	}

	public double 全职业气功防御
	{
		get
		{
			return _全职业气功防御;
		}
		set
		{
			_全职业气功防御 = value;
		}
	}

	public double 大魔神添加全职业气功防御几率
	{
		get
		{
			return _大魔神添加全职业气功防御几率;
		}
		set
		{
			_大魔神添加全职业气功防御几率 = value;
		}
	}

	public double 升天四式_红月狂风
	{
		get
		{
			return _升天四式_红月狂风;
		}
		set
		{
			_升天四式_红月狂风 = value;
		}
	}

	public double 升天四式_即月狂风
	{
		get
		{
			return _升天四式_即月狂风;
		}
		set
		{
			_升天四式_即月狂风 = value;
		}
	}

	public double 升天四式_毒蛇出洞
	{
		get
		{
			return _升天四式_毒蛇出洞;
		}
		set
		{
			_升天四式_毒蛇出洞 = value;
		}
	}

	public double 升天四式_满月狂风
	{
		get
		{
			return _升天四式_满月狂风;
		}
		set
		{
			_升天四式_满月狂风 = value;
		}
	}

	public double 升天四式_烈日炎炎
	{
		get
		{
			return _升天四式_烈日炎炎;
		}
		set
		{
			_升天四式_烈日炎炎 = value;
		}
	}

	public double 升天四式_望梅添花
	{
		get
		{
			return _升天四式_望梅添花;
		}
		set
		{
			_升天四式_望梅添花 = value;
		}
	}

	public double 升天四式_悬丝诊脉
	{
		get
		{
			return _升天四式_悬丝诊脉;
		}
		set
		{
			_升天四式_悬丝诊脉 = value;
		}
	}

	public double 升天四式_长虹贯天
	{
		get
		{
			return _升天四式_长虹贯天;
		}
		set
		{
			_升天四式_长虹贯天 = value;
		}
	}

	public double 升天四式_哀鸿遍野
	{
		get
		{
			return _升天四式_哀鸿遍野;
		}
		set
		{
			_升天四式_哀鸿遍野 = value;
		}
	}

	public double 升天五式_致残
	{
		get
		{
			return _升天五式_致残;
		}
		set
		{
			_升天五式_致残 = value;
		}
	}

	public double 升天五式_龙魂附体
	{
		get
		{
			return _升天五式_龙魂附体;
		}
		set
		{
			_升天五式_龙魂附体 = value;
		}
	}

	public double 升天五式_惊天动地
	{
		get
		{
			return _升天五式_惊天动地;
		}
		set
		{
			_升天五式_惊天动地 = value;
		}
	}

	public double 升天五式_灭世狂舞
	{
		get
		{
			return _升天五式_灭世狂舞;
		}
		set
		{
			_升天五式_灭世狂舞 = value;
		}
	}

	public double 升天五式_千里一击
	{
		get
		{
			return _升天五式_千里一击;
		}
		set
		{
			_升天五式_千里一击 = value;
		}
	}

	public double 升天五式_形移妖相
	{
		get
		{
			return _升天五式_形移妖相;
		}
		set
		{
			_升天五式_形移妖相 = value;
		}
	}

	public double 升天五式_一招杀神
	{
		get
		{
			return _升天五式_一招杀神;
		}
		set
		{
			_升天五式_一招杀神 = value;
		}
	}

	public double 升天五式_龙爪纤指手
	{
		get
		{
			return _升天五式_龙爪纤指手;
		}
		set
		{
			_升天五式_龙爪纤指手 = value;
		}
	}

	public double 升天五式_天魔之力
	{
		get
		{
			return _升天五式_天魔之力;
		}
		set
		{
			_升天五式_天魔之力 = value;
		}
	}

	public double 升天五式_惊涛骇浪
	{
		get
		{
			return _升天五式_惊涛骇浪;
		}
		set
		{
			_升天五式_惊涛骇浪 = value;
		}
	}

	public double 升天五式_不死之躯
	{
		get
		{
			return _升天五式_不死之躯;
		}
		set
		{
			_升天五式_不死之躯 = value;
		}
	}

	public double 升天五式_魔魂之力
	{
		get
		{
			return _升天五式_魔魂之力;
		}
		set
		{
			_升天五式_魔魂之力 = value;
		}
	}

	public double 升天五式_破空坠星
	{
		get
		{
			return _升天五式_破空坠星;
		}
		set
		{
			_升天五式_破空坠星 = value;
		}
	}

	public double 神女运气行心
	{
		get
		{
			return _神女运气行心;
		}
		set
		{
			_神女运气行心 = value;
		}
	}

	public double 神女太极心法
	{
		get
		{
			return _神女太极心法;
		}
		set
		{
			_神女太极心法 = value;
		}
	}

	public double 神女神力激发
	{
		get
		{
			return _神女神力激发;
		}
		set
		{
			_神女神力激发 = value;
		}
	}

	public double 神女神力激发对怪防御力
	{
		get
		{
			return _神女神力激发对怪防御力;
		}
		set
		{
			_神女神力激发对怪防御力 = value;
		}
	}

	public double 神女杀星义虎
	{
		get
		{
			return _神女杀星义虎;
		}
		set
		{
			_神女杀星义虎 = value;
		}
	}

	public double 神女杀星义杀
	{
		get
		{
			return _神女杀星义杀;
		}
		set
		{
			_神女杀星义杀 = value;
		}
	}

	public double 神女杀星义气
	{
		get
		{
			return _神女杀星义气;
		}
		set
		{
			_神女杀星义气 = value;
		}
	}

	public double 神女洗髓易筋
	{
		get
		{
			return _神女洗髓易筋;
		}
		set
		{
			_神女洗髓易筋 = value;
		}
	}

	public double 神女黑花漫开
	{
		get
		{
			return _神女黑花漫开;
		}
		set
		{
			_神女黑花漫开 = value;
		}
	}

	public double 神女妙手回春
	{
		get
		{
			return _神女妙手回春;
		}
		set
		{
			_神女妙手回春 = value;
		}
	}

	public double 神女长功击力
	{
		get
		{
			return _神女长功击力;
		}
		set
		{
			_神女长功击力 = value;
		}
	}

	public double 神女黑花集中
	{
		get
		{
			return _神女黑花集中;
		}
		set
		{
			_神女黑花集中 = value;
		}
	}

	public double 神女真武绝击
	{
		get
		{
			return _神女真武绝击;
		}
		set
		{
			_神女真武绝击 = value;
		}
	}

	public double 神女万毒不侵
	{
		get
		{
			return _神女万毒不侵;
		}
		set
		{
			_神女万毒不侵 = value;
		}
	}

	public double 神女愤怒调节
	{
		get
		{
			return _神女愤怒调节;
		}
		set
		{
			_神女愤怒调节 = value;
		}
	}

	public double 神女蛊毒解除
	{
		get
		{
			return _神女蛊毒解除;
		}
		set
		{
			_神女蛊毒解除 = value;
		}
	}

	public double 神女尸毒爆发
	{
		get
		{
			return _神女尸毒爆发;
		}
		set
		{
			_神女尸毒爆发 = value;
		}
	}

	public double 阎王爆累计伤害
	{
		get
		{
			return _阎王爆累计伤害;
		}
		set
		{
			_阎王爆累计伤害 = value;
		}
	}
}

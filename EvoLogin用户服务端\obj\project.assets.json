{"version": 3, "targets": {".NETFramework,Version=v4.8": {}, ".NETFramework,Version=v4.8/win-x86": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\PPBBG-20250615\\服务端优化项目\\EvoLogin用户服务端\\LoginServer.csproj", "projectName": "<PERSON>vo<PERSON><PERSON><PERSON>", "projectPath": "E:\\PPBBG-20250615\\服务端优化项目\\EvoLogin用户服务端\\LoginServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\PPBBG-20250615\\服务端优化项目\\EvoLogin用户服务端\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}
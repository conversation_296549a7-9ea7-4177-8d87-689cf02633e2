using System;
using System.Collections.Concurrent;
using System.Globalization;
using System.Text;

namespace RxjhServer.HelperTools;

public class Converter
{
	public static ConcurrentDictionary<string, byte[]> Hexstring = new ConcurrentDictionary<string, byte[]>();

	public static string ToString(byte[] bytes)
	{
		StringBuilder stringBuilder = new StringBuilder(bytes.Length * 2);
		foreach (byte b in bytes)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return stringBuilder.ToString();
	}

	public static string ToString1(byte[] bytes)
	{
		StringBuilder stringBuilder = new StringBuilder(bytes.Length * 2);
		foreach (byte b in bytes)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return "0x" + stringBuilder.ToString();
	}

	public static byte[] hexStringToByte(string hex)
	{
		string key = ((hex.Length > 40) ? hex.Remove(40, hex.Length - 40) : hex);
		if (!Hexstring.TryGetValue(key, out var value))
		{
			value = hexStringToByte2(hex);
			Hexstring.TryAdd(key, value);
		}
		byte[] array = new byte[value.Length];
		value.CopyTo(array, 0);
		return array;
	}

	public static string DateTimeToString(DateTime dt)
	{
		return dt.ToString("yyyyMMdd");
	}

	public static byte[] hexStringToByte2(string hex)
	{
		try
		{
			int num = hex.Length / 2;
			byte[] array = new byte[num];
			for (int i = 0; i < num; i++)
			{
				array[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
			}
			return array;
		}
		catch (Exception)
		{
			return new byte[hex.Length];
		}
	}

	public static int getItmeid(string Item)
	{
		string text = Item.Substring(6, 2);
		string text2 = Item.Substring(4, 2);
		string text3 = Item.Substring(2, 2);
		string text4 = Item.Substring(0, 2);
		string s = text + text2 + text3 + text4;
		return int.Parse(s, NumberStyles.HexNumber);
	}
}

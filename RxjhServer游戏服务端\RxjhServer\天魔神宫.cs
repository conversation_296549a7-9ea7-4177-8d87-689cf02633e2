using System;
using System.Data;
using System.Text;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class 天魔神宫
{
	public static void 读取天魔神宫占领数据()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 天魔神宫信息  where 分区= '" + World.分区编号 + "'");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count > 0)
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DateTime dateTime = DateTime.Parse(dBToDataTable.Rows[dBToDataTable.Rows.Count - 1]["更新时间"].ToString());
				if (dateTime > DateTime.Now)
				{
					World.天魔神宫占领者 = dBToDataTable.Rows[dBToDataTable.Rows.Count - 1]["占领者"].ToString();
					World.初始占领日期 = int.Parse(dBToDataTable.Rows[dBToDataTable.Rows.Count - 1]["占领日期"].ToString());
					World.天魔神宫更新时间 = dateTime;
					World.城门强化等级 = int.Parse(dBToDataTable.Rows[dBToDataTable.Rows.Count - 1]["城门强化等级"].ToString());
					break;
				}
			}
		}
		dBToDataTable.Dispose();
	}

	public static void 城门强化确认(byte[] 封包数据, Players players)
	{
		string hex = "AA551A00B003DB041400070000000000000000000000000000000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Player_Money < 50000000)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(10138), 0, array, 26, 4);
			players.系统提示("金币不足，你的金币不足5000万！", 9, "系统提示");
		}
		else if (players.Player_Whtb < 10000)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(10236), 0, array, 26, 4);
			players.系统提示("武皇币不足，你的武皇币不足10000 请用武勋!兑换武皇币！", 9, "系统提示");
		}
		else
		{
			players.Player_Money -= 50000000L;
            players.Player_Whtb -= 10000;
			World.城门强化等级++;
			players.游戏币消耗提示(50000000u, -1);
			players.更新武功和状态();
			players.更新金钱和负重();
			players.系统提示("强化城门成功，当前城门强化等级：[" + World.城门强化等级 + "]级！", 10, "系统提示");
		}
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 查看城门强化信息(byte[] 封包数据, Players players)
	{
		string hex = "AA551A00B003D90414000700000005000000A0252600F40100000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(World.城门强化等级), 0, array, 14, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(World.城门强化等级 * 250000), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(World.城门强化等级 * 50), 0, array, 22, 4);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 取消攻城申请(byte[] 封包数据, Players players)
	{
		if (players.帮派名字 == "" || players.帮派人物等级 != 6)
		{
			return;
		}
		if (players.门派联盟盟主.Length > 0)
		{
			if (players.帮派名字 != players.门派联盟盟主)
			{
				return;
			}
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.门派联盟盟主 == players.门派联盟盟主)
				{
					发送攻城相关BUFF(value, 是否消失: true);
					value.宣告攻城 = 0;
				}
			}
			DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Guild]  where  联盟盟主='" + players.门派联盟盟主 + "'");
			if (dBToDataTable != null)
			{
				if (dBToDataTable.Rows.Count == 1)
				{
					RxjhClass.更新门派联盟盟主(players.门派联盟盟主, "", 0);
					players.门派联盟盟主 = "";
				}
				else
				{
					RxjhClass.更新联盟宣告攻城状态(players.门派联盟盟主, 0);
				}
				dBToDataTable.Dispose();
			}
		}
		else
		{
			RxjhClass.更新门派联盟盟主(players.帮派名字, "", 0);
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.帮派名字 == players.帮派名字)
				{
					发送攻城相关BUFF(value2, 是否消失: true);
					value2.宣告攻城 = 0;
				}
			}
		}
		string hex = "AA5522002100D6041C0007000000BDADBAFEB5DAD2BBB4F3B0EF00000000020000000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 查看同盟门派信息(byte[] 封包数据, Players players)
	{
		string hex = "AA550E011000CA04080107000000330000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000100000001000000010000000000000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		DataTable dataTable = RxjhClass.取得门派联盟列表(World.天魔神宫占领者);
		if (dataTable != null)
		{
			int num = 0;
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				string text = dataTable.Rows[i]["G_Name"].ToString();
				if (text != World.天魔神宫占领者)
				{
					byte[] bytes = Encoding.Default.GetBytes(text);
					Buffer.BlockCopy(bytes, 0, array, 18 + 24 * num, bytes.Length);
					num++;
				}
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 270, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 查看同盟申请门派信息(byte[] 封包数据, Players players)
	{
		byte[] array = new byte[20];
		for (int i = 0; i < 20 && 封包数据[i + 10] != 0; i++)
		{
			array[i] = 封包数据[i + 10];
		}
		string name = Encoding.Default.GetString(array).Replace("\0", "");
		DataTable dataTable = RxjhClass.得到帮派数据(name);
		string hex = "AA552D000400C8042700B9000000000000000000000000000000D70000000000000000000000000000010000000100000055AA";
		byte[] array2 = Converter.hexStringToByte(hex);
		if (dataTable != null)
		{
			byte[] array3 = new byte[20];
			array3 = Encoding.Default.GetBytes(dataTable.Rows[0]["G_Name"].ToString());
			Buffer.BlockCopy(array3, 0, array2, 10, array3.Length);
			array3 = Encoding.Default.GetBytes(dataTable.Rows[0]["G_Master"].ToString());
			Buffer.BlockCopy(array3, 0, array2, 26, array3.Length);
			Buffer.BlockCopy(BitConverter.GetBytes((int)dataTable.Rows[0]["Leve"]), 0, array2, 45, 4);
			dataTable.Dispose();
			dataTable = RxjhClass.得到帮派人数(name);
			Buffer.BlockCopy(BitConverter.GetBytes(dataTable.Rows.Count), 0, array2, 41, 4);
			dataTable.Dispose();
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array2, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array2, array2.Length);
		}
	}

	public static void 同盟管理(byte[] 封包数据, Players players)
	{
		string hex = "AA550E010400C604080107000000330000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000008904000001000000010000000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		DataTable dataTable = RxjhClass.取得门派联盟列表(players.帮派名字);
		int num = 0;
		if (dataTable != null)
		{
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				string text = dataTable.Rows[i]["G_Name"].ToString();
				if (text != players.帮派名字)
				{
					byte[] bytes = Encoding.Default.GetBytes(text);
					Buffer.BlockCopy(bytes, 0, array, 18 + 24 * num, bytes.Length);
					Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 34 + 24 * num, 4);
					num++;
				}
			}
		}
		for (int j = 0; j < World.门派联盟申请状态.Count; j++)
		{
			门派联盟申请状态 门派联盟申请状态2 = World.门派联盟申请状态[j];
			if (门派联盟申请状态2.盟主门派名字 == players.帮派名字)
			{
				byte[] bytes2 = Encoding.Default.GetBytes(门派联盟申请状态2.申请门派名字);
				Buffer.BlockCopy(bytes2, 0, array, 18 + 24 * (j + num), bytes2.Length);
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 回应其它门派的同盟申请(byte[] 封包数据, Players players)
	{
		byte[] array = new byte[20];
		for (int i = 0; i < 20 && 封包数据[i + 14] != 0; i++)
		{
			array[i] = 封包数据[i + 14];
		}
		string text = Encoding.Default.GetString(array).Replace("\0", "");
		switch (封包数据[30])
		{
		case 2:
		{
			if (players.帮派名字 == players.门派联盟盟主)
			{
				RxjhClass.更新门派联盟盟主(players.帮派名字, "", 0);
				foreach (Players value in World.allConnectedChars.Values)
				{
					if (value.帮派名字 == text)
					{
						value.门派联盟盟主 = "";
						value.宣告攻城 = 0;
						发送攻城相关BUFF(value, 是否消失: true);
					}
				}
				break;
			}
			if (players.门派联盟盟主 != "")
			{
				RxjhClass.更新门派联盟盟主(players.帮派名字, "", 0);
				foreach (Players value2 in World.allConnectedChars.Values)
				{
					if (value2.帮派名字 == players.帮派名字)
					{
						value2.门派联盟盟主 = "";
						value2.宣告攻城 = 0;
						发送攻城相关BUFF(value2, 是否消失: true);
					}
				}
				break;
			}
			for (int k = 0; k < World.门派联盟申请状态.Count; k++)
			{
				门派联盟申请状态 门派联盟申请状态3 = World.门派联盟申请状态[k];
				if (门派联盟申请状态3.申请门派名字 == players.帮派名字 && 门派联盟申请状态3.盟主门派名字 == text)
				{
					World.门派联盟申请状态.RemoveAt(k);
					break;
				}
			}
			break;
		}
		case 1:
		{
			for (int j = 0; j < World.门派联盟申请状态.Count; j++)
			{
				门派联盟申请状态 门派联盟申请状态2 = World.门派联盟申请状态[j];
				if (!(门派联盟申请状态2.申请门派名字 == text) || !(门派联盟申请状态2.盟主门派名字 == players.帮派名字))
				{
					continue;
				}
				World.门派联盟申请状态.RemoveAt(j);
				RxjhClass.更新门派联盟盟主(门派联盟申请状态2.申请门派名字, 门派联盟申请状态2.盟主门派名字, players.宣告攻城);
				foreach (Players value3 in World.allConnectedChars.Values)
				{
					if (value3.帮派名字 == text)
					{
						value3.宣告攻城 = players.宣告攻城;
						value3.门派联盟盟主 = 门派联盟申请状态2.盟主门派名字;
						发送攻城相关BUFF(value3, 是否消失: false);
					}
				}
				break;
			}
			break;
		}
		}
		string hex = "AA5522000400C4041C000700000000000000000000000000000000000000010000000100000055AA";
		byte[] array2 = Converter.hexStringToByte(hex);
		byte[] bytes = Encoding.Default.GetBytes(text);
		Buffer.BlockCopy(bytes, 0, array2, 14, bytes.Length);
		array2[30] = 封包数据[30];
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array2, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array2, array2.Length);
		}
	}

	public static void 查看天魔神宫占领信息(byte[] 封包数据, Players players)
	{
		string hex = "AA552E001000C2042800070000001500000000000000000000000000000000000000A53C340100000000030000007F3D340155AA";
		byte[] array = Converter.hexStringToByte(hex);
		byte[] bytes = Encoding.Default.GetBytes(World.天魔神宫占领者);
		Buffer.BlockCopy(bytes, 0, array, 18, bytes.Length);
		Buffer.BlockCopy(BitConverter.GetBytes(World.初始占领日期), 0, array, 34, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(Converter.DateTimeToString(DateTime.Now.AddDays(2.0)))), 0, array, 46, 4);
		if (players.门派联盟盟主 == "")
		{
			if (查询门派向哪个门派发出联盟申请(players.帮派名字) == World.天魔神宫占领者)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 42, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 42, 4);
			}
		}
		else if (players.门派联盟盟主 == World.天魔神宫占领者)
		{
			if (players.帮派名字 == players.门派联盟盟主)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 42, 4);
			}
			else
			{
				Buffer.BlockCopy(BitConverter.GetBytes(21), 0, array, 14, 4);
				Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 42, 4);
			}
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 42, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 同盟申请(byte[] 封包数据, Players players)
	{
		string hex = "AA5522001000C0041C0007000000B4000000000000000000000000000000010000000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		byte[] array2 = new byte[20];
		for (int i = 0; i < 20 && 封包数据[i + 14] != 0; i++)
		{
			array[i + 14] = 封包数据[i + 14];
			array2[i] = 封包数据[i + 14];
		}
		string 盟主门派 = Encoding.Default.GetString(array2).Replace("\0", "");
		申请门派联盟(players, 盟主门派);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 查看攻城同盟列表(byte[] 封包数据, Players players)
	{
		int num = 封包数据[22];
		string hex = "AA550E01C405BE04080107000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		DataTable dataTable = RxjhClass.得到已申请攻城的门派列表();
		players.门派联盟盟主 = RxjhClass.取得门派联盟盟主(players.帮派名字);
		if (dataTable != null && dataTable.Rows.Count > num * 10 - 10)
		{
			int num2 = 0;
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				string text = dataTable.Rows[num * 10 - 10 + i]["G_Name"].ToString();
				if (text != World.天魔神宫占领者)
				{
					byte[] bytes = Encoding.Default.GetBytes(text);
					Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 14 + 24 * num2, 4);
					Buffer.BlockCopy(bytes, 0, array, 18 + 24 * num2, bytes.Length);
					if (text == players.门派联盟盟主)
					{
						Buffer.BlockCopy(BitConverter.GetBytes(3), 0, array, 34 + 24 * num2, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
					}
					else if (text == 查询门派向哪个门派发出联盟申请(players.帮派名字))
					{
						Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 34 + 24 * num2, 4);
						Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 270, 4);
					}
					else
					{
						Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 34 + 24 * num2, 4);
					}
					num2++;
				}
			}
			Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 262, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(num2 / 10 + 1), 0, array, 266, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 申请攻城(byte[] 封包数据, Players players)
	{
		try
		{
			if (players.宣告攻城 == 1)
			{
				players.系统提示("已申请攻城的门派不能重复申请");
				return;
			}
			if (players.帮派人物等级 != 6)
			{
				players.系统提示("只有门主可以申请攻城");
				return;
			}
			string hex = "AA5512000400BC040C0007000000020000000100000055AA";
			byte[] array = Converter.hexStringToByte(hex);
			Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
			if (players.Client != null)
			{
				players.Client.Send(array, array.Length);
			}
			DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [天魔神宫信息]  where 分区信息= '" + World.分区编号 + "' ");
			if (dBToDataTable == null || dBToDataTable.Rows.Count == 0)
			{
				World.天魔神宫占领者 = players.帮派名字;
				DBA.ExeSqlCommand(string.Format("INSERT INTO 天魔神宫信息 (占领者,占领日期,城门强化等级,分区) VALUES ('{0}',{1},{2},'{3}')", players.帮派名字, Converter.DateTimeToString(DateTime.Now), "0", World.分区编号));
			}
			dBToDataTable.Dispose();
			if (players.门派联盟盟主 != "")
			{
				if (players.门派联盟盟主 != players.帮派名字)
				{
					players.系统提示("只有门派联盟的盟主可以宣告攻城");
					return;
				}
				RxjhClass.更新联盟宣告攻城状态(players.帮派名字, 1);
				{
					foreach (Players value in World.allConnectedChars.Values)
					{
						if (value.门派联盟盟主 == players.门派联盟盟主)
						{
							value.门派联盟盟主 = players.门派联盟盟主;
							value.宣告攻城 = 1;
							发送攻城相关BUFF(value, 是否消失: false);
						}
					}
					return;
				}
			}
			RxjhClass.更新门派联盟盟主(players.帮派名字, players.帮派名字, 1);
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.帮派名字 == players.帮派名字)
				{
					value2.宣告攻城 = 1;
					value2.门派联盟盟主 = players.帮派名字;
					发送攻城相关BUFF(value2, 是否消失: false);
				}
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleGameException(ex, players, "申请攻城", $"天魔神宫攻城申请");
		}
	}

	public static void 发送攻城相关BUFF(Players players, bool 是否消失)
	{
		读取天魔神宫占领数据();
		string hex = "AA553200210043012C0000000000000000007AE4143C0000000070000100585D94770000000001000000E84B9377000000000100000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		uint value = uint.Parse(DateTime.Now.ToString("yyMMddHHmm"));
		uint value2 = uint.Parse(World.天魔神宫更新时间.ToString("yyMMddHHmm"));
		if (是否消失)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(-1), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 38, 4);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 42, 4);
			if (players.帮派名字 != players.门派联盟盟主 || players.帮派人物等级 != 6)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 50, 4);
			}
		}
		if (players.门派联盟盟主.Length > 0 && players.门派联盟盟主 == World.天魔神宫占领者)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(1008002169), 0, array, 18, 4);
			if (!players.追加状态列表.ContainsKey(1008002169))
			{
				追加状态类 value3 = new 追加状态类(players, 604800000, 1008002169, 0);
				players.追加状态列表.TryAdd(1008002169, value3);
			}
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(1008002170), 0, array, 18, 4);
			if (!players.追加状态列表.ContainsKey(1008002170))
			{
				追加状态类 value4 = new 追加状态类(players, 604800000, 1008002170, 0);
				players.追加状态列表.TryAdd(1008002170, value4);
			}
		}
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 发送攻城相关BUFF(Players players, int BUFFID, DateTime 到期时间, bool 是否消失)
	{
		string hex = "AA5532001F0443012C0000000000000000008FE4143C00000000700000003C00000000000000010000008EE69377000000000000000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		uint value = uint.Parse(到期时间.ToString("yyMMddHHmm"));
		uint value2 = (uint)到期时间.Subtract(DateTime.Now).TotalSeconds;
		if (是否消失)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(-1), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 38, 4);
		}
		else
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value2), 0, array, 30, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array, 42, 4);
		}
		Buffer.BlockCopy(BitConverter.GetBytes(BUFFID), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 发出攻城战准备开始第一次通知(Players players)
	{
		string hex = "AA5514002C0503510E0000000000FFFFFFFFFFFF0700000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 发出攻城战准备开始第二次通知(Players players)
	{
		string hex = "AA551400B90005510E000000000001000000020011A4000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
		if (players.Client != null)
		{
			players.Client.Send(array, array.Length);
		}
	}

	public static void 发送玩家被击败的消息(string 胜利者, string 失败者)
	{
		string hex = "AA552C007D03E3042600B30000000000000000000000000000CA0000000000000000000000000000020000000200000055AA";
		byte[] array = Converter.hexStringToByte(hex);
		byte[] bytes = Encoding.Default.GetBytes(胜利者);
		Buffer.BlockCopy(bytes, 0, array, 10, bytes.Length);
		bytes = Encoding.Default.GetBytes(失败者);
		Buffer.BlockCopy(bytes, 0, array, 25, bytes.Length);
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.人物坐标_地图 == 42001)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(value.人物全服ID), 0, array, 4, 2);
				if (value.Client != null)
				{
					value.Client.Send(array, array.Length);
				}
			}
		}
	}

	public static string 查询门派向哪个门派发出联盟申请(string 门派名字)
	{
		int num = 0;
		门派联盟申请状态 门派联盟申请状态2;
		while (true)
		{
			if (num < World.门派联盟申请状态.Count)
			{
				门派联盟申请状态2 = World.门派联盟申请状态[num];
				if (门派联盟申请状态2.申请门派名字 == 门派名字)
				{
					break;
				}
				num++;
				continue;
			}
			return "";
		}
		return 门派联盟申请状态2.盟主门派名字;
	}

	public static int 申请门派联盟(Players player, string 盟主门派)
	{
		foreach (门派联盟申请状态 item in World.门派联盟申请状态)
		{
			if (item.申请门派名字 == player.帮派名字)
			{
				return -1;
			}
		}
		门派联盟申请状态 门派联盟申请状态2 = new 门派联盟申请状态();
		门派联盟申请状态2.申请门派名字 = player.帮派名字;
		门派联盟申请状态2.盟主门派名字 = 盟主门派;
		World.门派联盟申请状态.Add(门派联盟申请状态2);
		return 0;
	}

	public static void 天魔神宫邀请(Players player, byte[] 封包数据, int 封包大小)
	{
		byte[] array = new byte[4];
		Buffer.BlockCopy(封包数据, 20, array, 0, 4);
		if (World.攻城战进程 == 0)
		{
			player.发送提示文本("现在不是攻城战的时候");
		}
		else
		{
			if (BitConverter.ToInt32(array, 0) != 42001)
			{
				return;
			}
			if (player.门派联盟盟主 == "")
			{
				player.系统提示("请先申请攻城战", 10, "系统提示");
				return;
			}
			if (player.门派联盟盟主 == World.天魔神宫占领者)
			{
				player.移动(-437f, 89f, 15f, 42001);
			}
			else
			{
				player.移动(-430f, -660f, 15f, 42001);
			}
			player.发送攻城战剩余时间((int)攻城战.当前进程结束时间.Subtract(DateTime.Now).TotalSeconds);
		}
	}

	public static void 天魔神宫回城(Players player, byte[] 封包数据, int 封包大小)
	{
		if (player.人物坐标_地图 == 42001)
		{
			player.移动(420f, 1550f, 15f, 101);
		}
	}

	public static void 天魔神宫正城门已开启(Players 人物)
	{
		try
		{
			byte[] array = Converter.hexStringToByte("AA550B000000D40405002E4000000055AA");
			if (人物.Client != null)
			{
				人物.Client.Send(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleSystemException(ex, "天魔神宫", "正城门开启", "发送城门状态数据包");
		}
	}

	public static void 天魔神宫东城门已开启(Players 人物)
	{
		try
		{
			byte[] array = Converter.hexStringToByte("AA550B000000D40405002F4000000055AA");
			if (人物.Client != null)
			{
				人物.Client.Send(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleSystemException(ex, "天魔神宫", "东城门开启", "发送城门状态数据包");
		}
	}
}

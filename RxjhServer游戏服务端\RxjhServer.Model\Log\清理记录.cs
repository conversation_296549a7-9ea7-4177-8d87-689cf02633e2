﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 清理记录 {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int ID { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 分区 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 类型 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 全局ID { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 时间 { get; set; }

		[JsonProperty]
		public int 数量 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 物品名 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string 物品ID { get; set; }

		[JsonProperty]
		public int MAGIC0 { get; set; }

		[JsonProperty]
		public int MAGIC1 { get; set; }

		[JsonProperty]
		public int MAGIC2 { get; set; }

		[JsonProperty]
		public int MAGIC3 { get; set; }

		[JsonProperty]
		public int MAGIC4 { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string USERID { get; set; }

		[JsonProperty, Column(DbType = "varchar(255)", IsNullable = false)]
		public string USERNAME { get; set; }

	}

}

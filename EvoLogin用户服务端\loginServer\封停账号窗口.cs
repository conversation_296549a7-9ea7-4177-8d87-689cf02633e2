using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

public class 封停账号窗口 : Form
{
	private IContainer components = null;

	private Button button1;

	private TextBox textBox1;

	private ListView listView1;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader7;

	private ColumnHeader columnHeader6;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 解封ToolStripMenuItem;

	private ToolStripMenuItem 封号ToolStripMenuItem;

	private StatusStrip statusStrip1;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ToolStripStatusLabel toolStripStatusLabel1;

	public 封停账号窗口()
	{
		InitializeComponent();
	}

	private void 封停账号窗口_Load(object sender, EventArgs e)
	{
		try
		{
			string sqlCommand = "SELECT * FROM TBL_ACCOUNT WHERE FLD_ZT>0";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count > 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					string text = dBToDataTable.Rows[i]["FLD_ID"].ToString();
					string text2 = dBToDataTable.Rows[i]["FLD_RXPIONT"].ToString();
					string text3 = dBToDataTable.Rows[i]["FLD_RXPIONTX"].ToString();
					string text4 = dBToDataTable.Rows[i]["FLD_ZT"].ToString();
					string text5 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					string[] items = new string[5] { text, text2, text3, text4, text5 };
					ListViewItem item = new ListViewItem(items);
					listView1.Items.Insert(listView1.Items.Count, item);
				}
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "加载封停账号出错:" + ex.ToString());
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			string text = textBox1.Text;
			if (text.Length <= 0)
			{
				return;
			}
            listView1.Items.Clear();
            string sqlCommand = "SELECT * FROM TBL_ACCOUNT WHERE FLD_ZT = 0 AND  FLD_ID like '%" + text + "%'";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count > 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					string text2 = dBToDataTable.Rows[i]["FLD_ID"].ToString();
					string text3 = dBToDataTable.Rows[i]["FLD_RXPIONT"].ToString();
					string text4 = dBToDataTable.Rows[i]["FLD_RXPIONTX"].ToString();
					string text5 = dBToDataTable.Rows[i]["FLD_ZT"].ToString();
					string text6 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					string[] items = new string[5] { text2, text3, text4, text5, text6 };
					ListViewItem item = new ListViewItem(items);
					listView1.Items.Insert(listView1.Items.Count, item);
				}
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "搜索封停账号出错:" + ex.ToString());
		}
	}

    private void 解封ToolStripMenuItem_Click(object sender, EventArgs e) //EVIAS
    {
        try
        {
            if (listView1.SelectedItems.Count <= 0)
            {
                return; 
            }

            string accountId = listView1.SelectedItems[0].SubItems[0].Text;
            if (!string.IsNullOrEmpty(accountId))
            {
                // 更新 SQL，增加 FLD_LOCK=0 条件
                string sql = $"UPDATE TBL_ACCOUNT SET FLD_ZT=0, 账号是否在线=0, 随机时间在线=0, FLD_LOCK=0 WHERE FLD_ID='{accountId}'";

                if (DBA.ExeSqlCommand(sql, "rxjhaccount") == -1)
                {
                    toolStripStatusLabel1.Text = "解封失败";
                    return;
                }

                toolStripStatusLabel1.Text = "解封成功";
                MessageBox.Show("解封成功");
            }
        }
        catch (Exception ex)
        {
            toolStripStatusLabel1.Text = "解封异常";
            MessageBox.Show($"解封时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void 封号ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listView1.SelectedItems.Count <= 0)
			{
				return;
			}
			string text = listView1.SelectedItems[0].SubItems[0].Text;
			if (text.Length <= 0)
			{
				return;
			}
			playerS playerS2 = World.查询玩家(text);
			if (playerS2 != null)
			{
				// 2025-0618 EVIAS 使用新的玩家管理器
				HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
				World.服务器踢出ID(playerS2.ServerID, playerS2.UserId);
				playerS2.npcyd.Dispose();
			}
			if (DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ZT=99999 WHERE FLD_ID='" + text + "'", "rxjhaccount") == -1)
			{
				toolStripStatusLabel1.Text = "封号失败";
			}
			else
			{
				toolStripStatusLabel1.Text = "封号成功";
			}
		}
		catch (Exception)
		{
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(封停账号窗口));
            this.button1 = new System.Windows.Forms.Button();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.解封ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.contextMenuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(267, 8);
            this.button1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(168, 34);
            this.button1.TabIndex = 4;
            this.button1.Text = "查询";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(54, 10);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(166, 28);
            this.textBox1.TabIndex = 3;
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader5,
            this.columnHeader7,
            this.columnHeader6,
            this.columnHeader1,
            this.columnHeader2});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(0, 51);
            this.listView1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(694, 475);
            this.listView1.TabIndex = 5;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "账号";
            this.columnHeader5.Width = 100;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "元宝数";
            this.columnHeader7.Width = 80;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "钻石数";
            this.columnHeader6.Width = 104;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "封停时间";
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "封停原因";
            this.columnHeader2.Width = 104;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.解封ToolStripMenuItem,
            this.封号ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(117, 64);
            // 
            // 解封ToolStripMenuItem
            // 
            this.解封ToolStripMenuItem.Name = "解封ToolStripMenuItem";
            this.解封ToolStripMenuItem.Size = new System.Drawing.Size(116, 30);
            this.解封ToolStripMenuItem.Text = "解封";
            this.解封ToolStripMenuItem.Click += new System.EventHandler(this.解封ToolStripMenuItem_Click);
            // 
            // 封号ToolStripMenuItem
            // 
            this.封号ToolStripMenuItem.Name = "封号ToolStripMenuItem";
            this.封号ToolStripMenuItem.Size = new System.Drawing.Size(116, 30);
            this.封号ToolStripMenuItem.Text = "封号";
            this.封号ToolStripMenuItem.Click += new System.EventHandler(this.封号ToolStripMenuItem_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1});
            this.statusStrip1.Location = new System.Drawing.Point(0, 542);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(2, 0, 21, 0);
            this.statusStrip1.Size = new System.Drawing.Size(714, 22);
            this.statusStrip1.TabIndex = 6;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.AutoSize = false;
            this.toolStripStatusLabel1.BackColor = System.Drawing.Color.Transparent;
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(200, 15);
            this.toolStripStatusLabel1.Text = "等待操作";
            this.toolStripStatusLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // 封停账号窗口
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(714, 564);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.listView1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "封停账号窗口";
            this.Text = "封停账号窗口";
            this.Load += new System.EventHandler(this.封停账号窗口_Load);
            this.contextMenuStrip1.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

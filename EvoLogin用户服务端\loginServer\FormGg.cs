using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace loginServer;

public class FormGg : Form
{
	private ComboBox comboBox1;

	private TextBox textBox1;

	private Button button1;

	public FormGg()
	{
		InitializeComponent();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (comboBox1.Text == "系统公告")
		{
			公告(0, textBox1.Text);
		}
		else if (comboBox1.Text == "系统滚动公告")
		{
			公告(1, textBox1.Text);
		}
		else if (comboBox1.Text == "系统提示")
		{
			公告(2, textBox1.Text);
		}
	}

	public void 公告(int id, string txt)
	{
		foreach (SockClient value in World.ServerLst.Values)
		{
			value.Sendd("发送公告|" + id + "|" + txt);
		}
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormGg));
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // comboBox1
            // 
            this.comboBox1.AutoCompleteCustomSource.AddRange(new string[] {
            "系统公告",
            "系统滚动公告",
            "系统提示"});
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "系统公告",
            "系统滚动公告",
            "系统提示"});
            this.comboBox1.Location = new System.Drawing.Point(18, 18);
            this.comboBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(136, 26);
            this.comboBox1.TabIndex = 0;
            this.comboBox1.Text = "系统公告";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(18, 57);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBox1.Multiline = true;
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(436, 224);
            this.textBox1.TabIndex = 1;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(326, 14);
            this.button1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(112, 34);
            this.button1.TabIndex = 2;
            this.button1.Text = "发送";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // FormGg
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(474, 302);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.comboBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.MaximizeBox = false;
            this.Name = "FormGg";
            this.Text = "FormGg";
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

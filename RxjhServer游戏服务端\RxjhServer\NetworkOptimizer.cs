using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace RxjhServer
{
    // 2025-0619 EVIAS 网络封包优化管理器
    public class NetworkOptimizer
    {
        private static readonly Lazy<NetworkOptimizer> _instance = new(() => new NetworkOptimizer());
        public static NetworkOptimizer Instance => _instance.Value;

        // 封包缓存池
        private readonly ConcurrentDictionary<string, byte[]> _packetCache = new();
        private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
        
        // 批量发送队列
        private readonly ConcurrentDictionary<int, List<byte[]>> _batchQueues = new();
        private readonly object _batchLock = new object();
        
        // 压缩缓存
        private readonly ConcurrentDictionary<string, byte[]> _compressedCache = new();

        // 统计信息
        private long _cacheHits = 0;
        private long _cacheMisses = 0;
        private long _batchSent = 0;
        private long _compressionSaved = 0;

        // 配置参数
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5); // 缓存5分钟过期
        private readonly int _maxCacheSize = 1000; // 最大缓存数量
        private readonly int _batchSize = 10; // 批量发送大小
        private readonly int _compressionThreshold = 100; // 压缩阈值（字节）

        private NetworkOptimizer() { }

        // 获取缓存的封包
        public byte[] GetCachedPacket(string packetKey)
        {
            try
            {
                if (_packetCache.TryGetValue(packetKey, out var cachedPacket))
                {
                    // 检查是否过期
                    if (_cacheTimestamps.TryGetValue(packetKey, out var timestamp))
                    {
                        if (DateTime.Now.Subtract(timestamp) < _cacheExpiry)
                        {
                            Interlocked.Increment(ref _cacheHits);
                            return cachedPacket;
                        }
                        else
                        {
                            // 过期，移除缓存
                            _packetCache.TryRemove(packetKey, out _);
                            _cacheTimestamps.TryRemove(packetKey, out _);
                        }
                    }
                }

                Interlocked.Increment(ref _cacheMisses);
                return null;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取缓存封包失败: ", packetKey, " - ", ex.Message));
                return null;
            }
        }

        // 缓存封包
        public void CachePacket(string packetKey, byte[] packet)
        {
            try
            {
                // 检查缓存大小限制
                if (_packetCache.Count >= _maxCacheSize)
                {
                    CleanupExpiredCache();
                }

                _packetCache.TryAdd(packetKey, packet);
                _cacheTimestamps.TryAdd(packetKey, DateTime.Now);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("缓存封包失败: ", packetKey, " - ", ex.Message));
            }
        }

        // 添加到批量发送队列
        public void AddToBatchQueue(int clientId, byte[] packet)
        {
            try
            {
                lock (_batchLock)
                {
                    var queue = _batchQueues.GetOrAdd(clientId, _ => new List<byte[]>());
                    queue.Add(packet);

                    // 达到批量大小时发送
                    if (queue.Count >= _batchSize)
                    {
                        FlushBatchQueue(clientId);
                    }
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("添加批量队列失败: ", clientId, " - ", ex.Message));
            }
        }

        // 刷新批量发送队列
        public void FlushBatchQueue(int clientId)
        {
            try
            {
                lock (_batchLock)
                {
                    if (_batchQueues.TryGetValue(clientId, out var queue) && queue.Count > 0)
                    {
                        // 合并封包
                        var mergedPacket = MergePackets(queue);
                        
                        // 发送合并后的封包
                        if (World.list.TryGetValue(clientId, out var client))
                        {
                            client.Send单包(mergedPacket, mergedPacket.Length);
                            Interlocked.Increment(ref _batchSent);
                        }

                        queue.Clear();
                    }
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("刷新批量队列失败: ", clientId, " - ", ex.Message));
            }
        }

        // 刷新所有批量队列
        public void FlushAllBatchQueues()
        {
            try
            {
                var clientIds = _batchQueues.Keys.ToList();
                foreach (var clientId in clientIds)
                {
                    FlushBatchQueue(clientId);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("刷新所有批量队列失败: ", ex.Message));
            }
        }

        // 合并封包
        private byte[] MergePackets(List<byte[]> packets)
        {
            try
            {
                if (packets.Count == 1)
                {
                    return packets[0];
                }

                int totalLength = packets.Sum(p => p.Length);
                var merged = ByteArrayPool.Instance.Get(totalLength);
                
                int offset = 0;
                foreach (var packet in packets)
                {
                    Buffer.BlockCopy(packet, 0, merged, offset, packet.Length);
                    offset += packet.Length;
                }

                return merged;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("合并封包失败: ", ex.Message));
                return new byte[0];
            }
        }

        // 压缩封包
        public byte[] CompressPacket(byte[] packet, string compressionKey = null)
        {
            try
            {
                if (packet.Length < _compressionThreshold)
                {
                    return packet; // 小封包不压缩
                }

                // 检查压缩缓存
                if (!string.IsNullOrEmpty(compressionKey))
                {
                    if (_compressedCache.TryGetValue(compressionKey, out var cachedCompressed))
                    {
                        return cachedCompressed;
                    }
                }

                // 简单的RLE压缩算法
                var compressed = SimpleRLECompress(packet);
                
                if (compressed.Length < packet.Length)
                {
                    Interlocked.Add(ref _compressionSaved, packet.Length - compressed.Length);
                    
                    // 缓存压缩结果
                    if (!string.IsNullOrEmpty(compressionKey))
                    {
                        _compressedCache.TryAdd(compressionKey, compressed);
                    }
                    
                    return compressed;
                }

                return packet; // 压缩后更大，返回原始数据
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("压缩封包失败: ", ex.Message));
                return packet;
            }
        }

        // 简单的RLE压缩
        private byte[] SimpleRLECompress(byte[] data)
        {
            var compressed = new List<byte>();
            
            for (int i = 0; i < data.Length; i++)
            {
                byte current = data[i];
                int count = 1;
                
                // 计算连续相同字节的数量
                while (i + count < data.Length && data[i + count] == current && count < 255)
                {
                    count++;
                }
                
                if (count > 3) // 只有连续超过3个才压缩
                {
                    compressed.Add(0xFF); // 压缩标记
                    compressed.Add(current);
                    compressed.Add((byte)count);
                    i += count - 1;
                }
                else
                {
                    compressed.Add(current);
                }
            }
            
            return compressed.ToArray();
        }

        // 清理过期缓存
        public void CleanupExpiredCache()
        {
            try
            {
                var now = DateTime.Now;
                var expiredKeys = _cacheTimestamps
                    .Where(kvp => now.Subtract(kvp.Value) > _cacheExpiry)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _packetCache.TryRemove(key, out _);
                    _cacheTimestamps.TryRemove(key, out _);
                    _compressedCache.TryRemove(key, out _);
                }

                if (expiredKeys.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("清理过期网络缓存: ", expiredKeys.Count, "个"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理过期缓存失败: ", ex.Message));
            }
        }

        // 获取网络优化统计
        public string GetNetworkStats()
        {
            double hitRate = (_cacheHits + _cacheMisses) > 0 ? (double)_cacheHits / (_cacheHits + _cacheMisses) * 100 : 0;
            int queuedPackets = _batchQueues.Values.Sum(q => q.Count);
            
            return FastString.Format(
                "网络优化: 缓存:{0} 命中率:{1:F1}% 批量:{2} 队列:{3} 压缩节省:{4}KB",
                _packetCache.Count,
                hitRate,
                _batchSent,
                queuedPackets,
                _compressionSaved / 1024
            );
        }

        // 获取批量队列状态
        public string GetBatchQueueStatus()
        {
            try
            {
                var queueStats = _batchQueues
                    .Where(kvp => kvp.Value.Count > 0)
                    .OrderByDescending(kvp => kvp.Value.Count)
                    .Take(10)
                    .Select(kvp => FastString.Concat("客户端", kvp.Key, ":", kvp.Value.Count))
                    .ToArray();

                if (queueStats.Length == 0)
                {
                    return "所有批量队列为空";
                }

                return FastString.Concat("批量队列状态(前10): ", string.Join(" | ", queueStats));
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取批量队列状态失败: ", ex.Message));
                return "状态获取失败";
            }
        }

        // 清理所有缓存
        public void ClearAllCache()
        {
            try
            {
                _packetCache.Clear();
                _cacheTimestamps.Clear();
                _compressedCache.Clear();
                
                lock (_batchLock)
                {
                    _batchQueues.Clear();
                }
                
                // 重置统计
                _cacheHits = 0;
                _cacheMisses = 0;
                _batchSent = 0;
                _compressionSaved = 0;
                
                Form1.WriteLine(6, "网络优化缓存已清空");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清空网络缓存失败: ", ex.Message));
            }
        }

        // 检查是否需要清理
        public bool ShouldCleanup()
        {
            return _packetCache.Count > _maxCacheSize * 0.8; // 达到80%容量时清理
        }
    }
}

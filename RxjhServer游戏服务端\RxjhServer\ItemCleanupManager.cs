using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace RxjhServer
{
    // 2025-0619 EVIAS 地面物品清理管理器
    public class ItemCleanupManager
    {
        private static readonly Lazy<ItemCleanupManager> _instance = new(() => new ItemCleanupManager());
        public static ItemCleanupManager Instance => _instance.Value;

        // 地图索引 - 快速按地图查找物品
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<long, 地面物品类>> _itemsByMap = new();
        
        // 过期物品队列 - 按过期时间排序
        private readonly ConcurrentDictionary<DateTime, List<long>> _expirationQueue = new();
        
        // 清理统计
        private long _totalCleaned = 0;
        private long _totalIndexed = 0;
        private DateTime _lastCleanup = DateTime.Now;

        // 清理配置
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(2); // 每2分钟清理一次
        private readonly TimeSpan _itemExpireTime = TimeSpan.FromSeconds(20); // 物品20秒过期
        private readonly int _maxItemsPerMap = 1000; // 每个地图最大物品数

        private ItemCleanupManager() { }

        // 添加物品到索引
        public void AddItem(地面物品类 item)
        {
            if (item == null) return;

            try
            {
                Interlocked.Increment(ref _totalIndexed);

                // 添加到地图索引
                var mapItems = _itemsByMap.GetOrAdd(item.Rxjh_Map, _ => new ConcurrentDictionary<long, 地面物品类>());
                mapItems.TryAdd(item.id, item);

                // 添加到过期队列
                var expireTime = item.time.Add(_itemExpireTime);
                var expireList = _expirationQueue.GetOrAdd(expireTime, _ => new List<long>());
                lock (expireList)
                {
                    expireList.Add(item.id);
                }

                // 检查地图物品数量限制
                if (mapItems.Count > _maxItemsPerMap)
                {
                    CleanupMapItems(item.Rxjh_Map, mapItems.Count - _maxItemsPerMap);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("添加物品索引失败: ", item.id, " - ", ex.Message));
            }
        }

        // 从索引中移除物品
        public void RemoveItem(long itemId, int mapId)
        {
            try
            {
                // 从地图索引移除
                if (_itemsByMap.TryGetValue(mapId, out var mapItems))
                {
                    mapItems.TryRemove(itemId, out _);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("移除物品索引失败: ", itemId, " - ", ex.Message));
            }
        }

        // 获取地图内所有物品
        public IEnumerable<地面物品类> GetMapItems(int mapId)
        {
            if (_itemsByMap.TryGetValue(mapId, out var mapItems))
            {
                return mapItems.Values;
            }
            return Enumerable.Empty<地面物品类>();
        }

        // 批量清理过期物品
        public void CleanupExpiredItems()
        {
            try
            {
                var now = DateTime.Now;
                var expiredTimes = _expirationQueue.Keys.Where(time => time <= now).ToList();
                
                foreach (var expiredTime in expiredTimes)
                {
                    if (_expirationQueue.TryRemove(expiredTime, out var expiredItems))
                    {
                        foreach (var itemId in expiredItems)
                        {
                            CleanupSingleItem(itemId);
                        }
                    }
                }

                _lastCleanup = now;
                
                if (expiredTimes.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("批量清理过期物品: ", expiredTimes.Sum(t => _expirationQueue.ContainsKey(t) ? _expirationQueue[t].Count : 0), "个"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("批量清理过期物品失败: ", ex.Message));
            }
        }

        // 清理单个物品
        private void CleanupSingleItem(long itemId)
        {
            try
            {
                if (World.ItmeTeM.TryRemove(itemId, out var item))
                {
                    // 从地图索引移除
                    RemoveItem(itemId, item.Rxjh_Map);
                    
                    // 通知范围内玩家物品消失
                    item.获取范围玩家发送地面消失物品数据包();
                    
                    // 释放资源
                    item.Dispose();
                    
                    Interlocked.Increment(ref _totalCleaned);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理单个物品失败: ", itemId, " - ", ex.Message));
            }
        }

        // 清理指定地图的多余物品
        private void CleanupMapItems(int mapId, int cleanupCount)
        {
            try
            {
                if (!_itemsByMap.TryGetValue(mapId, out var mapItems)) return;

                // 按时间排序，清理最旧的物品
                var oldestItems = mapItems.Values
                    .OrderBy(item => item.time)
                    .Take(cleanupCount)
                    .ToList();

                foreach (var item in oldestItems)
                {
                    CleanupSingleItem(item.id);
                }

                if (oldestItems.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("地图", mapId, "清理多余物品: ", oldestItems.Count, "个"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理地图物品失败: ", mapId, " - ", ex.Message));
            }
        }

        // 强制清理所有过期物品
        public void ForceCleanupAll()
        {
            try
            {
                var now = DateTime.Now;
                var expiredItems = new List<long>();

                // 收集所有过期物品
                foreach (var item in World.ItmeTeM.Values)
                {
                    if (now.Subtract(item.time) > _itemExpireTime)
                    {
                        expiredItems.Add(item.id);
                    }
                }

                // 批量清理
                foreach (var itemId in expiredItems)
                {
                    CleanupSingleItem(itemId);
                }

                Form1.WriteLine(6, FastString.Concat("强制清理完成: ", expiredItems.Count, "个过期物品"));
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("强制清理失败: ", ex.Message));
            }
        }

        // 获取清理统计信息
        public string GetCleanupStats()
        {
            var totalItems = World.ItmeTeM?.Count ?? 0;
            var indexedMaps = _itemsByMap.Count;
            var pendingExpiration = _expirationQueue.Values.Sum(list => list.Count);

            return FastString.Format(
                "物品清理统计: 总物品:{0} 已索引:{1} 地图数:{2} 待过期:{3} 已清理:{4} 上次清理:{5}",
                totalItems,
                _totalIndexed,
                indexedMaps,
                pendingExpiration,
                _totalCleaned,
                _lastCleanup.ToString("HH:mm:ss")
            );
        }

        // 获取地图物品统计
        public string GetMapItemStats()
        {
            try
            {
                var mapStats = _itemsByMap
                    .Where(kvp => kvp.Value.Count > 0)
                    .OrderByDescending(kvp => kvp.Value.Count)
                    .Take(10)
                    .Select(kvp => FastString.Concat("地图", kvp.Key, ":", kvp.Value.Count))
                    .ToArray();

                if (mapStats.Length == 0)
                {
                    return "暂无地图物品数据";
                }

                return FastString.Concat("地图物品分布(前10): ", string.Join(" | ", mapStats));
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("获取地图物品统计失败: ", ex.Message));
                return "统计获取失败";
            }
        }

        // 检查是否需要清理
        public bool ShouldCleanup()
        {
            var timeSinceLastCleanup = DateTime.Now.Subtract(_lastCleanup);
            var totalItems = World.ItmeTeM?.Count ?? 0;

            // 如果距离上次清理超过间隔时间，或物品数量过多
            return timeSinceLastCleanup > _cleanupInterval || totalItems > 5000;
        }

        // 清理空的地图索引
        public void CleanupEmptyMapIndexes()
        {
            try
            {
                var emptyMaps = _itemsByMap
                    .Where(kvp => kvp.Value.IsEmpty)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var mapId in emptyMaps)
                {
                    _itemsByMap.TryRemove(mapId, out _);
                }

                if (emptyMaps.Count > 0)
                {
                    Form1.WriteLine(6, FastString.Concat("清理空地图索引: ", emptyMaps.Count, "个"));
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理空地图索引失败: ", ex.Message));
            }
        }

        // 优化地面物品查找
        public 地面物品类 FindNearestItem(int mapId, float x, float y, float maxDistance = 100f)
        {
            try
            {
                if (!_itemsByMap.TryGetValue(mapId, out var mapItems))
                    return null;

                地面物品类 nearestItem = null;
                float nearestDistance = float.MaxValue;

                foreach (var item in mapItems.Values)
                {
                    float distance = CalculateDistance(x, y, item.Rxjh_X, item.Rxjh_Y);
                    if (distance <= maxDistance && distance < nearestDistance)
                    {
                        nearestDistance = distance;
                        nearestItem = item;
                    }
                }

                return nearestItem;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("查找最近物品失败: ", ex.Message));
                return null;
            }
        }

        // 计算距离
        private float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            float dx = x1 - x2;
            float dy = y1 - y2;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }
    }
}

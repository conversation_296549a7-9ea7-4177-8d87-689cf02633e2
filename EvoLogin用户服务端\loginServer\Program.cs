using System;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

internal static class Program
{
	[STAThread]
	private static void Main()
	{
		Application.EnableVisualStyles();
		Application.SetCompatibleTextRenderingDefault(defaultValue: false);
		RegisterForm form = new RegisterForm();
		
		World.Key1 = Config.IniReadValue("LoginServer", "Key1").Trim();
		World.Key2 = Config.IniReadValue("LoginServer", "Key2").Trim();
		
		try
		{
			if (form.qdyzcod())
			{
				Application.Run(new Form1());
			}
			else
			{
				Application.Run(new RegisterForm());
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show("注册机Main  错误" + ex);
		}
	}
}

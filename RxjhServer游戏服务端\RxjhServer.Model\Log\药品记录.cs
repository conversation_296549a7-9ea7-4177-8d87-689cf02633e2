﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 药品记录 {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int ID { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)")]
		public string 分区 { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)")]
		public string FLD_ID { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)")]
		public string FLD_INAME { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)")]
		public string FLD_NAME { get; set; }

		[JsonProperty]
		public int? FLD_NUM { get; set; }

		[JsonProperty]
		public int? FLD_PID { get; set; }

		[JsonProperty, Column(DbType = "smalldatetime", InsertValueSql = "getdate()")]
		public DateTime? FLD_TIME { get; set; }

	}

}

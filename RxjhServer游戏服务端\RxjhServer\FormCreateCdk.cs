using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class FormCreateCdk : Form
{
	private IContainer components = null;

	private Button btnCopyNotUsed;

	private Button btnRefresh;

	private Button btnAddCdkey;

	private ListView listView2;

	private ColumnHeader columnHeader7;

	private ColumnHeader columnHeader8;

	private ColumnHeader columnHeader9;

	private ColumnHeader columnHeader15;

	private ColumnHeader columnHeader16;

	private ColumnHeader columnHeader10;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ListView listView1;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader6;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private Button btnQuery;

	private ComboBox cbbUsed;

	private ColumnHeader columnHeader17;

	private ColumnHeader columnHeader18;

	private ColumnHeader columnHeader19;

	private ColumnHeader columnHeader20;

	private ColumnHeader columnHeader21;

	private Label label3;

	private TextBox txtFQ;

	private Label label2;

	private TextBox textBox1;

	//private TextBox textBox2;

	private Label label4;

	private TextBox txtRegSum;

	private Label label1;

	private Label label5;

	private TextBox textBox3;

	private Label label6;

	private TextBox textBox4;

	private ColumnHeader columnHeader14;

	private ComboBox cbbCdkeyName;
    private Button btnExport;
    private ComboBox cbbDescription; // 替换 textBox2

    public FormCreateCdk()
	{
		InitializeComponent();
	}


    private void FormCreateCdk_Load(object sender, EventArgs e) //EVIAS
    {
        try
        {
            listView1.Items.Clear();

            string typeFilter = "";
            if (cbbCdkeyName.SelectedIndex > 0) // 非"全部"选项
            {
                typeFilter = $" AND 说明='{cbbCdkeyName.Text}'";
            }

            string usageFilter = cbbUsed.SelectedIndex switch
            {
                1 => " AND 是否使用=0",  // 未使用
                2 => " AND 是否使用=1",  // 已使用
                _ => ""                // 全部
            };

            string text = $"SELECT * FROM 兑换记录 WHERE 1=1 {usageFilter} {typeFilter} AND 分区='{World.分区编号}'";
            text += " ORDER BY 是否使用, 时间 DESC";

            DataTable dBToDataTable = DBA.GetDBToDataTable(text, "GameLog");
            if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
            {
                for (int i = 0; i < dBToDataTable.Rows.Count; i++)
                {
                    DataRow dataRow = dBToDataTable.Rows[i];
                    string[] array = new string[10]
                    {
                    dataRow["ID"]?.ToString() ?? "",
                    dataRow["FLD_CDK"]?.ToString() ?? "",
                    null, null, null, null, null, null, null, null
                    };
                    int key = (int)dataRow["ID"];
                    兑换码Class 兑换码Class2 = World.兑换码[key];
                    array[2] = 兑换码Class2.玩家名字;
                    array[3] = 称号名字(兑换码Class2.称号).ToString();
                    array[4] = 兑换码Class2.物品编号.ToString();
                    array[5] = 兑换码Class2.元宝.ToString();
                    array[6] = 兑换码Class2.IP地址.ToString();
                    array[7] = (((int)dataRow["是否使用"] == 1) ? "已使用" : "未使用");
                    array[8] = dataRow["时间"]?.ToString() ?? "";
                    array[9] = dataRow["说明"]?.ToString();
                    listView1.Items.Add(new ListViewItem(array));
                }
                dBToDataTable.Dispose();
            }
            else
            {
                listView1.Items.Clear();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("加载失败: " + ex.Message);
        }
    }


    private void btnRefresh_Click(object sender, EventArgs e)
	{
		FormCreateCdk_Load(sender, e);
	}

    private void btnAddCdkey_Click(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(cbbDescription.Text) ||
                !(cbbDescription.Text == "动态称号" ||
                  cbbDescription.Text == "新人礼包" ||
                  cbbDescription.Text == "新人路费" ||
                  cbbDescription.Text == "其它礼包" ||
                  cbbDescription.Text == "在线奖励"))
            {
                MessageBox.Show("请选择生成类型！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string 小写 = "qwertyuiopasdfghjklzxcvbnm";
            int regScueessQty = 0;
            int regFailQty = 0;
            string 分区 = textBox4.Text;
            if (textBox4.Text != World.分区编号)
            {
                分区 = World.分区编号;
            }

            Invoke((Action)delegate
            {
                int num = int.Parse(txtRegSum.Text);
                for (int i = 1; i <= num; i++)
                {
                    string text = Get10ReadomStr(out 小写);
                    string sqlCommand = "select * from 兑换记录 where FLD_CDK = '" + text + "'";
                    DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "GameLog");
                    if (dBToDataTable != null && dBToDataTable.Rows.Count == 0)
                    {
                        sqlCommand = "INSERT [兑换记录] (FLD_CDK,玩家名字,玩家帐号,称号,物品编号,元宝,IP地址,是否使用,时间,说明,分区) VALUES ('" + text + "','" + string.Empty + "','" + string.Empty + "'," + int.Parse(txtFQ.Text) + "," + int.Parse(textBox3.Text) + " ," + int.Parse(textBox1.Text) + ",'" + string.Empty + "',0 ,'" + DateTime.Now.ToString() + "','" + cbbDescription.Text + "','" + 分区 + "')";
                        if (DBA.ExeSqlCommand(sqlCommand, "GameLog") > 0)
                        {
                            regScueessQty++;
                        }
                        else
                        {
                            regFailQty++;
                        }
                    }
                }
            });
            World.Set兑换码();
            MessageBox.Show("写入成功" + regScueessQty + "个，失败" + regFailQty + "个", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show("生成CdK错误: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }


    private static string Get10ReadomStr(out string key)
	{
		string text = Guid.NewGuid().ToString().Replace("-", "")
			.ToLower();
		key = text.Substring(0, 10);
		return key;
	}


    private void btnCopyNotUsed_Click(object sender, EventArgs e)
    {
        try
        {
            string sqlCommand = "SELECT [ID],[FLD_CDK] FROM 兑换记录 WHERE 是否使用=0";

            if (cbbCdkeyName.SelectedIndex > 0)
            {
                sqlCommand += " AND 说明='" + cbbCdkeyName.Text + "'";
            }

            DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "GameLog");
            Dictionary<int, List<string>> dictionary = new Dictionary<int, List<string>>();
            if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
            {
                for (int i = 0; i < dBToDataTable.Rows.Count; i++)
                {
                    DataRow dataRow = dBToDataTable.Rows[i];
                    int key = (int)dataRow["ID"];
                    兑换码Class 兑换码Class2 = World.兑换码[key];
                    string text = dataRow["FLD_CDK"].ToString();
                    string item = "类型: " + 兑换码Class2.说明 + " 称号: " + 称号名字(兑换码Class2.称号) + " 卡号: " + text + " 分区: " + 分区名字(兑换码Class2.分区) + "  状态: " + 是否使用(兑换码Class2.是否使用);
                    if (dictionary.ContainsKey(key))
                    {
                        dictionary[key].Add(item);
                        continue;
                    }
                    dictionary.Add(key, new List<string> { item });
                }
            }
            FormCdkeyShow formCdkeyShow = new FormCdkeyShow(dictionary);
            formCdkeyShow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show("查看未使用CdKey错误", ex.Message);
        }
    }


    public static string 称号名字(int mapid)
	{
		if (1 == 0)
		{
		}
		string result = mapid switch
		{
			300 => "傲视群雄", 
			500 => "势不可挡", 
			800 => "举世无双", 
			1100 => "横行天下", 
			1500 => "壮志凌云", 
			2000 => "纵横天下", 
			3000 => "君临天下", 
			5000 => "剑指苍穹", 
			_ => string.Empty, 
		};
		if (1 == 0)
		{
		}
		return result;
	}

	public static string 是否使用(int mapid)
	{
		if (1 == 0)
		{
		}
		string result = mapid switch
		{
			0 => "未使用", 
			1 => "使用", 
			_ => string.Empty, 
		};
		if (1 == 0)
		{
		}
		return result;
	}

	private void btnQuery_Click(object sender, EventArgs e)
	{
		try
		{
			listView2.Items.Clear();
			string text = "SELECT * FROM 兑换记录 WHERE 1=1";
			if (cbbUsed.SelectedIndex != 0)
			{
				text += $"AND 是否使用={cbbUsed.SelectedIndex - 1}";
			}
			if (cbbCdkeyName.SelectedIndex != 0)
			{
				text = text + "AND 说明='" + cbbCdkeyName.Text + "'";
			}
			text += "order by 是否使用,时间 desc";
			DataTable dBToDataTable = DBA.GetDBToDataTable(text, "GameLog");
			if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					DataRow dataRow = dBToDataTable.Rows[i];
					string[] array = new string[11]
					{
						dataRow["ID"]?.ToString() ?? "",
						dataRow["FLD_CDK"]?.ToString() ?? "",
						null,
						null,
						null,
						null,
						null,
						null,
						null,
						null,
						null
					};
					int key = (int)dataRow["ID"];
					兑换码Class 兑换码Class2 = World.兑换码[key];
					array[2] = 兑换码Class2.玩家名字;
					array[3] = 兑换码Class2.称号.ToString();
					array[4] = 兑换码Class2.物品编号.ToString();
					array[5] = 兑换码Class2.元宝.ToString();
					array[6] = RxjhClass.GetUserIpadds(兑换码Class2.IP地址.ToString()).ToString();
					array[7] = (((int)dataRow["是否使用"] == 1) ? "已使用" : "未使用");
					array[8] = dataRow["时间"]?.ToString() ?? "";
					array[9] = dataRow["说明"]?.ToString();
					array[10] = 分区名字(dataRow["分区"]?.ToString()).ToString();
					listView2.Items.Add(new ListViewItem(array));
				}
				dBToDataTable.Dispose();
			}
			else
			{
				listView2.Items.Clear();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show("查询cdkey使用记录错误", ex.Message);
		}
	}

	public static string 分区名字(string 分区)
	{
		if (1 == 0)
		{
		}
		string result = 分区 switch
		{
			"d1" => "一区", 
			"d2" => "二区", 
			"d3" => "三区", 
			"d4" => "四区", 
			"d5" => "五区", 
			"d6" => "六区", 
			"d7" => "七区", 
			"d8" => "八区", 
			_ => string.Empty, 
		};
		if (1 == 0)
		{
		}
		return result;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}


    private void btnExport_Click(object sender, EventArgs e) //EVIAS 新增功能
    {
        try
        {
            string selectedType = cbbCdkeyName.SelectedIndex > 0 ? cbbCdkeyName.Text : null;
            string usageFilter = cbbUsed.SelectedIndex switch
            {
                1 => "是否使用=0",  // 未使用
                2 => "是否使用=1",  // 已使用
                _ => "1=1"         // 全部
            };

            string whereClause = $"{usageFilter}";
            if (!string.IsNullOrEmpty(selectedType))
                whereClause += $" AND 说明='{selectedType}'";
            if (!string.IsNullOrEmpty(textBox4.Text))
                whereClause += $" AND 分区='{textBox4.Text}'";

            string sql = $"SELECT FLD_CDK,说明,称号,物品编号,元宝,分区,是否使用 FROM 兑换记录 WHERE {whereClause}";
            DataTable dt = DBA.GetDBToDataTable(sql, "GameLog");

            if (dt == null || dt.Rows.Count == 0)
            {
                MessageBox.Show("没有找到符合条件的CDK！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "文本文件|*.txt|CSV文件|*.csv";
            saveFileDialog.Title = "导出CDK";
            saveFileDialog.FileName = $"CDK_{cbbUsed.Text}_{selectedType ?? "全部"}_{DateTime.Now:yyyyMMdd}";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                StringBuilder sb = new StringBuilder();

                if (Path.GetExtension(saveFileDialog.FileName).ToLower() == ".csv")
                {
                    // CSV格式
                    sb.AppendLine("CDK,类型,称号,物品编号,元宝,分区,状态");
                    foreach (DataRow row in dt.Rows)
                    {
                        sb.AppendLine($"\"{row["FLD_CDK"]}\",\"{row["说明"]}\",\"{称号名字(Convert.ToInt32(row["称号"]))}\","
                                    + $"\"{row["物品编号"]}\",\"{row["元宝"]}\",\"{分区名字(row["分区"].ToString())}\","
                                    + $"\"{是否使用(Convert.ToInt32(row["是否使用"]))}\"");
                    }
                }
                else
                {
                    // TXT表格格式
                    int[] columnWidths = { 15, 10, 10, 12, 8, 8, 8 };
                    string header = $"{"CDK".PadRight(columnWidths[0])}|{"类型".PadRight(columnWidths[1])}|{"称号".PadRight(columnWidths[2])}"
                                  + $"|{"物品编号".PadRight(columnWidths[3])}|{"元宝".PadRight(columnWidths[4])}"
                                  + $"|{"分区".PadRight(columnWidths[5])}|{"状态".PadRight(columnWidths[6])}";

                    string divider = new string('-', header.Length);

                    sb.AppendLine("CDK导出报告");
                    sb.AppendLine($"生成时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    sb.AppendLine($"筛选条件：{cbbUsed.Text} | {selectedType ?? "全部类型"} | 分区:{textBox4.Text}");
                    sb.AppendLine(divider);
                    sb.AppendLine(header);
                    sb.AppendLine(divider);

                    foreach (DataRow row in dt.Rows)
                    {
                        sb.AppendLine(
                            $"{row["FLD_CDK"].ToString().PadRight(columnWidths[0])}|"
                            + $"{row["说明"].ToString().PadRight(columnWidths[1])}|"
                            + $"{称号名字(Convert.ToInt32(row["称号"])).PadRight(columnWidths[2])}|"
                            + $"{row["物品编号"].ToString().PadRight(columnWidths[3])}|"
                            + $"{row["元宝"].ToString().PadRight(columnWidths[4])}|"
                            + $"{分区名字(row["分区"].ToString()).PadRight(columnWidths[5])}|"
                            + $"{是否使用(Convert.ToInt32(row["是否使用"])).PadRight(columnWidths[6])}"
                        );
                    }

                    sb.AppendLine(divider);
                    sb.AppendLine($"总计: {dt.Rows.Count} 条记录");
                }

                File.WriteAllText(saveFileDialog.FileName, sb.ToString(), Encoding.UTF8);
                MessageBox.Show($"成功导出 {dt.Rows.Count} 条CDK记录！\n文件路径: {saveFileDialog.FileName}",
                              "导出完成",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"导出失败：{ex.Message}\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }


    private void cbbDescription_SelectedIndexChanged(object sender, EventArgs e) //EVIAS
    {
        // 当选择变化时立即更新默认值
        if (cbbDescription.SelectedItem == null) return;

        switch (cbbDescription.Text)
        {
            case "动态称号":
                txtFQ.Text = "300";
                textBox1.Text = "0";
                textBox3.Text = "0";
                break;
            case "新人礼包":
                txtFQ.Text = "0";
                textBox1.Text = "0";
                textBox3.Text = "1008000530";
                break;
            case "新人路费":
                txtFQ.Text = "0";
                textBox1.Text = "2000";
                textBox3.Text = "0";
                break;
            case "其它礼包":
                txtFQ.Text = "0";
                textBox1.Text = "2000";
                textBox3.Text = "0";
                break;
            case "在线奖励":
                txtFQ.Text = "0";
                textBox1.Text = "0";
                textBox3.Text = "1008001584";
                break;
        }
    }

    private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormCreateCdk));
            this.btnCopyNotUsed = new System.Windows.Forms.Button();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.btnAddCdkey = new System.Windows.Forms.Button();
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader15 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader16 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader21 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader17 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader18 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader19 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader20 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.btnQuery = new System.Windows.Forms.Button();
            this.cbbUsed = new System.Windows.Forms.ComboBox();
            this.cbbCdkeyName = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.txtFQ = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.txtRegSum = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.cbbDescription = new System.Windows.Forms.ComboBox();
            this.btnExport = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // btnCopyNotUsed
            // 
            this.btnCopyNotUsed.Location = new System.Drawing.Point(1026, 397);
            this.btnCopyNotUsed.Margin = new System.Windows.Forms.Padding(4);
            this.btnCopyNotUsed.Name = "btnCopyNotUsed";
            this.btnCopyNotUsed.Size = new System.Drawing.Size(168, 30);
            this.btnCopyNotUsed.TabIndex = 10;
            this.btnCopyNotUsed.Text = "复制未使用cdkey";
            this.btnCopyNotUsed.UseVisualStyleBackColor = true;
            this.btnCopyNotUsed.Click += new System.EventHandler(this.btnCopyNotUsed_Click);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Location = new System.Drawing.Point(522, 400);
            this.btnRefresh.Margin = new System.Windows.Forms.Padding(4);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(168, 30);
            this.btnRefresh.TabIndex = 8;
            this.btnRefresh.Text = "刷新";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // btnAddCdkey
            // 
            this.btnAddCdkey.ForeColor = System.Drawing.Color.Blue;
            this.btnAddCdkey.Location = new System.Drawing.Point(1323, 10);
            this.btnAddCdkey.Margin = new System.Windows.Forms.Padding(4);
            this.btnAddCdkey.Name = "btnAddCdkey";
            this.btnAddCdkey.Size = new System.Drawing.Size(134, 28);
            this.btnAddCdkey.TabIndex = 9;
            this.btnAddCdkey.Text = "批量生成";
            this.btnAddCdkey.UseVisualStyleBackColor = true;
            this.btnAddCdkey.Click += new System.EventHandler(this.btnAddCdkey_Click);
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader7,
            this.columnHeader8,
            this.columnHeader9,
            this.columnHeader15,
            this.columnHeader16,
            this.columnHeader10,
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader21,
            this.columnHeader14});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(18, 442);
            this.listView2.Margin = new System.Windows.Forms.Padding(4);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(1448, 343);
            this.listView2.TabIndex = 7;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "编号";
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "FLD_CDK";
            this.columnHeader8.Width = 100;
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "玩家名字";
            this.columnHeader9.Width = 100;
            // 
            // columnHeader15
            // 
            this.columnHeader15.Text = "称号";
            // 
            // columnHeader16
            // 
            this.columnHeader16.Text = "物品编号";
            this.columnHeader16.Width = 100;
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "元宝";
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "IP地址";
            this.columnHeader11.Width = 120;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "是否使用";
            this.columnHeader12.Width = 80;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "时间";
            this.columnHeader13.Width = 120;
            // 
            // columnHeader21
            // 
            this.columnHeader21.Text = "说明";
            this.columnHeader21.Width = 80;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "分区";
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader17,
            this.columnHeader18,
            this.columnHeader19,
            this.columnHeader20});
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(18, 46);
            this.listView1.Margin = new System.Windows.Forms.Padding(4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(1448, 343);
            this.listView1.TabIndex = 6;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "编号";
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "FLD_CDK";
            this.columnHeader5.Width = 100;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "玩家名字";
            this.columnHeader6.Width = 100;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "称号";
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "物品编号";
            this.columnHeader3.Width = 100;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "元宝";
            // 
            // columnHeader17
            // 
            this.columnHeader17.Text = "IP地址";
            this.columnHeader17.Width = 120;
            // 
            // columnHeader18
            // 
            this.columnHeader18.Text = "是否使用";
            this.columnHeader18.Width = 80;
            // 
            // columnHeader19
            // 
            this.columnHeader19.Text = "时间";
            this.columnHeader19.Width = 120;
            // 
            // columnHeader20
            // 
            this.columnHeader20.Text = "说明";
            this.columnHeader20.Width = 80;
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(774, 397);
            this.btnQuery.Margin = new System.Windows.Forms.Padding(4);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(168, 30);
            this.btnQuery.TabIndex = 13;
            this.btnQuery.Text = "查询";
            this.btnQuery.UseVisualStyleBackColor = true;
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // cbbUsed
            // 
            this.cbbUsed.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbbUsed.FormattingEnabled = true;
            this.cbbUsed.Items.AddRange(new object[] {
            "全部",
            "未使用",
            "已使用"});
            this.cbbUsed.Location = new System.Drawing.Point(18, 402);
            this.cbbUsed.Margin = new System.Windows.Forms.Padding(4);
            this.cbbUsed.Name = "cbbUsed";
            this.cbbUsed.Size = new System.Drawing.Size(168, 26);
            this.cbbUsed.TabIndex = 12;
            // 
            // cbbCdkeyName
            // 
            this.cbbCdkeyName.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbbCdkeyName.FormattingEnabled = true;
            this.cbbCdkeyName.Items.AddRange(new object[] {
            "全部",
            "动态称号",
            "新人路费",
            "新人礼包",
            "其它礼包",
            "在线奖励"});
            this.cbbCdkeyName.Location = new System.Drawing.Point(270, 402);
            this.cbbCdkeyName.Margin = new System.Windows.Forms.Padding(4);
            this.cbbCdkeyName.Name = "cbbCdkeyName";
            this.cbbCdkeyName.Size = new System.Drawing.Size(168, 26);
            this.cbbCdkeyName.TabIndex = 11;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.ForeColor = System.Drawing.Color.Blue;
            this.label3.Location = new System.Drawing.Point(22, 15);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(44, 18);
            this.label3.TabIndex = 14;
            this.label3.Text = "称号";
            // 
            // txtFQ
            // 
            this.txtFQ.Location = new System.Drawing.Point(74, 10);
            this.txtFQ.Margin = new System.Windows.Forms.Padding(4);
            this.txtFQ.Name = "txtFQ";
            this.txtFQ.Size = new System.Drawing.Size(125, 28);
            this.txtFQ.TabIndex = 15;
            this.txtFQ.Text = "0";
            this.txtFQ.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.Blue;
            this.label2.Location = new System.Drawing.Point(237, 15);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(44, 18);
            this.label2.TabIndex = 17;
            this.label2.Text = "元宝";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(293, 10);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(125, 28);
            this.textBox1.TabIndex = 18;
            this.textBox1.Text = "2000";
            this.textBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.ForeColor = System.Drawing.Color.Blue;
            this.label4.Location = new System.Drawing.Point(667, 15);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(44, 18);
            this.label4.TabIndex = 20;
            this.label4.Text = "说明";
            // 
            // txtRegSum
            // 
            this.txtRegSum.Location = new System.Drawing.Point(510, 10);
            this.txtRegSum.Margin = new System.Windows.Forms.Padding(4);
            this.txtRegSum.Name = "txtRegSum";
            this.txtRegSum.Size = new System.Drawing.Size(125, 28);
            this.txtRegSum.TabIndex = 21;
            this.txtRegSum.Text = "10";
            this.txtRegSum.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.Blue;
            this.label1.Location = new System.Drawing.Point(452, 15);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(44, 18);
            this.label1.TabIndex = 22;
            this.label1.Text = "数量";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ForeColor = System.Drawing.Color.Blue;
            this.label5.Location = new System.Drawing.Point(887, 15);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(80, 18);
            this.label5.TabIndex = 23;
            this.label5.Text = "物品编号";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(980, 10);
            this.textBox3.Margin = new System.Windows.Forms.Padding(4);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(130, 28);
            this.textBox3.TabIndex = 24;
            this.textBox3.Text = "0";
            this.textBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ForeColor = System.Drawing.Color.Blue;
            this.label6.Location = new System.Drawing.Point(1143, 15);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(44, 18);
            this.label6.TabIndex = 25;
            this.label6.Text = "分区";
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(1200, 10);
            this.textBox4.Margin = new System.Windows.Forms.Padding(4);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(90, 28);
            this.textBox4.TabIndex = 26;
            this.textBox4.Text = "d1";
            this.textBox4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbbDescription
            // 
            this.cbbDescription.Items.AddRange(new object[] {
            "动态称号",
            "新人路费",
            "新人礼包",
            "其它礼包",
            "在线奖励"});
            this.cbbDescription.Location = new System.Drawing.Point(725, 11);
            this.cbbDescription.Margin = new System.Windows.Forms.Padding(4);
            this.cbbDescription.Name = "cbbDescription";
            this.cbbDescription.Size = new System.Drawing.Size(130, 26);
            this.cbbDescription.TabIndex = 19;
            this.cbbDescription.Text = "其它礼包";
            this.cbbDescription.SelectedIndexChanged += new System.EventHandler(this.cbbDescription_SelectedIndexChanged);
            // 
            // btnExport
            // 
            this.btnExport.Location = new System.Drawing.Point(1278, 399);
            this.btnExport.Margin = new System.Windows.Forms.Padding(4);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(168, 30);
            this.btnExport.TabIndex = 28;
            this.btnExport.Text = "导出CDK记录";
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // FormCreateCdk
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1472, 792);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.textBox4);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.txtRegSum);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.txtFQ);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.btnQuery);
            this.Controls.Add(this.cbbUsed);
            this.Controls.Add(this.cbbCdkeyName);
            this.Controls.Add(this.btnCopyNotUsed);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.btnAddCdkey);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.listView1);
            this.Controls.Add(this.cbbDescription);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "FormCreateCdk";
            this.Text = "FormCreateCdk";
            this.Load += new System.EventHandler(this.FormCreateCdk_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

    }
}

using System;
using System.IO;
using System.Text;

namespace RxjhVer23;

internal class PacketDataWriteStream : IDisposable
{
	private byte[] _baseTypeDataBuffer;

	private MemoryStream _memoryStream;

	private bool _disposedValue;

	public PacketDataWriteStream()
	{
		_baseTypeDataBuffer = new byte[8];
		_memoryStream = new MemoryStream();
	}

	public void Write(byte[] value)
	{
		_memoryStream.Write(value, 0, value.Length);
	}

	public void Write(float value)
	{
		_baseTypeDataBuffer = BitConverter.GetBytes(value);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 4);
	}

	public void Write(int value)
	{
		_memoryStream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write1(int value)
	{
		_memoryStream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write2(int value)
	{
		_baseTypeDataBuffer[0] = (byte)value;
		_baseTypeDataBuffer[1] = (byte)(value >> 8);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 2);
	}

	public void Write4(int value)
	{
		_baseTypeDataBuffer[0] = (byte)value;
		_baseTypeDataBuffer[1] = (byte)(value >> 8);
		_baseTypeDataBuffer[2] = (byte)(value >> 16);
		_baseTypeDataBuffer[3] = (byte)(value >> 24);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 4);
	}

	public void Write4(uint value)
	{
		_baseTypeDataBuffer[0] = (byte)value;
		_baseTypeDataBuffer[1] = (byte)(value >> 8);
		_baseTypeDataBuffer[2] = (byte)(value >> 16);
		_baseTypeDataBuffer[3] = (byte)(value >> 24);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 4);
	}

	public void Write8(long value)
	{
		_baseTypeDataBuffer[0] = (byte)value;
		_baseTypeDataBuffer[1] = (byte)(value >> 8);
		_baseTypeDataBuffer[2] = (byte)(value >> 16);
		_baseTypeDataBuffer[3] = (byte)(value >> 24);
		_baseTypeDataBuffer[4] = (byte)(value >> 32);
		_baseTypeDataBuffer[5] = (byte)(value >> 40);
		_baseTypeDataBuffer[6] = (byte)(value >> 48);
		_baseTypeDataBuffer[7] = (byte)(value >> 56);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 8);
	}

	public void Write(long value)
	{
		_baseTypeDataBuffer[0] = (byte)value;
		_baseTypeDataBuffer[1] = (byte)(value >> 8);
		_baseTypeDataBuffer[2] = (byte)(value >> 16);
		_baseTypeDataBuffer[3] = (byte)(value >> 24);
		_baseTypeDataBuffer[4] = (byte)(value >> 32);
		_baseTypeDataBuffer[5] = (byte)(value >> 40);
		_baseTypeDataBuffer[6] = (byte)(value >> 48);
		_baseTypeDataBuffer[7] = (byte)(value >> 56);
		_memoryStream.Write(_baseTypeDataBuffer, 0, 8);
	}

	public void Write(byte[] buffer, int offset, int size)
	{
		_memoryStream.Write(buffer, offset, size);
	}

	public void WriteAsciiFixed(string value)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		byte[] bytes = Encoding.Default.GetBytes(value);
		Write2((byte)bytes.Length);
		_memoryStream.Write(bytes, 0, bytes.Length);
	}

	public void WriteString(string value, int bufSize)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		byte[] array = new byte[bufSize];
		byte[] bytes = Encoding.Default.GetBytes(value);
		int count = bytes.Length;
		if (bytes.Length > bufSize)
		{
			count = bufSize;
		}
		Buffer.BlockCopy(bytes, 0, array, 0, count);
		_memoryStream.Write(array, 0, array.Length);
	}

	public void WriteName(string value)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		byte[] bytes = Encoding.Default.GetBytes(value);
		byte[] array = new byte[15];
		if (bytes.Length <= 15)
		{
			Buffer.BlockCopy(bytes, 0, array, 0, bytes.Length);
		}
		else
		{
			Buffer.BlockCopy(bytes, 0, array, 0, 15);
		}
		_memoryStream.Write(array, 0, array.Length);
	}

	public byte[] GetPacketData(int opcode, int guid)
	{
		using MemoryStream memoryStream = new MemoryStream();
		using (BinaryWriter binaryWriter = new BinaryWriter(memoryStream))
		{
			binaryWriter.Write((uint)guid);
			binaryWriter.Write((ushort)opcode);
			binaryWriter.Write((ushort)_memoryStream.Length);
			binaryWriter.Write(_memoryStream.ToArray());
		}
		return memoryStream.ToArray();
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				_memoryStream?.Dispose();
				_memoryStream = null;
			}
			_disposedValue = true;
		}
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}
}

namespace RxjhServer;

public class 累计充值礼包
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private string string_0;

	private string string_1;

	public int 累计最小
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int 累计最大
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int 礼包编号
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int 档次
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int 防御
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int 攻击
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int 血量
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int 战斗力
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int 颜色
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public string 自定开头内容
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string 上线提示
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public static string 查称号名字(int 数字)
	{
		foreach (累计充值礼包 value in World.累计充值称号.Values)
		{
			if (value.累计最小 == 数字)
			{
				return value.自定开头内容;
			}
		}
		return string.Empty;
	}
}

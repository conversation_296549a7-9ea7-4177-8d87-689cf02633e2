using System;
using System.Data;
using System.Data.SqlClient;

namespace RxjhServer.DbClss;

public class DBA
{

    public static IFreeSql LogSql = new FreeSql.FreeSqlBuilder()
	.UseConnectionString(FreeSql.DataType.SqlServer, DBA.getstrConnection("GameLog"))
	.Build(); //请务必定义成 Singleton 单例模式
    public static void serlog(string txt)
	{
		string sqlJl = World.SqlJl;
		if (sqlJl.Length == 0)
		{
			return;
		}
		string text = sqlJl;
		char[] separator = new char[1] { '|' };
		string[] array = text.Split(separator);
		string[] array2 = array;
		foreach (string text2 in array2)
		{
			if (txt.ToLower().IndexOf(text2.ToLower()) != -1)
			{
				Form1.WriteLine(99, txt);
			}
		}
	}

	public static void serlog(string txt, SqlParameter[] prams)
	{
		string sqlJl = World.SqlJl;
		if (sqlJl.Length == 0)
		{
			return;
		}
		string[] array = sqlJl.Split('|');
		for (int i = 0; i < array.Length; i++)
		{
			if (txt.ToLower().IndexOf(array[i].ToLower()) != -1)
			{
				Form1.WriteLine(99, txt);
			}
		}
		for (int j = 0; j < array.Length; j++)
		{
			foreach (SqlParameter sqlParameter in prams)
			{
				if (sqlParameter.SqlValue.ToString().ToLower().IndexOf(array[j].ToLower()) != -1)
				{
					Form1.WriteLine(99, txt + " " + sqlParameter.SqlValue.ToString());
				}
			}
		}
	}

	public static void Setlog(string txt, SqlParameter[] prams, Exception ex)
	{
		Form1.WriteLine(100, "-----------DBA数据层_错误-----------");
		Form1.WriteLine(100, txt);
		if (prams != null)
		{
			foreach (SqlParameter sqlParameter in prams)
			{
				Form1.WriteLine(100, sqlParameter.SqlValue.ToString());
			}
		}
		Form1.WriteLine(100, ex.Message);
	}

	public static string getstrConnection(string db)
	{
		try
		{
			if (db == null)
			{
				db = "GameServer";
			}
            World.sql = (World.Db.TryGetValue(db, out var value) ? value.SqlConnect : null);

			return World.sql;
        }
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "获取数据库连接字符串", db);
			return null;
		}
	}

	public static int ExeSqlCommand(string sqlCommand, SqlParameter[] prams)
	{
		serlog(sqlCommand, prams);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlCommand sqlCommand2 = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", sqlCommand);
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行SQL命令", sqlCommand);
			Setlog(sqlCommand, prams, ex);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string sqlCommand, SqlParameter[] prams, string server)
	{
		serlog(sqlCommand, prams);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlCommand sqlCommand2 = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", $"服务器: {server}, SQL: {sqlCommand}");
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行SQL命令", $"服务器: {server}, SQL: {sqlCommand}");
			Setlog(sqlCommand, prams, ex);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string sqlCommand)
	{
		serlog(sqlCommand);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", sqlCommand);
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行SQL命令", sqlCommand);
			Setlog(sqlCommand, null, ex);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string sqlCommand, string server)
	{
		serlog(sqlCommand);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		int result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", $"服务器: {server}, SQL: {sqlCommand}");
			return -1;
		}
		try
		{
			result = sqlCommand2.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行SQL命令", $"服务器: {server}, SQL: {sqlCommand}");
			Setlog(sqlCommand, null, ex);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static DataTable GetDBToDataTable(string sqlCommand, SqlParameter[] prams)
	{
		serlog(sqlCommand, prams);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				RxjhClass.HandleDatabaseException(ex, "打开数据库连接", sqlCommand);
				Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex2)
			{
				RxjhClass.HandleDatabaseException(ex2, "填充数据表", sqlCommand);
				Setlog(sqlCommand, prams, ex2);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string sqlCommand, SqlParameter[] prams, string server)
	{
		serlog(sqlCommand, prams);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex2)
			{
				Setlog(sqlCommand, prams, ex2);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string sqlCommand)
	{
		serlog(sqlCommand);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = new SqlCommand(sqlCommand, sqlConnection));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex2)
			{
				Setlog(sqlCommand, null, ex2);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string sqlCommand, string server)
	{
		serlog(sqlCommand);
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(server));
		using SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
		SqlCommand sqlCommand3 = (sqlDataAdapter.SelectCommand = new SqlCommand(sqlCommand, sqlConnection));
		SqlCommand sqlCommand4 = sqlCommand3;
		using (sqlCommand4)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				RxjhClass.HandleDatabaseException(ex, "打开数据库连接", $"服务器: {server}, SQL: {sqlCommand}");
				return null;
			}
			DataTable dataTable = new DataTable();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception ex)
			{
				RxjhClass.HandleDatabaseException(ex, "填充数据表", $"服务器: {server}, SQL: {sqlCommand}");
				Setlog(sqlCommand, null, ex);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static object GetDBValue_3(string sqlCommand)
	{
		serlog(sqlCommand);
		object result = null;
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", sqlCommand);
			return null;
		}
		try
		{
			result = sqlCommand2.ExecuteScalar();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行标量查询", sqlCommand);
			Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static object GetDBValue_3(string sqlCommand, SqlParameter[] prams)
	{
		serlog(sqlCommand, prams);
		object result = null;
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		using SqlCommand sqlCommand2 = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams);
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", sqlCommand);
			return null;
		}
		try
		{
			result = sqlCommand2.ExecuteScalar();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行标量查询", sqlCommand);
			Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static object GetDBValue_3(string sqlCommand, string db)
	{
		serlog(sqlCommand);
		object result = null;
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(db));
		using SqlCommand sqlCommand2 = new SqlCommand(sqlCommand, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", $"数据库: {db}, SQL: {sqlCommand}");
			return null;
		}
		try
		{
			result = sqlCommand2.ExecuteScalar();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行标量查询", $"数据库: {db}, SQL: {sqlCommand}");
			Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static object GetDBValue_3(string sqlCommand, SqlParameter[] prams, string db)
	{
		serlog(sqlCommand, prams);
		object result = null;
		using SqlConnection sqlConnection = new SqlConnection(getstrConnection(db));
		using SqlCommand sqlCommand2 = SqlDBA.CreateCommandSql(sqlConnection, sqlCommand, prams);
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "打开数据库连接", $"数据库: {db}, SQL: {sqlCommand}");
			return null;
		}
		try
		{
			result = sqlCommand2.ExecuteScalar();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleDatabaseException(ex, "执行标量查询", $"数据库: {db}, SQL: {sqlCommand}");
			Form1.WriteLine(100, "DBA数据层_错误" + ex.Message + " " + sqlCommand);
		}
		sqlCommand2.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

}

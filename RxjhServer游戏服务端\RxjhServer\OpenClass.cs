using System.Collections;

namespace RxjhServer;

public class OpenClass
{
	private int _FLD_PID;

	private string _FLD_NAME;

	private int _FLD_PIDX;

	private string _FLD_NAMEX;

	private int _FLD_NUMBER;

	private int _FLD_PP;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _FLD_MAGIC5;

	private int _FLD_觉醒;

	private int _FLD_进化;

	private int _FLD_中级附魂;

	private int _FLD_BD;

	private int _FLD_DAYS;

	public int _是否开启公告;

	private int _FLD_SUNX;

	private int _FLD_SUND;

	private int _装备等级;

	private int _是否随机属性;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public int FLD_PIDX
	{
		get
		{
			return _FLD_PIDX;
		}
		set
		{
			_FLD_PIDX = value;
		}
	}

	public string FLD_NAMEX
	{
		get
		{
			return _FLD_NAMEX;
		}
		set
		{
			_FLD_NAMEX = value;
		}
	}

	public int FLD_NUMBER
	{
		get
		{
			return _FLD_NUMBER;
		}
		set
		{
			_FLD_NUMBER = value;
		}
	}

	public int FLD_PP
	{
		get
		{
			return _FLD_PP;
		}
		set
		{
			_FLD_PP = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return _FLD_MAGIC5;
		}
		set
		{
			_FLD_MAGIC5 = value;
		}
	}

	public int FLD_觉醒
	{
		get
		{
			return _FLD_觉醒;
		}
		set
		{
			_FLD_觉醒 = value;
		}
	}

	public int FLD_进化
	{
		get
		{
			return _FLD_进化;
		}
		set
		{
			_FLD_进化 = value;
		}
	}

	public int FLD_中级附魂
	{
		get
		{
			return _FLD_中级附魂;
		}
		set
		{
			_FLD_中级附魂 = value;
		}
	}

	public int FLD_BD
	{
		get
		{
			return _FLD_BD;
		}
		set
		{
			_FLD_BD = value;
		}
	}

	public int FLD_DAYS
	{
		get
		{
			return _FLD_DAYS;
		}
		set
		{
			_FLD_DAYS = value;
		}
	}

	public int 是否开启公告
	{
		get
		{
			return _是否开启公告;
		}
		set
		{
			_是否开启公告 = value;
		}
	}

	public int FLD_SUNX
	{
		get
		{
			return _FLD_SUNX;
		}
		set
		{
			_FLD_SUNX = value;
		}
	}

	public int FLD_SUND
	{
		get
		{
			return _FLD_SUND;
		}
		set
		{
			_FLD_SUND = value;
		}
	}

	public int 装备等级
	{
		get
		{
			return _装备等级;
		}
		set
		{
			_装备等级 = value;
		}
	}

	public int 是否随机属性
	{
		get
		{
			return _是否随机属性;
		}
		set
		{
			_是否随机属性 = value;
		}
	}

	public static OpenClass GetOpen(int Pid, int job, int ZX, int SEX, int 等级)
	{
		ArrayList arrayList = new ArrayList();
		if (Pid == 1000000071 && World.是否开启数据库宝箱 == 1)
		{
			foreach (ItmeClass value in World.Itme.Values)
			{
				if (value.FLD_RESIDE2 == 1792 && (value.FLD_RESIDE1 == job || value.FLD_RESIDE1 == 0) && (value.FLD_LEVEL == 10 || value.FLD_LEVEL == 60 || value.FLD_LEVEL == 80))
				{
					arrayList.Add(new OpenClass
					{
						FLD_PID = 1000000071,
						FLD_PIDX = value.FLD_PID,
						FLD_NAME = "上古宝箱",
						FLD_NAMEX = value.ItmeNAME
					});
				}
			}
		}
		if (Pid == 1000000006 && World.是否开启数据库宝箱 == 1)
		{
			foreach (ItmeClass value2 in World.Itme.Values)
			{
				if (value2.FLD_RESIDE2 == 19 && (value2.FLD_RESIDE1 == job || value2.FLD_RESIDE1 == 0) && value2.FLD_LEVEL <= 等级 && (value2.FLD_ZX == 0 || value2.FLD_ZX == ZX) && value2.FLD_LEVEL != 100 && value2.FLD_LEVEL != 104 && value2.FLD_LEVEL != 108)
				{
					arrayList.Add(new OpenClass
					{
						FLD_PID = 1000000006,
						FLD_PIDX = value2.FLD_PID,
						FLD_NAME = "乾坤箱",
						FLD_NAMEX = value2.ItmeNAME
					});
				}
			}
		}
		if (Pid == 1000000251 && World.是否开启数据库宝箱 == 1)
		{
			foreach (ItmeClass value3 in World.Itme.Values)
			{
				if (value3.FLD_RESIDE2 == 12 && value3.FLD_SEX == SEX && value3.FLD_LEVEL == 1 && !value3.ItmeDES.Contains("激活斗战披风"))
				{
					arrayList.Add(new OpenClass
					{
						FLD_PID = 1000000251,
						FLD_PIDX = value3.FLD_PID,
						FLD_NAME = "冰灵宝盒",
						FLD_NAMEX = value3.ItmeNAME
					});
				}
			}
		}
		if (Pid == 1008000491 && World.是否开启数据库宝箱 == 1)
		{
			foreach (ItmeClass value4 in World.Itme.Values)
			{
				if (value4.FLD_RESIDE2 == 12 && value4.FLD_SEX == SEX && value4.FLD_LEVEL == 1 && value4.ItmeDES.Contains("激活斗战披风"))
				{
					arrayList.Add(new OpenClass
					{
						FLD_PID = 1008000491,
						FLD_PIDX = value4.FLD_PID,
						FLD_NAME = "披风宝箱(斗战)",
						FLD_NAMEX = value4.ItmeNAME
					});
				}
			}
		}
		if (Pid == 1000000595 && World.是否开启数据库宝箱 == 1)
		{
			foreach (ItmeClass value5 in World.Itme.Values)
			{
				if (value5.FLD_RESIDE2 == 14 && value5.FLD_ZX == ZX && value5.FLD_SEX == SEX && value5.FLD_LEVEL == 35)
				{
					arrayList.Add(new OpenClass
					{
						FLD_PID = 1000000595,
						FLD_PIDX = value5.FLD_PID,
						FLD_NAME = "门派宝箱",
						FLD_NAMEX = value5.ItmeNAME
					});
				}
			}
		}
		else
		{
			int num = RNG.Next(1, 500);
			foreach (OpenClass item in World.Open)
			{
				if (item.FLD_PID == Pid && item.FLD_PP >= num && 等级 >= item.装备等级)
				{
					arrayList.Add(item);
				}
			}
			if (arrayList.Count == 0)
			{
				return null;
			}
		}
		return (OpenClass)arrayList[RNG.Next(0, arrayList.Count - 1)];
	}

	public static OpenClass 钥匙GetOpen(int Pid)
	{
		ArrayList arrayList = new ArrayList();
		int num = RNG.Next(1, 500);
		foreach (OpenClass item in World.Open)
		{
			if (item.FLD_PID == Pid && item.FLD_PP >= num)
			{
				arrayList.Add(item);
			}
		}
		if (arrayList.Count == 0)
		{
			return null;
		}
		return (OpenClass)arrayList[RNG.Next(0, arrayList.Count - 1)];
	}

	public static OpenClass 锤子GetOpen(int Pid)
	{
		ArrayList arrayList = new ArrayList();
		int num = RNG.Next(150, 500);
		foreach (OpenClass item in World.Open)
		{
			if (item.FLD_PID == Pid && item.FLD_PP >= num)
			{
				arrayList.Add(item);
			}
		}
		if (arrayList.Count == 0)
		{
			return null;
		}
		return (OpenClass)arrayList[RNG.Next(0, arrayList.Count - 1)];
	}

	public static OpenClass 灵宠蛋GetOpen(int Pid)
	{
		ArrayList arrayList = new ArrayList();
		int num = RNG.Next(1, 500);
		foreach (OpenClass item in World.Open)
		{
			if (item.FLD_PID == Pid && item.FLD_PP >= num)
			{
				arrayList.Add(item);
			}
		}
		if (arrayList.Count == 0)
		{
			return null;
		}
		return (OpenClass)arrayList[RNG.Next(0, arrayList.Count - 1)];
	}
}

﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 百宝记录 {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int ID { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 分区 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 类型 { get; set; }

		[JsonProperty, Column(DbType = "smalldatetime", InsertValueSql = "getdate()")]
		public DateTime? 时间 { get; set; }

		[JsonProperty]
		public int? 物品数量 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 物品ID { get; set; }

		[JsonProperty]
		public int? 元宝数 { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string UserId { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string UserName { get; set; }

	}

}

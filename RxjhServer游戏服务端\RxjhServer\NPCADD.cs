using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace RxjhServer;

public class NPCADD : Form
{
	private static Dictionary<int, MonSterClss> List = new Dictionary<int, MonSterClss>();

	private string FLD_Name;

	private int Rxjh_HP;

	private int FLD_AT;

	private int FLD_DF;

	private int FLD_NPC;

	private int FLD_LEVEL;

	private int FLD_EXP;

	private int FLD_BOSS;

	private int FLD_AUTO;

	private IContainer components = null;

	private ComboBox comboBox2;

	private Label label20;

	private ComboBox comboBox1;

	private Label label1;

	private Button button1;

	private Button button2;

	private TextBox textBoxX;

	private Label label2;

	private Label label3;

	private TextBox textBoxY;

	private Label label4;

	private TextBox textBoxFW;

	private Label label5;

	private TextBox textBoxSL;

	private Label label7;

	private TextBox textBoxMAPID;

	private Label label8;

	private TextBox textBoxNPCID;

	private Label label6;

	public NPCADD()
	{
		InitializeComponent();
	}

	private void Form1_Load(object sender, EventArgs e)
	{
		List = new Dictionary<int, MonSterClss>();
		comboBox1.Items.Clear();
		comboBox2.Items.Clear();
		comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
		comboBox2.DropDownStyle = ComboBoxStyle.DropDownList;
		foreach (坐标Class item in World.移动)
		{
			KeyValuePair<int, string> keyValuePair = new KeyValuePair<int, string>(item.Rxjh_Map, item.Rxjh_name);
			comboBox2.Items.Add(keyValuePair);
		}
		foreach (MonSterClss value in World.MonSter.Values)
		{
			try
			{
				KeyValuePair<int, string> keyValuePair2 = new KeyValuePair<int, string>(value.FLD_PID, value.Name);
				List.Add(value.FLD_PID, value);
				comboBox1.Items.Add(keyValuePair2);
			}
			catch
			{
				MessageBox.Show(value.FLD_PID + "|" + value.Name);
			}
		}
	}

	private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
	{
		int key = ((KeyValuePair<int, string>)comboBox2.SelectedItem).Key;
		textBoxMAPID.Text = key.ToString();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			if (textBoxNPCID.Text.Length > 0 && textBoxMAPID.Text.Length > 0 && textBoxX.Text.Length > 0 && textBoxY.Text.Length > 0)
			{
				int num = int.Parse(textBoxNPCID.Text);
				int mapp = int.Parse(textBoxMAPID.Text);
				if (num > 0)
				{
					if (World.MonSter.ContainsKey(num))
					{
						if (World.SerNpc刷怪(num, int.Parse(textBoxX.Text), int.Parse(textBoxY.Text), mapp) != -1)
						{
							MessageBox.Show("添加成功");
						}
					}
					else
					{
						MessageBox.Show("指定怪物不存在");
					}
				}
				else
				{
					MessageBox.Show("怪物PID不正确");
				}
			}
			else
			{
				MessageBox.Show("地图ID或者怪物ID或坐标为空");
			}
		}
		catch (Exception)
		{
			throw;
		}
	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			MonSterClss monSterClass = MonSterClss.GetMonSterClass(((KeyValuePair<int, string>)comboBox1.SelectedItem).Key);
			if (monSterClass != null)
			{
				int fLD_PID = monSterClass.FLD_PID;
				FLD_Name = monSterClass.Name;
				Rxjh_HP = monSterClass.Rxjh_HP;
				FLD_AT = (int)monSterClass.FLD_AT;
				FLD_DF = (int)monSterClass.FLD_DF;
				FLD_NPC = monSterClass.FLD_NPC;
				FLD_LEVEL = monSterClass.Level;
				FLD_EXP = monSterClass.Rxjh_Exp;
				FLD_BOSS = monSterClass.FLD_BOSS;
				FLD_AUTO = monSterClass.FLD_AUTO;
				textBoxNPCID.Text = fLD_PID.ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
		try
		{
			if (textBoxNPCID.Text.Length > 0 && textBoxMAPID.Text.Length > 0)
			{
				int npcid = int.Parse(textBoxNPCID.Text);
				int mapp = int.Parse(textBoxMAPID.Text);
				for (int i = 0; i < int.Parse(textBoxSL.Text); i++)
				{
					Random random = new Random(DateTime.Now.Millisecond);
					int num = random.Next(0, 4);
					double num2 = float.Parse(textBoxX.Text);
					double num3 = float.Parse(textBoxY.Text);
					double num4 = random.NextDouble() * (double)float.Parse(textBoxFW.Text);
					double num5 = random.NextDouble() * (double)float.Parse(textBoxFW.Text);
					double num6 = random.NextDouble() * (double)float.Parse(textBoxFW.Text);
					double num7 = random.NextDouble() * (double)float.Parse(textBoxFW.Text);
					switch (num)
					{
					case 0:
						num2 = float.Parse(textBoxX.Text) + (float)num4;
						num3 = float.Parse(textBoxY.Text) + (float)num5;
						break;
					case 1:
						num2 = float.Parse(textBoxX.Text) + (float)num4;
						num3 = float.Parse(textBoxY.Text) - (float)num5;
						break;
					case 2:
						num2 = float.Parse(textBoxX.Text) - (float)num4;
						num3 = float.Parse(textBoxY.Text) - (float)num5;
						break;
					case 3:
						num2 = float.Parse(textBoxX.Text) - (float)num4;
						num3 = float.Parse(textBoxY.Text) + (float)num5;
						break;
					}
					World.SerNpc(npcid, (int)num2, (int)num3, mapp);
					Thread.Sleep(10);
				}
				MessageBox.Show("刷怪完成");
			}
			else
			{
				MessageBox.Show("地图ID或者怪物ID为空");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NPCADD));
            this.comboBox2 = new System.Windows.Forms.ComboBox();
            this.label20 = new System.Windows.Forms.Label();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.textBoxX = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.textBoxY = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxFW = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBoxSL = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.textBoxMAPID = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.textBoxNPCID = new System.Windows.Forms.TextBox();
            this.SuspendLayout();
            // 
            // comboBox2
            // 
            this.comboBox2.FormattingEnabled = true;
            this.comboBox2.Location = new System.Drawing.Point(110, 18);
            this.comboBox2.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.comboBox2.Name = "comboBox2";
            this.comboBox2.Size = new System.Drawing.Size(272, 26);
            this.comboBox2.TabIndex = 37;
            this.comboBox2.SelectedIndexChanged += new System.EventHandler(this.comboBox2_SelectedIndexChanged);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(18, 68);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(89, 18);
            this.label20.TabIndex = 38;
            this.label20.Text = "怪物选择:";
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(110, 62);
            this.comboBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(272, 26);
            this.comboBox1.TabIndex = 39;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(18, 22);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 18);
            this.label1.TabIndex = 40;
            this.label1.Text = "地图选择:";
            // 
            // button1
            // 
            this.button1.BackColor = System.Drawing.SystemColors.Control;
            this.button1.Location = new System.Drawing.Point(443, 131);
            this.button1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(139, 34);
            this.button1.TabIndex = 41;
            this.button1.Text = "固定刷怪";
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(443, 218);
            this.button2.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(139, 34);
            this.button2.TabIndex = 42;
            this.button2.Text = "批量刷怪";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // textBoxX
            // 
            this.textBoxX.Location = new System.Drawing.Point(110, 106);
            this.textBoxX.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxX.Name = "textBoxX";
            this.textBoxX.Size = new System.Drawing.Size(272, 28);
            this.textBoxX.TabIndex = 43;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(18, 114);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(62, 18);
            this.label2.TabIndex = 44;
            this.label2.Text = "坐标X:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(18, 160);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(62, 18);
            this.label3.TabIndex = 46;
            this.label3.Text = "坐标Y:";
            // 
            // textBoxY
            // 
            this.textBoxY.Location = new System.Drawing.Point(110, 152);
            this.textBoxY.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxY.Name = "textBoxY";
            this.textBoxY.Size = new System.Drawing.Size(272, 28);
            this.textBoxY.TabIndex = 45;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(18, 206);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 18);
            this.label4.TabIndex = 48;
            this.label4.Text = "范围:";
            // 
            // textBoxFW
            // 
            this.textBoxFW.Location = new System.Drawing.Point(110, 198);
            this.textBoxFW.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxFW.Name = "textBoxFW";
            this.textBoxFW.Size = new System.Drawing.Size(272, 28);
            this.textBoxFW.TabIndex = 47;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(18, 252);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 18);
            this.label5.TabIndex = 50;
            this.label5.Text = "数量:";
            // 
            // textBoxSL
            // 
            this.textBoxSL.Location = new System.Drawing.Point(110, 244);
            this.textBoxSL.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxSL.Name = "textBoxSL";
            this.textBoxSL.Size = new System.Drawing.Size(272, 28);
            this.textBoxSL.TabIndex = 49;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ForeColor = System.Drawing.Color.Red;
            this.label6.Location = new System.Drawing.Point(18, 309);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(314, 18);
            this.label6.TabIndex = 52;
            this.label6.Text = "说明：固定刷怪/NPC不用写数量和范围";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(399, 22);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 18);
            this.label7.TabIndex = 54;
            this.label7.Text = "地图ID:";
            // 
            // textBoxMAPID
            // 
            this.textBoxMAPID.Location = new System.Drawing.Point(472, 16);
            this.textBoxMAPID.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxMAPID.Name = "textBoxMAPID";
            this.textBoxMAPID.Size = new System.Drawing.Size(110, 28);
            this.textBoxMAPID.TabIndex = 53;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(399, 70);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(71, 18);
            this.label8.TabIndex = 56;
            this.label8.Text = "怪物ID:";
            // 
            // textBoxNPCID
            // 
            this.textBoxNPCID.Location = new System.Drawing.Point(472, 64);
            this.textBoxNPCID.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.textBoxNPCID.Name = "textBoxNPCID";
            this.textBoxNPCID.Size = new System.Drawing.Size(110, 28);
            this.textBoxNPCID.TabIndex = 55;
            // 
            // NPCADD
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(603, 348);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.textBoxNPCID);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.textBoxMAPID);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.textBoxSL);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBoxFW);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBoxY);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBoxX);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.comboBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.comboBox2);
            this.Controls.Add(this.label20);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "NPCADD";
            this.Text = "刷怪工具";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

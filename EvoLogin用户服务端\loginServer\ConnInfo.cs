using System;
using System.Collections.Generic;
using System.Linq;

namespace loginServer;

public class ConnInfo
{
	public static List<ConnInfo> ConnInfos = new List<ConnInfo>();

	private static object _lock = new object();

	public string Ip { get; set; }

	public int Port { get; set; }

	public DateTime Time { get; set; }

	public bool Kill { get; set; }

	public static void Add(ConnInfo info)
	{
		lock (_lock)
		{
			ConnInfos.Add(info);
		}
	}

	public static bool Check(string ip, int port)
	{
		lock (_lock)
		{
			List<ConnInfo> t = ConnInfos.Where((ConnInfo i) => i.Ip == ip).ToList();
			if (t.Count < World.快速连接限制次数)
			{
				return true;
			}
			if (t.FirstOrDefault((ConnInfo i) => i.Kill) != null)
			{
				return false;
			}
			IEnumerable<ConnInfo> t2 = t.OrderByDescending((ConnInfo i) => i.Time).Take(World.快速连接限制次数);
			DateTime ct = t2.Min((ConnInfo i) => i.Time);
			if ((DateTime.Now - ct).TotalSeconds < (double)World.快速连接限制时间)
			{
				return false;
			}
			return true;
		}
	}
}

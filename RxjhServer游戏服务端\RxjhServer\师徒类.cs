namespace RxjhServer;

public class 师徒类
{
	private int _TID;

	private string _STNAME;

	private int _TLEVEL;

	private int _STLEVEL;

	private int _STYHD;

	private int _STWG1;

	private int _STWG2;

	private int _STWG3;

	public int TID
	{
		get
		{
			return _TID;
		}
		set
		{
			_TID = value;
		}
	}

	public string STNAME
	{
		get
		{
			return _STNAME;
		}
		set
		{
			_STNAME = value;
		}
	}

	public int TLEVEL
	{
		get
		{
			return _TLEVEL;
		}
		set
		{
			_TLEVEL = value;
		}
	}

	public int STLEVEL
	{
		get
		{
			return _STLEVEL;
		}
		set
		{
			_STLEVEL = value;
		}
	}

	public int STYHD
	{
		get
		{
			return _STYHD;
		}
		set
		{
			_STYHD = value;
		}
	}

	public int STWG1
	{
		get
		{
			return _STWG1;
		}
		set
		{
			_STWG1 = value;
		}
	}

	public int STWG2
	{
		get
		{
			return _STWG2;
		}
		set
		{
			_STWG2 = value;
		}
	}

	public int STWG3
	{
		get
		{
			return _STWG3;
		}
		set
		{
			_STWG3 = value;
		}
	}
}

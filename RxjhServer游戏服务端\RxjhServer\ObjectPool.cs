using System;
using System.Collections.Concurrent;
using System.Threading;

namespace RxjhServer
{
    // 2025-0618 EVIAS 通用对象池基类
    public abstract class ObjectPool<T> where T : class
    {
        private readonly ConcurrentQueue<T> _objects = new();
        private readonly Func<T> _objectGenerator;
        private readonly Action<T> _resetAction;
        private readonly int _maxSize;
        
        private long _totalCreated = 0;
        private long _totalRented = 0;
        private long _totalReturned = 0;

        protected ObjectPool(Func<T> objectGenerator, Action<T> resetAction = null, int maxSize = 100)
        {
            _objectGenerator = objectGenerator ?? throw new ArgumentNullException(nameof(objectGenerator));
            _resetAction = resetAction;
            _maxSize = maxSize;
        }

        // 获取对象
        public T Get()
        {
            Interlocked.Increment(ref _totalRented);
            
            if (_objects.TryDequeue(out T item))
            {
                return item;
            }

            Interlocked.Increment(ref _totalCreated);
            return _objectGenerator();
        }

        // 归还对象
        public void Return(T item)
        {
            if (item == null) return;
            
            Interlocked.Increment(ref _totalReturned);
            
            // 重置对象状态
            try
            {
                _resetAction?.Invoke(item);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"对象池重置失败: {ex.Message}");
                return; // 重置失败的对象不放回池中
            }
            
            // 如果池已满，不放回
            if (_objects.Count >= _maxSize)
            {
                return;
            }
            
            _objects.Enqueue(item);
        }

        // 获取统计信息
        public string GetStats()
        {
            return $"{typeof(T).Name}池: 当前:{_objects.Count} 创建:{_totalCreated} 租用:{_totalRented} 归还:{_totalReturned}";
        }
    }

    // 2025-0618 EVIAS 字节数组对象池
    public class ByteArrayPool : ObjectPool<byte[]>
    {
        private static readonly Lazy<ByteArrayPool> _instance = new(() => new ByteArrayPool());
        public static ByteArrayPool Instance => _instance.Value;

        private ByteArrayPool() : base(() => new byte[1024], arr => Array.Clear(arr, 0, arr.Length), 50)
        {
        }

        // 获取指定大小的字节数组
        public byte[] Get(int size)
        {
            var array = Get();
            if (array.Length < size)
            {
                // 如果数组太小，创建新的
                return new byte[size];
            }
            return array;
        }
    }

    // 2025-0618 EVIAS 网络封包对象池
    public class PacketPool : ObjectPool<NetworkPacket>
    {
        private static readonly Lazy<PacketPool> _instance = new(() => new PacketPool());
        public static PacketPool Instance => _instance.Value;

        private PacketPool() : base(() => new NetworkPacket(), packet => packet.Reset(), 200)
        {
        }
    }

    // 2025-0618 EVIAS 网络封包类
    public class NetworkPacket
    {
        public byte[] Data { get; private set; }
        public int Length { get; private set; }
        public DateTime CreateTime { get; private set; }

        public NetworkPacket()
        {
            Data = new byte[2048]; // 默认2KB
            Reset();
        }

        public void SetData(byte[] data, int length)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (length > Data.Length)
            {
                // 如果数据太大，扩展数组
                Data = new byte[length];
            }
            
            Buffer.BlockCopy(data, 0, Data, 0, length);
            Length = length;
            CreateTime = DateTime.Now;
        }

        public void Reset()
        {
            Length = 0;
            CreateTime = DateTime.MinValue;
            // 不清空Data数组，提高性能
        }
    }

    // 2025-0618 EVIAS 临时数据对象池
    public class TempDataPool : ObjectPool<TempData>
    {
        private static readonly Lazy<TempDataPool> _instance = new(() => new TempDataPool());
        public static TempDataPool Instance => _instance.Value;

        private TempDataPool() : base(() => new TempData(), data => data.Reset(), 100)
        {
        }
    }

    // 2025-0618 EVIAS 临时数据类
    public class TempData
    {
        public int IntValue { get; set; }
        public string StringValue { get; set; }
        public float FloatValue { get; set; }
        public DateTime TimeValue { get; set; }
        public object ObjectValue { get; set; }

        public void Reset()
        {
            IntValue = 0;
            StringValue = null;
            FloatValue = 0f;
            TimeValue = DateTime.MinValue;
            ObjectValue = null;
        }
    }

    // 2025-0618 EVIAS 对象池管理器
    public class ObjectPoolManager
    {
        private static readonly Lazy<ObjectPoolManager> _instance = new(() => new ObjectPoolManager());
        public static ObjectPoolManager Instance => _instance.Value;

        private readonly System.Timers.Timer _statsTimer;

        private ObjectPoolManager()
        {
            // EVIAS 性能优化：每5分钟输出一次对象池统计
            _statsTimer = new System.Timers.Timer(5 * 60 * 1000);
            _statsTimer.Elapsed += (s, e) => LogPoolStats();
            _statsTimer.AutoReset = true;
            _statsTimer.Start();
        }

        private void LogPoolStats()
        {
            try
            {
                var stats = FastString.Join(" | ",
                    StringBuilderPool.Instance.GetPoolStats(),
                    ByteArrayPool.Instance.GetStats(),
                    PacketPool.Instance.GetStats(),
                    TempDataPool.Instance.GetStats()
                );
                
                Form1.WriteLine(6, $"对象池统计: {stats}");
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"对象池统计失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _statsTimer?.Stop();
            _statsTimer?.Dispose();
        }
    }
}

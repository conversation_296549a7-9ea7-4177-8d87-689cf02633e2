using System;

namespace RxjhServer;

public class 离线打架系统
{
	public static void 离线打架(Players player)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(player.内挂挂机打怪时间).TotalMilliseconds >= World.内挂打怪说话时间)
			{
				if (World.内挂打怪说话内容.Length != 0)
				{
					player.内挂喊话(player, World.内挂打怪说话内容);
				}
				player.内挂挂机打怪时间 = DateTime.Now;
			}
			if (player.离线打架模式 != 1)
			{
				return;
			}
			if (player.人物_HP <= (int)((double)player.人物最大_HP * 0.95) && (long)player.人物_HP > 0L)
			{
				int sl = player.人物最大_HP - player.人物_HP;
				player.加血(sl);
				player.吃药效果(1000000102);
				player.更新HP_MP_SP();
			}
			if (player.人物_MP <= (int)((double)player.人物最大_MP * 0.95) && (long)player.人物_MP > 0L)
			{
				player.加魔(20000);
				player.吃药效果(1000000104);
				player.更新HP_MP_SP();
			}
			long num = player.内挂获取自动拾取物品(player);
			if (num != 0L && World.ItmeTeM.ContainsKey(num))
			{
				int num2 = player.得到包裹空位(player);
				if (player.离线打架模式 != 0 || num2 == -1)
				{
					player.内挂拾取物品包(player, num);
				}
			}
			float num3 = player.人物坐标_X - (float)player.自动挂机坐标X;
			float num4 = player.人物坐标_Y - (float)player.自动挂机坐标Y;
			double num5 = (int)Math.Sqrt((double)num3 * (double)num3 + (double)num4 * (double)num4);
			if ((int)num5 > World.离线挂机打怪范围)
			{
				player.离线打架攻击人物 = 0;
				player.内挂移动包(player, player.自动挂机坐标X, player.自动挂机坐标Y, (float)num5);
			}
			else if ((long)player.人物_HP <= 0L || player.Player死亡)
			{
				player.移动(player.自动挂机坐标X, player.自动挂机坐标Y, player.人物坐标_Z, player.自动挂机地图);
				player.人物_HP = player.人物最大_HP;
				player.更新HP_MP_SP();
				player.Player死亡 = false;
			}
			else if (player.离线打架模式 == 1)
			{
				player.内挂吃药(player);
				player.内挂转职(player);
				打架地图读取(player);
				player.离线打架攻击人物 = 获取自动攻击NPC目标(player, player.自动挂机坐标X, player.自动挂机坐标Y);
				if (player.离线打架攻击人物 < 10000)
				{
					player.内挂攻击包(player);
				}
			}
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "离线打架系统", "自动打架", "离线打架处理");
			Form1.WriteLine(1, "离线打架出错:" + ex.Message);
		}
	}

	private static void 打架地图读取(Players player)
	{
		if (player.人物坐标_地图 != 101)
		{
			int num = player.获取随机数1(350, 500);
			int num2 = player.获取随机数1(1100, 1250);
			player.自动挂机坐标X = num;
			player.自动挂机坐标Y = num2;
			player.自动挂机地图 = 101;
			player.移动(num, num2, 15f, 101);
			player.获取复查范围玩家();
		}
	}

	public static int 获取自动攻击NPC目标(Players player, float float_0, float float_1)
	{
		int result = 0;
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.人物_HP > 0 && !value.Player死亡 && !value.Client.挂机 && player.查找范围玩家(60, value) && player.人物全服ID != value.人物全服ID)
			{
				result = value.人物全服ID;
			}
		}
		return result;
	}
}

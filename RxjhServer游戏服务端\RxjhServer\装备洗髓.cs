namespace RxjhServer
{
    public class 装备洗髓
    {
        private int _属性一最大;
        private int _属性一最小;
        private int _属性二最大;
        private int _属性二最小;
        private int _属性三最大;
        private int _属性三最小;
        private int _属性四最大;
        private int _属性四最小;
        private int _类型;
        private int _装备ID;
        private int _模式;

        public int 装备ID
        {
            get { return _装备ID; }
            set { _装备ID = value; }
        }

        public int 类型
        {
            get { return _类型; }
            set { _类型 = value; }
        }

        public int 模式
        {
            get { return _模式; }
            set { _模式 = value; }
        }

        public int 属性一最小
        {
            get { return _属性一最小; }
            set { _属性一最小 = value; }
        }

        public int 属性一最大
        {
            get { return _属性一最大; }
            set { _属性一最大 = value; }
        }

        public int 属性二最小
        {
            get { return _属性二最小; }
            set { _属性二最小 = value; }
        }

        public int 属性二最大
        {
            get { return _属性二最大; }
            set { _属性二最大 = value; }
        }

        public int 属性三最小
        {
            get { return _属性三最小; }
            set { _属性三最小 = value; }
        }

        public int 属性三最大
        {
            get { return _属性三最大; }
            set { _属性三最大 = value; }
        }

        public int 属性四最小
        {
            get { return _属性四最小; }
            set { _属性四最小 = value; }
        }

        public int 属性四最大
        {
            get { return _属性四最大; }
            set { _属性四最大 = value; }
        }
    }
}

﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <AssemblyName>EvoLogin</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>True</UseWindowsForms>
    <TargetFramework>net48</TargetFramework>
    <Prefer32Bit>True</Prefer32Bit>
  </PropertyGroup>
<Target Name="CleanOldBuildTimeLogs" BeforeTargets="Build">
		<Exec Command="del /q &quot;更新时间_*.log&quot;" IgnoreExitCode="true" />
	</Target>
	
	<Target Name="WriteBuildTimeLog" AfterTargets="Build">
		<PropertyGroup>
			<BuildTime>$([System.DateTime]::Now.ToString("yyyy-MM-dd HH:mm:ss"))</BuildTime>
			<BuildDate>$([System.DateTime]::Now.ToString("yyyyMMdd"))</BuildDate>
			<BuildTimeHMS>$([System.DateTime]::Now.ToString("HHmmss"))</BuildTimeHMS>
			<LogFileName>更新时间_$(BuildDate)_$(BuildTimeHMS).log</LogFileName>
		</PropertyGroup>
		<WriteLinesToFile
		  File="$(LogFileName)"
		  Lines="Build Time: $(BuildTime)&#x0D;&#x0A;User: $([System.Environment]::UserName)&#x0D;&#x0A;Machine: $([System.Environment]::MachineName)&#x0D;&#x0A;Project: $(MSBuildProjectName)"
		  Overwrite="true" />
	</Target>

  <PropertyGroup>
    <LangVersion>11.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup />
  <ItemGroup>
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Data" />
  </ItemGroup>
</Project>
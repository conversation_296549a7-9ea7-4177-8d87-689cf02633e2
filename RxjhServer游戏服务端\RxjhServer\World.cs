using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using Newtonsoft.Json;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using RxjhVer23;

namespace RxjhServer;

public class World
{
	public class CompetitionWorld : ICompetitionWorld
	{
		List<ICompetitionPlayer> ICompetitionWorld.GetOnlinePlayerList()
		{
			List<ICompetitionPlayer> list = new List<ICompetitionPlayer>();
			foreach (Players value in allConnectedChars.Values)
			{
				list.Add(value);
			}
			return list;
		}
	}
    public static Random 披风鉴定随机数生成器 = new Random(); //EVIAS 披风鉴定

    public static Dictionary<int, 道具组合类> 所有可组合道具;

    public static Dictionary<int, 材料进阶装备> 装备进阶;  //EVIAS

    public static Dictionary<int, 装备分解材料> 装备分解;  //EVIAS

    public static string[] 碎片数量; //EVIAS

    public static string[] 神石数量; //EVIAS

    public static bool 自动清理记录 = false; //EVIAS  自动清理

    public static DateTime 上次清理时间 = DateTime.MinValue;

    public static ConcurrentDictionary<int, 讨伐战队伍类> 讨伐队伍;

	public static 双倍扣武勋 开启双倍扣武勋;

	public static int 讨伐副本最少人数;

	public static int 讨伐副本最多人数;

	public static int 讨伐副本时长;

	public static 讨伐战系统 讨伐战副本;

	public static string 讨伐队伍名;

	public static int 群体辅助组队范围;

	public static int 群医加经验开关;

	public static int 群医加爆率;

	public static double 医生群疗经验加成;

	public static int 自动存取时间;

	public static int 内挂打怪说话时间;

	public static int 双倍扣武勋倍数;

	public static string 双倍扣武勋公告内容;

	public static int 双倍扣武勋结束时间;

	public static int 双倍扣武勋元宝数量;

	public static string 内挂打怪说话内容;

	public static int 离线挂机打怪范围;

	public static int 道具锁开关;

	public static int 邮件物品数量开关;

	public static int 邮件物品交易数量;

	public static int 邮件物品强化数量;

	public static int 邮件传书杀人次数;

	public static DateTime 膜拜大神时间间隔;

	public static DateTime 发红包当前时间;

	public static DateTime 天魔神宫更新时间;

	public static int 攻城战胜利占领时间;

	public static string[] 移动速度;

	public static string[] 充值排行比例;

	public static int 限制连环飞舞确认时间;

	public static int MainServer;

	public static bool Droplog;

	public static int 离线数量;

	public static int 假人数量;

	public static int 云挂机数量;

	public static int 假人开商店;

	public static int 假人关商店;

	public static string Key1;

	public static string Key2;

	public static int 初始占领日期;

	public static string 天魔神宫占领者;

	public static string 门战占领者;

	public static List<门派联盟申请状态> 门派联盟申请状态;

	public static List<Players> 参加红包列表;

	public static ConcurrentDictionary<string, Players> 申请仙魔大战人物列表;

	public static ConcurrentDictionary<int, Players> Iplist;

	public static ConcurrentDictionary<string, Players> privateTeams;

	public static 全服经验 开启全服经验;

	public static 异口同声 开启异口同声;

	public static 幸运玩家 开启幸运玩家;

	public static 伏魔洞副本 伏魔洞副本;

	public static 冰宫内城副本 冰宫内城副本;

	public static 野外BOSS 野外boss;

	public static List<假人出售物品> 假人出售物品列表;

	public static int 开启吃药泡点加成;

	public static int 野外BOSS开关;

	public static int 野外BOSS倒计时;

	public static int 野外BOSS总时间;

	public static string 野外BOSS时间;

	public static string 野外BOSS配置;

	public static boss攻城 boss攻城;

	public static int BOSS攻城倒计时;

	public static int BOSS攻城时间;

	public static int BOSS攻城是否开启;

	public static int BOSS攻城开启小时;

	public static int BOSS攻城开启分;

	public static int BOSS攻城开启秒;

	public static int BOSS攻城地图编号;

	public static 世界BOSS 世界boss;

	public static int 世界BOSS攻城倒计时;

	public static int 世界BOSS攻城时间;

	public static int 世界BOSS攻城是否开启;

	public static int 世界BOSS攻城开启小时;

	public static int 世界BOSS攻城开启分;

	public static int 世界BOSS攻城开启秒;

	public static int 世界BOSS怪物ID;

	public static int 世界BOSS出现地图;

	public static int 世界BOSS坐标X;

	public static int 世界BOSS坐标Y;

	public static 发红包 红包系统;

	public static string 幸运奖奖励;

	public static string 幸运奖奖励单件物品;

	public static int 幸运奖开启分;

	public static int 幸运奖开启秒;

	public static int 幸运奖开启小时;

	public static int 幸运奖是否开启;

	public static bool 自动换端口同步变量;

	public static int AllItmelog;

	public static int 开启死亡无敌;

	public static int 开启人物删除;

	public static int 禁止PK;

	public static int 禁止开店;

	public static int 禁止交易;

	public static int 是否开启共用银币市场;

	public static int 当前是否是银币线路;

	public static int 交易等级限制;

	public static int 进店等级限制;

	public static int 丢物品等级限制;

	public static int 开箱保底箱子编号;

	public static int 开箱保底次数;

	public static string 开箱保底物品编号;

	public static int 交易中物品时间;

	public static int 商店买东西时间;

	public static int 百宝买东西时间;

	public static int 仓库存取需时间;

	public static int 活动开启中;

	public static int 打开换线线路1;

	public static int 打开换线线路2;

	public static int 打开换线线路3;

	public static int 打开换线线路4;

	public static int 打开换线线路5;

	public static int 打开换线线路6;

	public static int 打开换线线路7;

	public static int 打开换线线路8;

	public static int 打开换线线路9;

	public static int 打开换线线路10;

	public static int 打开换线线路11;

	public static int 打开换线线路12;

	public static int 打开换线线路13;

	public static int 打开换线线路14;

	public static int 打开换线线路15;

	public static int 打开换线线路16;

	public static int 打开换线线路17;

	public static int 打开换线线路18;

	public static int 打开换线线路19;

	public static int 打开换线线路20;

    // 2025-05-19: 新增拍卖系统配置常量
    public static int 拍卖系统是否开启; // 2025-05-19: 寄售改为拍卖

	public static int 是否拍卖绑定装备; // 2025-05-19: 寄售改为拍卖

	public static string 拍卖物品锁定; // 2025-05-19: 寄售改为拍卖

	public static int 最大拍卖数量 = 10;           // 每个玩家最多同时拍卖的物品数量
	
	public static int 拍卖持续天数 = 7;            // 拍卖持续天数
	
	public static double 拍卖手续费比例 = 0.05;    // 拍卖手续费比例（5%）
	public static int 拍卖自动结束倍数 = 2;         // 达到一口价几倍时自动结束（默认2倍）
	public static int 拍卖超价结束时间 = 10;        // 竞价超过一口价后多少分钟自动结束（默认10分钟）
	public static int 拍卖记录清理天数 = 7;         // 拍卖记录保留天数，0=立即清理，其他=保留对应天数（默认7天）

    // 2025-06-14: EVIAS 兑换配置
    public static int 钻石兑换元宝是否开启;
    public static int 元宝兑换钻石是否开启;
    public static int 钻石兑换元宝比例;
    public static int 元宝兑换钻石比例;

    // 2025-06-14: 抽奖价格配置
    public static int 单抽价格;
    public static int 十抽价格;

    public static int 在线奖励需要小时数; // EVIAS 24.0 在线奖励配置

    public static string 转换职业命令;

	public static int 转换职业功能是否开启;

	public static int 转换职业需要元宝数量;

	public static string 转换性别命令;

	public static int 转换性别功能是否开启;

	public static int 转换性别需要元宝数量;

	public static string 转换正邪命令;

	public static int 转换正邪功能是否开启;

	public static int 转换正邪需要元宝数量;

    public static int 限制气功点数;

	public static double 限制气功百分比;

	public static int 武器宝珠增加属性类型;

	public static int 武器宝珠增加属性值;

	public static int 衣服宝珠增加属性类型;

	public static int 衣服宝珠增加属性值;

	public static int 护手宝珠增加属性类型;

	public static int 护手宝珠增加属性值;

	public static int 鞋子宝珠增加属性类型;

	public static int 鞋子宝珠增加属性值;

	public static int 内甲宝珠增加属性类型;

	public static int 内甲宝珠增加属性值;

	public static int 允许挂机;

	public static int 报错踢号次数;

	public static int 死亡不复活踢号时间;

	public static int 连续快速攻击次数;

	public static int 连续攻击有效时间;

	public static int 非法攻击外挂操作;

	public static bool AlWorldlog;

	public static bool Process;

	public static object lockLogin;

	public static object AsyncLocksw;

	public static ConcurrentDictionary<int, 装备检测类> 装备检测list;

	public static ConcurrentDictionary<int, NetState> list;

	public static List<int> 普通气功ID;

	public static List<int> 反气功ID; //24.0 EVIAS 新增反气功ID列表

	public static Dictionary<int, 反气功类> 反气功List; //24.0 EVIAS 新增反气功配置

	public static ConcurrentDictionary<string, 仙魔大战top> 仙魔Top;

	public static ConcurrentDictionary<int, Players> allConnectedChars;

	public static 武林血战 个人血战;

	public static ConcurrentDictionary<string, string> 禁言列表;

	public static ConcurrentDictionary<int, 升天气功总类> 升天气功List;

	public static ConcurrentDictionary<int, 任务类> 任务list;

	public static ConcurrentDictionary<string, DbClass> Db;

	public static ConcurrentDictionary<int, ServerList> SerList;

	public static Queue m_Disposed;

	public static Queue SqlPool;

	public static ConcurrentDictionary<int, MapClass> Map;

	public static ConcurrentDictionary<int, 帮战Class> 帮战list;

	public static ConcurrentDictionary<int, 帮战Class> 帮战Namelist;

	public static ConcurrentDictionary<int, string> Maplist;

	public static ConcurrentDictionary<int, NpcClass> NpcList;

	public static ConcurrentDictionary<int, Wedding> 婚礼list;

	public static ConcurrentDictionary<int, 门派成员> 门派成员list;

	public static ConcurrentDictionary<int, 百宝阁类> 百宝阁属性物品类list;

	public static ConcurrentDictionary<int, 百宝阁类> 百宝阁抽奖物品类list;

	public static ConcurrentDictionary<int, PVP类> PVP装备;

	public static List<NpcClass> 一转地图;

	public static List<NpcClass> 二转地图;

	public static List<NpcClass> 三转地图;

	public static List<NpcClass> 四转地图;

	public static List<NpcClass> 五转地图;

	public static List<NpcClass> 六转地图;

	public static List<NpcClass> 七转地图;

	public static List<NpcClass> 八转地图;

	public static List<NpcClass> 九转地图;

	public static List<NpcClass> 十转地图;

	public static List<NpcClass> 十一转地图;

    public static List<NpcClass> 十二转地图;

    public static int 物品交易后锁定时间;

	public static string 临时GM命令;

	public static string 临时管理员;

	public static int 新门战进程;

	public static int 攻击时间间隔;

	public static bool 龙赡殿是否在使用中;

	public static bool 华婚殿是否在使用中;

	public static bool 圣礼殿是否在使用中;

	public static int 最大速度超出次数操作;

	public static int 三十秒内允许超出次数;

	public static int 周末武勋量;

	public static string 追杀命令;

	public static int 追杀需要元宝数量;

	public static string 装备升级命令;

	public static string 签到命令;

	public static int 签到奖励元宝数;

	public static int 签到奖励武勋数;

	public static string 福利命令;

	public static string 路费命令;

    public static string 在线奖励命令; //0420

    public static int 在线奖励开关 = 1;  // 1=开启，0=关闭

    // 开店奖励系统配置
    public static int 开店奖励开关 = 1;        // 0=关闭，1=开启
    public static int 开店奖励小时 = 28;       // 一周内需要28小时
    public static int 开店奖励元宝 = 100;      // 奖励100元宝
    public static int 开店奖励冷却天数 = 7;    // 7天冷却期

    public static string 转生命令;

	public static int 限制转生次数;

	public static int 转生需要等级;

	public static int 转生需要几转;

	public static int 转生回落等级;

	public static string 转生获得属性;

	public static int 天气系统开关;

	public static int 无限负重;

	public static int 限时行囊;

	public static int PK掉装备;

	public static int PK掉装备几率;

	public static int PK掉装备善恶;

	public static int 开启下雪场景;

	public static int 八彩提示是否开启;

	public static string 八彩红色提示内容;

	public static string 八彩赤色提示内容;

	public static string 八彩橙色提示内容;

	public static string 八彩绿色提示内容;

	public static string 八彩蓝色提示内容;

	public static string 八彩深蓝提示内容;

	public static string 八彩紫色提示内容;

	public static string 八彩浅色提示内容;

    public static int 在线人数是否开启;

    public static int 在线基本人数;

    public static int 二转每日武勋上限;

	public static int 三转每日武勋上限;

	public static int 四转每日武勋上限;

	public static int 五转每日武勋上限;

	public static int 六转每日武勋上限;

	public static int 七转每日武勋上限;

	public static int 八转每日武勋上限;

	public static int 九转每日武勋上限;

	public static int 十转每日武勋上限;

	public static int 十一转每日武勋上限;

	public static int 是否开启上线BUFF;

	public static int 是否开启任务领取;

	public static int 补偿的任务物品ID;

	public static int 新手上线说话等级;

	public static int 异口同声是否开启;

	public static int 异口同声开启中;

	public static int 异口同声开启时;

	public static int 异口同声开启分;

	public static int 异口同声开启秒;

	public static int 异口同声结束时间;

	public static string 异口同声内容;

	public static int 双倍奖励是否开启;

	public static int 双倍奖励开启小时;

	public static int 双倍奖励开启分;

	public static int 双倍奖励开启秒;

	public static double 双倍奖励经验倍数;

	public static int 双倍奖励爆率倍数;

	public static int 双倍奖励武勋倍数;

	public static int 双倍奖励结束时间;

	public static string 双倍奖励公告内容;

	public static double 三十五级以下经验倍数;

	public static double 六十级以下经验倍数;

	public static double 八十级以下经验倍数;

	public static double 一百级以下经验倍数;

	public static double 一百一以下经验倍数;

	public static double 一百二以下经验倍数;

	public static double 一百三以下经验倍数;

	public static double 一百四以下经验倍数;

	public static double 一百五以下经验倍数;

	public static double 一百六以下经验倍数;

	public static int 自定义经验等级;

	public static double 自定义等级经验倍数;

	public static List<int> 限制PK地图列表;

	public static List<int> 限时PK地图列表;

	public static int 限时地图开PK时间;

	public static int 限时地图关PK时间;

	public static int 限时地图是否开启;

	public static int 周末全天PK是否开启;

	public static List<int> 周末全天PK地图列表;

	public static int 工作日限时地图开PK时间;

	public static int 工作日限时地图关PK时间;

	public static int 工作日限时地图是否开启;

	public static int 门战准备时间;

	public static int 门战总时间;

	public static int 武林血战是否开启;

	public static int 武林血战开启小时;

	public static int 武林血战开启分;

	public static int 武林血战开启秒;

	public static int 武林血战参战等级;

	public static int 武林血战准备时间;

	public static string 武林血战参加奖励;

	public static int 武林血战第一轮人物;

	public static int 武林血战第一轮时间;

	public static string 武林血战第一回合奖励;

	public static int 武林血战第二轮人物;

	public static int 武林血战第二轮时间;

	public static string 武林血战第二回合奖励;

	public static int 武林血战第三轮人物;

	public static int 武林血战第三轮时间;

	public static string 武林血战第三回合奖励;

	public static int 武林血战进程;

	public static int 武林血战人数;

	public static ConcurrentDictionary<string, string> 仙魔大战掉线玩家;

	public static ConcurrentDictionary<int, 荣誉Class> 武林血战排行数据;

	public static ConcurrentDictionary<int, 荣誉Class> 杀人排行数据;

	public static List<门派排名> 门派排名数据;

	public static List<势力排名> 势力排名数据;

	public static List<武林排名> 武林排名数据;

	public static List<讨伐排名> 讨伐排名数据;

	public static int 同类型数据包处理间隔;

	public static List<int> 过滤数据包类型;

	public static int 开启数据包限制;

	public static int 开启卡技能;

	public static int 卡技能次数;

	public static int 坐标刷新时间;

	public static int 攻击时间控制;

	public static int 贡献元宝数;

	public static int 贡献元宝荣誉点;

	public static int 英雄职业转职需要武器;

	public static int 限制最高级别;

	public static int 发包单个物品大小;

	public static int 数据库单个物品大小;

	public static int 物品属性大小;

	public static int 外挂PK时间;

	public static double locklist2;

	public static int 每次狮吼功消耗元宝;

	public static string 信任连接IP;

	public static int 是否开启推广返利;

	public static int 累计充值属性加成;

	public static int 累计充值属性提示;

	public static int 是否开启票红字;

	public static int 是否开启票红字2;

	public static int 是否开启安全码;

	public static int 药品冲突是否开启;

	public static int 是否开启门战系统;

	public static int 开启门战系统;

	public static int 开启攻城战系统;

	public static int 攻城战时长;

	public static int 攻城战预备时间;

	public static int 攻城战开启小时;

	public static int 攻城战开启分;

	public static int 攻城战开启秒;

	public static int 申请门战需要元宝;

	public static int 门战系统开启时;

	public static int 门战系统开启分;

	public static int 门战系统开启秒;

	public static int 胜利帮派ID;

	public static int 每次分解消耗元宝数;

	public static int 游戏登陆端口最大连接数;

	public static int 游戏登陆端口最大连接时间数;

	public static int 查非法物品;

	public static int 查非法物品操作;

	public static int 心跳检测开关;

	public static int 是否开启等级奖励;

	public static int 心跳检测时间阀值;

	public static int 心跳检测时长;

	public static int 安全模式消耗元宝;

	public static int 是否开启安全模式;

	public static string[] 安全模式时间;

	public static int 地下密路副本是否开启;

	public static string[] 地下密路副本时间;

	public static int 是否开启新手上线设置;

	public static int 登录器模式;

	public static int 上线等级;

	public static int 赠送气功书;

	public static int 上线转职等级;

	public static int 上线金币数量;

	public static int 上线历练数量;

	public static int 上线武勋设置;

	public static int 上线升天气功点;

	public static int 自动分配正邪;

	public static int 银票兑换元宝;

	public static int 是否开启银票兑换元宝;

	public static int 上线送礼包是否开启;

	public static int 上线送金符是否开启;

	public static int 上线送药品是否开启;

	public static int 转职赠送礼包;

	public static int 上线送礼包套装;

	public static int 人物越级怪物掉落差;

	public static int 怪物越级人物掉落差;

	public static int 人物越级怪物经验差;

	public static int 怪物越级人物经验差;

	public static int 击杀BOSS等级差;

	public static int 使用武勋丹上限;

	public static int 随机武勋丹最小;

	public static int 随机武勋丹最大;

	public static int 使用经验珠等级上限;

	public static List<int> 禁创职业;

	public static int 移动间隔时间;

	public static int 是否开启装备加解锁功能;

	public static int 装备加锁消耗元宝;

	public static int 装备解锁消耗元宝;

	public static int 是否开启挂机奖励;

	public static int 挂机奖励时间周期;

	public static int 普通挂机奖励元宝;

	public static int 会员挂机奖励元宝;

	public static int 普通挂机奖励钻石;

	public static int 会员挂机奖励钻石;

	public static int 会员挂机奖励武勋;

	public static int 会员挂机奖励金币;

	public static int 普通挂机奖励金币;

	public static int 普通挂机奖励武勋;

	public static int 挂机消除宠物忠诚度;

	public static int 挂机奖励要求等级;

	public static string 挂机双倍时间段;

	public static int 购买武勋装备消耗武勋;

	public static int BOSS掉落物品数量下限;

	public static int BOSS掉落物品数量上限;

	public static int BOSS掉落元宝几率;

	public static int BOSS掉落元宝数量下限;

	public static int BOSS掉落元宝数量上限;

	public static int BOSS掉落钻石几率;

	public static int BOSS掉落钻石数量下限;

	public static int BOSS掉落钻石数量上限;

	public static int BOSS掉落金币几率;

	public static int BOSS掉落金币数量下限;

	public static int BOSS掉落金币数量上限;

	public static int BOSS掉落武勋几率;

	public static int BOSS掉落武勋数量下限;

	public static int BOSS掉落武勋数量上限;

	public static int BOSS掉落物品几率;

	public static int 是否开启公告掉落提示;

	public static int 是否支持扩展物品属性位数;

	public static int 安全挂机时间;

	public static double 灵宠高级进化率;

	public static double 灵宠宝物进化率;

	public static double 灵宠传说进化率;

	public static double 灵宠神灵普通进化率;

	public static double 灵宠神灵高级进化率;

	public static double 灵宠神灵宝物进化率;

	public static double 灵宠神灵传说进化率;

	public static double 密路宝珠高级进化率;

	public static double 密路宝珠稀有进化率;

	public static double 密路宝珠传说进化率;

	public static double 火龙宝珠高级进化率;

	public static double 火龙宝珠稀有进化率;

	public static double 火龙宝珠传说进化率;

	public static int PK掉耐久度;

	public static int 打怪掉耐久度;

	public static string 修炼药品;

	public static string 遗忘药品;

	public static string 皮皮岛药品;

	public static string 世外药品;

	public static int 单次交易元宝数量上限;

	public static int 帐号总元宝上限;

	public static int 元宝检测操作;

	public static int 是否开启武勋系统;

	public static int PK等级差;

	public static int 武勋保护等级;

	public static int 武勋保护数量;

	public static string 死亡减少武勋数量;

	public static string 死亡回收武勋数量;

	public static string 限制地图武勋掉落倍数;

	public static int 锁定记录;

    public static int 物品记录;

	public static int 登陆记录;

	public static int 掉落记录;

	public static int 首爆记录;

	public static int 开盒记录;

	public static int 商店记录;

	public static int 仓库记录;

	public static int 药品记录;

	public static int 合成记录;

	public static int 进化记录;

	public static int 传书记录;

	public static int 卡号记录;

	public static int 百宝记录;

	public static int 元宝记录;

	public static int 武勋记录;

	public static int 记录保存天数;

	public static int 传书保存天数;

	public static bool 封IP;

	public static List<IPAddress> BipList;

	public static int 版本验证时间;

	public static bool 主Socket;

	public static string SocketState;

	public static bool 断开连接;

	public static bool 加入过滤列表;

	public static int 世界时间;

	public static int W组队Id;

	public static int jlMsg;

	public static int week;

	public static int 是否允许快速攻击;

	public static int 是否开启公告结婚完成;

	public static int 是否开启拜师完成任务;

	public static int 拜师收徒需要等级;

	public static int 拜师收徒需要类型;

	public static int 拜师收徒需要数量;

	public static int 拜师收徒任务时间;

	public static int 拜师收徒贡献数量;

	public static int 限制职业一;

	public static int 限制职业二;

	public static int 限制职业一等级;

	public static int 限制职业二等级;

	public static int 限制职业一数量;

	public static int 限制职业二数量;

	public static double 限制职业一降低百分比;

	public static double 限制职业二降低百分比;

	public static int 是否开启数据库宝箱;

	public static int 学习制作技能熟练度;

	public static int 情侣爱情度数量值;

	public static int 是否开启告白需要物品;

	public static int 告白需要物品编号;

	public static int 结婚赠送物品编号;

	public static int 仙魔大战进程;

	public static int 仙魔大战时间;

	public static int 仙魔大战正分数;

	public static int 仙魔大战邪分数;

	public static int 仙魔大战正人数;

	public static int 仙魔大战邪人数;

	public static int 仙魔大战是否开启;

	public static int 仙魔大战开启小时;

	public static int 仙魔大战开启分;

	public static int 仙魔大战开启秒;

	public static int 仙魔大战时长;

	public static int 仙魔大战预备时间;

	public static int 强化合成概率周否开启;

	public static string 强化合成概率周开启时间星期;

	public static int 强化合成概率周开启小时;

	public static int 强化合成概率周结束小时;

	public static int 强化合成概率周分;

	public static int 强化合成概率周秒;

	public static double 强化合成概率周倍数;

	public static int 是否开启强化合成概率周;

	public static int 武林血战奖励礼包;

	public static int 第一名奖励礼包;

	public static int 是否开启对练场赌元宝;

	public static int 允许玩家押注数量;

	public static float 场地有效范围;

	public static int 进场最低费用;

	public static double 场地佣金百分比;

	public static int 允许逃跑次数;

	public static int 分数扣完扣除元宝;

	public static int 分数扣完扣除金钱;

	public static int Eve90进程;

	public static int Eve90时间;

	public static int 元宝合成;

	public static int 死亡恢复全部经验元宝数;

	public static int 死亡恢复固定经验元宝数;

	public static double 发送速度;

	public static double 接收速度;

	public static double 发包基数;

	public static double 经验倍数;

	public static int 吸魂成功几率;

	public static double 钱倍数;

	public static int 历练倍数;

	public static int 属性一替换成功率;

	public static int 属性二替换成功率;

	public static int 属性三替换成功率;

	public static int 属性四替换成功率;

	public static int 暴率;

	public static int 是否开启附魂;

	public static int 最大附魂数;

	public static int 附魂加战斗力;

	public static int 附魂加攻击;

	public static int 附魂加防御;

	public static double 初魂成功几率;

	public static double 中魂成功几率;

	public static double 四神成功几率;

	public static double 四神变更卷成功几率;

	public static double 强化转移符成功几率;

	public static double 武器防具进化2成功几率;

	public static int 强化附加战斗力;

	public static double 首饰加工一成功率;

	public static double 首饰加工二成功率;

	public static double 首饰加工三成功率;

	public static double 首饰加工四成功率;

	public static double 首饰加工五成功率;

	public static double 首饰加工六成功率;

	public static double 首饰加工七成功率;

	public static double 首饰加工八成功率;

	public static double 首饰加工九成功率;

	public static double 首饰加工十成功率;

	public static double 提真成功几率;

	public static string 老百宝阁地址;

	public static string 老百宝阁服务器IP;

	public static int 老百宝阁服务器端口;

	public static string 新百宝阁地址;

	public static string 新百宝阁服务器IP;

	public static int 新百宝阁服务器端口;

	public static string 神器兑换地址;

	public static string 神器兑换服务器IP;

	public static int 神器兑换服务器端口;

	public static string 帐号验证服务器IP;

	public static int 帐号验证服务器端口;

	public static int 游戏服务器端口;

	public static int 游戏服务器端口2;

	public static int vip线;

	public static int 最大在线;

	public static int 服务器组ID;

	public static int 服务器ID;

	public static string 服务器名;

	public static int 狮子吼ID;

	public static Queue 狮子吼List;

	public static ConcurrentDictionary<int, 门战数据> 门战数据list;

	public static ConcurrentDictionary<int, 门派战绩> 门派战绩list;

	public static int 吸怪数量;

	public static int 吸怪距离;

	public static int 引怪距离;

	public static int 怪物打人距离;

	public static int 群攻打怪距离;

	public static int 组队范围距离;

	public static int 移动坐标异常后反弹;

	public static int 开启实时坐标检测;

	public static int 是否开启实时坐标显示;

	public static int 实时检测距离;

	public static float 普通走;

	public static float 轻功一;

	public static float 轻功二;

	public static float 轻功三;

	public static int 实时移动时间;

	public static float 宠物普通走;

	public static float 韩轻功一;

	public static float 韩轻功二;

	public static float 韩轻功三;

	public static float 韩轻功四;

	public static int 伏魔洞副怪是否死亡;

	public static int 冰宫内城副怪是否死亡;

	public static int 天魔神宫大门是否死亡;

	public static int 天魔神宫东门是否死亡;

	public static int 攻城战进程;

	public static int 城门强化等级;

	public static int 攻城时间;

	public static int 狮子吼最大数;

	public static int 元宝送积分是否开启;

	public static int 百宝消费榜是否开启;

	public static string 充值榜第一名;

	public static string 充值榜第二名;

	public static string 充值榜第三名;

	public static string 充值榜第四名;

	public static string 充值榜第五名;

	public static string 充值榜第六名;

	public static string 充值榜第七名;

	public static string 充值榜第八名;

	public static string 充值榜第九名;

	public static string 充值榜第十名;

	public static int 封包封号;

	public static int 组队等级限制;

	public static int 特效道具ID;

	public static int 神豪上线公告;

	public static int 前十上线公告;

	public static int Vip上线公告;

	public static string Vip上线公告内容;

	public static string 神豪上线公告内容;

	public static string 前十上线公告内容;

	public static string VIP地图;

	public static int 点赞元宝数量;

	public static string 地图锁定;

	public static string 每日武勋解锁地图;

	public static string 允许开店地图;

	public static string SqlJl;

	public static int 元宝检测;

	public static int 装备最大数;

	public static int 自动存档;

	public static double 灵宠强化一阶段概率;

	public static double 灵宠强化二阶段概率;

	public static double 灵宠强化三阶段概率;

	public static double 灵宠强化四阶段概率;

	public static double 灵宠强化五阶段概率;

	public static double 灵宠强化六阶段概率;

	public static double 灵宠强化七阶段概率;

	public static double 灵宠强化八阶段概率;

	public static double 灵宠强化九阶段概率;

	public static double 灵宠强化十阶段概率;

	public static double 灵宠强化十一阶段概率;

	public static double 灵宠强化十二阶段概率;

	public static double 灵宠强化十三阶段概率;

	public static double 灵宠强化十四阶段概率;

	public static double 灵宠强化十五阶段概率;

	public static double 披风强化一阶段概率;

	public static double 披风强化二阶段概率;

	public static double 披风强化三阶段概率;

	public static double 披风强化四阶段概率;

	public static double 披风强化五阶段概率;

	public static double 披风强化六阶段概率;

	public static double 披风强化七阶段概率;

	public static double 披风强化八阶段概率;

	public static double 披风强化九阶段概率;

	public static double 披风强化十阶段概率;

	public static double 披风强化十一阶段概率;

	public static double 披风强化十二阶段概率;

	public static double 披风强化十三阶段概率;

	public static double 披风强化十四阶段概率;

	public static double 披风强化十五阶段概率;

	public static long 王龙的金币;

	public static int 是否开启王龙;

	public static double 九泉金币比率;

	public static int 王龙地图ID;

	public static string 文件MD5;

	public static string 再造金刚石攻击;

	public static string 再造金刚石追伤;

	public static string 再造金刚石武功;

	public static string 再造金刚石命中;

	public static string 再造金刚石生命;

	public static string 再造寒玉石防御;

	public static string 再造寒玉石回避;

	public static string 再造寒玉石生命;

	public static string 再造寒玉石内功;

	public static string 再造寒玉石武防;

	public static double 武功防增加百分比;

	public static double 武功防降低百分比;

	public static double 武功攻击力百分比;

	public static double 攻减防加乘;

	public static double 武功减武防加乘;

	public static int 是否开启死亡掉经验;

	public static double 二人组队增加百分比;

	public static double 三人组队增加百分比;

	public static double 四人组队增加百分比;

	public static double 五人组队增加百分比;

	public static double 六人组队增加百分比;

	public static double 七人组队增加百分比;

	public static double 八人组队增加百分比;

	public static double 二人组队暴率百分比;

	public static double 三人组队暴率百分比;

	public static double 四人组队暴率百分比;

	public static double 五人组队暴率百分比;

	public static double 六人组队暴率百分比;

	public static double 七人组队暴率百分比;

	public static double 八人组队暴率百分比;

	public static double 四级门派增加百分比;

	public static double 五级门派增加百分比;

	public static double 六级门派增加百分比;

	public static double 七级门派增加百分比;

	public static double 队伍红包增加经验百分比;

	public static double 队伍红包增加金钱百分比;

	public static double 一级推广经验增加百分比;

	public static double 二级推广经验增加百分比;

	public static double 三级推广经验增加百分比;

	public static double VIP经验增加百分比;

	public static double VIP历练增加百分比;

	public static double VIP金钱增加百分比;

	public static double VIP合成率增加百分比;

	public static double 医生PK距离;

	public static double 弓箭手PK距离;

	public static double 神女PK距离;

	public static double 梅柳真PK距离;

	public static double 其他职业PK距离;

	public static double 医生打怪距离;

	public static double 弓箭手打怪距离;

	public static double 神女打怪距离;

	public static double 梅柳真打怪距离;

	public static double 其他职业打怪距离;

	public static int VIP爆率增加;

	public static int 披风强化是否消耗元宝;

	public static int 防具强化最大数量;

	public static int 披风强化最大数量;

	public static int 披风强化消耗元宝数量;

	public static int 灵宠强化是否消耗开关;

	public static int 灵宠强化最大数量;

	public static int 灵宠强化消耗武皇币数量;

	public static int 灵宠强化消耗冰魄水玉数量;

	public static double 天关经验提高百分比基数;

	public static double 天关经验提高百分比递增;

	public static int 天关物品爆率提高基数;

	public static int 天关物品爆率提高递增;

	public static int 装备提真消耗;

	public static int 装备提真数量;

	public static string 世界BOSS奖励物品;

	public static string 门战参与奖励物品;

	public static string 随机BOSS出现时间表;

	public static int 是否开启外挂占卜;

	public static int 是否开启NPC占卜;

	public static int 元宝占卜费用;

	public static int 金币占卜费用;

	public static ConcurrentDictionary<int, 制作物品类> 制作物品列表;

	public static ConcurrentDictionary<int, 制药物品类> 制药物品列表;

	public static string[] RanString;

	public static string 贡献元宝命令;

	public static string[] 门派第一称号奖励;

	public static string[] 门派第二称号奖励;

	public static string[] 门派第三称号奖励;

	public static string[] 玫瑰第一名奖励;

	public static string[] 玫瑰第二名奖励;

	public static string[] 玫瑰第三名奖励;

	public static string[] 玫瑰第四名奖励;

	public static string[] 玫瑰第五名奖励;

	public static string[] 地图限制等级;


    public static string[] 强化数量大于发送快报;

	public static string 分区编号;

	public static 攻城战 攻城;

	public static object 地面物品Lock;

	public static object 开箱Lock;

	public static ConcurrentDictionary<int, double> lever;

	public static ConcurrentDictionary<int, 武勋加成类> Wxlever;

	public static ConcurrentDictionary<int, 公告类> 公告;

	public static ConcurrentDictionary<int, 等级奖励类> 等级奖励;

	public static ConcurrentDictionary<int, 假人等级奖励> 假人等级奖励;

	public static ConcurrentDictionary<int, 比武泡点奖励> 比武泡点奖励;

	public static ConcurrentDictionary<int, 大乱斗奖励> 大乱斗奖励;

	public static ConcurrentDictionary<int, 石头属性调整类> 石头属性调整;

	public static ConcurrentDictionary<int, 气功加成属性> 气功加成;

	public static ConcurrentDictionary<int, 物品兑换类> 物品兑换;

	public static ConcurrentDictionary<int, 神器兑换类> 神器兑换;

	public static ConcurrentDictionary<int, 职业系数类> 职业系数数据;

	public static ConcurrentDictionary<long, 地面物品类> ItmeTeM;

	public static ConcurrentDictionary<int, MonSterClss> MonSter;

	public static ConcurrentDictionary<int, ItmeClass> Itme;

	public static ConcurrentDictionary<int, 武功类> TBL_KONGFU;

	public static ConcurrentDictionary<int, 组队Class> W组队;

	public static ConcurrentDictionary<int, 物品回收类> 物品回收数据;

	public static ConcurrentDictionary<int, 装备洗髓> 装备洗髓系统;

	public static ConcurrentDictionary<int, 首爆> 装备首爆;

	public static ConcurrentDictionary<int, 英雄职业武器> 英雄职业武器系统;

	public static ConcurrentDictionary<int, 累计充值礼包> 累计充值称号;

	public static ConcurrentDictionary<string, 冲关地图类> 冲关地图list;

	public static 帮派战_血战 血战;

	public static 帮派战_门战 帮战;

	public static List<ShopClass> Shop;

	public static List<检查物品类> 物品检查;

	public static List<KillClass> Kill;

	public static List<DropClass> Drop;

	public static List<DropClass> Drop_GS;

	public static List<DropClass> BossDrop;

	public static List<OpenClass> Open;

	public static List<MoveClass> Mover;

	public static List<坐标Class> 移动;

	public static ConcurrentDictionary<int, 兑换码Class> 兑换码;

	public static List<攻城怪物> 攻城怪物列表;

	public static List<伏魔洞怪物> 伏魔洞怪物列表;

	public static List<冰宫内城怪物> 冰宫内城怪物列表;

	public static List<活动副本怪物> 活动副本怪物列表;

	public static List<ItemSellClass> 套装数据;

	public static 仙魔大战Class 仙魔大战;

	public static string 势力战胜利奖励物品;

	public static string 势力战参与奖励物品;

	public static string 仙魔大战胜利奖励物品;

	public static string 仙魔大战失败奖励物品;

	public static string 仙魔大战平局奖励物品;

	public static string 天魔胜利奖励物品;

	public static string 天魔失败奖励物品;

	public static string 武器合成需要物品;

	public static string 防具合成需要物品;

	public static List<ChouJiangClass> ChouJiang;

	public static List<累充系统> 累充数据;

	public static ConcurrentDictionary<int, 安全区Class> 地图安全区;

	public static List<坐标Class> 对练区;

	public static List<坐标Class> 仙魔大战区域;

	public static List<坐标Class> 攻城战区域;

	public static List<坐标Class> 势力战区域;

	public static List<坐标Class> 帮战区;

	public static EvePVPClass EVEPVP;

	public static Players 擂台赛甲方;

	public static Players 擂台赛乙方;

	public static List<int> BOSSListTime;

	private static System.Timers.Timer BOSS时间计时器;

	public static bool BOSSTIME;

	public static int Log;

	public static int 验证服务器log;

	public static int jllog;

	public static ScriptClass 脚本;

	public static Connect conn;

	public static int AutGC;

	public static string 冲关地图;

	public static int 每次消耗的数量;

	public static int 每次再造消耗设置;

	public static int 平砍间隔时间;

	public static double 强化一合成率;

	public static double 强化二合成率;

	public static double 强化三合成率;

	public static double 强化四合成率;

	public static double 强化五合成率;

	public static double 强化六合成率;

	public static double 强化七合成率;

	public static double 强化八合成率;

	public static double 强化九合成率;

	public static double 强化十合成率;

	public static double 强化十一合成率;

	public static double 强化十二合成率;

	public static double 强化十三合成率;

	public static double 强化十四合成率;

	public static double 强化十五合成率;

	public static double 水晶取玉符强1;

	public static double 水晶取玉符强2;

	public static double 水晶取玉符强3;

	public static double 水晶取玉符强4;

	public static double 水晶取玉符强5;

	public static double 水晶取玉符强6;

	public static double 水晶取玉符强7;

	public static double 水晶取玉符强8;

	public static double 水晶取玉符强9;

	public static double 水晶取玉符强10;

	public static double 至尊取玉符强11;

	public static double 至尊取玉符强12;

	public static double 至尊取玉符强13;

	public static double 至尊取玉符强14;

	public static double 至尊取玉符强15;

	public static int 升级经验是否开启数据库;

	public static double 升级经验表基数;

	public static string[] 至高无上称号奖励;

	public static string[] 举世无双称号奖励;

	public static string[] 雄霸天下称号奖励;

	public static string[] 孤胆英雄称号奖励;

	public static string[] 英雄豪杰称号奖励;

	public static string[] 全套强12增加属性;

	public static string[] 全套强13增加属性;

	public static string[] 全套强14增加属性;

	public static string[] 全套强15增加属性;

	public static double 刺客攻击倍数;

	public static double 弓手攻击倍数;

	public static int 武器十五阶段添加攻击;

	public static int 武器十六阶段添加攻击;

	public static int 武器十七阶段添加攻击;

	public static int 武器十八阶段添加攻击;

	public static int 武器十九阶段添加攻击;

	public static int 武器二十阶段添加攻击;

	public static string 清理绑定背包命令;

	public static int 讨伐伤害排行1;

	public static int 讨伐伤害排行2;

	public static int 讨伐伤害排行3;

	public static string 讨伐账号排行1;

	public static string 讨伐账号排行2;

	public static string 讨伐账号排行3;

	public static int 是否开启打坐打怪;

	public static double 怪物防御百分比;

	public static double 怪物攻击百分比;

	public static string 换线命令;

	public static string 发红包命令;

	public static string 发红包公告;

	public static string 抢红包命令;

	public static bool 是否有人发红包;

	public static int 红包检测时间;

	public static int 冲关地图模式切换;

	public static int 坐牢系统是否开启;

	public static string 监狱地图;

	public static int 坐牢善恶;

	public static int 坐牢善恶恢复间隔;

	public static int 坐牢恢复善恶值;

	public static string 坐牢回城坐标;

	public static string 坐牢杀人公告;

	public static string 刑满释放公告;

	public static int 斗神称号激活方式;

	public static int 斗神称号需要数量;

	public static int 势力战开始时向其它线广播;

	public static int 同IP势力战不计分;

	public static int 势力战进程;

	public static int 势力战正分数;

	public static int 势力战邪分数;

	public static int 势力战正派参战人数;

	public static int 势力战邪派参战人数;

	public static int 势力战是否开启;

	public static int 势力战开启分;

	public static int 势力战开启秒;

	public static string 势力战设置;

	public static int 势力战开启自动踢人;

	public static string 势力战踢人设置;

	public static int 势力战打死大怪得分;

	public static int 势力战打死小怪得分;

	public static List<KeyValuePair<int, int>> 势力战踢人方案;

	public static ConcurrentDictionary<int, 势力战场次> 所有势力战场次;

	public static int 势力战参加最低转职;

	public static int 势力战参加最高转职;

	public static int 势力战类型;

	public static int 势力战战斗时间;

	public static int 势力战预备时间;

	public static EventClass eve;

	public static ConcurrentDictionary<string, Players> 申请势力人物列表;

	public static int 势力战正派申请人数;

	public static int 势力战邪派申请人数;

	public static string 势力怪暴热血石;

	public static ConcurrentDictionary<string, EventTopClass> EventTop;

	public static ConcurrentDictionary<string, 比武泡点TopClass> 比武泡点Top;

	public static ConcurrentDictionary<string, 大乱斗TopClass> 大乱斗Top;

	public static int 是否开启猜拳活动;

	public static string 回收命令;

	public static string 猜拳命令;

	public static int 石头总元宝数;

	public static int 剪刀总元宝数;

	public static int 布总元宝数;

	public static int 剪刀石头布返还几率;

	public static int 剪刀石头布元宝倍数;

	public static int 剪刀石头布元开奖;

	public static int 猜拳倒计时;

	public static int 猜拳最大下注额;

	public static int 猜拳最小下注额;

	public static 猜拳系统 猜拳;

	public static double 属性一合成率;

	public static double 属性二合成率;

	public static double 属性三合成率;

	public static double 属性四合成率;

	public static int 是否开启别墅邀请系统;

	public static string 别墅邀请所需物品ID;

	public static string 别墅邀请命令;

	public static int 别墅邀请IP限制;

	public static int 别墅邀请人数;

	public static string[] 掉落开盒杀人提示开关;

	public static string[] 掉落开盒杀人提示语言;

	public static string[] 掉落开盒杀人提示颜色;

	public static string 攻城战开启时间星期;

	public static string 势力战开启时间星期;

	public static string 仙魔战开启时间星期;

	public static double 属性阶段一合成率;

	public static double 属性阶段二合成率;

	public static double 属性阶段三合成率;

	public static double 属性阶段四合成率;

	public static double 属性阶段五合成率;

	public static double 属性阶段六合成率;

	public static double 属性阶段七合成率;

	public static double 属性阶段八合成率;

	public static double 属性阶段九合成率;

	public static double 属性阶段十合成率;

	public static double 水晶属性阶段一合成率;

	public static double 水晶属性阶段二合成率;

	public static double 水晶属性阶段三合成率;

	public static double 水晶属性阶段四合成率;

	public static double 水晶属性阶段五合成率;

	public static double 水晶属性阶段六合成率;

	public static double 水晶属性阶段七合成率;

	public static double 水晶属性阶段八合成率;

	public static double 水晶属性阶段九合成率;

	public static double 水晶属性阶段十合成率;

	public static int 二级帮派升级金钱;
 	public static int 二级帮派升级元宝;
	public static int 三级帮派升级金钱;

	public static int 三级帮派升级元宝;
	public static int 四级帮派升级金钱;

 	public static int 四级帮派升级元宝;
	public static int 五级帮派升级金钱;

	public static int 五级帮派升级元宝;
	public static int 六级帮派升级金钱;

	public static int 六级帮派升级元宝;
	public static int 七级帮派升级金钱;
	
 	public static int 七级帮派升级元宝;
   

    

   

    

    

   

    public static int 武勋掉落限制;

	public static int 武勋掉落数量;

	public static int 翅膀造型是否切换;

	public static int 背后翅膀是否开启;

	public static int 比武泡点进程;

	public static int 比武泡点倒计时;

	public static int 比武泡点总时间;

	public static int 比武泡点是否开启;

	public static double 比武场经验基数;

	public static int 比武泡点元宝基数;

	public static int 比武泡点元宝时间;

	public static int 比武泡点武勋基数;

	public static int 比武泡点金钱基数;

	public static int 比武泡点开启小时;

	public static int 比武泡点开启分;

	public static int 比武泡点开启秒;

	public static int 是否开启活动禁止加血;

	public static string 比武泡点开启时间星期;

	public static 比武泡点系统 比武泡点;

	public static int 极限比武是否开启;

	public static int 极限比武开启小时;

	public static int 极限比武开启分;

	public static int 极限比武开启秒;

	public static int 极限比武最大人数;

	public static string 极限比武开启时间星期;

	public static string 极限比武胜利奖励物品;

	public static string 极限比武参与奖励物品;

	public static string sql;

	public static 红包雨系统 红包雨;

	public static string 红包雨开启时间星期;

	public static string 天降红包奖励物品;

	public static int 红包雨是否开启;

	public static int 红包雨开启小时;

	public static int 红包雨开启分钟;

	public static int 红包雨开启秒钟;

	public static int 红包雨倒计时;

	public static int 红包雨总时间;

	public static int 红包雨开启元宝;

	public static int 红包雨最小元宝;

	public static int 红包雨最大元宝;

	public static int 红包雨开启武勋;

	public static int 红包雨最小武勋;

	public static int 红包雨最大武勋;

	public static int 红包雨开启掉落;

	public static int 红包雨进程;

	public static bool 开启快速连接;

	public static int 快速连接限制次数;

	public static int 快速连接限制时间;

	public static BossClass 南林钟离;

	public static int 南林钟离开关;

	public static string 南林钟离时间;

	public static int 南林钟离总时间;

	public static string 南林钟离一参数;

	public static int 公告刷新时间;

	public static int 排名刷新时间;

	public static 大乱斗系统 大乱斗;

	public static string 大乱斗开启时间星期;

	public static int 大乱斗进程;

	public static int 大乱斗倒计时;

	public static int 大乱斗总时间;

	public static int 大乱斗是否开启;

	public static int 大乱斗开启小时;

	public static int 大乱斗开启分;

	public static int 大乱斗开启秒;

	public static int 大乱斗进入需要武勋;

	public static string 大乱斗参与奖励物品;

	public static int 阎王爆范围爆炸是否开启;

	public static int 阎王爆爆炸距离;

	public static int 阎王爆伤害降低百分比;

	public static SqlConnectInfo 百宝阁数据库连接信息;

	public static INewWebShopService 百宝阁服务;

	public static ICompetitionServeice 极限比武台服务;

	public static int 内置百宝阁开关;

	public static int 假人加入门派;

	public static int 假人自动结婚;

	public static string[] 假人帮派数据一;

	public static string[] 假人帮派数据二;

	public static string[] 假人帮派数据三;

	public static string[] 假人帮派数据四;

	public static string[] 假人帮派数据五;

	public static void 讨伐发奖励()
	{
		try
		{
			讨伐伤害排行1 = 0;
			讨伐伤害排行2 = 0;
			讨伐伤害排行3 = 0;
			讨伐账号排行1 = "";
			讨伐账号排行2 = "";
			讨伐账号排行3 = "";
			int num = 0;
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.讨伐累计伤害 >= 1)
				{
					num++;
				}
			}
			foreach (Players value2 in allConnectedChars.Values)
			{
				value2.是否排行 = 0;
			}
			if (num >= 1)
			{
				全局提示("系统提示", 20, "本届副本战参加人数:" + num + "人");
				int num2 = 0;
				for (num2 = 0; num2 < 31; num2++)
				{
					if (num2 == 1)
					{
						foreach (Players value3 in allConnectedChars.Values)
						{
							if (value3.讨伐累计伤害 >= 讨伐伤害排行1 && value3.讨伐累计伤害 > 0 && value3.是否排行 == 0)
							{
								讨伐伤害排行1 = value3.讨伐累计伤害;
								讨伐账号排行1 = value3.Userid;
							}
						}
						foreach (Players value4 in allConnectedChars.Values)
						{
							if (讨伐账号排行1 == value4.Userid)
							{
								value4.是否排行 = 1;
							}
						}
					}
					if (num2 == 2)
					{
						foreach (Players value5 in allConnectedChars.Values)
						{
							if (value5.讨伐累计伤害 >= 讨伐伤害排行2 && value5.讨伐累计伤害 > 0 && value5.是否排行 == 0)
							{
								讨伐伤害排行2 = value5.讨伐累计伤害;
								讨伐账号排行2 = value5.Userid;
							}
						}
						foreach (Players value6 in allConnectedChars.Values)
						{
							if (讨伐账号排行2 == value6.Userid)
							{
								value6.是否排行 = 1;
							}
						}
					}
					if (num2 != 3)
					{
						continue;
					}
					foreach (Players value7 in allConnectedChars.Values)
					{
						if (value7.讨伐累计伤害 >= 讨伐伤害排行3 && value7.讨伐累计伤害 > 0 && value7.是否排行 == 0)
						{
							讨伐伤害排行3 = value7.讨伐累计伤害;
							讨伐账号排行3 = value7.Userid;
						}
					}
					foreach (Players value8 in allConnectedChars.Values)
					{
						if (讨伐账号排行3 == value8.Userid)
						{
							value8.是否排行 = 1;
						}
					}
				}
			}
			if (讨伐账号排行1.Length >= 1)
			{
				Players players = 检查玩家(讨伐账号排行1);
				if (players != null)
				{
					int num3 = players.得到包裹空位位置();
					if (num3 >= 0)
					{
						全局提示("系统提示", 20, "恭喜玩家[" + players.UserName + "]赢得本局副本之战第一名");
						players.增加物品带属性(1000001595, num3, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					}
					else
					{
						players.系统提示("没有空位，本次没有获得奖励！", 10, "提示");
					}
				}
			}
			if (讨伐账号排行2.Length >= 1)
			{
				Players players2 = 检查玩家(讨伐账号排行2);
				if (players2 != null)
				{
					int num4 = players2.得到包裹空位位置();
					if (num4 >= 0)
					{
						players2.增加物品带属性(1000001595, num4, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
						全局提示("系统提示", 20, "恭喜玩家[" + players2.UserName + "]赢得本局副本之战第二名");
					}
					else
					{
						players2.系统提示("没有空位，本次没有获得奖励！", 10, "提示");
					}
				}
			}
			if (讨伐账号排行3.Length < 1)
			{
				return;
			}
			Players players3 = 检查玩家(讨伐账号排行3);
			if (players3 != null)
			{
				int num5 = players3.得到包裹空位位置();
				if (num5 >= 0)
				{
					全局提示("系统提示", 20, "恭喜玩家[" + players3.UserName + "]赢得本局副本之战第三名");
					players3.增加物品带属性(1000001595, num5, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
				else
				{
					players3.系统提示("没有空位，本次没有获得奖励！", 10, "提示");
				}
			}
		}
		catch
		{
			Form1.WriteLine(1, "发奖励错误 ");
		}
	}

	public static void 更新所有排行数据()
	{
		try
		{
			武林血战排行数据.Clear();
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  top  10  *  FROM  荣誉系统排行  where  FLD_类型  =  4 and FLD_FQ='" + 分区编号 + "' Order  By  FLD_分数  Desc");
			if (dBToDataTable != null && dBToDataTable.Rows.Count != 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					武林血战排行数据.TryAdd(i, new 荣誉Class
					{
						人物名 = dBToDataTable.Rows[i]["FLD_人物名"].ToString(),
						帮派 = dBToDataTable.Rows[i]["FLD_帮派"].ToString(),
						职业 = (int)dBToDataTable.Rows[i]["FLD_职业"],
						势力 = (int)dBToDataTable.Rows[i]["FLD_势力"],
						等级 = (int)dBToDataTable.Rows[i]["FLD_等级"],
						分数 = (int)dBToDataTable.Rows[i]["FLD_分数"],
						类型 = 4
					});
				}
			}
			dBToDataTable.Dispose();
			杀人排行数据.Clear();
			DataTable dBToDataTable2 = DBA.GetDBToDataTable("SELECT  top  10  *  FROM  荣誉系统排行  where  FLD_类型  =  6  and FLD_FQ='" + 分区编号 + "' Order  By  FLD_分数  Desc");
			if (dBToDataTable2 != null && dBToDataTable2.Rows.Count != 0)
			{
				for (int j = 0; j < dBToDataTable2.Rows.Count; j++)
				{
					杀人排行数据.TryAdd(j, new 荣誉Class
					{
						人物名 = dBToDataTable2.Rows[j]["FLD_人物名"].ToString(),
						帮派 = dBToDataTable2.Rows[j]["FLD_帮派"].ToString(),
						职业 = (int)dBToDataTable2.Rows[j]["FLD_职业"],
						势力 = (int)dBToDataTable2.Rows[j]["FLD_势力"],
						等级 = (int)dBToDataTable2.Rows[j]["FLD_等级"],
						分数 = (int)dBToDataTable2.Rows[j]["FLD_分数"],
						类型 = 6
					});
				}
			}
			dBToDataTable2.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "更新所有排行数据错误" + ex.Message);
		}
	}

	public static void 发送全服大武圣称号获得消息(string name, int zx)
	{
		try
		{
			byte[] array = Converter.hexStringToByte("AA55BA0060000051B400010000000A000000BC17000000000000000000000000000000000000000000000000000033300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			byte[] bytes = Encoding.Default.GetBytes(name);
			Buffer.BlockCopy(bytes, 0, array, 46, bytes.Length);
			if (zx == 2)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(6077), 0, array, 18, 2);
			}
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.Client != null)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(value.人物全服ID), 0, array, 4, 2);
					value.Client.Send(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.BuildErrorInfo("World发送全服武勋称号获得消息数据", name, 0, ex.Message)); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static void 发送全服武勋称号获得消息(string name, int zx)
	{
		try
		{
			byte[] array = Converter.hexStringToByte("AA55C200E8040051B400010000000A00000064110000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			byte[] bytes = Encoding.Default.GetBytes(name);
			Buffer.BlockCopy(bytes, 0, array, 46, bytes.Length);
			if (zx == 1)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(4451), 0, array, 18, 2);
			}
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.Client != null)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(value.人物全服ID), 0, array, 4, 2);
					value.Client.Send(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.BuildErrorInfo("World发送全服武勋称号获得消息数据", name, 0, ex.Message)); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static int 计算玩家下注结果(int 分数1, int 分数2, int 专场代码)
	{
		int num = RNG.Next(0, 9) + 分数1 + 分数2;
		try
		{
			int num2 = 0;
			int num3 = 0;
			foreach (Players value in allConnectedChars.Values)
			{
				if (!value.Client.挂机 && value.是否押注 && value.押注专场代码 == 专场代码)
				{
					if (value.押注单双 == "单")
					{
						num2++;
					}
					else
					{
						num3++;
					}
				}
			}
			if (num % 2 == 0)
			{
				if (num3 != 0 && num2 != 0)
				{
					int num4 = (int)((double)(num2 * 允许玩家押注数量) * (1.0 - 场地佣金百分比) / (double)num3);
					foreach (Players value2 in allConnectedChars.Values)
					{
						if (!value2.Client.挂机 && value2.是否押注 && value2.押注专场代码 == 专场代码)
						{
							if (value2.押注单双 == "双")
							{
								value2.检察元宝数据(num4, 1, "下注");
								value2.系统提示("恭喜你，这回出的是【双】数。", 13, "系统提示");
								RxjhClass.百宝记录(value2.Userid, value2.UserName, 0.0, "押注赢", 1, num4, 分区编号);
							}
							else
							{
								value2.检察元宝数据(允许玩家押注数量, 0, "下注");
								value2.系统提示("手气真差，这回出的是【双】数。", 13, "系统提示");
								RxjhClass.百宝记录(value2.Userid, value2.UserName, 0.0, "押注输", 1, 允许玩家押注数量, 分区编号);
							}
							value2.押注单双 = string.Empty;
							value2.押注专场代码 = 0;
							value2.是否押注 = false;
							value2.元宝账户状态 = false;
						}
					}
				}
				else if (num2 == 0)
				{
					foreach (Players value3 in allConnectedChars.Values)
					{
						if (!value3.Client.挂机 && value3.是否押注 && value3.押注专场代码 == 专场代码)
						{
							if (value3.押注单双 == "双")
							{
								value3.检察元宝数据((int)((double)允许玩家押注数量 * 场地佣金百分比), 0, "下注");
								value3.系统提示("由于本场无人压【单】数,系统将扣除压【双】数玩家手续费" + (int)((double)允许玩家押注数量 * 场地佣金百分比) + "元宝！", 13, "系统提示");
								RxjhClass.百宝记录(value3.Userid, value3.UserName, 0.0, "押注手续费", 1, (int)((double)允许玩家押注数量 * 场地佣金百分比), 分区编号);
							}
							value3.押注单双 = string.Empty;
							value3.押注专场代码 = 0;
							value3.是否押注 = false;
							value3.元宝账户状态 = false;
						}
					}
				}
				else if (num3 == 0)
				{
					foreach (Players value4 in allConnectedChars.Values)
					{
						if (!value4.Client.挂机 && value4.是否押注 && value4.押注专场代码 == 专场代码)
						{
							if (value4.押注单双 == "单")
							{
								value4.检察元宝数据(允许玩家押注数量, 0, "下注");
								value4.系统提示("手气真差,这回出的是【双】数。", 13, "系统提示");
								RxjhClass.百宝记录(value4.Userid, value4.UserName, 0.0, "押注输", 1, 允许玩家押注数量, 分区编号);
							}
							value4.押注单双 = string.Empty;
							value4.押注专场代码 = 0;
							value4.是否押注 = false;
							value4.元宝账户状态 = false;
						}
					}
				}
			}
			else if (num2 != 0 && num3 != 0)
			{
				int num5 = (int)((double)(num3 * 允许玩家押注数量) * (1.0 - 场地佣金百分比) / (double)num2);
				foreach (Players value5 in allConnectedChars.Values)
				{
					if (!value5.Client.挂机 && value5.是否押注 && value5.押注专场代码 == 专场代码)
					{
						if (value5.押注单双 == "单")
						{
							value5.检察元宝数据(num5, 1, "下注");
							value5.系统提示("恭喜你，这回出的是【单】数。", 13, "系统提示");
							RxjhClass.百宝记录(value5.Userid, value5.UserName, 0.0, "押注赢", 1, num5, 分区编号);
						}
						else
						{
							value5.检察元宝数据(允许玩家押注数量, 0, "下注");
							value5.系统提示("手气真差，这回出的是【单】数。", 13, "系统提示");
							RxjhClass.百宝记录(value5.Userid, value5.UserName, 0.0, "押注输", 1, 允许玩家押注数量, 分区编号);
						}
						value5.押注单双 = string.Empty;
						value5.押注专场代码 = 0;
						value5.是否押注 = false;
						value5.元宝账户状态 = false;
					}
				}
			}
			else if (num3 == 0)
			{
				foreach (Players value6 in allConnectedChars.Values)
				{
					if (!value6.Client.挂机 && value6.是否押注 && value6.押注专场代码 == 专场代码)
					{
						if (value6.押注单双 == "单")
						{
							value6.检察元宝数据((int)((double)允许玩家押注数量 * 场地佣金百分比), 0, "下注");
							value6.系统提示("由于本场无人压【双】数,系统将扣除压【单】数玩家手续费" + (int)((double)允许玩家押注数量 * 场地佣金百分比) + "元宝。", 13, "系统提示");
							RxjhClass.百宝记录(value6.Userid, value6.UserName, 0.0, "押注手续费", 1, (int)((double)允许玩家押注数量 * 场地佣金百分比), 分区编号);
						}
						value6.押注单双 = string.Empty;
						value6.押注专场代码 = 0;
						value6.是否押注 = false;
						value6.元宝账户状态 = false;
					}
				}
			}
			else if (num2 == 0)
			{
				foreach (Players value7 in allConnectedChars.Values)
				{
					if (!value7.Client.挂机 && value7.是否押注 && value7.押注专场代码 == 专场代码)
					{
						if (value7.押注单双 == "双")
						{
							value7.检察元宝数据(允许玩家押注数量, 0, "下注");
							value7.系统提示("手气真差，这回出的是【单】数。", 13, "系统提示");
							RxjhClass.百宝记录(value7.Userid, value7.UserName, 0.0, "押注输", 1, 允许玩家押注数量, 分区编号);
						}
						value7.押注单双 = string.Empty;
						value7.押注专场代码 = 0;
						value7.是否押注 = false;
						value7.元宝账户状态 = false;
					}
				}
			}
			return num;
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "押注系统", $"专场代码: {专场代码}, 结果: {num}");
			return num;
		}
	}

	public static Players 检查玩家(string Userid)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (value.Userid == Userid)
			{
				return value;
			}
		}
		return null;
	}

	public static Players 检查玩家世界ID(int ID)
	{
		Players value;
		return (!allConnectedChars.TryGetValue(ID, out value)) ? null : value;
	}

	public static Players 检查玩家name(string Username)
	{
		// 2025-0618 EVIAS 使用高性能索引查找，从O(n)优化到O(1)
		var player = PlayerIndexManager.Instance.FindByUserName(Username);
		if (player != null)
		{
			return player;
		}

		// 备用方案：如果索引中没有找到，回退到原始查找方式
		foreach (Players value in allConnectedChars.Values)
		{
			if (value.UserName == Username)
			{
				return value;
			}
		}
		return null;
	}

	public static void 发送特殊公告(string 内容, int 颜色, string 提示)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机)
			{
				value.系统提示(内容, 颜色, 提示);
			}
		}
	}

	public static void 发送玫瑰公告(string msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机)
			{
				value.送花公告(msg);
			}
		}
	}

	public static void Process狮子吼Queue()
	{
		if (狮子吼List.Count > 0)
		{
			狮子吼Class 狮子吼Class2 = (狮子吼Class)狮子吼List.Dequeue();
			发送狮子吼消息广播数据(狮子吼Class2.FLD_INDEX, 狮子吼Class2.UserName, 狮子吼Class2.TxtId, 狮子吼Class2.Txt);
		}
	}

	private int 得到气功ID(int i, int Player_Job)
	{
		int result = 0;
		foreach (气功加成属性 value in 气功加成.Values)
		{
			if (value.FLD_JOB == Player_Job && value.FLD_INDEX == i)
			{
				result = value.FLD_PID;
			}
		}
		return result;
	}

	public static void 发送狮子吼消息广播数据(int 人物全服ID, string name, int msgid, string msg)
	{
		try
		{
			byte[] array = Converter.hexStringToByte("AA55B6002D016600A800CC00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			array[10] = 35;
			byte[] bytes = Encoding.Default.GetBytes(name);
			byte[] bytes2 = Encoding.Default.GetBytes(msg);
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(人物全服ID), 0, array, 4, 2);
			array[34] = (byte)msgid;
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.Client != null && !value.Client.挂机)
				{
					value.Client.Send(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.BuildErrorInfo("World发送狮子吼消息广播数据", name, 人物全服ID, FastString.Concat("[", msg, "]", ex.Message))); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static string 得到职业文本(int player_job)
	{
	
        return player_job switch
		{
			1 => "刀客", 
			2 => "剑客", 
			3 => "枪客", 
			4 => "弓箭手", 
			5 => "医生", 
			6 => "刺客", 
			7 => "琴师", 
			8 => "韩飞官", 
			9 => "谭花灵", 
			10 => "格斗家", 
			11 => "梅柳真", 
			12 => "卢风郎", 
			13 => "东陵神女", 
			_ => string.Empty, 
		};
	
	}

	public static void 发送全服狮子吼消息广播数据(int 人物全服ID, string name, int msgid, string msg, int 线, int map, int 样式)
	{
		try
		{
			string hex = "AA55B600E5026600A800260000000000000000000000000000000000000000000000560000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000065000000000000000000000055AA";
			byte[] array = Converter.hexStringToByte(hex);
			array[10] = (byte)样式;
			Buffer.BlockCopy(BitConverter.GetBytes(线), 0, array, 169, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(map), 0, array, 174, 4);
			byte[] bytes = Encoding.Default.GetBytes(name);
			byte[] bytes2 = Encoding.Default.GetBytes(msg);
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(人物全服ID), 0, array, 4, 2);
			array[34] = (byte)msgid;
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.Client != null && !value.Client.挂机)
				{
					value.Client.Send(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.BuildErrorInfo("World发送狮子吼消息广播数据", name, 人物全服ID, FastString.Concat("[", msg, "]", ex.Message))); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static void 发送传音消息(int 人物全服ID, string name, string ToName, string msg, int msgType, string StringHex)
	{
		try
		{
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.UserName == ToName)
				{
					if (value.Config.传音 == 0)
					{
						value.系统提示("您有传音消息,请打开传音开关。", 50, "系统提示");
					}
					else
					{
						value.发送传音消息(name, 人物全服ID, value, msg, msgType);
					}
					break;
				}
			}
		}
		catch
		{
		}
	}

	public static void 发送同盟聊天(string bpname, string username, string msg, int 服务器组)
	{
		try
		{
			string text = RxjhClass.取得门派联盟盟主(bpname);
			if (text == "")
			{
				return;
			}
			string hex = "AA55BE000F276600B8002001B60000000000000000000000000000000000000000000C30000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			byte[] array = Converter.hexStringToByte(hex);
			array[10] = 32;
			array[33] = 1;
			byte[] bytes = Encoding.Default.GetBytes(username);
			byte[] bytes2 = Encoding.Default.GetBytes(msg);
			array[34] = (byte)bytes2.Length;
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			byte[] bytes3 = Encoding.Default.GetBytes(bpname);
			Buffer.BlockCopy(bytes3, 0, array, 178, bytes3.Length);
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.门派联盟盟主 == text && value.Client != null)
				{
					value.Client.Send多包(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.BuildErrorInfo("发送同盟消息", username, 0, FastString.Concat("[", bpname, "]", ex.Message))); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static void 发送帮派消息(string BpName, byte[] data, int length)
	{
		try
		{
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.帮派名字 == BpName && value.Client != null)
				{
					value.Client.Send(data, length);
				}
			}
		}
		catch
		{
		}
	}

	public static int Add组队(组队Class 队)
	{
		W组队Id++;
		W组队.TryAdd(W组队Id, 队);
		return W组队Id;
	}

	static World()
	{
		所有可组合道具 = new Dictionary<int, 道具组合类>();
        装备进阶 = new Dictionary<int, 材料进阶装备>();   //新增装备进阶 EVIAS
        装备分解 = new Dictionary<int, 装备分解材料>();   //EVIAS
        讨伐副本最少人数 = 10;
		讨伐副本最多人数 = 30;
		讨伐副本时长 = 60;
		天魔神宫占领者 = string.Empty;
		门战占领者 = string.Empty;
		门派联盟申请状态 = new List<门派联盟申请状态>();
		参加红包列表 = new List<Players>();
		申请仙魔大战人物列表 = new ConcurrentDictionary<string, Players>();
		Iplist = new ConcurrentDictionary<int, Players>();
		privateTeams = new ConcurrentDictionary<string, Players>();
		红包系统 = new 发红包();
		百宝阁抽奖物品类list = new ConcurrentDictionary<int, 百宝阁类>();
		信任连接IP = "";
		修炼药品 = "1008001190;1008001327;1008001502;1008001503;1008001504";
		遗忘药品 = "1008001182;1008001183;1008001184;1008001328";
		皮皮岛药品 = "1008001513";
		世外药品 = "1008002585;1008002586;1008002587";
		物品回收数据 = new ConcurrentDictionary<int, 物品回收类>();
		装备洗髓系统 = new ConcurrentDictionary<int, 装备洗髓>();
		装备首爆 = new ConcurrentDictionary<int, 首爆>();
		英雄职业武器系统 = new ConcurrentDictionary<int, 英雄职业武器>();
		累计充值称号 = new ConcurrentDictionary<int, 累计充值礼包>();
		冲关地图list = new ConcurrentDictionary<string, 冲关地图类>();
		地图安全区 = new ConcurrentDictionary<int, 安全区Class>();
		冲关地图 = "";
		是否有人发红包 = false;
		红包检测时间 = 10;
		坐牢杀人公告 = "{0}杀人啦！";
		刑满释放公告 = "{0}刑满释放了！";
		极限比武台服务 = CompetitionServeice.Create(new CompetitionWorld(), 极限比武最大人数);
		假人自动结婚 = 0;
		假人加入门派 = 0;
		内置百宝阁开关 = 1;
		阎王爆爆炸距离 = 15;
		阎王爆伤害降低百分比 = 100;
		阎王爆范围爆炸是否开启 = 0;
		大乱斗进入需要武勋 = 0;
		大乱斗开启小时 = 0;
		大乱斗开启分 = 0;
		大乱斗开启秒 = 0;
		大乱斗进程 = 0;
		大乱斗倒计时 = 3;
		大乱斗总时间 = 30;
		大乱斗是否开启 = 0;
		快速连接限制次数 = 5;
		快速连接限制时间 = 1;
		红包雨是否开启 = 0;
		红包雨开启小时 = 0;
		红包雨开启分钟 = 0;
		红包雨开启秒钟 = 0;
		红包雨倒计时 = 0;
		红包雨总时间 = 0;
		红包雨最小元宝 = 0;
		红包雨最大元宝 = 0;
		红包雨最小武勋 = 0;
		红包雨最大武勋 = 0;
		红包雨进程 = 0;
		红包雨开启元宝 = 0;
		红包雨开启武勋 = 0;
		红包雨开启掉落 = 0;
		开启数据包限制 = 0;
		同类型数据包处理间隔 = 100;
		比武泡点开启小时 = 0;
		比武泡点开启分 = 0;
		比武泡点开启秒 = 0;
		比武泡点进程 = 0;
		比武泡点倒计时 = 3;
		比武泡点总时间 = 30;
		比武泡点是否开启 = 0;
		是否开启活动禁止加血 = 0;
		极限比武是否开启 = 0;
		极限比武开启小时 = 0;
		极限比武开启分 = 0;
		极限比武开启秒 = 0;
		极限比武最大人数 = 100;
		限制职业一 = 0;
		限制职业一等级 = 55;
		限制职业一数量 = 3;
		限制职业一降低百分比 = 0.0;
		限制职业二 = 0;
		限制职业二等级 = 55;
		限制职业二数量 = 3;
		限制职业二降低百分比 = 0.0;
		强化合成概率周否开启 = 0;
		强化合成概率周开启小时 = 18;
		强化合成概率周结束小时 = 24;
		强化合成概率周分 = 0;
		强化合成概率周秒 = 0;
		强化合成概率周倍数 = 0.2;
		是否开启强化合成概率周 = 0;
		签到命令 = "!签到";
		签到奖励元宝数 = 200;
		签到奖励武勋数 = 1000;
		拍卖系统是否开启 = 1; // 2025-05-19: 寄售改为拍卖
		是否拍卖绑定装备 = 0; // 2025-05-19: 寄售改为拍卖
		拍卖物品锁定 = ""; // 2025-05-19: 寄售改为拍卖
		最大拍卖数量 = 10; // 2025-05-19: 新增拍卖配置
		拍卖持续天数 = 7; // 2025-05-19: 新增拍卖配置
		拍卖手续费比例 = 0.05; // 2025-05-19: 新增拍卖配置
		拍卖自动结束倍数 = 2;
		拍卖超价结束时间 = 10;
		拍卖记录清理天数 = 7;

        // 2025-06-14: EVIAS 兑换配置默认值
        钻石兑换元宝是否开启 = 0; //EVIAS 兑换
        元宝兑换钻石是否开启 = 0; //EVIAS 兑换
        钻石兑换元宝比例 = 1000;
        元宝兑换钻石比例 = 2000;

        // 2025-06-14: 抽奖价格配置默认值
        单抽价格 = 1000;
        十抽价格 = 10000;
		转换正邪命令 = "!转换正邪";
		转换正邪功能是否开启 = 0;
		转换正邪需要元宝数量 = 2000;
		转换性别命令 = "!转换性别";
		转换性别功能是否开启 = 0;
		转换性别需要元宝数量 = 2000;
		路费命令 = "!领取路费";
        在线奖励命令 = "!领取在线奖励";  //0420
        追杀命令 = "!追杀";
		追杀需要元宝数量 = 1000;
		限制气功点数 = 60;
		限制气功百分比 = 0.3;
		转换职业命令 = "!转换职业";
		转换职业功能是否开启 = 0;
		转换职业需要元宝数量 = 2000;

        翅膀造型是否切换 = 0;
		背后翅膀是否开启 = 0;
		武勋掉落限制 = 1500000;
		武勋掉落数量 = 5;
		二级帮派升级金钱 = 1;
		三级帮派升级金钱 = 2;
		四级帮派升级金钱 = 3;
		五级帮派升级金钱 = 5;
		六级帮派升级金钱 = 8;
		七级帮派升级金钱 = 15;
        二级帮派升级元宝 = 10;
        三级帮派升级元宝 = 50;
        四级帮派升级元宝 = 100;
        五级帮派升级元宝 = 500;
        六级帮派升级元宝 = 1000;
        七级帮派升级元宝 = 2000;
        属性阶段一合成率 = 100.0;
		属性阶段二合成率 = 95.0;
		属性阶段三合成率 = 90.0;
		属性阶段四合成率 = 60.0;
		属性阶段五合成率 = 40.0;
		属性阶段六合成率 = 25.0;
		属性阶段七合成率 = 15.0;
		属性阶段八合成率 = 5.0;
		属性阶段九合成率 = 5.0;
		属性阶段十合成率 = 5.0;
		水晶属性阶段一合成率 = 100.0;
		水晶属性阶段二合成率 = 95.0;
		水晶属性阶段三合成率 = 90.0;
		水晶属性阶段四合成率 = 60.0;
		水晶属性阶段五合成率 = 40.0;
		水晶属性阶段六合成率 = 25.0;
		水晶属性阶段七合成率 = 15.0;
		水晶属性阶段八合成率 = 5.0;
		水晶属性阶段九合成率 = 5.0;
		水晶属性阶段十合成率 = 5.0;
		攻城战开启时间星期 = "";
		势力战开启时间星期 = "";
		仙魔战开启时间星期 = "";
		别墅邀请人数 = 10;
		别墅邀请IP限制 = 1;
		是否开启别墅邀请系统 = 0;
		别墅邀请所需物品ID = "";
		别墅邀请命令 = "!邀请";
		属性一合成率 = 100.0;
		属性二合成率 = 100.0;
		属性三合成率 = 100.0;
		属性四合成率 = 100.0;
		是否开启猜拳活动 = 0;
		猜拳命令 = "!离线打怪";
		回收命令 = "!回收物品";
		石头总元宝数 = 0;
		剪刀总元宝数 = 0;
		布总元宝数 = 0;
		剪刀石头布返还几率 = 200;
		剪刀石头布元开奖 = 0;
		剪刀石头布元宝倍数 = 3;
		猜拳倒计时 = 3;
		猜拳最大下注额 = 50000;
		猜拳最小下注额 = 500;
		内挂打怪说话内容 = "我爱热血江湖";
		离线挂机打怪范围 = 0;
		膜拜大神时间间隔 = DateTime.Now;
		道具锁开关 = 0;
		邮件物品数量开关 = 0;
		邮件物品交易数量 = 0;
		邮件物品强化数量 = 0;
		邮件传书杀人次数 = 0;
		天魔神宫更新时间 = DateTime.Now;
		攻城战胜利占领时间 = 2;
		内挂打怪说话时间 = 0;
		自动存取时间 = 0;
		群体辅助组队范围 = 400;
		讨伐队伍 = new ConcurrentDictionary<int, 讨伐战队伍类>();
		讨伐副本时长 = 60;
		充值榜第一名 = string.Empty;
		充值榜第二名 = string.Empty;
		充值榜第三名 = string.Empty;
		充值榜第四名 = string.Empty;
		充值榜第五名 = string.Empty;
		充值榜第六名 = string.Empty;
		充值榜第七名 = string.Empty;
		充值榜第八名 = string.Empty;
		充值榜第九名 = string.Empty;
		充值榜第十名 = string.Empty;
		讨伐队伍名 = string.Empty;
		移动速度 = new string[5];
		限制连环飞舞确认时间 = 25;
		MainServer = 1;
		Droplog = false;
		离线数量 = 0;
		假人数量 = 0;
		云挂机数量 = 0;
		假人开商店 = 0;
		假人关商店 = 0;
		Key1 = "192.168.0.4";
		Key2 = "192.168.0.4";
		自动换端口同步变量 = false;
		AllItmelog = 1;
		AutGC = 1;
		开启死亡无敌 = 0;
		开启人物删除 = 0;
		禁止PK = 0;
		是否开启共用银币市场 = 0;
		当前是否是银币线路 = 0;
		交易等级限制 = 1;
		进店等级限制 = 1;
		丢物品等级限制 = 1;
		开箱保底箱子编号 = 0;
		开箱保底次数 = 0;
		活动开启中 = 1;
		打开换线线路1 = 0;
		打开换线线路2 = 0;
		打开换线线路3 = 0;
		打开换线线路4 = 0;
		打开换线线路5 = 0;
		打开换线线路6 = 0;
		打开换线线路7 = 0;
		打开换线线路8 = 0;
		打开换线线路9 = 0;
		打开换线线路10 = 0;
		打开换线线路11 = 0;
		打开换线线路12 = 0;
		打开换线线路13 = 0;
		打开换线线路14 = 0;
		打开换线线路15 = 0;
		打开换线线路16 = 0;
		打开换线线路17 = 0;
		打开换线线路18 = 0;
		打开换线线路19 = 0;
		打开换线线路20 = 0;
		武器宝珠增加属性类型 = 1;
		武器宝珠增加属性值 = 5;
		衣服宝珠增加属性类型 = 1;
		衣服宝珠增加属性值 = 5;
		护手宝珠增加属性类型 = 1;
		护手宝珠增加属性值 = 5;
		鞋子宝珠增加属性类型 = 1;
		鞋子宝珠增加属性值 = 5;
		内甲宝珠增加属性类型 = 1;
		内甲宝珠增加属性值 = 5;
		允许挂机 = 0;
		报错踢号次数 = 0;
		死亡不复活踢号时间 = 3600;
		在线奖励需要小时数 = 480; // EVIAS CDK在线奖励默认值
		野外BOSS开关 = 0;
		开启吃药泡点加成 = 0;
		商店买东西时间 = 1;
		交易中物品时间 = 1;
		百宝买东西时间 = 1;
		仓库存取需时间 = 1;
		野外BOSS时间 = "";
		野外BOSS配置 = "";
		BOSS攻城倒计时 = 3;
		BOSS攻城时间 = 15;
		BOSS攻城是否开启 = 0;
		BOSS攻城开启小时 = 20;
		BOSS攻城开启分 = 0;
		BOSS攻城开启秒 = 0;
		BOSS攻城地图编号 = 101;
		世界BOSS攻城是否开启 = 0;
		世界BOSS攻城开启小时 = 19;
		世界BOSS攻城开启分 = 0;
		世界BOSS攻城开启秒 = 0;
		世界BOSS攻城倒计时 = 0;
		世界BOSS攻城时间 = 0;
		世界BOSS怪物ID = 16404;
		世界BOSS出现地图 = 101;
		世界BOSS坐标X = 0;
		世界BOSS坐标Y = 0;
		连续快速攻击次数 = 1;
		连续攻击有效时间 = 1000;
		非法攻击外挂操作 = 0;
		AlWorldlog = true;
		Process = false;
		lockLogin = new object();
		AsyncLocksw = new object();
		装备检测list = new ConcurrentDictionary<int, 装备检测类>();
		list = new ConcurrentDictionary<int, NetState>();
		普通气功ID = new List<int>();
		反气功ID = new List<int>();				   //24.0 EVIAS 新增反气功ID列表初始化
		反气功List = new Dictionary<int, 反气功类>(); //24.0 EVIAS 新增反气功配置初始化
		仙魔Top = new ConcurrentDictionary<string, 仙魔大战top>();
		allConnectedChars = new ConcurrentDictionary<int, Players>();
		禁言列表 = new ConcurrentDictionary<string, string>();
		升天气功List = new ConcurrentDictionary<int, 升天气功总类>();
		任务list = new ConcurrentDictionary<int, 任务类>();
		Db = new ConcurrentDictionary<string, DbClass>();
		m_Disposed = Queue.Synchronized(new Queue());
		SqlPool = Queue.Synchronized(new Queue());
		Map = new ConcurrentDictionary<int, MapClass>();
		帮战list = new ConcurrentDictionary<int, 帮战Class>();
		帮战Namelist = new ConcurrentDictionary<int, 帮战Class>();
		Maplist = new ConcurrentDictionary<int, string>();
		NpcList = new ConcurrentDictionary<int, NpcClass>();
		婚礼list = new ConcurrentDictionary<int, Wedding>();
		门派成员list = new ConcurrentDictionary<int, 门派成员>();
		百宝阁属性物品类list = new ConcurrentDictionary<int, 百宝阁类>();
		ChouJiang = new List<ChouJiangClass>();
		累充数据 = new List<累充系统>();
		PVP装备 = new ConcurrentDictionary<int, PVP类>();
		过滤数据包类型 = new List<int>();
		一转地图 = new List<NpcClass>();
		二转地图 = new List<NpcClass>();
		三转地图 = new List<NpcClass>();
		四转地图 = new List<NpcClass>();
		五转地图 = new List<NpcClass>();
		六转地图 = new List<NpcClass>();
		七转地图 = new List<NpcClass>();
		八转地图 = new List<NpcClass>();
		九转地图 = new List<NpcClass>();
		十转地图 = new List<NpcClass>();
		十一转地图 = new List<NpcClass>();
        十二转地图 = new List<NpcClass>();
        假人出售物品列表 = new List<假人出售物品>();
		EventTop = new ConcurrentDictionary<string, EventTopClass>();
		比武泡点Top = new ConcurrentDictionary<string, 比武泡点TopClass>();
		大乱斗Top = new ConcurrentDictionary<string, 大乱斗TopClass>();
		SerList = new ConcurrentDictionary<int, ServerList>(1, 20);
		新门战进程 = 0;
		物品交易后锁定时间 = 10;
		攻击时间间隔 = 1000;
		龙赡殿是否在使用中 = false;
		华婚殿是否在使用中 = false;
		圣礼殿是否在使用中 = false;
		最大速度超出次数操作 = 0;
		三十秒内允许超出次数 = 0;
		周末武勋量 = 100000;
		二转每日武勋上限 = 0;
		三转每日武勋上限 = 0;
		四转每日武勋上限 = 0;
		五转每日武勋上限 = 0;
		六转每日武勋上限 = 0;
		七转每日武勋上限 = 0;
		八转每日武勋上限 = 0;
		九转每日武勋上限 = 0;
		十转每日武勋上限 = 0;
		十一转每日武勋上限 = 0;
		是否开启上线BUFF = 0;
		是否开启任务领取 = 0;
		补偿的任务物品ID = 1000000290;
		新手上线说话等级 = 20;
		异口同声是否开启 = 0;
		异口同声开启中 = 0;
		异口同声开启时 = 0;
		异口同声开启分 = 0;
		异口同声开启秒 = 0;
		异口同声内容 = "";
		异口同声结束时间 = 0;
		三十五级以下经验倍数 = 10.0;
		六十级以下经验倍数 = 9.0;
		八十级以下经验倍数 = 8.0;
		一百级以下经验倍数 = 7.5;
		一百一以下经验倍数 = 6.5;
		一百二以下经验倍数 = 5.5;
		一百三以下经验倍数 = 4.5;
		一百四以下经验倍数 = 3.5;
		一百五以下经验倍数 = 2.5;
		一百六以下经验倍数 = 1.5;
		自定义经验等级 = 15;
		自定义等级经验倍数 = 2.0;
		限制PK地图列表 = new List<int>();
		限时PK地图列表 = new List<int>();
		限时地图开PK时间 = 19;
		限时地图关PK时间 = 23;
		限时地图是否开启 = 0;
		周末全天PK是否开启 = 0;
		周末全天PK地图列表 = new List<int>();
		工作日限时地图开PK时间 = 5;
		工作日限时地图关PK时间 = 23;
		工作日限时地图是否开启 = 1;
		门战准备时间 = 10;
		门战总时间 = 30;
		武林血战是否开启 = 0;
		武林血战开启小时 = 0;
		武林血战开启分 = 0;
		武林血战开启秒 = 0;
		武林血战准备时间 = 3;
		武林血战参战等级 = 60;
		武林血战参加奖励 = "";
		武林血战第一轮人物 = 10;
		武林血战第一轮时间 = 10;
		武林血战第一回合奖励 = "";
		武林血战第二轮人物 = 5;
		武林血战第二轮时间 = 5;
		武林血战第二回合奖励 = "";
		武林血战第三轮人物 = 1;
		武林血战第三轮时间 = 3;
		武林血战第三回合奖励 = "";
		武林血战进程 = 0;
		武林血战人数 = 0;
		仙魔大战掉线玩家 = new ConcurrentDictionary<string, string>();
		武林血战排行数据 = new ConcurrentDictionary<int, 荣誉Class>();
		杀人排行数据 = new ConcurrentDictionary<int, 荣誉Class>();
		门派排名数据 = new List<门派排名>();
		武林排名数据 = new List<武林排名>();
		讨伐排名数据 = new List<讨伐排名>();
		势力排名数据 = new List<势力排名>();
		开启卡技能 = 1;
		卡技能次数 = 5;
		坐标刷新时间 = 3000;
		攻击时间控制 = 600;
		贡献元宝数 = 0;
		贡献元宝荣誉点 = 0;
		冲关地图模式切换 = 1;
		坐牢系统是否开启 = 1;
		坐牢善恶 = -100000;
		坐牢善恶恢复间隔 = 1;
		坐牢恢复善恶值 = 10000;
		限制最高级别 = 180;
		发包单个物品大小 = 96;
		数据库单个物品大小 = 77;
		物品属性大小 = 60;
		外挂PK时间 = 0;
		locklist2 = 0.0;
		每次狮吼功消耗元宝 = 20;
		是否开启门战系统 = 0;
		开启门战系统 = 0;
		开启攻城战系统 = 0;
		攻城战时长 = 60;
		攻城战预备时间 = 3;
		攻城战开启小时 = 0;
		攻城战开启分 = 0;
		攻城战开启秒 = 0;
		申请门战需要元宝 = 0;
		门战系统开启时 = 0;
		门战系统开启分 = 0;
		门战系统开启秒 = 0;
		胜利帮派ID = 0;
		每次分解消耗元宝数 = 10;
		游戏登陆端口最大连接数 = 20;
		游戏登陆端口最大连接时间数 = 1000;
		查非法物品 = 0;
		查非法物品操作 = 3;
		心跳检测开关 = 0;
		是否开启等级奖励 = 0;
		心跳检测时间阀值 = 10000;
		心跳检测时长 = 0;
		安全模式消耗元宝 = 0;
		是否开启安全模式 = 1;
		是否开启新手上线设置 = 0;
		上线等级 = 0;
		赠送气功书 = 0;
		上线转职等级 = 0;
		上线金币数量 = 0;
		上线历练数量 = 0;
		上线武勋设置 = 0;
		上线升天气功点 = 0;
		自动分配正邪 = 0;
		银票兑换元宝 = 0;
		是否开启银票兑换元宝 = 0;
		转职赠送礼包 = 1;
		上线送礼包是否开启 = 1;
		上线送金符是否开启 = 0;
		上线送药品是否开启 = 0;
		上线送礼包套装 = 0;
		使用经验珠等级上限 = 80;
		使用武勋丹上限 = 150000;
		随机武勋丹最小 = 50000;
		随机武勋丹最大 = 200000;
		人物越级怪物掉落差 = 15;
		怪物越级人物掉落差 = 15;
		人物越级怪物经验差 = 15;
		怪物越级人物经验差 = 15;
		击杀BOSS等级差 = 15;
		移动间隔时间 = 600;
		是否开启装备加解锁功能 = 0;
		装备加锁消耗元宝 = 0;
		装备解锁消耗元宝 = 0;
		是否开启挂机奖励 = 0;
		挂机奖励时间周期 = 0;
		普通挂机奖励元宝 = 0;
		普通挂机奖励钻石 = 0;
		会员挂机奖励钻石 = 0;
		会员挂机奖励武勋 = 100;
		普通挂机奖励武勋 = 50;
		普通挂机奖励金币 = 0;
		会员挂机奖励金币 = 0;
		挂机消除宠物忠诚度 = 0;
		挂机奖励要求等级 = 0;
		BOSSTIME = false;
		挂机双倍时间段 = "20;22";
		购买武勋装备消耗武勋 = 1000;
		是否支持扩展物品属性位数 = 0;
		是否开启公告掉落提示 = 0;
		BOSS掉落物品数量下限 = 10;
		BOSS掉落物品数量上限 = 30;
		BOSS掉落元宝几率 = 10;
		BOSS掉落元宝数量下限 = 1;
		BOSS掉落元宝数量上限 = 30;
		BOSS掉落钻石几率 = 10;
		BOSS掉落钻石数量下限 = 1;
		BOSS掉落钻石数量上限 = 30;
		BOSS掉落金币几率 = 10;
		BOSS掉落金币数量下限 = 1;
		BOSS掉落金币数量上限 = 30;
		BOSS掉落武勋几率 = 10;
		BOSS掉落武勋数量下限 = 1;
		BOSS掉落武勋数量上限 = 30;
		BOSS掉落物品几率 = 10;
		安全挂机时间 = 0;
		会员挂机奖励元宝 = 0;
		灵宠高级进化率 = 100.0;
		灵宠宝物进化率 = 80.0;
		灵宠传说进化率 = 60.0;
		灵宠神灵普通进化率 = 20.0;
		灵宠神灵高级进化率 = 20.0;
		灵宠神灵宝物进化率 = 10.0;
		灵宠神灵传说进化率 = 5.0;
		密路宝珠高级进化率 = 80.0;
		密路宝珠稀有进化率 = 60.0;
		密路宝珠传说进化率 = 40.0;
		火龙宝珠高级进化率 = 10.0;
		火龙宝珠稀有进化率 = 0.0;
		火龙宝珠传说进化率 = -10.0;
		PK掉耐久度 = 0;
		打怪掉耐久度 = 0;
		单次交易元宝数量上限 = 0;
		帐号总元宝上限 = 0;
		元宝检测操作 = 0;
		是否开启武勋系统 = 0;
		PK等级差 = 20;
		武勋保护等级 = 80;
		武勋保护数量 = 80;
		死亡减少武勋数量 = "0;0;0;0;0;0";
		死亡回收武勋数量 = "0;0;0;0;0;0";
		限制地图武勋掉落倍数 = "1301;2";
		锁定记录 = 0;
		物品记录 = 0;
        登陆记录 = 0;
		掉落记录 = 0;
		首爆记录 = 0;
		开盒记录 = 0;
		商店记录 = 0;
		仓库记录 = 0;
		药品记录 = 0;
		进化记录 = 0;
		卡号记录 = 0;
		合成记录 = 0;
		传书记录 = 0;
		武勋记录 = 0;
		百宝记录 = 0;
		元宝记录 = 0;
		记录保存天数 = 5;
		传书保存天数 = 1;
		南林钟离开关 = 0;
		南林钟离总时间 = 60;
		公告刷新时间 = 180;
		排名刷新时间 = 3600;
		封IP = true;
		开启快速连接 = true;
		BipList = new List<IPAddress>();
		版本验证时间 = 10000;
		主Socket = false;
		SocketState = "Stoped";
		断开连接 = true;
		加入过滤列表 = true;
		世界时间 = 0;
		W组队Id = 1;
		jlMsg = 0;
		禁创职业 = new List<int>();
		week = (int)DateTime.Now.DayOfWeek;
		是否允许快速攻击 = 1;
		是否开启公告结婚完成 = 0;
		是否开启拜师完成任务 = 0;
		拜师收徒需要等级 = 10;
		拜师收徒需要类型 = 0;
		拜师收徒需要数量 = 10;
		拜师收徒任务时间 = 3;
		拜师收徒贡献数量 = 2000;
		是否开启数据库宝箱 = 0;
		学习制作技能熟练度 = 0;
		情侣爱情度数量值 = 35000;
		结婚赠送物品编号 = 1008000562;
		是否开启告白需要物品 = 1;
		告白需要物品编号 = 1000000416;
		仙魔大战进程 = 0;
		仙魔大战时间 = 0;
		仙魔大战正分数 = 0;
		仙魔大战邪分数 = 0;
		仙魔大战正人数 = 0;
		仙魔大战邪人数 = 0;
		仙魔大战是否开启 = 0;
		仙魔大战开启小时 = 0;
		仙魔大战开启分 = 0;
		仙魔大战开启秒 = 0;
		仙魔大战时长 = 30;
		仙魔大战预备时间 = 5;
		武林血战奖励礼包 = 0;
		第一名奖励礼包 = 0;
		是否开启对练场赌元宝 = 0;
		允许玩家押注数量 = 100;
		场地有效范围 = 60f;
		比武场经验基数 = 0.0;
		比武泡点元宝时间 = 0;
		比武泡点元宝基数 = 0;
		比武泡点武勋基数 = 0;
		比武泡点金钱基数 = 0;
		进场最低费用 = 100;
		场地佣金百分比 = 0.2;
		允许逃跑次数 = 10;
		分数扣完扣除元宝 = 10;
		分数扣完扣除金钱 = 10000;
		Eve90进程 = 0;
		Eve90时间 = 0;
		元宝合成 = 5;
		死亡恢复全部经验元宝数 = 20;
		死亡恢复固定经验元宝数 = 5;
		发送速度 = 0.0;
		接收速度 = 0.0;
		经验倍数 = 100.0;
		发包基数 = 1.0;
		吸魂成功几率 = 70;
		钱倍数 = 1.0;
		历练倍数 = 80;
		属性一替换成功率 = 10;
		属性二替换成功率 = 10;
		属性三替换成功率 = 10;
		属性四替换成功率 = 10;
		暴率 = 800;
		是否开启附魂 = 0;
		最大附魂数 = 5;
		附魂加战斗力 = 5;
		强化附加战斗力 = 10;
		附魂加攻击 = 5;
		附魂加防御 = 5;
		初魂成功几率 = 0.0;
		中魂成功几率 = 0.0;
		四神成功几率 = 0.0;
		四神变更卷成功几率 = 0.0;
		武器防具进化2成功几率 = 0.0;
		强化转移符成功几率 = 0.0;
		首饰加工一成功率 = 100.0;
		首饰加工二成功率 = 100.0;
		首饰加工三成功率 = 100.0;
		首饰加工四成功率 = 100.0;
		首饰加工五成功率 = 100.0;
		首饰加工六成功率 = 100.0;
		首饰加工七成功率 = 100.0;
		首饰加工八成功率 = 100.0;
		首饰加工九成功率 = 100.0;
		首饰加工十成功率 = 100.0;
		提真成功几率 = 0.0;
		老百宝阁地址 = "http://bbg.xwwl.net/login.aspx?server=1";
		老百宝阁服务器IP = "127.0.0.1";
		老百宝阁服务器端口 = 9001;
		新百宝阁地址 = "http://bbg.xwwl.net/login.aspx?server=1";
		新百宝阁服务器IP = "127.0.0.1";
		新百宝阁服务器端口 = 9001;
		神器兑换地址 = "http://bbg.xwwl.net/login.aspx?server=1";
		神器兑换服务器IP = "127.0.0.1";
		神器兑换服务器端口 = 9001;
		帐号验证服务器IP = "127.0.0.1";
		帐号验证服务器端口 = 55970;
		游戏服务器端口 = 13001;
		游戏服务器端口2 = 13001;
		vip线 = 0;
		最大在线 = 100;
		服务器组ID = 1;
		服务器ID = 0;
		服务器名 = "热血江湖";
		八彩提示是否开启 = 1;
		八彩红色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩赤色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩橙色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩绿色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩蓝色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩深蓝提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩紫色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
		八彩浅色提示内容 = "《{0}》驭龙而来，你就是最亮的一颗小星星; 八彩提示";
        在线人数是否开启 = 1;
        在线基本人数 = 10;
        狮子吼ID = 0;
		狮子吼List = Queue.Synchronized(new Queue());
		门战数据list = new ConcurrentDictionary<int, 门战数据>();
		门派战绩list = new ConcurrentDictionary<int, 门派战绩>();
		冰宫内城副怪是否死亡 = 0;
		伏魔洞副怪是否死亡 = 0;
		天魔神宫大门是否死亡 = 0;
		天魔神宫东门是否死亡 = 0;
		城门强化等级 = 0;
		攻城战进程 = 0;
		攻城时间 = 30;
		狮子吼最大数 = 100;
		元宝送积分是否开启 = 1;
		封包封号 = 0;
		组队等级限制 = 10;
		Vip上线公告 = 0;
		神豪上线公告 = 0;
		前十上线公告 = 0;
		Vip上线公告内容 = "尊贵的VIP玩家{0}上线了！大家欢迎！";
		神豪上线公告内容 = "尊贵的VIP玩家{0}上线了！大家欢迎！";
		VIP地图 = string.Empty;
		地图锁定 = string.Empty;
		每日武勋解锁地图 = string.Empty;
		允许开店地图 = string.Empty;
		SqlJl = string.Empty;
		移动坐标异常后反弹 = 0;
		开启实时坐标检测 = 0;
		普通走 = 3.15f;
		轻功一 = 4.15f;
		轻功二 = 5.15f;
		轻功三 = 6.15f;
		吸怪数量 = 10;
		吸怪距离 = 100;
		引怪距离 = 100;
		怪物打人距离 = 100;
		实时移动时间 = 100;
		群攻打怪距离 = 40;
		组队范围距离 = 700;
		宠物普通走 = 3.15f;
		韩轻功一 = 3.15f;
		韩轻功二 = 3.15f;
		韩轻功三 = 3.15f;
		韩轻功四 = 3.15f;
		元宝检测 = 0;
		装备最大数 = 36;
		自动存档 = 1;
		灵宠强化一阶段概率 = 100.0;
		灵宠强化二阶段概率 = 100.0;
		灵宠强化三阶段概率 = 100.0;
		灵宠强化四阶段概率 = 100.0;
		灵宠强化五阶段概率 = 100.0;
		灵宠强化六阶段概率 = 50.0;
		灵宠强化七阶段概率 = 30.0;
		灵宠强化八阶段概率 = 20.0;
		灵宠强化九阶段概率 = 10.0;
		灵宠强化十阶段概率 = 0.0;
		灵宠强化十一阶段概率 = -10.0;
		灵宠强化十二阶段概率 = -10.0;
		灵宠强化十三阶段概率 = -10.0;
		灵宠强化十四阶段概率 = -10.0;
		灵宠强化十五阶段概率 = -10.0;
		披风强化一阶段概率 = 100.0;
		披风强化二阶段概率 = 100.0;
		披风强化三阶段概率 = 100.0;
		披风强化四阶段概率 = 100.0;
		披风强化五阶段概率 = 100.0;
		披风强化六阶段概率 = 50.0;
		披风强化七阶段概率 = 30.0;
		披风强化八阶段概率 = 20.0;
		披风强化九阶段概率 = 10.0;
		披风强化十阶段概率 = 0.0;
		披风强化十一阶段概率 = -10.0;
		披风强化十二阶段概率 = -10.0;
		披风强化十三阶段概率 = -10.0;
		披风强化十四阶段概率 = -10.0;
		披风强化十五阶段概率 = -10.0;
		王龙的金币 = 5000000L;
		是否开启王龙 = 1;
		九泉金币比率 = 0.8;
		王龙地图ID = 0;
		文件MD5 = string.Empty;
		再造金刚石攻击 = string.Empty;
		再造金刚石追伤 = string.Empty;
		再造金刚石武功 = string.Empty;
		再造金刚石命中 = string.Empty;
		再造金刚石生命 = string.Empty;
		再造寒玉石防御 = string.Empty;
		再造寒玉石回避 = string.Empty;
		再造寒玉石生命 = string.Empty;
		再造寒玉石内功 = string.Empty;
		再造寒玉石武防 = string.Empty;
		武功防降低百分比 = 0.925;
		武功防增加百分比 = 0.925;
		武功攻击力百分比 = 0.01;
		攻减防加乘 = 1.5;
		武功减武防加乘 = 1.3;
		是否开启死亡掉经验 = 0;
		二人组队增加百分比 = 0.05;
		三人组队增加百分比 = 0.1;
		四人组队增加百分比 = 0.15;
		五人组队增加百分比 = 0.2;
		六人组队增加百分比 = 0.25;
		七人组队增加百分比 = 0.3;
		八人组队增加百分比 = 0.35;
		二人组队暴率百分比 = 0.05;
		三人组队暴率百分比 = 0.1;
		四人组队暴率百分比 = 0.15;
		五人组队暴率百分比 = 0.2;
		六人组队暴率百分比 = 0.25;
		七人组队暴率百分比 = 0.3;
		八人组队暴率百分比 = 0.35;
		四级门派增加百分比 = 0.02;
		五级门派增加百分比 = 0.03;
		六级门派增加百分比 = 0.05;
		七级门派增加百分比 = 0.08;
		一级推广经验增加百分比 = 1.1;
		一级推广经验增加百分比 = 1.2;
		一级推广经验增加百分比 = 1.3;
		队伍红包增加经验百分比 = 0.2;
		队伍红包增加金钱百分比 = 0.2;
		VIP经验增加百分比 = 0.0;
		VIP历练增加百分比 = 0.0;
		VIP金钱增加百分比 = 0.0;
		VIP合成率增加百分比 = 0.0;
		医生PK距离 = 50.0;
		弓箭手PK距离 = 60.0;
		神女PK距离 = 50.0;
		梅柳真PK距离 = 60.0;
		其他职业PK距离 = 40.0;
		医生打怪距离 = 50.0;
		弓箭手打怪距离 = 60.0;
		神女打怪距离 = 50.0;
		梅柳真打怪距离 = 60.0;
		其他职业打怪距离 = 40.0;
		VIP爆率增加 = 0;
		双倍奖励是否开启 = 0;
		双倍奖励开启小时 = 21;
		双倍奖励开启分 = 0;
		双倍奖励开启秒 = 0;
		双倍奖励结束时间 = 120;
		双倍奖励经验倍数 = 2.0;
		双倍奖励爆率倍数 = 2;
		双倍奖励武勋倍数 = 2;
		披风强化是否消耗元宝 = 0;
		灵宠强化最大数量 = 100;
		披风强化最大数量 = 100;
		防具强化最大数量 = 15;
		披风强化消耗元宝数量 = 1;
		灵宠强化是否消耗开关 = 0;
		灵宠强化消耗武皇币数量 = 1;
		灵宠强化消耗冰魄水玉数量 = 1;
		天关经验提高百分比基数 = 0.0;
		天关物品爆率提高基数 = 0;
		天关经验提高百分比递增 = 0.0;
		天关物品爆率提高递增 = 0;
		势力战开始时向其它线广播 = 1;
		同IP势力战不计分 = 0;
		势力战打死大怪得分 = 100;
		势力战打死小怪得分 = 500;
		势力战踢人方案 = new List<KeyValuePair<int, int>>();
		所有势力战场次 = new ConcurrentDictionary<int, 势力战场次>();
		势力战参加最低转职 = 2;
		势力战参加最高转职 = 11;
		申请势力人物列表 = new ConcurrentDictionary<string, Players>();
		势力怪暴热血石 = "";
		势力战类型 = 0;
		势力战进程 = 0;
		势力战正分数 = 0;
		势力战邪分数 = 0;
		势力战正派参战人数 = 0;
		势力战邪派参战人数 = 0;
		势力战是否开启 = 0;
		势力战开启分 = 0;
		势力战开启秒 = 0;
		势力战设置 = "";
		势力战开启自动踢人 = 0;
		势力战战斗时间 = 30;
		势力战预备时间 = 5;
		装备提真消耗 = 0;
		装备提真数量 = 0;
		随机BOSS出现时间表 = string.Empty;
		是否开启外挂占卜 = 1;
		是否开启NPC占卜 = 1;
		金币占卜费用 = 2000000;
		元宝占卜费用 = 200;
		制作物品列表 = new ConcurrentDictionary<int, 制作物品类>();
		制药物品列表 = new ConcurrentDictionary<int, 制药物品类>();
		RanString = new string[14]
		{
			"~", "/", "@", "#", "$", "%", "^", "&", "*", "|",
			"<", ">", ":", "`"
		};
		平砍间隔时间 = 1000;
		强化一合成率 = 100.0;
		强化二合成率 = 90.0;
		强化三合成率 = 80.0;
		强化四合成率 = 70.0;
		强化五合成率 = 60.0;
		强化六合成率 = 50.0;
		强化七合成率 = 40.0;
		强化八合成率 = 30.0;
		强化九合成率 = 20.0;
		强化十合成率 = 10.0;
		强化十一合成率 = 5.0;
		强化十二合成率 = 5.0;
		强化十三合成率 = 5.0;
		强化十四合成率 = 5.0;
		强化十五合成率 = 5.0;
		无限负重 = 0;
		限时行囊 = 0;
		开启下雪场景 = 0;
		是否开启安全码 = 0;
		是否开启推广返利 = 0;
		累计充值属性加成 = 0;
		累计充值属性提示 = 0;
		是否开启票红字 = 0;
		是否开启票红字2 = 0;
		药品冲突是否开启 = 0;
		水晶取玉符强1 = 0.0;
		水晶取玉符强2 = 0.0;
		水晶取玉符强3 = 0.0;
		水晶取玉符强4 = 0.0;
		水晶取玉符强5 = 0.0;
		水晶取玉符强6 = 0.0;
		水晶取玉符强7 = 0.0;
		水晶取玉符强8 = 0.0;
		水晶取玉符强9 = 0.0;
		水晶取玉符强10 = 0.0;
		至尊取玉符强11 = 0.0;
		至尊取玉符强12 = 0.0;
		至尊取玉符强13 = 0.0;
		至尊取玉符强14 = 0.0;
		至尊取玉符强15 = 0.0;
		升级经验表基数 = 100.0;
		升级经验是否开启数据库 = 0;
		刺客攻击倍数 = 1.4;
		弓手攻击倍数 = 1.5;
		武器十五阶段添加攻击 = 0;
		武器十六阶段添加攻击 = 0;
		武器十七阶段添加攻击 = 0;
		武器十八阶段添加攻击 = 0;
		武器十九阶段添加攻击 = 0;
		武器二十阶段添加攻击 = 0;
		斗神称号激活方式 = 0;
		斗神称号需要数量 = 0;
		try
		{
			Kill = new List<KillClass>();
			帮战Namelist = new ConcurrentDictionary<int, 帮战Class>();
			W组队 = new ConcurrentDictionary<int, 组队Class>();
			公告 = new ConcurrentDictionary<int, 公告类>();
			等级奖励 = new ConcurrentDictionary<int, 等级奖励类>();
			假人等级奖励 = new ConcurrentDictionary<int, 假人等级奖励>();
			比武泡点奖励 = new ConcurrentDictionary<int, 比武泡点奖励>();
			大乱斗奖励 = new ConcurrentDictionary<int, 大乱斗奖励>();
			石头属性调整 = new ConcurrentDictionary<int, 石头属性调整类>();
			气功加成 = new ConcurrentDictionary<int, 气功加成属性>();
			物品兑换 = new ConcurrentDictionary<int, 物品兑换类>();
			职业系数数据 = new ConcurrentDictionary<int, 职业系数类>();
			神器兑换 = new ConcurrentDictionary<int, 神器兑换类>();
			lever = new ConcurrentDictionary<int, double>();
			Wxlever = new ConcurrentDictionary<int, 武勋加成类>();
			Itme = new ConcurrentDictionary<int, ItmeClass>();
			ItmeTeM = new ConcurrentDictionary<long, 地面物品类>();
			TBL_KONGFU = new ConcurrentDictionary<int, 武功类>();
			MonSter = new ConcurrentDictionary<int, MonSterClss>();
			物品检查 = new List<检查物品类>();
			BossDrop = new List<DropClass>();
			Drop = new List<DropClass>();
			Drop_GS = new List<DropClass>();
			Open = new List<OpenClass>();
			套装数据 = new List<ItemSellClass>();
			Shop = new List<ShopClass>();
			Mover = new List<MoveClass>();
			移动 = new List<坐标Class>();
			兑换码 = new ConcurrentDictionary<int, 兑换码Class>();
			对练区 = new List<坐标Class>();
			BOSSListTime = new List<int>();
			仙魔大战区域 = new List<坐标Class>();
			攻城战区域 = new List<坐标Class>();
			势力战区域 = new List<坐标Class>();
			帮战区 = new List<坐标Class>();
			开箱Lock = new object();
			地面物品Lock = new object();
			攻城怪物列表 = new List<攻城怪物>();
			伏魔洞怪物列表 = new List<伏魔洞怪物>();
			冰宫内城怪物列表 = new List<冰宫内城怪物>();
			活动副本怪物列表 = new List<活动副本怪物>();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, FastString.Concat("系统错误:World", ex)); // 2025-0618 EVIAS 优化字符串拼接
			Environment.Exit(0);
		}
	}

	public static void 发送地图PK公告2(int msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			foreach (int item in 限时PK地图列表)
			{
				if (value.人物坐标_地图 == item)
				{
					if (msg == 1)
					{
						value.系统提示("本地图现在开始开放PK", 6, "公告");
					}
					else
					{
						value.系统提示("时间太晚了,为了广大玩家身心健康,本地图现在开始禁止PK", 6, "公告");
					}
				}
			}
		}
	}

	public static void 发送地图PK公告(int msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			foreach (int item in 限时PK地图列表)
			{
				if (value.人物坐标_地图 == item)
				{
					if (msg == 1)
					{
						value.系统提示("本地图现在开始开放PK", 6, "公告");
					}
					else
					{
						value.系统提示("本地图现在开始禁止PK", 6, "公告");
					}
				}
			}
		}
	}

	private void 世界时间事件()
	{
		try
		{
			if ((int)DateTime.Now.Subtract(膜拜大神时间间隔).TotalMilliseconds >= 4000 && 充值榜第一名 != string.Empty)
			{
				foreach (Players value in allConnectedChars.Values)
				{
					if (value.UserName == 充值榜第一名)
					{
						value.膜拜特效(特效道具ID, value.人物全服ID);
					}
				}
				膜拜大神时间间隔 = DateTime.Now;
			}
			if (是否有人发红包 && (int)DateTime.Now.Subtract(发红包当前时间).TotalMilliseconds >= 红包检测时间)
			{
				是否有人发红包 = false;
				参加红包列表.Clear();
				红包系统 = new 发红包();
				发送特殊公告("由于剩余红包有没抢完系统自动回收！可以新一轮的发红包了！！", 6, "公告");
			}
			foreach (Players value2 in allConnectedChars.Values)
			{
				value2.称号物品结束();
				value2.时间物品结束();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, FastString.Concat("膜拜/发红包/称号药品出错", ex.Message)); // 2025-0618 EVIAS 优化字符串拼接
		}
		try
		{
			foreach (Players value3 in allConnectedChars.Values)
			{
				if (value3.神女异常状态 != null && value3.神女异常状态.ContainsKey(48))
				{
					value3.人物_HP -= (int)((double)value3.人物最大_HP * 0.03);
					if (value3.人物_HP <= 0)
					{
						value3.人物_HP = 1;
					}
					value3.更新HP_MP_SP();
				}
				if (value3.神女异常状态 != null && value3.神女异常状态.ContainsKey(49))
				{
					value3.人物_HP -= (int)((double)value3.人物最大_HP * 0.05);
					if (value3.人物_HP <= 0)
					{
						value3.人物_HP = 1;
					}
					value3.更新HP_MP_SP();
				}
			}
		}
		catch (Exception ex2)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex2, null, "神女异常状态处理", "处理神女异常状态48/49");
			Form1.WriteLine(1, "神女异常错误/48/49" + ex2.Message);
		}
		try
		{
			if (异口同声是否开启 == 1 && DateTime.Now.Hour == 异口同声开启时 && DateTime.Now.Minute == 异口同声开启分 && DateTime.Now.Second >= 异口同声开启秒 && 开启异口同声 == null)
			{
				开启异口同声 = new 异口同声();
				Form1.WriteLine(66, "自动开启异口同声成功");
			}
			if (幸运奖是否开启 == 1 && DateTime.Now.Hour == 幸运奖开启小时 && DateTime.Now.Minute == 幸运奖开启分 && DateTime.Now.Second >= 幸运奖开启秒 && 开启幸运玩家 == null)
			{
				开启幸运玩家 = new 幸运玩家();
				Form1.WriteLine(66, "自动开启幸运奖系统成功");
			}
			if (BOSS攻城是否开启 == 1 && DateTime.Now.Hour == BOSS攻城开启小时 && DateTime.Now.Minute == BOSS攻城开启分 && DateTime.Now.Second >= BOSS攻城开启秒 && boss攻城 == null)
			{
				boss攻城 = new boss攻城();
				Form1.WriteLine(66, "自动开启boss攻城成功");
			}
			if (世界BOSS攻城是否开启 == 1 && DateTime.Now.Hour == 世界BOSS攻城开启小时 && DateTime.Now.Minute == 世界BOSS攻城开启分 && DateTime.Now.Second >= 世界BOSS攻城开启秒 && 世界boss == null)
			{
				世界boss = new 世界BOSS();
				Form1.WriteLine(66, "自动开启世界boss成功");
			}
			if (双倍奖励是否开启 == 1 && DateTime.Now.Hour == 双倍奖励开启小时 && DateTime.Now.Minute == 双倍奖励开启分 && DateTime.Now.Second >= 双倍奖励开启秒 && 开启全服经验 == null)
			{
				开启全服经验 = new 全服经验();
				Form1.WriteLine(66, "自动开启双倍经验成功");
			}
		}
		catch (Exception ex3)
		{
			Form1.WriteLine(1, "开启异口同声/幸运抽奖/BOSS攻城/世界BOSS/双倍/出错" + ex3.Message);
		}
		try
		{
			if (DateTime.Now.Hour == 限时地图开PK时间)
			{
				if (限时地图是否开启 == 0)
				{
					限时地图是否开启 = 1;
					发送地图PK公告(1);
					Form1.WriteLine(66, "自动开启限时PK成功");
				}
			}
			else if (DateTime.Now.Hour == 限时地图关PK时间 && 限时地图是否开启 == 1)
			{
				限时地图是否开启 = 0;
				发送地图PK公告(0);
				Form1.WriteLine(66, "自动关闭限时PK成功");
			}
			if (Convert.ToInt32(DateTime.Today.DayOfWeek) < 5 && 周末全天PK是否开启 == 1)
			{
				if (DateTime.Now.Hour == 工作日限时地图开PK时间)
				{
					if (工作日限时地图是否开启 == 0)
					{
						工作日限时地图是否开启 = 1;
						发送地图PK公告2(1);
						Form1.WriteLine(66, "自动开启工作日限时PK成功");
					}
				}
				else if (DateTime.Now.Hour == 工作日限时地图关PK时间 && 工作日限时地图是否开启 == 1)
				{
					工作日限时地图是否开启 = 0;
					发送地图PK公告2(0);
					Form1.WriteLine(66, "自动关闭工作日限时PK成功");
				}
			}
		}
		catch (Exception ex4)
		{
			Form1.WriteLine(1, "限制PK出错" + ex4.Message);
		}
		try
		{
			if (Convert.ToInt32(DateTime.Today.DayOfWeek) == 1 && DateTime.Now.Hour == 0 && DateTime.Now.Minute == 1 && DateTime.Now.Second == 30)
			{
				DBA.ExeSqlCommand("UPDATE TBL_XWWL_Char SET FLD_讨伐次数=5 WHERE FLD_讨伐次数<5", "GameServer");
				DBA.ExeSqlCommand("DELETE FROM 荣誉讨伐排行");
				Form1.WriteLine(55, "活动删除讨伐排行榜");
				foreach (Players value4 in allConnectedChars.Values)
				{
					if (value4.Client.在线)
					{
						value4.副本剩余次数 = 5;
						value4.保存人物的数据();
					}
				}
				发送特殊公告("每周讨伐已重置,每周一凌晨准时自动重置....", 6, "系统提示");
				Form1.WriteLine(66, "自动周一重置讨伐成功");
			}
			if (DateTime.Now.Hour == 0 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 20)
			{
				foreach (Players value5 in allConnectedChars.Values)
				{
					value5.每日获得武勋 = 0;
					value5.丢失武勋 = 0;
					value5.恢复精力 = 100;
					value5.火龙觉醒一时间 = 0;
					value5.火龙觉醒二时间 = 0;
					value5.更新武功和状态();
				}
				发送特殊公告("精力恢复100点,每日武勋,觉醒时间,每天凌晨准时自动重置....", 6, "系统提示");
				Form1.WriteLine(66, "自动每日觉醒时间恢复精力每日武勋任务成功");
			}
			if (DateTime.Now.Hour == 0 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 30)
			{
				DBA.ExeSqlCommand("UPDATE  TBL_XWWL_DROP  SET  当前数量=0 WHERE 最大数量>0", "PublicDb");
				SetDrop();
				发送特殊公告("每日凌晨准时自动重置掉落限制....", 6, "系统提示");
				Form1.WriteLine(66, "自动重置掉落成功");
			}
			if (是否开启安全模式 == 1 && DateTime.Now.Hour == int.Parse(安全模式时间[1]) && DateTime.Now.Minute == 0 && DateTime.Now.Second == 30)
			{
				foreach (Players value6 in allConnectedChars.Values)
				{
					if (value6.追加状态列表 != null && value6.GetAddState(900000619))
					{
						value6.追加状态列表[900000619].时间结束事件();
					}
					value6.安全模式 = 0;
				}
				发送特殊公告("每天[" + int.Parse(安全模式时间[1]) + "]点,安全模式自动解除....", 6, "系统提示");
				Form1.WriteLine(66, "自动每日清理安全模式成功");
			}
		}
		catch (Exception ex5)
		{
			Form1.WriteLine(1, "安全/每日武勋/讨伐清零/出错" + ex5.Message);
		}
		try
		{
			if (是否开启王龙 == 1)
			{
				开启九泉王龙();
			}
			if (野外BOSS开关 == 1)
			{
				野外BOSS开启();
			}
			if (BOSSListTime.Count > 1)
			{
				随机BOSS开启();
			}
			if (强化合成概率周否开启 == 1)
			{
				概率周活动();
			}
			if (比武泡点是否开启 == 1)
			{
				开启比武泡点();
			}
			if (极限比武是否开启 == 1)
			{
				开启极限比武();
			}
			if (红包雨是否开启 == 1)
			{
				开启红包雨();
			}
			if (南林钟离开关 == 1)
			{
				南林钟离开启();
			}
			if (大乱斗是否开启 == 1)
			{
				开启大乱斗();
			}
			if (势力战是否开启 == 1)
			{
				开启势力战();
			}
			if (仙魔大战是否开启 == 1)
			{
				开启仙魔大战();
			}
			if (开启攻城战系统 == 1)
			{
				开启攻城战();
			}
			if (开启门战系统 == 1)
			{
				帮战开启();
			}
			if (武林血战是否开启 == 1)
			{
				武林血战开启();
			}
			if (是否开启对练场赌元宝 == 1)
			{
				PVP单挑开启();
			}
		}
		catch (Exception ex6)
		{
			Form1.WriteLine(1, FastString.Concat("九泉/野外/随机/概率周/比武/红包雨/钟离/大乱斗/势力战/仙魔战/攻城战/出错", ex6.Message)); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static void 势力战开启公告(int 最低转职, int 最高转职)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			value.滚动公告(2046, "5", FastString.Concat(最低转职, "~", 最高转职)); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static int 取星期几()
	{
		int result = 0;
		switch (DateTime.Today.DayOfWeek.ToString())
		{
		case "Thursday":
			result = 4;
			break;
		case "Monday":
			result = 1;
			break;
		case "Saturday":
			result = 6;
			break;
		case "Sunday":
			result = 7;
			break;
		case "Friday":
			result = 5;
			break;
		case "Tuesday":
			result = 2;
			break;
		case "Wednesday":
			result = 3;
			break;
		}
		return result;
	}

	public World()
	{
		Timer.DelayCall(TimeSpan.FromMilliseconds(1000.0), TimeSpan.FromMilliseconds(1000.0), 世界时间事件);
		Timer.DelayCall(TimeSpan.FromMilliseconds(1000.0), TimeSpan.FromMilliseconds(1000.0), 卡黑屏核查);
		Timer.DelayCall(TimeSpan.FromMilliseconds(1500.0), TimeSpan.FromMilliseconds(1500.0), 内挂打怪);
	}

	public void 野外BOSS开启()
	{
		try
		{
        	string[] array = 野外BOSS时间.Split('/');
        	foreach (string text in array)
			{
				if (text.Length > 0 && DateTime.Now.Hour == Convert.ToInt32(text) && DateTime.Now.Minute == 0 && DateTime.Now.Second >= 0 && 野外boss == null)
				{
					野外boss = new 野外BOSS();
					Form1.WriteLine(66, "自动开启野外BOSS成功");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "野外BOSS触发出错" + ex.Message);
		}
	}

	public void 南林钟离开启()
	{
		try
		{
        	string[] array = 南林钟离时间.Split('/');
        	foreach (string text in array)
			{
				if (text.Length > 0 && DateTime.Now.Hour == Convert.ToInt32(text) && DateTime.Now.Minute == 0 && DateTime.Now.Second >= 0 && 南林钟离 == null)
				{
					南林钟离 = new BossClass();
					Form1.WriteLine(66, "自动开启南林钟离成功");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "南林钟离触发出错" + ex.Message);
		}
	}

	public void 帮战开启()
	{
		try
		{
			if (DateTime.Now.Hour == 门战系统开启时 && DateTime.Now.Minute == 门战系统开启分 && DateTime.Now.Second >= 门战系统开启秒 && 帮战 == null && 是否开启共用银币市场 == 0)
			{
				是否开启门战系统 = 1;
				胜利帮派ID = 0;
				帮战 = new 帮派战_门战();
				Form1.WriteLine(66, "自动开启帮战成功");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启门战系统错误 " + ex.Message);
		}
	}

	public void 武林血战开启()
	{
		try
		{
			if (DateTime.Now.Hour == 武林血战开启小时 && DateTime.Now.Minute == 武林血战开启分 && DateTime.Now.Second >= 武林血战开启秒 && 个人血战 == null && 是否开启共用银币市场 == 0)
			{
				if (个人血战 != null)
				{
					个人血战.Dispose();
					个人血战 = null;
				}
				个人血战 = new 武林血战();
				Form1.WriteLine(66, "自动开启个人血战成功");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启武林血战错误 " + ex.Message);
		}
	}

	public void PVP单挑开启()
	{
		try
		{
			if (EVEPVP == null && 擂台赛甲方 != null && 擂台赛乙方 != null)
			{
				EVEPVP = new EvePVPClass(擂台赛甲方, 擂台赛乙方);
				Form1.WriteLine(66, "自动开启个人战成功");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "个人战/循环开启出错" + ex.Message);
		}
	}

	public void 随机BOSS开启()
	{
		try
		{
			for (int i = 0; i < BOSSListTime.Count; i++)
			{
				if (DateTime.Now.Hour == BOSSListTime[0] && DateTime.Now.Minute == BOSSListTime[1] && (DateTime.Now.Second == 0 || DateTime.Now.Second == 1 || DateTime.Now.Second == 2))
				{
					if (!BOSSTIME)
					{
						BOSSTIME = true;
						随机BOSS系统();
						Form1.WriteLine(66, "自动开启随机BOSS成功");
					}
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "泫勃派随机BOSS触发出错" + ex.Message);
		}
	}

	public void 开启极限比武()
	{
		try
		{
			string[] array = 极限比武开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 极限比武开启小时 && DateTime.Now.Minute == 极限比武开启分 && DateTime.Now.Second >= 极限比武开启秒 && !极限比武台服务.IsCompetitionStarted() && 是否开启共用银币市场 == 0)
				{
					极限比武台服务.StartCompetition();
					Form1.WriteLine(66, "自动开启极限比武");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启比武泡点触发出错" + ex.Message);
		}
	}

	public void 开启比武泡点()
	{
		try
		{
			string[] array = 比武泡点开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 比武泡点开启小时 && DateTime.Now.Minute == 比武泡点开启分 && DateTime.Now.Second >= 比武泡点开启秒 && 比武泡点 == null)
				{
					DBA.ExeSqlCommand("DELETE FROM 荣誉比武排行 where 分区信息='" + 分区编号 + "'", "GameServer");
					比武泡点 = new 比武泡点系统();
					Form1.WriteLine(66, "自动开启比武泡点");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启比武泡点触发出错" + ex.Message);
		}
	}

	public void 开启大乱斗()
	{
		try
		{
			string[] array = 大乱斗开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 大乱斗开启小时 && DateTime.Now.Minute == 大乱斗开启分 && DateTime.Now.Second >= 大乱斗开启秒 && 大乱斗 == null)
				{
					DBA.ExeSqlCommand("DELETE FROM 荣誉乱斗排行 where 分区信息='" + 分区编号 + "'", "GameServer");
					大乱斗 = new 大乱斗系统();
					Form1.WriteLine(66, "自动开启大乱斗");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启大乱斗触发出错" + ex.Message);
		}
	}

	public void 开启势力战()
	{
		try
		{
			string[] array = 势力战开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && 所有势力战场次.TryGetValue(DateTime.Now.Hour, out var value) && DateTime.Now.Minute == 势力战开启分 && DateTime.Now.Second == 势力战开启秒 && eve == null && 是否开启共用银币市场 == 0)
				{
					势力战类型 = value.势力战类型;
					势力战参加最低转职 = value.最小转职;
					势力战参加最高转职 = value.最大转职;
					eve = new EventClass();
					势力战开启公告(value.最小转职, value.最大转职);
					Form1.WriteLine(66, "自动开启势力战成功");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启势力战触发出错" + ex.Message);
		}
	}

	public void 开启仙魔大战()
	{
		try
		{
			string[] array = 仙魔战开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 仙魔大战开启小时 && DateTime.Now.Minute == 仙魔大战开启分 && DateTime.Now.Second == 仙魔大战开启秒 && 仙魔大战 == null && 是否开启共用银币市场 == 0)
				{
					DBA.ExeSqlCommand("DELETE FROM 荣誉仙魔排行 where 分区信息='" + 分区编号 + "'", "GameServer");
					Form1.WriteLine(55, "活动删除荣誉仙魔排行");
					仙魔大战 = new 仙魔大战Class();
					Form1.WriteLine(66, "单线仙魔大战开启成功");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启仙魔大战触发出错" + ex.Message);
		}
	}

	public void 开启攻城战()
	{
		try
		{
			string[] array = 攻城战开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 攻城战开启小时 && DateTime.Now.Minute == 攻城战开启分 && DateTime.Now.Second == 攻城战开启秒 && 攻城 == null && 是否开启共用银币市场 == 0)
				{
					攻城 = new 攻城战();
					Form1.WriteLine(66, "单线攻城战开启成功");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启攻城战触发出错" + ex.Message);
		}
	}

	public void 开启红包雨()
	{
		try
		{
			string[] array = 红包雨开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几() && DateTime.Now.Hour == 红包雨开启小时 && DateTime.Now.Minute == 红包雨开启分钟 && DateTime.Now.Second >= 红包雨开启秒钟 && 红包雨 == null)
				{
					红包雨 = new 红包雨系统();
					Form1.WriteLine(66, "自动开启红包雨");
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "开启红包雨触发出错" + ex.Message);
		}
	}

	public void 概率周活动()
	{
		try
		{
			string[] array = 强化合成概率周开启时间星期.Split(':');
			for (int i = 0; i < array.Length; i++)
			{
				if (int.Parse(array[i]) == 取星期几())
				{
					if (DateTime.Now.Hour == 强化合成概率周开启小时 && DateTime.Now.Minute == 强化合成概率周分 && DateTime.Now.Second == 强化合成概率周秒 && 是否开启强化合成概率周 == 0)
					{
						是否开启强化合成概率周 = 1;
						发送特殊公告("强化合成概率提升:" + 强化合成概率周倍数 * 100.0 + "%,活动开始", 6, "公告");
						Form1.WriteLine(66, "自动开启强化合成概率成功");
					}
					if (DateTime.Now.Hour == 强化合成概率周结束小时 && DateTime.Now.Minute == 强化合成概率周分 && DateTime.Now.Second == 强化合成概率周秒 && 是否开启强化合成概率周 == 1)
					{
						是否开启强化合成概率周 = 0;
						发送特殊公告("强化合成概率提升:" + 强化合成概率周倍数 * 100.0 + " %,活动结束", 6, "公告");
						Form1.WriteLine(22, "自动关闭强化合成概率成功");
					}
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "强化合成概率周触发出错" + ex.Message);
		}
	}

	public void 开启九泉王龙()
	{
		try
		{
			bool flag = false;
			if (DateTime.Now.Hour == 0 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp);
				王龙地图ID = mapp;
				flag = true;
			}
			if (DateTime.Now.Hour == 4 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp2 = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp2);
				王龙地图ID = mapp2;
				flag = true;
			}
			if (DateTime.Now.Hour == 8 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp3 = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp3);
				王龙地图ID = mapp3;
				flag = true;
			}
			if (DateTime.Now.Hour == 12 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp4 = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp4);
				王龙地图ID = mapp4;
				flag = true;
			}
			if (DateTime.Now.Hour == 16 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp5 = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp5);
				王龙地图ID = mapp5;
				flag = true;
			}
			if (DateTime.Now.Hour == 20 && DateTime.Now.Minute == 0 && DateTime.Now.Second == 0)
			{
				delNpc(王龙地图ID, 15261);
				int mapp6 = RNG.Next(23002, 24000);
				AddNpc(15261, 0f, 0f, mapp6);
				王龙地图ID = mapp6;
				flag = true;
			}
			if (flag)
			{
				发送特殊公告("[BOSS王龙]现身九泉[" + (王龙地图ID - 23000) + "]层,当前九泉累积金币数为" + 王龙的金币 / 10000 + "万,泫勃派南门码头:殷黎亭", 6, "公告");
				Form1.WriteLine(66, "自动开启九泉王龙成功");
			}
		}
		catch
		{
			Form1.WriteLine(1, "九泉BOSS王龙错误");
		}
	}

	private void 内挂打怪()
	{
		try
		{
			foreach (Players value in allConnectedChars.Values)
			{
				if (value.Client != null && !value.Player死亡 && (long)value.人物_HP > 0L)
				{
					if (value.江湖小助手打怪模式 == 1)
					{
						江湖小助手系统.江湖小助手(value);
					}
					if (value.云挂机打怪模式 == 1)
					{
						云挂机系统.离线云挂机(value);
					}
					if (value.离线挂机打怪模式 == 1)
					{
						离线挂机系统.离线挂机(value);
					}
					if (value.离线打架模式 == 1)
					{
						离线打架系统.离线打架(value);
					}
				}
			}
		}
		catch
		{
			Form1.WriteLine(1, "小助手/云挂机/离线打怪/离线打架/循环出现错误");
		}
	}

	private void 卡黑屏核查()
	{
		try
		{
			foreach (Players value in allConnectedChars.Values)
			{
				if (!value.Client.挂机 && !value.Client.假人 && !value.Client.云挂机 && value.报错次数阀值 >= 报错踢号次数)
				{
					RxjhClass.卡号记录(value.Userid, value.UserName, "报错次数", value.Player_Job, 分区编号);
					value.系统提示("你被系统踢出,检测到错误次数超过" + value.报错次数阀值 + "次。请重新上线即可", 50, "系统提示");
					value.Client.Dispose();
				}
				if ((!value.Client.挂机 && !value.Client.假人 && !value.Client.云挂机 && value.Player死亡) || value.人物_HP < 0)
				{
					if (!value.触发新手安全区(value))
					{
						value.死亡不复活计时++;
					}
					if (value.死亡不复活计时 > 死亡不复活踢号时间)
					{
						RxjhClass.卡号记录(value.Userid, value.UserName, "死亡1小时", value.Player_Job, 分区编号);
						value.系统提示("死亡延迟复活超过1小时被系统踢出。请重新上线即可", 50, "系统提示");
						value.Client.Dispose();
					}
				}
				if (value.账号是否在线 > 1)
				{
					RxjhClass.卡号记录(value.Userid, value.UserName, "世界时间同进在线", value.Player_Job, 分区编号);
					value.封号(720, value.Userid, "世界时间同进在线封号");
				}
				if (!value.Client.挂机 && !value.Client.假人 && !value.Client.云挂机)
				{
					value.人物上线计时++;
					if (value.人物上线计时 <= 30)
					{
						value.查询随机检测();
					}
					if (value.人物上线计时 == value.登陆时间随机)
					{
						value.随机时间在线++;
						value.保存随机检测();
					}
					if (value.人物上线计时 == 3600 && value.数据库被盗锁定)
					{
						value.数据库被盗锁定 = false;
						value.系统提示("异常IP登陆已解除.");
					}
				}
				if (value.随机时间在线 > 1)
				{
					RxjhClass.卡号记录(value.Userid, value.UserName, "世界时间随机在线", value.Player_Job, 分区编号);
					value.封号(720, value.Userid, "世界时间随机在线封号");
				}
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleGameException(ex, null, "卡黑屏核查", "检查玩家状态和踢号逻辑");
		}
	}

	public static void 随机BOSS系统()
	{
		try
		{
			string text = string.Empty;
			int num = 0;
			int num2 = 0;
			ConcurrentDictionary<int, NpcClass> concurrentDictionary = new ConcurrentDictionary<int, NpcClass>();
			int num3 = 1;
			foreach (NpcClass value2 in MapClass.GetnpcTemplate(101).Values)
			{
				if (value2.IsNpc != 1)
				{
					concurrentDictionary.TryAdd(num3, value2);
					num3++;
				}
			}
			int key = RNG.Next(1, concurrentDictionary.Count - 1);
			if (!concurrentDictionary.TryGetValue(key, out var value))
			{
				return;
			}
			int num4 = 0;
			foreach (MonSterClss value3 in MonSter.Values)
			{
				if (value3.FLD_BOSS == 1 && value3.FLD_PID == 15259)
				{
					float num5 = value.Rxjh_X + (float)num4;
					float num6 = value.Rxjh_Y - (float)num4;
					AddNpc(value3.FLD_PID, (int)num5, (int)num6, 101);
					num = (int)value.Rxjh_X;
					num2 = (int)value.Rxjh_Y;
					text = value3.Name;
					num4 += 10;
				}
			}
			foreach (Players value4 in allConnectedChars.Values)
			{
				if (!value4.Client.挂机)
				{
					value4.发送传音消息(value4, text.Replace("\r", string.Empty) + "携带大量宝物出现在泫勃派" + num + "," + num2 + "。", 4);
				}
			}
			if (BOSS时间计时器 != null)
			{
				BOSS时间计时器.Enabled = false;
				BOSS时间计时器.Close();
				BOSS时间计时器.Dispose();
				BOSS时间计时器 = null;
			}
			BOSS时间计时器 = new System.Timers.Timer(3600000.0);
			BOSS时间计时器.Elapsed += BOSS时间结束事件;
			BOSS时间计时器.Enabled = true;
			BOSS时间计时器.AutoReset = false;
		}
		catch (Exception ex)
		{
			RxjhClass.HandleGameException(ex, null, "随机BOSS系统", "加载NPC数据");
		}
	}

	private static void BOSS时间结束事件(object source, ElapsedEventArgs e)
	{
		try
		{
			BOSSTIME = false;
			if (BOSS时间计时器 != null)
			{
				BOSS时间计时器.Enabled = false;
				BOSS时间计时器.Close();
				BOSS时间计时器.Dispose();
				BOSS时间计时器 = null;
			}
		}
		catch (Exception ex)
		{
			if (BOSS时间计时器 != null)
			{
				BOSS时间计时器.Enabled = false;
				BOSS时间计时器.Close();
				BOSS时间计时器.Dispose();
				BOSS时间计时器 = null;
			}
			RxjhClass.HandleGameException(ex, null, "BOSS时间结束事件", "清理BOSS计时器");
		}
	}

	public void SetScript()
	{
		脚本 = new ScriptClass();
	}

	public static void 全局提示(string name, int type, string msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机)
			{
				value.系统提示(msg, type, name);
			}
		}
	}

	public static void PK全局提示(string name, int type, string msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机 && value.PK提示 == 1)
			{
				value.系统提示(msg, type, name);
			}
		}
	}

	public static void 掉落全局提示(string name, int type, string msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机 && value.掉落提示 == 1)
			{
				value.系统提示(msg, type, name);
			}
		}
	}

	public static void 开箱全局提示(string name, int type, string msg)
	{
		foreach (Players value in allConnectedChars.Values)
		{
			if (!value.Client.挂机 && value.开箱提示 == 1)
			{
				value.系统提示(msg, type, name);
			}
		}
	}

	public void SetConfig()
	{
		string text = "获取配置文件路径";
		try
		{
			Config.Path = Application.StartupPath + "\\config\\config.ini";
			text = "1";
			MainServer = ((Config.IniReadValue("GameServer", "主服务器").Trim().Length == 0) ? MainServer : int.Parse(Config.IniReadValue("GameServer", "主服务器").Trim()));
			游戏登陆端口最大连接数 = ((Config.IniReadValue("GameServer", "游戏登陆端口最大连接数").Length == 0) ? 游戏登陆端口最大连接数 : int.Parse(Config.IniReadValue("GameServer", "游戏登陆端口最大连接数")));
			游戏登陆端口最大连接时间数 = ((Config.IniReadValue("GameServer", "游戏登陆端口最大连接时间数").Length == 0) ? 游戏登陆端口最大连接时间数 : int.Parse(Config.IniReadValue("GameServer", "游戏登陆端口最大连接时间数")));
			封IP = Config.IniReadValue("GameServer", "封IP").Trim().ToLower() == "true";
			断开连接 = Config.IniReadValue("GameServer", "断开连接").Trim().ToLower() == "true";
			加入过滤列表 = Config.IniReadValue("GameServer", "加入过滤列表").Trim().ToLower() == "true";
			Key1 = Config.IniReadValue("GameServer", "Key1").Trim();
			Key2 = Config.IniReadValue("GameServer", "Key2").Trim();
			发包基数 = ((Config.IniReadValue("GameServer", "发包基数").Trim().Length == 0) ? 发包基数 : double.Parse(Config.IniReadValue("GameServer", "发包基数").Trim()));
			经验倍数 = ((Config.IniReadValue("GameServer", "经验倍数").Trim().Length == 0) ? 经验倍数 : double.Parse(Config.IniReadValue("GameServer", "经验倍数").Trim()));
			钱倍数 = ((Config.IniReadValue("GameServer", "钱倍数").Trim().Length == 0) ? 钱倍数 : double.Parse(Config.IniReadValue("GameServer", "钱倍数").Trim()));
			吸魂成功几率 = ((Config.IniReadValue("GameServer", "吸魂成功几率").Trim().Length == 0) ? 吸魂成功几率 : int.Parse(Config.IniReadValue("GameServer", "吸魂成功几率").Trim()));
			历练倍数 = ((Config.IniReadValue("GameServer", "历练倍数").Trim().Length == 0) ? 历练倍数 : int.Parse(Config.IniReadValue("GameServer", "历练倍数").Trim()));
			属性一替换成功率 = ((Config.IniReadValue("GameServer", "属性一替换成功率").Trim().Length == 0) ? 属性一替换成功率 : int.Parse(Config.IniReadValue("GameServer", "属性一替换成功率").Trim()));
			text = "20";
			暴率 = ((Config.IniReadValue("GameServer", "暴率").Trim().Length == 0) ? 暴率 : int.Parse(Config.IniReadValue("GameServer", "暴率").Trim()));
			最大在线 = ((Config.IniReadValue("GameServer", "最大在线").Trim().Length == 0) ? 最大在线 : int.Parse(Config.IniReadValue("GameServer", "最大在线").Trim()));
			服务器组ID = ((Config.IniReadValue("GameServer", "服务器组ID").Trim().Length == 0) ? 服务器组ID : int.Parse(Config.IniReadValue("GameServer", "服务器组ID").Trim()));
			服务器ID = ((Config.IniReadValue("GameServer", "服务器ID").Trim().Length == 0) ? 服务器ID : int.Parse(Config.IniReadValue("GameServer", "服务器ID").Trim()));
			游戏服务器端口 = ((Config.IniReadValue("GameServer", "游戏服务器端口").Trim().Length == 0) ? 游戏服务器端口 : int.Parse(Config.IniReadValue("GameServer", "游戏服务器端口").Trim()));
			老百宝阁服务器端口 = ((Config.IniReadValue("GameServer", "老百宝阁服务器端口").Trim().Length == 0) ? 老百宝阁服务器端口 : int.Parse(Config.IniReadValue("GameServer", "老百宝阁服务器端口").Trim()));
			帐号验证服务器端口 = ((Config.IniReadValue("GameServer", "帐号验证服务器端口").Trim().Length == 0) ? 帐号验证服务器端口 : int.Parse(Config.IniReadValue("GameServer", "帐号验证服务器端口").Trim()));
			帐号验证服务器IP = Config.IniReadValue("GameServer", "帐号验证服务器IP").Trim();
			老百宝阁地址 = Config.IniReadValue("GameServer", "老百宝阁地址").Trim();
			服务器名 = Config.IniReadValue("GameServer", "服务器名").Trim();
			vip线 = ((Config.IniReadValue("GameServer", "vip线").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "vip线").Trim()) : 0);
			是否开启附魂 = ((Config.IniReadValue("GameServer", "是否开启附魂").Trim().Length == 0) ? 是否开启附魂 : int.Parse(Config.IniReadValue("GameServer", "是否开启附魂").Trim()));
			最大附魂数 = ((Config.IniReadValue("GameServer", "最大附魂数").Trim().Length == 0) ? 最大附魂数 : int.Parse(Config.IniReadValue("GameServer", "最大附魂数").Trim()));
			附魂加战斗力 = ((Config.IniReadValue("GameServer", "附魂加战斗力").Trim().Length == 0) ? 附魂加战斗力 : int.Parse(Config.IniReadValue("GameServer", "附魂加战斗力").Trim()));
			强化附加战斗力 = ((Config.IniReadValue("GameServer", "强化附加战斗力").Trim().Length == 0) ? 强化附加战斗力 : int.Parse(Config.IniReadValue("GameServer", "强化附加战斗力").Trim()));
			附魂加攻击 = ((Config.IniReadValue("GameServer", "附魂加攻击").Trim().Length == 0) ? 附魂加攻击 : int.Parse(Config.IniReadValue("GameServer", "附魂加攻击").Trim()));
			附魂加防御 = ((Config.IniReadValue("GameServer", "附魂加防御").Trim().Length == 0) ? 附魂加防御 : int.Parse(Config.IniReadValue("GameServer", "附魂加防御").Trim()));
			仙魔大战是否开启 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战是否开启").Trim());
			仙魔大战开启小时 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战开启小时").Trim());
			text = "40";
			仙魔大战开启分 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战开启分").Trim());
			仙魔大战开启秒 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战开启秒").Trim());
			仙魔大战时长 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战时长").Trim());
			仙魔大战预备时间 = int.Parse(Config.IniReadValue("GameServer", "仙魔大战预备时间").Trim());
			强化合成概率周否开启 = int.Parse(Config.IniReadValue("GameServer", "强化合成概率周否开启").Trim());
			强化合成概率周开启小时 = int.Parse(Config.IniReadValue("GameServer", "强化合成概率周开启小时").Trim());
			强化合成概率周结束小时 = int.Parse(Config.IniReadValue("GameServer", "强化合成概率周结束小时").Trim());
			强化合成概率周分 = int.Parse(Config.IniReadValue("GameServer", "强化合成概率周分").Trim());
			强化合成概率周秒 = int.Parse(Config.IniReadValue("GameServer", "强化合成概率周秒").Trim());
			强化合成概率周倍数 = ((Config.IniReadValue("GameServer", "强化合成概率周倍数").Length == 0) ? 强化合成概率周倍数 : double.Parse(Config.IniReadValue("GameServer", "强化合成概率周倍数")));
			强化合成概率周开启时间星期 = Config.IniReadValue("GameServer", "强化合成概率周开启时间星期").Trim();
			封包封号 = ((Config.IniReadValue("GameServer", "封包封号").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "封包封号").Trim()) : 0);
			狮子吼最大数 = ((Config.IniReadValue("GameServer", "狮子吼最大数").Length == 0) ? 50 : int.Parse(Config.IniReadValue("GameServer", "狮子吼最大数")));
			初魂成功几率 = ((Config.IniReadValue("GameServer", "初魂成功几率").Length == 0) ? 初魂成功几率 : double.Parse(Config.IniReadValue("GameServer", "初魂成功几率")));
			中魂成功几率 = ((Config.IniReadValue("GameServer", "中魂成功几率").Length == 0) ? 中魂成功几率 : double.Parse(Config.IniReadValue("GameServer", "中魂成功几率")));
			四神成功几率 = ((Config.IniReadValue("GameServer", "四神成功几率").Length == 0) ? 四神成功几率 : double.Parse(Config.IniReadValue("GameServer", "四神成功几率")));
			四神变更卷成功几率 = ((Config.IniReadValue("GameServer", "四神变更卷成功几率").Length == 0) ? 四神变更卷成功几率 : double.Parse(Config.IniReadValue("GameServer", "四神变更卷成功几率")));
			强化转移符成功几率 = ((Config.IniReadValue("GameServer", "强化转移符成功几率").Length == 0) ? 强化转移符成功几率 : double.Parse(Config.IniReadValue("GameServer", "强化转移符成功几率")));
			text = "60";
			提真成功几率 = ((Config.IniReadValue("GameServer", "提真成功几率").Length == 0) ? 提真成功几率 : double.Parse(Config.IniReadValue("GameServer", "提真成功几率")));
			元宝合成 = ((Config.IniReadValue("GameServer", "元宝合成").Trim().Length == 0) ? 元宝合成 : int.Parse(Config.IniReadValue("GameServer", "元宝合成").Trim()));
			元宝送积分是否开启 = ((Config.IniReadValue("GameServer", "元宝送积分是否开启").Trim().Length == 0) ? 元宝送积分是否开启 : int.Parse(Config.IniReadValue("GameServer", "元宝送积分是否开启").Trim()));
			百宝消费榜是否开启 = ((Config.IniReadValue("GameServer", "百宝消费榜是否开启").Trim().Length == 0) ? 百宝消费榜是否开启 : int.Parse(Config.IniReadValue("GameServer", "百宝消费榜是否开启").Trim()));
			版本验证时间 = ((Config.IniReadValue("GameServer", "版本验证时间").Trim().Length == 0) ? 版本验证时间 : int.Parse(Config.IniReadValue("GameServer", "版本验证时间").Trim()));
			神豪上线公告 = ((Config.IniReadValue("GameServer", "神豪上线公告").Trim().Length == 0) ? 神豪上线公告 : int.Parse(Config.IniReadValue("GameServer", "神豪上线公告").Trim()));
			前十上线公告 = ((Config.IniReadValue("GameServer", "前十上线公告").Trim().Length == 0) ? 前十上线公告 : int.Parse(Config.IniReadValue("GameServer", "前十上线公告").Trim()));
			Vip上线公告 = ((Config.IniReadValue("GameServer", "Vip上线公告").Trim().Length == 0) ? Vip上线公告 : int.Parse(Config.IniReadValue("GameServer", "Vip上线公告").Trim()));
			Vip上线公告内容 = ((Config.IniReadValue("GameServer", "Vip上线公告内容").Trim().Length == 0) ? Vip上线公告内容 : Config.IniReadValue("GameServer", "Vip上线公告内容").Trim());
			VIP地图 = Config.IniReadValue("GameServer", "VIP地图").Trim();
			前十上线公告内容 = ((Config.IniReadValue("GameServer", "前十上线公告内容").Trim().Length == 0) ? 前十上线公告内容 : Config.IniReadValue("GameServer", "前十上线公告内容").Trim());
			点赞元宝数量 = int.Parse(Config.IniReadValue("GameServer", "点赞元宝数量").Trim());
			神豪上线公告内容 = ((Config.IniReadValue("GameServer", "神豪上线公告内容").Trim().Length == 0) ? 神豪上线公告内容 : Config.IniReadValue("GameServer", "神豪上线公告内容").Trim());
			查非法物品 = ((Config.IniReadValue("GameServer", "查非法物品").Trim().Length == 0) ? 查非法物品 : int.Parse(Config.IniReadValue("GameServer", "查非法物品").Trim()));
			查非法物品操作 = ((Config.IniReadValue("GameServer", "查非法物品操作").Trim().Length == 0) ? 查非法物品操作 : int.Parse(Config.IniReadValue("GameServer", "查非法物品操作").Trim()));
			text = "80";
			仙魔大战胜利奖励物品 = Config.IniReadValue("GameServer", "仙魔大战胜利奖励物品").Trim();
			仙魔大战失败奖励物品 = Config.IniReadValue("GameServer", "仙魔大战失败奖励物品").Trim();
			仙魔大战平局奖励物品 = Config.IniReadValue("GameServer", "仙魔大战平局奖励物品").Trim();
			势力战胜利奖励物品 = Config.IniReadValue("GameServer", "势力战胜利奖励物品").Trim();
			势力战参与奖励物品 = Config.IniReadValue("GameServer", "势力战参与奖励物品").Trim();
			天魔胜利奖励物品 = Config.IniReadValue("GameServer", "天魔胜利奖励物品").Trim();
			天魔失败奖励物品 = Config.IniReadValue("GameServer", "天魔失败奖励物品").Trim();
			门战参与奖励物品 = Config.IniReadValue("GameServer", "门战参与奖励物品").Trim();
			世界BOSS奖励物品 = Config.IniReadValue("GameServer", "世界BOSS奖励物品").Trim();
			武器合成需要物品 = Config.IniReadValue("GameServer", "武器合成需要物品").Trim();
			防具合成需要物品 = Config.IniReadValue("GameServer", "防具合成需要物品").Trim();
			是否开启打坐打怪 = int.Parse(Config.IniReadValue("GameServer", "是否开启打坐打怪").Trim());
			text = "100";
			SqlJl = ((Config.IniReadValue("GameServer", "SqlJl").Trim().Length == 0) ? SqlJl : Config.IniReadValue("GameServer", "SqlJl").Trim());
			自动存档 = ((Config.IniReadValue("GameServer", "自动存档").Trim().Length == 0) ? 自动存档 : int.Parse(Config.IniReadValue("GameServer", "自动存档").Trim()));
			物品记录 = ((Config.IniReadValue("GameServer", "物品记录").Trim().Length == 0) ? 物品记录 : int.Parse(Config.IniReadValue("GameServer", "物品记录").Trim()));
            登陆记录 = ((Config.IniReadValue("GameServer", "登陆记录").Trim().Length == 0) ? 登陆记录 : int.Parse(Config.IniReadValue("GameServer", "登陆记录").Trim()));
			掉落记录 = ((Config.IniReadValue("GameServer", "掉落记录").Trim().Length == 0) ? 掉落记录 : int.Parse(Config.IniReadValue("GameServer", "掉落记录").Trim()));
			药品记录 = ((Config.IniReadValue("GameServer", "药品记录").Trim().Length == 0) ? 药品记录 : int.Parse(Config.IniReadValue("GameServer", "药品记录").Trim()));
			合成记录 = ((Config.IniReadValue("GameServer", "合成记录").Trim().Length == 0) ? 合成记录 : int.Parse(Config.IniReadValue("GameServer", "合成记录").Trim()));
			仓库记录 = ((Config.IniReadValue("GameServer", "仓库记录").Trim().Length == 0) ? 仓库记录 : int.Parse(Config.IniReadValue("GameServer", "仓库记录").Trim()));
			商店记录 = ((Config.IniReadValue("GameServer", "商店记录").Trim().Length == 0) ? 商店记录 : int.Parse(Config.IniReadValue("GameServer", "商店记录").Trim()));
			传书记录 = ((Config.IniReadValue("GameServer", "传书记录").Trim().Length == 0) ? 传书记录 : int.Parse(Config.IniReadValue("GameServer", "传书记录").Trim()));
			开盒记录 = ((Config.IniReadValue("GameServer", "开盒记录").Trim().Length == 0) ? 开盒记录 : int.Parse(Config.IniReadValue("GameServer", "开盒记录").Trim()));
			进化记录 = ((Config.IniReadValue("GameServer", "进化记录").Trim().Length == 0) ? 进化记录 : int.Parse(Config.IniReadValue("GameServer", "进化记录").Trim()));
			卡号记录 = ((Config.IniReadValue("GameServer", "卡号记录").Trim().Length == 0) ? 卡号记录 : int.Parse(Config.IniReadValue("GameServer", "卡号记录").Trim()));
			元宝记录 = ((Config.IniReadValue("GameServer", "元宝记录").Trim().Length == 0) ? 元宝记录 : int.Parse(Config.IniReadValue("GameServer", "元宝记录").Trim()));
			百宝记录 = ((Config.IniReadValue("GameServer", "百宝记录").Trim().Length == 0) ? 百宝记录 : int.Parse(Config.IniReadValue("GameServer", "百宝记录").Trim()));
			锁定记录 = ((Config.IniReadValue("GameServer", "锁定记录").Trim().Length == 0) ? 锁定记录 : int.Parse(Config.IniReadValue("GameServer", "锁定记录").Trim()));
			记录保存天数 = ((Config.IniReadValue("GameServer", "记录保存天数").Trim().Length == 0) ? 记录保存天数 : int.Parse(Config.IniReadValue("GameServer", "记录保存天数").Trim()));
			传书保存天数 = ((Config.IniReadValue("GameServer", "传书保存天数").Trim().Length == 0) ? 传书保存天数 : int.Parse(Config.IniReadValue("GameServer", "传书保存天数").Trim()));
            自动清理记录 = Config.IniReadValue("GameServer", "自动清理记录").Trim() == "1"; //Evias
            元宝检测 = ((Config.IniReadValue("GameServer", "元宝检测").Trim().Length == 0) ? 元宝检测 : int.Parse(Config.IniReadValue("GameServer", "元宝检测").Trim()));
			外挂PK时间 = ((Config.IniReadValue("GameServer", "外挂PK时间").Trim().Length == 0) ? 外挂PK时间 : int.Parse(Config.IniReadValue("GameServer", "外挂PK时间").Trim()));
			text = "120";
			是否开启安全模式 = ((Config.IniReadValue("GameServer", "是否开启安全模式").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "是否开启安全模式").Trim()) : 0);
			是否开启新手上线设置 = ((Config.IniReadValue("GameServer", "是否开启新手上线设置").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "是否开启新手上线设置").Trim()) : 0);
			登录器模式 = ((Config.IniReadValue("GameServer", "登录器模式").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "登录器模式").Trim()) : 0);
			上线等级 = ((Config.IniReadValue("GameServer", "上线等级").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线等级").Trim()) : 0);
			上线转职等级 = ((Config.IniReadValue("GameServer", "上线转职等级").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线转职等级").Trim()) : 0);
			上线金币数量 = ((Config.IniReadValue("GameServer", "上线金币数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线金币数量").Trim()) : 0);
			上线历练数量 = ((Config.IniReadValue("GameServer", "上线历练数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线历练数量").Trim()) : 0);
			上线武勋设置 = ((Config.IniReadValue("GameServer", "上线武勋设置").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线武勋设置").Trim()) : 0);
			自动分配正邪 = ((Config.IniReadValue("GameServer", "自动分配正邪").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "自动分配正邪").Trim()) : 0);
			上线升天气功点 = ((Config.IniReadValue("GameServer", "上线升天气功点").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线升天气功点").Trim()) : 0);
			赠送气功书 = ((Config.IniReadValue("GameServer", "赠送气功书").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "赠送气功书").Trim()) : 0);
			转职赠送礼包 = int.Parse(Config.IniReadValue("GameServer", "转职赠送礼包").Trim());
			上线送礼包是否开启 = int.Parse(Config.IniReadValue("GameServer", "上线送礼包是否开启").Trim());
			上线送金符是否开启 = int.Parse(Config.IniReadValue("GameServer", "上线送金符是否开启").Trim());
			上线送礼包套装 = ((Config.IniReadValue("GameServer", "上线送礼包套装").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "上线送礼包套装").Trim()) : 0);
			是否开启银票兑换元宝 = ((Config.IniReadValue("GameServer", "是否开启银票兑换元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "是否开启银票兑换元宝").Trim()) : 0);
			银票兑换元宝 = ((Config.IniReadValue("GameServer", "银票兑换元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "银票兑换元宝").Trim()) : 0);
			是否开启挂机奖励 = ((Config.IniReadValue("GameServer", "是否开启挂机奖励").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "是否开启挂机奖励").Trim()) : 0);
			挂机奖励时间周期 = ((Config.IniReadValue("GameServer", "挂机奖励时间周期").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "挂机奖励时间周期").Trim()) : 0);
			text = "140";
			挂机奖励要求等级 = ((Config.IniReadValue("GameServer", "挂机奖励要求等级").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "挂机奖励要求等级").Trim()) : 0);
			普通挂机奖励元宝 = ((Config.IniReadValue("GameServer", "普通挂机奖励元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "普通挂机奖励元宝").Trim()) : 0);
			会员挂机奖励元宝 = ((Config.IniReadValue("GameServer", "会员挂机奖励元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "会员挂机奖励元宝").Trim()) : 0);
			普通挂机奖励武勋 = ((Config.IniReadValue("GameServer", "普通挂机奖励武勋").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "普通挂机奖励武勋").Trim()) : 0);
			会员挂机奖励武勋 = ((Config.IniReadValue("GameServer", "会员挂机奖励武勋").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "会员挂机奖励武勋").Trim()) : 0);
			普通挂机奖励钻石 = ((Config.IniReadValue("GameServer", "普通挂机奖励钻石").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "普通挂机奖励钻石").Trim()) : 0);
			会员挂机奖励钻石 = ((Config.IniReadValue("GameServer", "会员挂机奖励钻石").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "会员挂机奖励钻石").Trim()) : 0);
			会员挂机奖励金币 = ((Config.IniReadValue("GameServer", "会员挂机奖励金币").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "会员挂机奖励金币").Trim()) : 0);
			普通挂机奖励金币 = ((Config.IniReadValue("GameServer", "普通挂机奖励金币").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "普通挂机奖励金币").Trim()) : 0);
			挂机双倍时间段 = Config.IniReadValue("GameServer", "挂机双倍时间段").Trim();
			临时GM命令 = Config.IniReadValue("GameServer", "临时GM命令").Trim();
			临时管理员 = Config.IniReadValue("GameServer", "临时管理员").Trim();
			防具强化最大数量 = ((Config.IniReadValue("GameServer", "防具强化最大数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "防具强化最大数量").Trim()) : 0);
			灵宠强化最大数量 = ((Config.IniReadValue("GameServer", "灵宠强化最大数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "灵宠强化最大数量").Trim()) : 0);
			披风强化最大数量 = ((Config.IniReadValue("GameServer", "披风强化最大数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "披风强化最大数量").Trim()) : 0);
			灵宠强化是否消耗开关 = ((Config.IniReadValue("GameServer", "灵宠强化是否消耗开关").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "灵宠强化是否消耗开关").Trim()) : 0);
			披风强化是否消耗元宝 = ((Config.IniReadValue("GameServer", "披风强化是否消耗元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "披风强化是否消耗元宝").Trim()) : 0);
			新手上线说话等级 = ((Config.IniReadValue("GameServer", "新手上线说话等级").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "新手上线说话等级").Trim()) : 0);
			异口同声是否开启 = ((Config.IniReadValue("GameServer", "异口同声是否开启").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "异口同声是否开启").Trim()) : 0);
			异口同声开启时 = ((Config.IniReadValue("GameServer", "异口同声开启时 ").Trim().Length == 0) ? 异口同声开启时 : int.Parse(Config.IniReadValue("GameServer", "异口同声开启时 ").Trim()));
			异口同声开启分 = ((Config.IniReadValue("GameServer", "异口同声开启分").Trim().Length == 0) ? 异口同声开启分 : int.Parse(Config.IniReadValue("GameServer", "异口同声开启分").Trim()));
			text = "160";
			异口同声开启秒 = ((Config.IniReadValue("GameServer", "异口同声开启秒").Trim().Length == 0) ? 异口同声开启秒 : int.Parse(Config.IniReadValue("GameServer", "异口同声开启秒").Trim()));
			异口同声结束时间 = int.Parse(Config.IniReadValue("GameServer", "异口同声结束时间").Trim());
			异口同声内容 = Config.IniReadValue("GameServer", "异口同声内容").Trim();
			幸运奖是否开启 = int.Parse(Config.IniReadValue("GameServer", "幸运奖是否开启").Trim());
			幸运奖开启小时 = int.Parse(Config.IniReadValue("GameServer", "幸运奖开启小时").Trim());
			幸运奖开启分 = int.Parse(Config.IniReadValue("GameServer", "幸运奖开启分").Trim());
			幸运奖开启秒 = int.Parse(Config.IniReadValue("GameServer", "幸运奖开启秒").Trim());
			幸运奖奖励 = Config.IniReadValue("GameServer", "幸运奖奖励").Trim();
			幸运奖奖励单件物品 = Config.IniReadValue("GameServer", "幸运奖奖励单件物品").Trim();
			BOSS攻城是否开启 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城是否开启").Trim());
			BOSS攻城开启小时 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城开启小时").Trim());
			BOSS攻城开启分 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城开启分").Trim());
			BOSS攻城开启秒 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城开启秒").Trim());
			BOSS攻城倒计时 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城倒计时").Trim());
			BOSS攻城时间 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城时间").Trim());
			世界BOSS攻城是否开启 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城是否开启").Trim());
			世界BOSS攻城开启小时 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城开启小时").Trim());
			世界BOSS攻城开启分 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城开启分").Trim());
			世界BOSS攻城开启秒 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城开启秒").Trim());
			世界BOSS攻城倒计时 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城倒计时").Trim());
			世界BOSS攻城时间 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS攻城时间").Trim());
			text = "180";
			世界BOSS怪物ID = int.Parse(Config.IniReadValue("GameServer", "世界BOSS怪物ID").Trim());
			世界BOSS出现地图 = int.Parse(Config.IniReadValue("GameServer", "世界BOSS出现地图").Trim());
			世界BOSS坐标X = int.Parse(Config.IniReadValue("GameServer", "世界BOSS坐标X").Trim());
			世界BOSS坐标Y = int.Parse(Config.IniReadValue("GameServer", "世界BOSS坐标Y").Trim());
			披风强化消耗元宝数量 = ((Config.IniReadValue("GameServer", "披风强化消耗元宝数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "披风强化消耗元宝数量").Trim()) : 0);
			灵宠强化消耗武皇币数量 = ((Config.IniReadValue("GameServer", "灵宠强化消耗武皇币数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "灵宠强化消耗武皇币数量").Trim()) : 0);
			灵宠强化消耗冰魄水玉数量 = ((Config.IniReadValue("GameServer", "灵宠强化消耗冰魄水玉数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "灵宠强化消耗冰魄水玉数量").Trim()) : 0);
			挂机消除宠物忠诚度 = ((Config.IniReadValue("GameServer", "挂机消除宠物忠诚度").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "挂机消除宠物忠诚度").Trim()) : 0);
			使用经验珠等级上限 = ((Config.IniReadValue("GameServer", "使用经验珠等级上限").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "使用经验珠等级上限").Trim()));
			使用武勋丹上限 = ((Config.IniReadValue("GameServer", "使用武勋丹上限").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "使用武勋丹上限").Trim()));
			人物越级怪物掉落差 = ((Config.IniReadValue("GameServer", "人物越级怪物掉落差").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "人物越级怪物掉落差").Trim()));
			怪物越级人物掉落差 = ((Config.IniReadValue("GameServer", "怪物越级人物掉落差").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "怪物越级人物掉落差").Trim()));
			是否开启装备加解锁功能 = ((Config.IniReadValue("GameServer", "是否开启装备加解锁功能").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "是否开启装备加解锁功能").Trim()) : 0);
			装备加锁消耗元宝 = ((Config.IniReadValue("GameServer", "装备加锁消耗元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "装备加锁消耗元宝").Trim()) : 0);
			装备解锁消耗元宝 = ((Config.IniReadValue("GameServer", "装备解锁消耗元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "装备解锁消耗元宝").Trim()) : 0);
			单次交易元宝数量上限 = ((Config.IniReadValue("GameServer", "单次交易元宝数量上限").Trim().Length == 0) ? 单次交易元宝数量上限 : int.Parse(Config.IniReadValue("GameServer", "单次交易元宝数量上限").Trim()));
			帐号总元宝上限 = ((Config.IniReadValue("GameServer", "帐号总元宝上限").Trim().Length == 0) ? 帐号总元宝上限 : int.Parse(Config.IniReadValue("GameServer", "帐号总元宝上限").Trim()));
			元宝检测操作 = ((Config.IniReadValue("GameServer", "元宝检测操作").Trim().Length == 0) ? 元宝检测操作 : int.Parse(Config.IniReadValue("GameServer", "元宝检测操作").Trim()));
			text = "200";
			是否开启武勋系统 = ((Config.IniReadValue("GameServer", "是否开启武勋系统").Trim().Length == 0) ? 是否开启武勋系统 : int.Parse(Config.IniReadValue("GameServer", "是否开启武勋系统").Trim()));
			PK等级差 = ((Config.IniReadValue("GameServer", "PK等级差").Trim().Length == 0) ? PK等级差 : int.Parse(Config.IniReadValue("GameServer", "PK等级差").Trim()));
			武勋保护等级 = ((Config.IniReadValue("GameServer", "武勋保护等级").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "武勋保护等级").Trim()) : 0);
			武勋保护数量 = ((Config.IniReadValue("GameServer", "武勋保护数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "武勋保护数量").Trim()) : 0);
			死亡减少武勋数量 = Config.IniReadValue("GameServer", "死亡减少武勋数量").Trim();
			死亡回收武勋数量 = Config.IniReadValue("GameServer", "死亡回收武勋数量").Trim();
			限制地图武勋掉落倍数 = Config.IniReadValue("GameServer", "限制地图武勋掉落倍数").Trim();
			再造金刚石攻击 = Config.IniReadValue("GameServer", "再造金刚石攻击").Trim();
			再造金刚石追伤 = Config.IniReadValue("GameServer", "再造金刚石追伤").Trim();
			再造金刚石武功 = Config.IniReadValue("GameServer", "再造金刚石武功").Trim();
			再造金刚石命中 = Config.IniReadValue("GameServer", "再造金刚石命中").Trim();
			再造金刚石生命 = Config.IniReadValue("GameServer", "再造金刚石生命").Trim();
			再造寒玉石防御 = Config.IniReadValue("GameServer", "再造寒玉石防御").Trim();
			再造寒玉石回避 = Config.IniReadValue("GameServer", "再造寒玉石回避").Trim();
			再造寒玉石生命 = Config.IniReadValue("GameServer", "再造寒玉石生命").Trim();
			再造寒玉石内功 = Config.IniReadValue("GameServer", "再造寒玉石内功").Trim();
			再造寒玉石武防 = Config.IniReadValue("GameServer", "再造寒玉石武防").Trim();
			text = "220";
			升级经验表基数 = ((Config.IniReadValue("GameServer", "升级经验表基数") == "") ? 升级经验表基数 : double.Parse(Config.IniReadValue("GameServer", "升级经验表基数")));
			累计充值属性加成 = int.Parse(Config.IniReadValue("GameServer", "累计充值属性加成").Trim());
			天气系统开关 = int.Parse(Config.IniReadValue("GameServer", "天气系统开关").Trim());
			无限负重 = int.Parse(Config.IniReadValue("GameServer", "无限负重").Trim());
			PK掉装备 = int.Parse(Config.IniReadValue("GameServer", "PK掉装备").Trim());
			PK掉装备善恶 = int.Parse(Config.IniReadValue("GameServer", "PK掉装备善恶").Trim());
			PK掉装备几率 = int.Parse(Config.IniReadValue("GameServer", "PK掉装备几率").Trim());
			至高无上称号奖励 = Config.IniReadValue("GameServer", "至高无上称号奖励").Trim().Split(';');
			举世无双称号奖励 = Config.IniReadValue("GameServer", "举世无双称号奖励").Trim().Split(';');
			雄霸天下称号奖励 = Config.IniReadValue("GameServer", "雄霸天下称号奖励").Trim().Split(';');
			孤胆英雄称号奖励 = Config.IniReadValue("GameServer", "孤胆英雄称号奖励").Trim().Split(';');
			英雄豪杰称号奖励 = Config.IniReadValue("GameServer", "英雄豪杰称号奖励").Trim().Split(';');
			text = "240";
			全套强12增加属性 = Config.IniReadValue("GameServer", "全套强12增加属性").Trim().Split(';');
			全套强13增加属性 = Config.IniReadValue("GameServer", "全套强13增加属性").Trim().Split(';');
			全套强14增加属性 = Config.IniReadValue("GameServer", "全套强14增加属性").Trim().Split(';');
			全套强15增加属性 = Config.IniReadValue("GameServer", "全套强15增加属性").Trim().Split(';');
			福利命令 = Config.IniReadValue("GameServer", "福利命令").Trim();
			装备升级命令 = Config.IniReadValue("GameServer", "装备升级命令").Trim();
			追杀命令 = Config.IniReadValue("GameServer", "追杀命令").Trim();
			转生命令 = Config.IniReadValue("GameServer", "转生命令").Trim();
			限制转生次数 = int.Parse(Config.IniReadValue("GameServer", "限制转生次数").Trim());
			转生需要几转 = int.Parse(Config.IniReadValue("GameServer", "转生需要几转").Trim());
			转生需要等级 = int.Parse(Config.IniReadValue("GameServer", "转生需要等级").Trim());
			转生回落等级 = int.Parse(Config.IniReadValue("GameServer", "转生回落等级").Trim());
			转生获得属性 = Config.IniReadValue("GameServer", "转生获得属性").Trim();
			text = "280";
			武器十五阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器十五阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器十五阶段添加攻击").Trim()) : 0);
			武器十六阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器十六阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器十六阶段添加攻击").Trim()) : 0);
			武器十七阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器十七阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器十七阶段添加攻击").Trim()) : 0);
			武器十八阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器十八阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器十八阶段添加攻击").Trim()) : 0);
			武器十九阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器十九阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器十九阶段添加攻击").Trim()) : 0);
			武器二十阶段添加攻击 = ((!(Config.IniReadValue("GameServer", "武器二十阶段添加攻击").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "武器二十阶段添加攻击").Trim()) : 0);
			八彩提示是否开启 = int.Parse(Config.IniReadValue("GameServer", "八彩提示是否开启").Trim());
			八彩红色提示内容 = Config.IniReadValue("GameServer", "八彩红色提示内容").Trim();
			八彩赤色提示内容 = Config.IniReadValue("GameServer", "八彩赤色提示内容").Trim();
			八彩橙色提示内容 = Config.IniReadValue("GameServer", "八彩橙色提示内容").Trim();
			八彩绿色提示内容 = Config.IniReadValue("GameServer", "八彩绿色提示内容").Trim();
			八彩蓝色提示内容 = Config.IniReadValue("GameServer", "八彩蓝色提示内容").Trim();
			text = "300";
			八彩深蓝提示内容 = Config.IniReadValue("GameServer", "八彩深蓝提示内容").Trim();
			八彩紫色提示内容 = Config.IniReadValue("GameServer", "八彩紫色提示内容").Trim();
			八彩浅色提示内容 = Config.IniReadValue("GameServer", "八彩浅色提示内容").Trim();

            在线人数是否开启 = ((Config.IniReadValue("GameServer", "在线人数是否开启").Trim() == "") ? 在线人数是否开启 : int.Parse(Config.IniReadValue("GameServer", "在线人数是否开启").Trim()));
            在线基本人数 = ((Config.IniReadValue("GameServer", "在线基本人数").Trim() == "") ? 在线基本人数 : int.Parse(Config.IniReadValue("GameServer", "在线基本人数").Trim()));

            怪物防御百分比 = ((Config.IniReadValue("GameServer", "怪物防御百分比").Trim().Length == 0) ? 怪物防御百分比 : double.Parse(Config.IniReadValue("GameServer", "怪物防御百分比").Trim()));
			怪物攻击百分比 = ((Config.IniReadValue("GameServer", "怪物攻击百分比").Trim().Length == 0) ? 怪物攻击百分比 : double.Parse(Config.IniReadValue("GameServer", "怪物攻击百分比").Trim()));
			每次再造消耗设置 = ((Config.IniReadValue("GameServer", "每次再造消耗设置").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "每次再造消耗设置").Trim()) : 0);
			每次消耗的数量 = ((Config.IniReadValue("GameServer", "每次消耗的数量").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "每次消耗的数量").Trim()) : 0);
			double num = ((Config.IniReadValue("GameServer", "武功防降低百分比").Trim().Length == 0) ? 武功防降低百分比 : double.Parse(Config.IniReadValue("GameServer", "武功防降低百分比").Trim()));
			武功攻击力百分比 = ((Config.IniReadValue("GameServer", "武功攻击力百分比").Trim().Length == 0) ? 武功攻击力百分比 : double.Parse(Config.IniReadValue("GameServer", "武功攻击力百分比").Trim()));
			攻减防加乘 = ((Config.IniReadValue("GameServer", "攻减防加乘").Trim().Length == 0) ? 攻减防加乘 : double.Parse(Config.IniReadValue("GameServer", "攻减防加乘").Trim()));
			武功减武防加乘 = ((Config.IniReadValue("GameServer", "武功减武防加乘").Trim().Length == 0) ? 武功减武防加乘 : double.Parse(Config.IniReadValue("GameServer", "武功减武防加乘").Trim()));
			换线命令 = Config.IniReadValue("GameServer", "换线命令").Trim();
			发红包命令 = Config.IniReadValue("GameServer", "发红包命令").Trim();
			抢红包命令 = Config.IniReadValue("GameServer", "抢红包命令").Trim();
			红包检测时间 = int.Parse(Config.IniReadValue("GameServer", "红包检测时间").Trim());
			发红包公告 = Config.IniReadValue("GameServer", "发红包公告").Trim();
			是否开启王龙 = ((Config.IniReadValue("GameServer", "是否开启王龙").Trim().Length == 0) ? 是否开启王龙 : int.Parse(Config.IniReadValue("GameServer", "是否开启王龙").Trim()));
			九泉金币比率 = ((Config.IniReadValue("GameServer", "九泉金币比率") == "") ? 九泉金币比率 : double.Parse(Config.IniReadValue("GameServer", "九泉金币比率")));
			text = "320";
			武功防增加百分比 = ((Config.IniReadValue("GameServer", "武功防增加百分比").Trim().Length == 0) ? 武功防增加百分比 : double.Parse(Config.IniReadValue("GameServer", "武功防增加百分比").Trim()));
			if (武功防降低百分比 != num)
			{
				武功防降低百分比 = num;
				foreach (Players value in allConnectedChars.Values)
				{
					value.计算人物装备数据();
				}
			}
			心跳检测开关 = ((Config.IniReadValue("GameServer", "心跳检测开关").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "心跳检测开关").Trim()) : 0);
			心跳检测时间阀值 = ((Config.IniReadValue("GameServer", "心跳检测时间阀值").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "心跳检测时间阀值").Trim()) : 0);
			心跳检测时长 = ((Config.IniReadValue("GameServer", "心跳检测时长").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "心跳检测时长").Trim()) : 0);
			安全模式时间 = Config.IniReadValue("GameServer", "安全模式时间").Trim().Split('-');
			安全模式消耗元宝 = ((Config.IniReadValue("GameServer", "安全模式消耗元宝").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "安全模式消耗元宝").Trim()) : 0);
			每次狮吼功消耗元宝 = ((Config.IniReadValue("GameServer", "每次狮吼功消耗元宝").Trim().Length == 0) ? 每次狮吼功消耗元宝 : int.Parse(Config.IniReadValue("GameServer", "每次狮吼功消耗元宝").Trim()));
			信任连接IP = Config.IniReadValue("GameServer", "信任连接IP").Trim();
			每次分解消耗元宝数 = ((Config.IniReadValue("GameServer", "每次分解消耗元宝数").Trim().Length == 0) ? 每次分解消耗元宝数 : int.Parse(Config.IniReadValue("GameServer", "每次分解消耗元宝数").Trim()));
			是否开启对练场赌元宝 = ((Config.IniReadValue("GameServer", "是否开启对练场赌元宝").Trim().Length == 0) ? 是否开启对练场赌元宝 : int.Parse(Config.IniReadValue("GameServer", "是否开启对练场赌元宝").Trim()));
			进场最低费用 = ((Config.IniReadValue("GameServer", "进场最低费用").Trim().Length == 0) ? 进场最低费用 : int.Parse(Config.IniReadValue("GameServer", "进场最低费用").Trim()));
			场地佣金百分比 = ((Config.IniReadValue("GameServer", "场地佣金百分比").Trim().Length == 0) ? 场地佣金百分比 : double.Parse(Config.IniReadValue("GameServer", "场地佣金百分比").Trim()));
			场地有效范围 = ((Config.IniReadValue("GameServer", "场地有效范围").Trim().Length == 0) ? 场地有效范围 : float.Parse(Config.IniReadValue("GameServer", "场地有效范围").Trim()));
			分数扣完扣除元宝 = ((Config.IniReadValue("GameServer", "分数扣完扣除元宝").Trim().Length == 0) ? 分数扣完扣除元宝 : int.Parse(Config.IniReadValue("GameServer", "分数扣完扣除元宝").Trim()));
			分数扣完扣除金钱 = ((Config.IniReadValue("GameServer", "分数扣完扣除金钱").Trim().Length == 0) ? 分数扣完扣除金钱 : int.Parse(Config.IniReadValue("GameServer", "分数扣完扣除金钱").Trim()));
			允许玩家押注数量 = ((Config.IniReadValue("GameServer", "允许玩家押注数量").Trim().Length == 0) ? 允许玩家押注数量 : int.Parse(Config.IniReadValue("GameServer", "允许玩家押注数量").Trim()));
			text = "340";
			地图锁定 = Config.IniReadValue("GameServer", "地图锁定").Trim();
			每日武勋解锁地图 = Config.IniReadValue("GameServer", "每日武勋解锁地图").Trim();
			允许开店地图 = Config.IniReadValue("GameServer", "允许开店地图").Trim();
			组队等级限制 = ((Config.IniReadValue("GameServer", "组队等级限制").Trim().Length == 0) ? 组队等级限制 : int.Parse(Config.IniReadValue("GameServer", "组队等级限制").Trim()));
			是否开启死亡掉经验 = ((Config.IniReadValue("GameServer", "是否开启死亡掉经验").Trim().Length == 0) ? 是否开启死亡掉经验 : int.Parse(Config.IniReadValue("GameServer", "是否开启死亡掉经验").Trim()));
			VIP经验增加百分比 = ((Config.IniReadValue("GameServer", "VIP经验增加百分比").Trim().Length == 0) ? VIP经验增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP经验增加百分比").Trim()));
			VIP历练增加百分比 = ((Config.IniReadValue("GameServer", "VIP历练增加百分比").Trim().Length == 0) ? VIP历练增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP历练增加百分比").Trim()));
			VIP金钱增加百分比 = ((Config.IniReadValue("GameServer", "VIP金钱增加百分比").Trim().Length == 0) ? VIP金钱增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP金钱增加百分比").Trim()));
			医生PK距离 = ((Config.IniReadValue("GameServer", "医生PK距离").Trim().Length == 0) ? 医生PK距离 : double.Parse(Config.IniReadValue("GameServer", "医生PK距离").Trim()));
			弓箭手PK距离 = ((Config.IniReadValue("GameServer", "弓箭手PK距离").Trim().Length == 0) ? 弓箭手PK距离 : double.Parse(Config.IniReadValue("GameServer", "弓箭手PK距离").Trim()));
			其他职业PK距离 = ((Config.IniReadValue("GameServer", "其他职业PK距离").Trim().Length == 0) ? 其他职业PK距离 : double.Parse(Config.IniReadValue("GameServer", "其他职业PK距离").Trim()));
			医生打怪距离 = ((Config.IniReadValue("GameServer", "医生打怪距离").Trim().Length == 0) ? 医生打怪距离 : double.Parse(Config.IniReadValue("GameServer", "医生打怪距离").Trim()));
			弓箭手打怪距离 = ((Config.IniReadValue("GameServer", "弓箭手打怪距离").Trim().Length == 0) ? 弓箭手打怪距离 : double.Parse(Config.IniReadValue("GameServer", "弓箭手打怪距离").Trim()));
			其他职业打怪距离 = ((Config.IniReadValue("GameServer", "其他职业打怪距离").Trim().Length == 0) ? 其他职业打怪距离 : double.Parse(Config.IniReadValue("GameServer", "其他职业打怪距离").Trim()));
			神女PK距离 = ((Config.IniReadValue("GameServer", "神女PK距离").Trim().Length == 0) ? 神女PK距离 : double.Parse(Config.IniReadValue("GameServer", "神女PK距离").Trim()));
			梅柳真PK距离 = ((Config.IniReadValue("GameServer", "梅柳真PK距离").Trim().Length == 0) ? 梅柳真PK距离 : double.Parse(Config.IniReadValue("GameServer", "梅柳真PK距离").Trim()));
			神女打怪距离 = ((Config.IniReadValue("GameServer", "神女打怪距离").Trim().Length == 0) ? 神女打怪距离 : double.Parse(Config.IniReadValue("GameServer", "神女打怪距离").Trim()));
			梅柳真打怪距离 = ((Config.IniReadValue("GameServer", "梅柳真打怪距离").Trim().Length == 0) ? 梅柳真打怪距离 : double.Parse(Config.IniReadValue("GameServer", "梅柳真打怪距离").Trim()));
			第一名奖励礼包 = ((Config.IniReadValue("GameServer", "第一名奖励礼包").Trim().Length == 0) ? 500 : int.Parse(Config.IniReadValue("GameServer", "第一名奖励礼包").Trim()));
			武林血战奖励礼包 = ((Config.IniReadValue("GameServer", "武林血战奖励礼包").Trim().Length == 0) ? 500 : int.Parse(Config.IniReadValue("GameServer", "武林血战奖励礼包").Trim()));
			双倍奖励是否开启 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励是否开启").Trim());
			text = "360";
			双倍奖励开启小时 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励开启小时").Trim());
			双倍奖励开启分 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励开启分").Trim());
			双倍奖励开启秒 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励开启秒").Trim());
			双倍奖励结束时间 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励结束时间").Trim());
			双倍奖励爆率倍数 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励爆率倍数").Trim());
			双倍奖励武勋倍数 = int.Parse(Config.IniReadValue("GameServer", "双倍奖励武勋倍数").Trim());
			双倍奖励公告内容 = Config.IniReadValue("GameServer", "双倍奖励公告内容").Trim();
			双倍奖励经验倍数 = ((Config.IniReadValue("GameServer", "双倍奖励经验倍数").Trim().Length == 0) ? 双倍奖励经验倍数 : double.Parse(Config.IniReadValue("GameServer", "双倍奖励经验倍数").Trim()));
			移动间隔时间 = ((Config.IniReadValue("GameServer", "移动间隔时间").Trim().Length == 0) ? 移动间隔时间 : int.Parse(Config.IniReadValue("GameServer", "移动间隔时间").Trim()));
			VIP经验增加百分比 = ((Config.IniReadValue("GameServer", "VIP经验增加百分比").Trim().Length == 0) ? VIP经验增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP经验增加百分比").Trim()));
			VIP历练增加百分比 = ((Config.IniReadValue("GameServer", "VIP历练增加百分比").Trim().Length == 0) ? VIP历练增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP历练增加百分比").Trim()));
			VIP金钱增加百分比 = ((Config.IniReadValue("GameServer", "VIP金钱增加百分比").Trim().Length == 0) ? VIP金钱增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP金钱增加百分比").Trim()));
			VIP合成率增加百分比 = ((Config.IniReadValue("GameServer", "VIP合成率增加百分比").Trim().Length == 0) ? VIP合成率增加百分比 : double.Parse(Config.IniReadValue("GameServer", "VIP合成率增加百分比").Trim()));
			一级推广经验增加百分比 = ((Config.IniReadValue("GameServer", "一级推广经验增加百分比").Trim().Length == 0) ? 一级推广经验增加百分比 : double.Parse(Config.IniReadValue("GameServer", "一级推广经验增加百分比").Trim()));
			二级推广经验增加百分比 = ((Config.IniReadValue("GameServer", "二级推广经验增加百分比").Trim().Length == 0) ? 二级推广经验增加百分比 : double.Parse(Config.IniReadValue("GameServer", "二级推广经验增加百分比").Trim()));
			三级推广经验增加百分比 = ((Config.IniReadValue("GameServer", "三级推广经验增加百分比").Trim().Length == 0) ? 三级推广经验增加百分比 : double.Parse(Config.IniReadValue("GameServer", "三级推广经验增加百分比").Trim()));
			队伍红包增加经验百分比 = ((Config.IniReadValue("GameServer", "队伍红包增加经验百分比").Trim().Length == 0) ? 队伍红包增加经验百分比 : double.Parse(Config.IniReadValue("GameServer", "队伍红包增加经验百分比").Trim()));
			队伍红包增加金钱百分比 = ((Config.IniReadValue("GameServer", "队伍红包增加金钱百分比").Trim().Length == 0) ? 队伍红包增加金钱百分比 : double.Parse(Config.IniReadValue("GameServer", "队伍红包增加金钱百分比").Trim()));
			text = "380";
			四级门派增加百分比 = ((Config.IniReadValue("GameServer", "四级门派增加百分比").Trim().Length == 0) ? 四级门派增加百分比 : double.Parse(Config.IniReadValue("GameServer", "四级门派增加百分比").Trim()));
			五级门派增加百分比 = ((Config.IniReadValue("GameServer", "五级门派增加百分比").Trim().Length == 0) ? 五级门派增加百分比 : double.Parse(Config.IniReadValue("GameServer", "五级门派增加百分比").Trim()));
			六级门派增加百分比 = ((Config.IniReadValue("GameServer", "六级门派增加百分比").Trim().Length == 0) ? 六级门派增加百分比 : double.Parse(Config.IniReadValue("GameServer", "六级门派增加百分比").Trim()));
			七级门派增加百分比 = ((Config.IniReadValue("GameServer", "七级门派增加百分比").Trim().Length == 0) ? 七级门派增加百分比 : double.Parse(Config.IniReadValue("GameServer", "七级门派增加百分比").Trim()));
			二人组队增加百分比 = ((Config.IniReadValue("GameServer", "二人组队增加百分比").Trim().Length == 0) ? 二人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "二人组队增加百分比").Trim()));
			三人组队增加百分比 = ((Config.IniReadValue("GameServer", "三人组队增加百分比").Trim().Length == 0) ? 三人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "三人组队增加百分比").Trim()));
			四人组队增加百分比 = ((Config.IniReadValue("GameServer", "四人组队增加百分比").Trim().Length == 0) ? 四人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "四人组队增加百分比").Trim()));
			五人组队增加百分比 = ((Config.IniReadValue("GameServer", "五人组队增加百分比").Trim().Length == 0) ? 五人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "五人组队增加百分比").Trim()));
			六人组队增加百分比 = ((Config.IniReadValue("GameServer", "六人组队增加百分比").Trim().Length == 0) ? 六人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "六人组队增加百分比").Trim()));
			七人组队增加百分比 = ((Config.IniReadValue("GameServer", "七人组队增加百分比").Trim().Length == 0) ? 七人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "七人组队增加百分比").Trim()));
			八人组队增加百分比 = ((Config.IniReadValue("GameServer", "八人组队增加百分比").Trim().Length == 0) ? 八人组队增加百分比 : double.Parse(Config.IniReadValue("GameServer", "八人组队增加百分比").Trim()));
			开启门战系统 = ((Config.IniReadValue("GameServer", "开启门战系统").Trim().Length == 0) ? 开启门战系统 : int.Parse(Config.IniReadValue("GameServer", "开启门战系统").Trim()));
			门战系统开启时 = ((Config.IniReadValue("GameServer", "门战系统开启时").Trim().Length == 0) ? 门战系统开启时 : int.Parse(Config.IniReadValue("GameServer", "门战系统开启时").Trim()));
			门战系统开启分 = ((Config.IniReadValue("GameServer", "门战系统开启分").Trim().Length == 0) ? 门战系统开启分 : int.Parse(Config.IniReadValue("GameServer", "门战系统开启分").Trim()));
			门战系统开启秒 = ((Config.IniReadValue("GameServer", "门战系统开启秒").Trim().Length == 0) ? 门战系统开启秒 : int.Parse(Config.IniReadValue("GameServer", "门战系统开启秒").Trim()));
			申请门战需要元宝 = ((Config.IniReadValue("GameServer", "申请门战需要元宝").Trim().Length == 0) ? 申请门战需要元宝 : int.Parse(Config.IniReadValue("GameServer", "申请门战需要元宝").Trim()));
			英雄职业转职需要武器 = ((Config.IniReadValue("GameServer", "英雄职业转职需要武器").Trim().Length == 0) ? 英雄职业转职需要武器 : int.Parse(Config.IniReadValue("GameServer", "英雄职业转职需要武器").Trim()));
			限制最高级别 = ((Config.IniReadValue("GameServer", "限制最高级别").Trim().Length == 0) ? 限制最高级别 : int.Parse(Config.IniReadValue("GameServer", "限制最高级别").Trim()));
			天关经验提高百分比基数 = ((Config.IniReadValue("GameServer", "天关经验提高百分比基数").Trim().Length == 0) ? 天关经验提高百分比基数 : double.Parse(Config.IniReadValue("GameServer", "天关经验提高百分比基数").Trim()));
			天关物品爆率提高基数 = ((Config.IniReadValue("GameServer", "天关物品爆率提高基数").Trim().Length == 0) ? 天关物品爆率提高基数 : int.Parse(Config.IniReadValue("GameServer", "天关物品爆率提高基数").Trim()));
			text = "400";
			天关经验提高百分比递增 = ((Config.IniReadValue("GameServer", "天关经验提高百分比递增").Trim().Length == 0) ? 天关经验提高百分比递增 : double.Parse(Config.IniReadValue("GameServer", "天关经验提高百分比递增").Trim()));
			天关物品爆率提高递增 = ((Config.IniReadValue("GameServer", "天关物品爆率提高递增").Trim().Length == 0) ? 天关物品爆率提高递增 : int.Parse(Config.IniReadValue("GameServer", "天关物品爆率提高递增").Trim()));
			装备提真消耗 = ((Config.IniReadValue("GameServer", "装备提真消耗").Trim().Length == 0) ? 装备提真消耗 : int.Parse(Config.IniReadValue("GameServer", "装备提真消耗").Trim()));
			装备提真数量 = ((Config.IniReadValue("GameServer", "装备提真数量").Trim().Length == 0) ? 装备提真数量 : int.Parse(Config.IniReadValue("GameServer", "装备提真数量").Trim()));
			随机BOSS出现时间表 = Config.IniReadValue("GameServer", "随机BOSS出现时间表").Trim();
			门派第一称号奖励 = Config.IniReadValue("GameServer", "门派第一称号奖励").Trim().Split(';');
			门派第二称号奖励 = Config.IniReadValue("GameServer", "门派第二称号奖励").Trim().Split(';');
			门派第三称号奖励 = Config.IniReadValue("GameServer", "门派第三称号奖励").Trim().Split(';');
			当前是否是银币线路 = ((Config.IniReadValue("GameServer", "当前是否是银币线路").Trim().Length == 0) ? 当前是否是银币线路 : int.Parse(Config.IniReadValue("GameServer", "当前是否是银币线路").Trim()));
			是否开启共用银币市场 = ((Config.IniReadValue("GameServer", "是否开启共用银币市场").Trim().Length == 0) ? 是否开启共用银币市场 : int.Parse(Config.IniReadValue("GameServer", "是否开启共用银币市场").Trim()));
			打开换线线路1 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路1").Trim());
			打开换线线路2 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路2").Trim());
			打开换线线路3 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路3").Trim());
			打开换线线路4 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路4").Trim());
			打开换线线路5 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路5").Trim());
			打开换线线路6 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路6").Trim());
			打开换线线路7 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路7").Trim());
			打开换线线路8 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路8").Trim());
			打开换线线路9 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路9").Trim());
			text = "420";
			打开换线线路10 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路10").Trim());
			打开换线线路11 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路11").Trim());
			打开换线线路12 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路12").Trim());
			武器宝珠增加属性类型 = int.Parse(Config.IniReadValue("GameServer", "武器宝珠增加属性类型").Trim());
			武器宝珠增加属性值 = int.Parse(Config.IniReadValue("GameServer", "武器宝珠增加属性值").Trim());
			衣服宝珠增加属性类型 = int.Parse(Config.IniReadValue("GameServer", "衣服宝珠增加属性类型").Trim());
			衣服宝珠增加属性值 = int.Parse(Config.IniReadValue("GameServer", "衣服宝珠增加属性值").Trim());
			护手宝珠增加属性类型 = int.Parse(Config.IniReadValue("GameServer", "护手宝珠增加属性类型").Trim());
			护手宝珠增加属性值 = int.Parse(Config.IniReadValue("GameServer", "护手宝珠增加属性值").Trim());
			鞋子宝珠增加属性类型 = int.Parse(Config.IniReadValue("GameServer", "鞋子宝珠增加属性类型").Trim());
			鞋子宝珠增加属性值 = int.Parse(Config.IniReadValue("GameServer", "鞋子宝珠增加属性值").Trim());
			内甲宝珠增加属性类型 = int.Parse(Config.IniReadValue("GameServer", "内甲宝珠增加属性类型").Trim());
			内甲宝珠增加属性值 = int.Parse(Config.IniReadValue("GameServer", "内甲宝珠增加属性值").Trim());
			冲关地图 = Config.IniReadValue("GameServer", "冲关地图").Trim();
			贡献元宝命令 = Config.IniReadValue("GameServer", "贡献元宝命令").Trim();
			贡献元宝数 = int.Parse(Config.IniReadValue("GameServer", "贡献元宝数").Trim());
			贡献元宝荣誉点 = int.Parse(Config.IniReadValue("GameServer", "贡献元宝荣誉点").Trim());
			特效道具ID = int.Parse(Config.IniReadValue("GameServer", "特效道具ID").Trim());
			玫瑰第一名奖励 = Config.IniReadValue("GameServer", "玫瑰第一名奖励").Trim().Split(';');
			玫瑰第二名奖励 = Config.IniReadValue("GameServer", "玫瑰第二名奖励").Trim().Split(';');
			text = "440";
			玫瑰第三名奖励 = Config.IniReadValue("GameServer", "玫瑰第三名奖励").Trim().Split(';');
			玫瑰第四名奖励 = Config.IniReadValue("GameServer", "玫瑰第四名奖励").Trim().Split(';');
			玫瑰第五名奖励 = Config.IniReadValue("GameServer", "玫瑰第五名奖励").Trim().Split(';');
			地图限制等级 = Config.IniReadValue("GameServer", "地图限制等级").Trim().Split(';');


            强化数量大于发送快报 = Config.IniReadValue("GameServer", "强化数量大于发送快报").Trim().Split(';');
			门战准备时间 = int.Parse(Config.IniReadValue("GameServer", "门战准备时间").Trim());
			门战总时间 = int.Parse(Config.IniReadValue("GameServer", "门战总时间").Trim());
			武林血战是否开启 = int.Parse(Config.IniReadValue("GameServer", "武林血战是否开启").Trim());
			武林血战开启小时 = int.Parse(Config.IniReadValue("GameServer", "武林血战开启小时").Trim());
			武林血战开启分 = int.Parse(Config.IniReadValue("GameServer", "武林血战开启分").Trim());
			武林血战开启秒 = int.Parse(Config.IniReadValue("GameServer", "武林血战开启秒").Trim());
			武林血战参加奖励 = Config.IniReadValue("GameServer", "武林血战参加奖励").Trim();
			武林血战第一回合奖励 = Config.IniReadValue("GameServer", "武林血战第一回合奖励").Trim();
			武林血战第二回合奖励 = Config.IniReadValue("GameServer", "武林血战第二回合奖励").Trim();
			武林血战第三回合奖励 = Config.IniReadValue("GameServer", "武林血战第三回合奖励").Trim();
			武林血战参战等级 = ((Config.IniReadValue("GameServer", "武林血战参战等级").Trim() == "") ? 武林血战参战等级 : int.Parse(Config.IniReadValue("GameServer", "武林血战参战等级").Trim()));
			分区编号 = Config.IniReadValue("GameServer", "分区编号").Trim();
			最大速度超出次数操作 = ((Config.IniReadValue("GameServer", "最大速度超出次数操作").Trim().Length == 0) ? 最大速度超出次数操作 : int.Parse(Config.IniReadValue("GameServer", "最大速度超出次数操作").Trim()));
			三十秒内允许超出次数 = ((Config.IniReadValue("GameServer", "三十秒内允许超出次数").Trim().Length == 0) ? 三十秒内允许超出次数 : int.Parse(Config.IniReadValue("GameServer", "三十秒内允许超出次数").Trim()));
			text = "460";
			周末武勋量 = ((Config.IniReadValue("GameServer", "周末武勋量").Trim().Length == 0) ? 周末武勋量 : int.Parse(Config.IniReadValue("GameServer", "周末武勋量").Trim()));
			二转每日武勋上限 = ((Config.IniReadValue("GameServer", "二转每日武勋上限").Trim().Length == 0) ? 二转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "二转每日武勋上限").Trim()));
			三转每日武勋上限 = ((Config.IniReadValue("GameServer", "三转每日武勋上限").Trim().Length == 0) ? 三转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "三转每日武勋上限").Trim()));
			四转每日武勋上限 = ((Config.IniReadValue("GameServer", "四转每日武勋上限").Trim().Length == 0) ? 四转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "四转每日武勋上限").Trim()));
			五转每日武勋上限 = ((Config.IniReadValue("GameServer", "五转每日武勋上限").Trim().Length == 0) ? 五转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "五转每日武勋上限").Trim()));
			六转每日武勋上限 = ((Config.IniReadValue("GameServer", "六转每日武勋上限").Trim().Length == 0) ? 六转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "六转每日武勋上限").Trim()));
			七转每日武勋上限 = ((Config.IniReadValue("GameServer", "七转每日武勋上限").Trim().Length == 0) ? 七转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "七转每日武勋上限").Trim()));
			八转每日武勋上限 = ((Config.IniReadValue("GameServer", "八转每日武勋上限").Trim().Length == 0) ? 八转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "八转每日武勋上限").Trim()));
			九转每日武勋上限 = ((Config.IniReadValue("GameServer", "九转每日武勋上限").Trim().Length == 0) ? 九转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "九转每日武勋上限").Trim()));
			十转每日武勋上限 = ((Config.IniReadValue("GameServer", "十转每日武勋上限").Trim().Length == 0) ? 十转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "十转每日武勋上限").Trim()));
			三十五级以下经验倍数 = ((Config.IniReadValue("GameServer", "三十五级以下经验倍数").Trim() == "") ? 三十五级以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "三十五级以下经验倍数").Trim()));
			六十级以下经验倍数 = ((Config.IniReadValue("GameServer", "六十级以下经验倍数").Trim() == "") ? 六十级以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "六十级以下经验倍数").Trim()));
			八十级以下经验倍数 = ((Config.IniReadValue("GameServer", "八十级以下经验倍数").Trim() == "") ? 八十级以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "八十级以下经验倍数").Trim()));
			一百级以下经验倍数 = ((Config.IniReadValue("GameServer", "一百级以下经验倍数").Trim() == "") ? 一百级以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百级以下经验倍数").Trim()));
			一百一以下经验倍数 = ((Config.IniReadValue("GameServer", "一百一以下经验倍数").Trim() == "") ? 一百一以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百一以下经验倍数").Trim()));
			一百二以下经验倍数 = ((Config.IniReadValue("GameServer", "一百二以下经验倍数").Trim() == "") ? 一百二以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百二以下经验倍数").Trim()));
			一百三以下经验倍数 = ((Config.IniReadValue("GameServer", "一百三以下经验倍数").Trim() == "") ? 一百三以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百三以下经验倍数").Trim()));
			一百四以下经验倍数 = ((Config.IniReadValue("GameServer", "一百四以下经验倍数").Trim() == "") ? 一百四以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百四以下经验倍数").Trim()));
			一百五以下经验倍数 = ((Config.IniReadValue("GameServer", "一百五以下经验倍数").Trim() == "") ? 一百五以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百五以下经验倍数").Trim()));
			一百六以下经验倍数 = ((Config.IniReadValue("GameServer", "一百六以下经验倍数").Trim() == "") ? 一百六以下经验倍数 : double.Parse(Config.IniReadValue("GameServer", "一百六以下经验倍数").Trim()));
			自定义经验等级 = ((Config.IniReadValue("GameServer", "自定义经验等级").Trim().Length == 0) ? 自定义经验等级 : int.Parse(Config.IniReadValue("GameServer", "自定义经验等级").Trim()));
			text = "480";
			自定义等级经验倍数 = ((Config.IniReadValue("GameServer", "自定义等级经验倍数").Trim() == "") ? 自定义等级经验倍数 : double.Parse(Config.IniReadValue("GameServer", "自定义等级经验倍数").Trim()));
			允许挂机 = ((Config.IniReadValue("GameServer", "允许挂机").Trim().Length == 0) ? 允许挂机 : int.Parse(Config.IniReadValue("GameServer", "允许挂机").Trim()));
			报错踢号次数 = ((Config.IniReadValue("GameServer", "报错踢号次数").Trim().Length == 0) ? 报错踢号次数 : int.Parse(Config.IniReadValue("GameServer", "报错踢号次数").Trim()));
			死亡不复活踢号时间 = ((Config.IniReadValue("GameServer", "死亡不复活踢号时间").Trim().Length == 0) ? 死亡不复活踢号时间 : int.Parse(Config.IniReadValue("GameServer", "死亡不复活踢号时间").Trim()));
			在线奖励需要小时数 = ((Config.IniReadValue("GameServer", "在线奖励需要小时数").Trim().Length == 0) ? 在线奖励需要小时数 : int.Parse(Config.IniReadValue("GameServer", "在线奖励需要小时数").Trim())); // EVIAS CDK在线奖励配置
			是否开启任务领取 = ((Config.IniReadValue("GameServer", "是否开启任务领取").Trim() == "") ? 是否开启任务领取 : int.Parse(Config.IniReadValue("GameServer", "是否开启任务领取").Trim()));
			补偿的任务物品ID = ((Config.IniReadValue("GameServer", "补偿的任务物品ID").Trim() == "") ? 补偿的任务物品ID : int.Parse(Config.IniReadValue("GameServer", "补偿的任务物品ID").Trim()));
			是否开启上线BUFF = ((Config.IniReadValue("GameServer", "是否开启上线BUFF").Trim() == "") ? 是否开启上线BUFF : int.Parse(Config.IniReadValue("GameServer", "是否开启上线BUFF").Trim()));
			移动速度 = Config.IniReadValue("GameServer", "最大移动速度").Trim().Split(';');
			充值排行比例 = Config.IniReadValue("GameServer", "充值排行比例").Trim().Split(';');
			是否允许快速攻击 = ((Config.IniReadValue("GameServer", "是否允许快速攻击").Trim().Length == 0) ? 是否允许快速攻击 : int.Parse(Config.IniReadValue("GameServer", "是否允许快速攻击").Trim()));
			是否开启拜师完成任务 = ((Config.IniReadValue("GameServer", "是否开启拜师完成任务").Trim().Length == 0) ? 是否开启拜师完成任务 : int.Parse(Config.IniReadValue("GameServer", "是否开启拜师完成任务").Trim()));
			拜师收徒需要等级 = int.Parse(Config.IniReadValue("GameServer", "拜师收徒需要等级").Trim());
			拜师收徒需要类型 = int.Parse(Config.IniReadValue("GameServer", "拜师收徒需要类型").Trim());
			拜师收徒需要数量 = int.Parse(Config.IniReadValue("GameServer", "拜师收徒需要数量").Trim());
			拜师收徒任务时间 = int.Parse(Config.IniReadValue("GameServer", "拜师收徒任务时间").Trim());
			拜师收徒贡献数量 = int.Parse(Config.IniReadValue("GameServer", "拜师收徒贡献数量").Trim());
			限制职业一 = int.Parse(Config.IniReadValue("GameServer", "限制职业一").Trim());
			限制职业一等级 = int.Parse(Config.IniReadValue("GameServer", "限制职业一等级").Trim());
			限制职业二 = int.Parse(Config.IniReadValue("GameServer", "限制职业二").Trim());
			text = "500";
			限制职业二等级 = int.Parse(Config.IniReadValue("GameServer", "限制职业二等级").Trim());
			限制职业一数量 = int.Parse(Config.IniReadValue("GameServer", "限制职业一数量").Trim());
			限制职业二数量 = int.Parse(Config.IniReadValue("GameServer", "限制职业二数量").Trim());
			限制职业一降低百分比 = ((Config.IniReadValue("GameServer", "限制职业一降低百分比").Length == 0) ? 限制职业一降低百分比 : double.Parse(Config.IniReadValue("GameServer", "限制职业一降低百分比")));
			限制职业二降低百分比 = ((Config.IniReadValue("GameServer", "限制职业二降低百分比").Length == 0) ? 限制职业二降低百分比 : double.Parse(Config.IniReadValue("GameServer", "限制职业二降低百分比")));
			是否开启数据库宝箱 = ((Config.IniReadValue("GameServer", "是否开启数据库宝箱").Trim().Length == 0) ? 是否开启数据库宝箱 : int.Parse(Config.IniReadValue("GameServer", "是否开启数据库宝箱").Trim()));
			是否开启公告结婚完成 = ((Config.IniReadValue("GameServer", "是否开启公告结婚完成").Trim().Length == 0) ? 是否开启公告结婚完成 : int.Parse(Config.IniReadValue("GameServer", "是否开启公告结婚完成").Trim()));
			是否开启告白需要物品 = ((Config.IniReadValue("GameServer", "是否开启告白需要物品").Trim().Length == 0) ? 是否开启告白需要物品 : int.Parse(Config.IniReadValue("GameServer", "是否开启告白需要物品").Trim()));
			学习制作技能熟练度 = ((Config.IniReadValue("GameServer", "学习制作技能熟练度").Trim().Length == 0) ? 学习制作技能熟练度 : int.Parse(Config.IniReadValue("GameServer", "学习制作技能熟练度").Trim()));
			情侣爱情度数量值 = ((Config.IniReadValue("GameServer", "情侣爱情度数量值").Trim().Length == 0) ? 情侣爱情度数量值 : int.Parse(Config.IniReadValue("GameServer", "情侣爱情度数量值").Trim()));
			结婚赠送物品编号 = ((Config.IniReadValue("GameServer", "结婚赠送物品编号").Trim().Length == 0) ? 结婚赠送物品编号 : int.Parse(Config.IniReadValue("GameServer", "结婚赠送物品编号").Trim()));
			告白需要物品编号 = ((Config.IniReadValue("GameServer", "告白需要物品编号").Trim().Length == 0) ? 告白需要物品编号 : int.Parse(Config.IniReadValue("GameServer", "告白需要物品编号").Trim()));
			BOSS掉落物品数量下限 = ((Config.IniReadValue("GameServer", "BOSS掉落物品数量下限").Trim().Length == 0) ? BOSS掉落物品数量下限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落物品数量下限").Trim()));
			BOSS掉落物品数量上限 = ((Config.IniReadValue("GameServer", "BOSS掉落物品数量上限").Trim().Length == 0) ? BOSS掉落物品数量上限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落物品数量上限").Trim()));
			BOSS掉落元宝几率 = ((Config.IniReadValue("GameServer", "BOSS掉落元宝几率").Trim().Length == 0) ? BOSS掉落元宝几率 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落元宝几率").Trim()));
			BOSS掉落元宝数量下限 = ((Config.IniReadValue("GameServer", "BOSS掉落元宝数量下限").Trim().Length == 0) ? BOSS掉落元宝数量下限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落元宝数量下限").Trim()));
			BOSS掉落元宝数量上限 = ((Config.IniReadValue("GameServer", "BOSS掉落元宝数量上限").Trim().Length == 0) ? BOSS掉落元宝数量上限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落元宝数量上限").Trim()));
			BOSS掉落钻石几率 = ((Config.IniReadValue("GameServer", "BOSS掉落钻石几率").Trim().Length == 0) ? BOSS掉落钻石几率 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落钻石几率").Trim()));
			BOSS掉落钻石数量下限 = ((Config.IniReadValue("GameServer", "BOSS掉落钻石数量下限").Trim().Length == 0) ? BOSS掉落钻石数量下限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落钻石数量下限").Trim()));
			BOSS掉落钻石数量上限 = ((Config.IniReadValue("GameServer", "BOSS掉落钻石数量上限").Trim().Length == 0) ? BOSS掉落钻石数量上限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落钻石数量上限").Trim()));
			text = "520";
			BOSS掉落金币几率 = ((Config.IniReadValue("GameServer", "BOSS掉落金币几率").Trim().Length == 0) ? BOSS掉落金币几率 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落金币几率").Trim()));
			BOSS掉落金币数量下限 = ((Config.IniReadValue("GameServer", "BOSS掉落金币数量下限").Trim().Length == 0) ? BOSS掉落金币数量下限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落金币数量下限").Trim()));
			BOSS掉落金币数量上限 = ((Config.IniReadValue("GameServer", "BOSS掉落金币数量上限").Trim().Length == 0) ? BOSS掉落金币数量上限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落金币数量上限").Trim()));
			BOSS掉落武勋几率 = ((Config.IniReadValue("GameServer", "BOSS掉落武勋几率").Trim().Length == 0) ? BOSS掉落武勋几率 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落武勋几率").Trim()));
			BOSS掉落武勋数量下限 = ((Config.IniReadValue("GameServer", "BOSS掉落武勋数量下限").Trim().Length == 0) ? BOSS掉落武勋数量下限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落武勋数量下限").Trim()));
			BOSS掉落武勋数量上限 = ((Config.IniReadValue("GameServer", "BOSS掉落武勋数量上限").Trim().Length == 0) ? BOSS掉落武勋数量上限 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落武勋数量上限").Trim()));
			BOSS掉落物品几率 = ((Config.IniReadValue("GameServer", "BOSS掉落物品几率").Trim().Length == 0) ? BOSS掉落物品几率 : int.Parse(Config.IniReadValue("GameServer", "BOSS掉落物品几率").Trim()));
			是否支持扩展物品属性位数 = ((Config.IniReadValue("GameServer", "是否支持扩展物品属性位数").Trim().Length == 0) ? 是否支持扩展物品属性位数 : int.Parse(Config.IniReadValue("GameServer", "是否支持扩展物品属性位数").Trim()));
			PK掉耐久度 = ((Config.IniReadValue("GameServer", "PK掉耐久度").Trim().Length == 0) ? PK掉耐久度 : int.Parse(Config.IniReadValue("GameServer", "PK掉耐久度").Trim()));
			打怪掉耐久度 = ((Config.IniReadValue("GameServer", "打怪掉耐久度").Trim().Length == 0) ? 打怪掉耐久度 : int.Parse(Config.IniReadValue("GameServer", "打怪掉耐久度").Trim()));
			安全挂机时间 = ((Config.IniReadValue("GameServer", "安全挂机时间").Trim().Length == 0) ? 安全挂机时间 : int.Parse(Config.IniReadValue("GameServer", "安全挂机时间").Trim()));
			灵宠高级进化率 = ((Config.IniReadValue("GameServer", "灵宠高级进化率").Trim().Length == 0) ? 灵宠高级进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠高级进化率").Trim()));
			灵宠宝物进化率 = ((Config.IniReadValue("GameServer", "灵宠宝物进化率").Trim().Length == 0) ? 灵宠宝物进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠宝物进化率").Trim()));
			灵宠传说进化率 = ((Config.IniReadValue("GameServer", "灵宠传说进化率").Trim().Length == 0) ? 灵宠传说进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠传说进化率").Trim()));
			密路宝珠高级进化率 = ((Config.IniReadValue("GameServer", "密路宝珠高级进化率").Trim().Length == 0) ? 密路宝珠高级进化率 : double.Parse(Config.IniReadValue("GameServer", "密路宝珠高级进化率").Trim()));
			密路宝珠稀有进化率 = ((Config.IniReadValue("GameServer", "密路宝珠稀有进化率").Trim().Length == 0) ? 密路宝珠稀有进化率 : double.Parse(Config.IniReadValue("GameServer", "密路宝珠稀有进化率").Trim()));
			密路宝珠传说进化率 = ((Config.IniReadValue("GameServer", "密路宝珠传说进化率").Trim().Length == 0) ? 密路宝珠传说进化率 : double.Parse(Config.IniReadValue("GameServer", "密路宝珠传说进化率").Trim()));
			火龙宝珠高级进化率 = ((Config.IniReadValue("GameServer", "火龙宝珠高级进化率").Trim().Length == 0) ? 火龙宝珠高级进化率 : double.Parse(Config.IniReadValue("GameServer", "火龙宝珠高级进化率").Trim()));
			火龙宝珠稀有进化率 = ((Config.IniReadValue("GameServer", "火龙宝珠稀有进化率").Trim().Length == 0) ? 火龙宝珠稀有进化率 : double.Parse(Config.IniReadValue("GameServer", "火龙宝珠稀有进化率").Trim()));
			火龙宝珠传说进化率 = ((Config.IniReadValue("GameServer", "火龙宝珠传说进化率").Trim().Length == 0) ? 火龙宝珠传说进化率 : double.Parse(Config.IniReadValue("GameServer", "火龙宝珠传说进化率").Trim()));
			text = "540";
			购买武勋装备消耗武勋 = ((Config.IniReadValue("GameServer", "购买武勋装备消耗武勋").Trim().Length == 0) ? 购买武勋装备消耗武勋 : int.Parse(Config.IniReadValue("GameServer", "购买武勋装备消耗武勋").Trim()));
			双倍扣武勋公告内容 = Config.IniReadValue("GameServer", "双倍扣武勋公告内容").Trim();
			双倍扣武勋结束时间 = int.Parse(Config.IniReadValue("GameServer", "双倍扣武勋结束时间").Trim());
			双倍扣武勋元宝数量 = int.Parse(Config.IniReadValue("GameServer", "双倍扣武勋元宝数量").Trim());
			双倍扣武勋倍数 = int.Parse(Config.IniReadValue("GameServer", "双倍扣武勋倍数").Trim());
			内挂打怪说话时间 = int.Parse(Config.IniReadValue("GameServer", "内挂打怪说话时间").Trim());
			自动存取时间 = int.Parse(Config.IniReadValue("GameServer", "自动存取时间").Trim());
			内挂打怪说话内容 = Config.IniReadValue("GameServer", "内挂打怪说话内容").Trim();
			离线挂机打怪范围 = int.Parse(Config.IniReadValue("GameServer", "离线挂机打怪范围").Trim());
			道具锁开关 = int.Parse(Config.IniReadValue("GameServer", "道具锁开关").Trim());
			讨伐副本时长 = int.Parse(Config.IniReadValue("GameServer", "讨伐副本时长").Trim());
			讨伐副本最少人数 = int.Parse(Config.IniReadValue("GameServer", "讨伐副本最少人数").Trim());
			讨伐副本最多人数 = int.Parse(Config.IniReadValue("GameServer", "讨伐副本最多人数").Trim());
			群体辅助组队范围 = int.Parse(Config.IniReadValue("GameServer", "群体辅助组队范围").Trim());
			群医加经验开关 = int.Parse(Config.IniReadValue("GameServer", "群医加经验开关").Trim());
			text = "560";
			群医加爆率 = int.Parse(Config.IniReadValue("GameServer", "群医加爆率").Trim());
			医生群疗经验加成 = ((Config.IniReadValue("GameServer", "医生群疗经验加成").Trim().Length == 0) ? 医生群疗经验加成 : double.Parse(Config.IniReadValue("GameServer", "医生群疗经验加成").Trim()));
			VIP爆率增加 = int.Parse(Config.IniReadValue("GameServer", "VIP爆率增加").Trim());
			开启卡技能 = int.Parse(Config.IniReadValue("GameServer", "开启卡技能").Trim());
			卡技能次数 = int.Parse(Config.IniReadValue("GameServer", "卡技能次数").Trim());
			冲关地图模式切换 = int.Parse(Config.IniReadValue("GameServer", "冲关地图模式切换").Trim());
			坐牢系统是否开启 = int.Parse(Config.IniReadValue("GameServer", "坐牢系统是否开启").Trim());
			坐牢回城坐标 = Config.IniReadValue("GameServer", "坐牢回城坐标").Trim();
			监狱地图 = Config.IniReadValue("GameServer", "监狱地图").Trim();
			坐牢善恶 = int.Parse(Config.IniReadValue("GameServer", "坐牢善恶").Trim());
			坐牢善恶恢复间隔 = int.Parse(Config.IniReadValue("GameServer", "坐牢善恶恢复间隔").Trim());
			坐牢恢复善恶值 = int.Parse(Config.IniReadValue("GameServer", "坐牢恢复善恶值").Trim());
			刑满释放公告 = ((Config.IniReadValue("GameServer", "刑满释放公告").Trim() == "") ? 刑满释放公告 : Config.IniReadValue("GameServer", "刑满释放公告").Trim());
			坐牢杀人公告 = ((Config.IniReadValue("GameServer", "坐牢杀人公告").Trim() == "") ? 坐牢杀人公告 : Config.IniReadValue("GameServer", "坐牢杀人公告").Trim());
			是否开启等级奖励 = int.Parse(Config.IniReadValue("GameServer", "是否开启等级奖励").Trim());
			药品冲突是否开启 = ((Config.IniReadValue("GameServer", "药品冲突是否开启").Trim().Length == 0) ? 药品冲突是否开启 : int.Parse(Config.IniReadValue("GameServer", "药品冲突是否开启").Trim()));
			开启攻城战系统 = ((Config.IniReadValue("GameServer", "开启攻城战系统").Trim().Length == 0) ? 开启攻城战系统 : int.Parse(Config.IniReadValue("GameServer", "开启攻城战系统").Trim()));
			攻城战时长 = int.Parse(Config.IniReadValue("GameServer", "攻城战时长").Trim());
			攻城战预备时间 = int.Parse(Config.IniReadValue("GameServer", "攻城战预备时间").Trim());
			攻城战开启小时 = int.Parse(Config.IniReadValue("GameServer", "攻城战开启小时").Trim());
			text = "580";
			攻城战开启分 = int.Parse(Config.IniReadValue("GameServer", "攻城战开启分").Trim());
			攻城战开启秒 = int.Parse(Config.IniReadValue("GameServer", "攻城战开启秒").Trim());
			斗神称号激活方式 = int.Parse(Config.IniReadValue("GameServer", "斗神称号激活方式").Trim());
			斗神称号需要数量 = int.Parse(Config.IniReadValue("GameServer", "斗神称号需要数量").Trim());
			吸怪数量 = ((Config.IniReadValue("GameServer", "吸怪数量").Trim().Length == 0) ? 吸怪数量 : int.Parse(Config.IniReadValue("GameServer", "吸怪数量").Trim()));
			吸怪距离 = ((Config.IniReadValue("GameServer", "吸怪距离").Trim().Length == 0) ? 吸怪距离 : int.Parse(Config.IniReadValue("GameServer", "吸怪距离").Trim()));
			引怪距离 = ((Config.IniReadValue("GameServer", "引怪距离").Trim().Length == 0) ? 引怪距离 : int.Parse(Config.IniReadValue("GameServer", "引怪距离").Trim()));
			怪物打人距离 = ((Config.IniReadValue("GameServer", "怪物打人距离").Trim().Length == 0) ? 怪物打人距离 : int.Parse(Config.IniReadValue("GameServer", "怪物打人距离").Trim()));
			群攻打怪距离 = ((Config.IniReadValue("GameServer", "群攻打怪距离").Trim().Length == 0) ? 群攻打怪距离 : int.Parse(Config.IniReadValue("GameServer", "群攻打怪距离").Trim()));
			组队范围距离 = ((Config.IniReadValue("GameServer", "组队范围距离").Trim().Length == 0) ? 组队范围距离 : int.Parse(Config.IniReadValue("GameServer", "组队范围距离").Trim()));
			移动坐标异常后反弹 = int.Parse(Config.IniReadValue("GameServer", "移动坐标异常后反弹").Trim());
			开启实时坐标检测 = int.Parse(Config.IniReadValue("GameServer", "开启实时坐标检测").Trim());
			是否开启实时坐标显示 = int.Parse(Config.IniReadValue("GameServer", "是否开启实时坐标显示").Trim());
			实时检测距离 = int.Parse(Config.IniReadValue("GameServer", "实时检测距离").Trim());
			轻功三 = ((Config.IniReadValue("GameServer", "轻功三") == "") ? 轻功三 : float.Parse(Config.IniReadValue("GameServer", "轻功三")));
			实时移动时间 = ((Config.IniReadValue("GameServer", "实时移动时间") == "") ? 实时移动时间 : int.Parse(Config.IniReadValue("GameServer", "实时移动时间")));
			普通走 = ((Config.IniReadValue("GameServer", "普通走") == "") ? 普通走 : float.Parse(Config.IniReadValue("GameServer", "普通走")));
			轻功一 = ((Config.IniReadValue("GameServer", "轻功一") == "") ? 轻功一 : float.Parse(Config.IniReadValue("GameServer", "轻功一")));
			轻功二 = ((Config.IniReadValue("GameServer", "轻功二") == "") ? 轻功二 : float.Parse(Config.IniReadValue("GameServer", "轻功二")));
			宠物普通走 = ((Config.IniReadValue("GameServer", "宠物普通走") == "") ? 宠物普通走 : float.Parse(Config.IniReadValue("GameServer", "宠物普通走")));
			text = "600";
			韩轻功一 = ((Config.IniReadValue("GameServer", "韩轻功一") == "") ? 韩轻功一 : float.Parse(Config.IniReadValue("GameServer", "韩轻功一")));
			韩轻功二 = ((Config.IniReadValue("GameServer", "韩轻功二") == "") ? 韩轻功二 : float.Parse(Config.IniReadValue("GameServer", "韩轻功二")));
			韩轻功三 = ((Config.IniReadValue("GameServer", "韩轻功三") == "") ? 韩轻功三 : float.Parse(Config.IniReadValue("GameServer", "韩轻功三")));
			韩轻功四 = ((Config.IniReadValue("GameServer", "韩轻功四") == "") ? 韩轻功四 : float.Parse(Config.IniReadValue("GameServer", "韩轻功四")));
			同IP势力战不计分 = int.Parse(Config.IniReadValue("GameServer", "同IP势力战不计分").Trim());
			势力战是否开启 = int.Parse(Config.IniReadValue("GameServer", "势力战是否开启").Trim());
			势力战开启分 = int.Parse(Config.IniReadValue("GameServer", "势力战开启分").Trim());
			势力战开启秒 = int.Parse(Config.IniReadValue("GameServer", "势力战开启秒").Trim());
			势力战设置 = Config.IniReadValue("GameServer", "势力战设置").Trim();
			势力战开启自动踢人 = int.Parse(Config.IniReadValue("GameServer", "势力战开启自动踢人").Trim());
			势力战踢人设置 = Config.IniReadValue("GameServer", "势力战踢人设置").Trim();
			势力战打死大怪得分 = int.Parse(Config.IniReadValue("GameServer", "势力战打死大怪得分").Trim());
			势力战打死小怪得分 = int.Parse(Config.IniReadValue("GameServer", "势力战打死小怪得分").Trim());
			势力战预备时间 = int.Parse(Config.IniReadValue("GameServer", "势力战预备时间").Trim());
			势力战战斗时间 = int.Parse(Config.IniReadValue("GameServer", "势力战战斗时间").Trim());
			势力战开始时向其它线广播 = int.Parse(Config.IniReadValue("GameServer", "势力战开始时向其它线广播").Trim());
			加载势力战场次();
			加载势力战踢人设置();
			开启吃药泡点加成 = int.Parse(Config.IniReadValue("GameServer", "开启吃药泡点加成").Trim());
			野外BOSS开关 = int.Parse(Config.IniReadValue("GameServer", "野外BOSS开关").Trim());
			text = "620";
			野外BOSS时间 = Config.IniReadValue("GameServer", "野外BOSS时间").Trim();
			野外BOSS配置 = Config.IniReadValue("GameServer", "野外BOSS配置").Trim();
			野外BOSS倒计时 = int.Parse(Config.IniReadValue("GameServer", "野外BOSS倒计时").Trim());
			野外BOSS总时间 = int.Parse(Config.IniReadValue("GameServer", "野外BOSS总时间").Trim());
			BOSS攻城地图编号 = int.Parse(Config.IniReadValue("GameServer", "BOSS攻城地图编号").Trim());
			是否开启猜拳活动 = int.Parse(Config.IniReadValue("GameServer", "是否开启猜拳活动").Trim());
			回收命令 = Config.IniReadValue("GameServer", "回收命令").Trim();
			猜拳命令 = Config.IniReadValue("GameServer", "猜拳命令").Trim();
			猜拳倒计时 = int.Parse(Config.IniReadValue("GameServer", "猜拳倒计时").Trim());
			猜拳最大下注额 = int.Parse(Config.IniReadValue("GameServer", "猜拳最大下注额").Trim());
			猜拳最小下注额 = int.Parse(Config.IniReadValue("GameServer", "猜拳最小下注额").Trim());
			剪刀石头布元宝倍数 = int.Parse(Config.IniReadValue("GameServer", "剪刀石头布元宝倍数").Trim());
			剪刀石头布返还几率 = int.Parse(Config.IniReadValue("GameServer", "剪刀石头布返还几率").Trim());
			属性一合成率 = ((Config.IniReadValue("GameServer", "属性一合成率") == "") ? 属性一合成率 : double.Parse(Config.IniReadValue("GameServer", "属性一合成率")));
			属性二合成率 = ((Config.IniReadValue("GameServer", "属性二合成率") == "") ? 属性二合成率 : double.Parse(Config.IniReadValue("GameServer", "属性二合成率")));
			属性三合成率 = ((Config.IniReadValue("GameServer", "属性三合成率") == "") ? 属性三合成率 : double.Parse(Config.IniReadValue("GameServer", "属性三合成率")));
			text = "640";
			属性四合成率 = ((Config.IniReadValue("GameServer", "属性四合成率") == "") ? 属性四合成率 : double.Parse(Config.IniReadValue("GameServer", "属性四合成率")));
			是否开启别墅邀请系统 = int.Parse(Config.IniReadValue("GameServer", "是否开启别墅邀请系统").Trim());
			别墅邀请所需物品ID = Config.IniReadValue("GameServer", "别墅邀请所需物品ID").Trim();
			别墅邀请命令 = Config.IniReadValue("GameServer", "别墅邀请命令").Trim();
			别墅邀请人数 = int.Parse(Config.IniReadValue("GameServer", "别墅邀请人数").Trim());
			别墅邀请IP限制 = int.Parse(Config.IniReadValue("GameServer", "别墅邀请IP限制").Trim());
			掉落开盒杀人提示开关 = Config.IniReadValue("GameServer", "掉落开盒杀人提示开关").Trim().Split(';');
			掉落开盒杀人提示语言 = Config.IniReadValue("GameServer", "掉落开盒杀人提示语言").Trim().Split(';');
			掉落开盒杀人提示颜色 = Config.IniReadValue("GameServer", "掉落开盒杀人提示颜色").Trim().Split(';');
			攻城战开启时间星期 = Config.IniReadValue("GameServer", "攻城战开启时间星期").Trim();
			势力战开启时间星期 = Config.IniReadValue("GameServer", "势力战开启时间星期").Trim();
			仙魔战开启时间星期 = Config.IniReadValue("GameServer", "仙魔战开启时间星期").Trim();

            //装备分解 EVIAS
            碎片数量 = Config.IniReadValue("GameServer", "碎片数量").Trim().Split(';');
            神石数量 = Config.IniReadValue("GameServer", "神石数量").Trim().Split(';');

            二级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "二级帮派升级金钱").Trim());
			三级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "三级帮派升级金钱").Trim());
			四级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "四级帮派升级金钱").Trim());
			五级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "五级帮派升级金钱").Trim());
			六级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "六级帮派升级金钱").Trim());
			七级帮派升级金钱 = int.Parse(Config.IniReadValue("GameServer", "七级帮派升级金钱").Trim());

            二级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "二级帮派升级元宝").Trim()); //EVIAS
            三级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "三级帮派升级元宝").Trim());
            四级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "四级帮派升级元宝").Trim());
            五级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "五级帮派升级元宝").Trim());
            六级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "六级帮派升级元宝").Trim());
            七级帮派升级元宝 = int.Parse(Config.IniReadValue("GameServer", "七级帮派升级元宝").Trim());
            text = "660";
			武勋掉落限制 = int.Parse(Config.IniReadValue("GameServer", "武勋掉落限制").Trim());
			武勋掉落数量 = int.Parse(Config.IniReadValue("GameServer", "武勋掉落数量").Trim());
			背后翅膀是否开启 = int.Parse(Config.IniReadValue("GameServer", "背后翅膀是否开启").Trim());
			翅膀造型是否切换 = int.Parse(Config.IniReadValue("GameServer", "翅膀造型是否切换").Trim());
			转换职业命令 = Config.IniReadValue("GameServer", "转换职业命令").Trim();
			转换职业功能是否开启 = int.Parse(Config.IniReadValue("GameServer", "转换职业功能是否开启").Trim());
			转换职业需要元宝数量 = int.Parse(Config.IniReadValue("GameServer", "转换职业需要元宝数量").Trim());
			限制气功点数 = int.Parse(Config.IniReadValue("GameServer", "限制气功点数").Trim());
			追杀需要元宝数量 = int.Parse(Config.IniReadValue("GameServer", "追杀需要元宝数量").Trim());
			路费命令 = Config.IniReadValue("GameServer", "路费命令").Trim();
            在线奖励命令 = Config.IniReadValue("GameServer", "在线奖励命令").Trim(); //0420
            string 在线奖励开关 = Config.IniReadValue("GameServer", "在线奖励开关").Trim();

            // 开店奖励系统配置读取
            开店奖励开关 = ((Config.IniReadValue("GameServer", "开店奖励开关").Trim().Length == 0) ? 开店奖励开关 : int.Parse(Config.IniReadValue("GameServer", "开店奖励开关").Trim()));
            开店奖励小时 = ((Config.IniReadValue("GameServer", "开店奖励小时").Trim().Length == 0) ? 开店奖励小时 : int.Parse(Config.IniReadValue("GameServer", "开店奖励小时").Trim()));
            开店奖励元宝 = ((Config.IniReadValue("GameServer", "开店奖励元宝").Trim().Length == 0) ? 开店奖励元宝 : int.Parse(Config.IniReadValue("GameServer", "开店奖励元宝").Trim()));
            开店奖励冷却天数 = ((Config.IniReadValue("GameServer", "开店奖励冷却天数").Trim().Length == 0) ? 开店奖励冷却天数 : int.Parse(Config.IniReadValue("GameServer", "开店奖励冷却天数").Trim()));

            限制气功百分比 = ((Config.IniReadValue("GameServer", "限制气功百分比") == "") ? 限制气功百分比 : double.Parse(Config.IniReadValue("GameServer", "限制气功百分比")));
      
            转换性别命令 = Config.IniReadValue("GameServer", "转换性别命令").Trim();
			转换性别功能是否开启 = int.Parse(Config.IniReadValue("GameServer", "转换性别功能是否开启").Trim());
			转换性别需要元宝数量 = int.Parse(Config.IniReadValue("GameServer", "转换性别需要元宝数量").Trim());
			转换正邪命令 = Config.IniReadValue("GameServer", "转换正邪命令").Trim();
			text = "680";
			转换正邪功能是否开启 = int.Parse(Config.IniReadValue("GameServer", "转换正邪功能是否开启").Trim());
			转换正邪需要元宝数量 = int.Parse(Config.IniReadValue("GameServer", "转换正邪需要元宝数量").Trim());
            // 2025-05-19: 新增拍卖配置读取
            拍卖物品锁定 = Config.IniReadValue("GameServer", "拍卖物品锁定").Trim(); // 2025-05-19: 寄售改为拍卖
			拍卖系统是否开启 = int.Parse(Config.IniReadValue("GameServer", "拍卖系统是否开启").Trim()); // 2025-05-19: 寄售改为拍卖
			是否拍卖绑定装备 = int.Parse(Config.IniReadValue("GameServer", "是否拍卖绑定装备").Trim()); // 2025-05-19: 寄售改为拍卖
			最大拍卖数量 = ((Config.IniReadValue("GameServer", "最大拍卖数量").Trim() == "") ? 最大拍卖数量 : int.Parse(Config.IniReadValue("GameServer", "最大拍卖数量").Trim()));
			拍卖持续天数 = ((Config.IniReadValue("GameServer", "拍卖持续天数").Trim() == "") ? 拍卖持续天数 : int.Parse(Config.IniReadValue("GameServer", "拍卖持续天数").Trim()));
			拍卖手续费比例 = ((Config.IniReadValue("GameServer", "拍卖手续费比例").Trim() == "") ? 拍卖手续费比例 : double.Parse(Config.IniReadValue("GameServer", "拍卖手续费比例").Trim()));
			拍卖自动结束倍数 = ((Config.IniReadValue("GameServer", "拍卖自动结束倍数").Trim() == "") ? 拍卖自动结束倍数 : int.Parse(Config.IniReadValue("GameServer", "拍卖自动结束倍数").Trim()));
			拍卖超价结束时间 = ((Config.IniReadValue("GameServer", "拍卖超价结束时间").Trim() == "") ? 拍卖超价结束时间 : int.Parse(Config.IniReadValue("GameServer", "拍卖超价结束时间").Trim()));
			拍卖记录清理天数 = ((Config.IniReadValue("GameServer", "拍卖记录清理天数").Trim() == "") ? 拍卖记录清理天数 : int.Parse(Config.IniReadValue("GameServer", "拍卖记录清理天数").Trim()));

            // 2025-06-14: 兑换配置
            钻石兑换元宝是否开启 = int.Parse(Config.IniReadValue("GameServer", "钻石兑换元宝是否开启").Trim());
            钻石兑换元宝比例 = int.Parse(Config.IniReadValue("GameServer", "钻石兑换元宝比例").Trim());
            元宝兑换钻石是否开启 = int.Parse(Config.IniReadValue("GameServer", "元宝兑换钻石是否开启").Trim());
            元宝兑换钻石比例 = int.Parse(Config.IniReadValue("GameServer", "元宝兑换钻石比例").Trim());

            // 2025-06-14: 读取抽奖价格配置
            单抽价格 = ((Config.IniReadValue("GameServer", "单抽价格").Trim().Length == 0) ? 单抽价格 : int.Parse(Config.IniReadValue("GameServer", "单抽价格").Trim()));
            十抽价格 = ((Config.IniReadValue("GameServer", "十抽价格").Trim().Length == 0) ? 十抽价格 : int.Parse(Config.IniReadValue("GameServer", "十抽价格").Trim()));
			
			是否开启外挂占卜 = int.Parse(Config.IniReadValue("GameServer", "是否开启外挂占卜").Trim());
			是否开启NPC占卜 = int.Parse(Config.IniReadValue("GameServer", "是否开启NPC占卜").Trim());
			金币占卜费用 = int.Parse(Config.IniReadValue("GameServer", "金币占卜费用").Trim());
			元宝占卜费用 = int.Parse(Config.IniReadValue("GameServer", "元宝占卜费用").Trim());
			签到命令 = Config.IniReadValue("GameServer", "签到命令").Trim();
			签到奖励元宝数 = int.Parse(Config.IniReadValue("GameServer", "签到奖励元宝数").Trim());
			签到奖励武勋数 = int.Parse(Config.IniReadValue("GameServer", "签到奖励武勋数").Trim());
			比武场经验基数 = ((Config.IniReadValue("GameServer", "比武场经验基数").Trim().Length == 0) ? 比武场经验基数 : double.Parse(Config.IniReadValue("GameServer", "比武场经验基数").Trim()));
			比武泡点是否开启 = int.Parse(Config.IniReadValue("GameServer", "比武泡点是否开启").Trim());
			比武泡点开启小时 = int.Parse(Config.IniReadValue("GameServer", "比武泡点开启小时").Trim());
			比武泡点开启分 = int.Parse(Config.IniReadValue("GameServer", "比武泡点开启分").Trim());
			比武泡点开启秒 = int.Parse(Config.IniReadValue("GameServer", "比武泡点开启秒").Trim());
			比武泡点倒计时 = int.Parse(Config.IniReadValue("GameServer", "比武泡点倒计时").Trim());
			比武泡点总时间 = int.Parse(Config.IniReadValue("GameServer", "比武泡点总时间").Trim());
			同类型数据包处理间隔 = int.Parse(Config.IniReadValue("GameServer", "同类型数据包处理间隔").Trim());
			text = "700";
			是否开启安全码 = int.Parse(Config.IniReadValue("GameServer", "是否开启安全码").Trim());
			比武泡点元宝基数 = int.Parse(Config.IniReadValue("GameServer", "比武泡点元宝基数").Trim());
			比武泡点金钱基数 = int.Parse(Config.IniReadValue("GameServer", "比武泡点金钱基数").Trim());
			比武泡点武勋基数 = int.Parse(Config.IniReadValue("GameServer", "比武泡点武勋基数").Trim());
			是否开启活动禁止加血 = int.Parse(Config.IniReadValue("GameServer", "是否开启活动禁止加血").Trim());
			升级经验是否开启数据库 = int.Parse(Config.IniReadValue("GameServer", "升级经验是否开启数据库").Trim());
			随机武勋丹最小 = int.Parse(Config.IniReadValue("GameServer", "随机武勋丹最小").Trim());
			随机武勋丹最大 = int.Parse(Config.IniReadValue("GameServer", "随机武勋丹最大").Trim());
			交易等级限制 = int.Parse(Config.IniReadValue("GameServer", "交易等级限制").Trim());
			比武泡点元宝时间 = int.Parse(Config.IniReadValue("GameServer", "比武泡点元宝时间").Trim());
			比武泡点开启时间星期 = Config.IniReadValue("GameServer", "比武泡点开启时间星期").Trim();
			上线送药品是否开启 = int.Parse(Config.IniReadValue("GameServer", "上线送药品是否开启").Trim());
			打开换线线路13 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路13").Trim());
			打开换线线路14 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路14").Trim());
			打开换线线路15 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路15").Trim());
			打开换线线路16 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路16").Trim());
			打开换线线路17 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路17").Trim());
			打开换线线路18 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路18").Trim());
			打开换线线路19 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路19").Trim());
			打开换线线路20 = int.Parse(Config.IniReadValue("GameServer", "打开换线线路20").Trim());
			text = "750";
			击杀BOSS等级差 = ((Config.IniReadValue("GameServer", "击杀BOSS等级差").Trim().Length == 0) ? 15 : int.Parse(Config.IniReadValue("GameServer", "击杀BOSS等级差").Trim()));
			累计充值属性提示 = int.Parse(Config.IniReadValue("GameServer", "累计充值属性提示").Trim());
			十一转每日武勋上限 = ((Config.IniReadValue("GameServer", "十一转每日武勋上限").Trim().Length == 0) ? 十一转每日武勋上限 : int.Parse(Config.IniReadValue("GameServer", "十一转每日武勋上限").Trim()));
			进店等级限制 = int.Parse(Config.IniReadValue("GameServer", "进店等级限制").Trim());
			丢物品等级限制 = int.Parse(Config.IniReadValue("GameServer", "丢物品等级限制").Trim());
			开箱保底箱子编号 = int.Parse(Config.IniReadValue("GameServer", "开箱保底箱子编号").Trim());
			开箱保底次数 = int.Parse(Config.IniReadValue("GameServer", "开箱保底次数").Trim());
			开箱保底物品编号 = Config.IniReadValue("GameServer", "开箱保底物品编号").Trim();
			武器防具进化2成功几率 = ((Config.IniReadValue("GameServer", "武器防具进化2成功几率").Length == 0) ? 武器防具进化2成功几率 : double.Parse(Config.IniReadValue("GameServer", "武器防具进化2成功几率")));
			新百宝阁服务器端口 = ((Config.IniReadValue("GameServer", "新百宝阁服务器端口").Trim().Length == 0) ? 新百宝阁服务器端口 : int.Parse(Config.IniReadValue("GameServer", "新百宝阁服务器端口").Trim()));
			新百宝阁地址 = Config.IniReadValue("GameServer", "新百宝阁地址").Trim();
			神器兑换服务器端口 = ((Config.IniReadValue("GameServer", "神器兑换服务器端口").Trim().Length == 0) ? 神器兑换服务器端口 : int.Parse(Config.IniReadValue("GameServer", "神器兑换服务器端口").Trim()));
			神器兑换地址 = Config.IniReadValue("GameServer", "神器兑换地址").Trim();
			商店买东西时间 = int.Parse(Config.IniReadValue("GameServer", "商店买东西时间").Trim());
			百宝买东西时间 = int.Parse(Config.IniReadValue("GameServer", "百宝买东西时间").Trim());
			红包雨是否开启 = ((!(Config.IniReadValue("GameServer", "红包雨是否开启").Trim() == "")) ? int.Parse(Config.IniReadValue("GameServer", "红包雨是否开启").Trim()) : 0);
			红包雨倒计时 = int.Parse(Config.IniReadValue("GameServer", "红包雨倒计时").Trim());
			红包雨总时间 = int.Parse(Config.IniReadValue("GameServer", "红包雨总时间").Trim());
			text = "800";
			红包雨开启时间星期 = Config.IniReadValue("GameServer", "红包雨开启时间星期").Trim();
			红包雨最小元宝 = int.Parse(Config.IniReadValue("GameServer", "红包雨最小元宝").Trim());
			红包雨最大元宝 = int.Parse(Config.IniReadValue("GameServer", "红包雨最大元宝").Trim());
			红包雨最小武勋 = int.Parse(Config.IniReadValue("GameServer", "红包雨最小武勋").Trim());
			红包雨最大武勋 = int.Parse(Config.IniReadValue("GameServer", "红包雨最大武勋").Trim());
			红包雨开启小时 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启小时").Trim());
			红包雨开启分钟 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启分钟").Trim());
			红包雨开启秒钟 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启秒钟").Trim());
			红包雨开启元宝 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启元宝").Trim());
			红包雨开启武勋 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启武勋").Trim());
			红包雨开启掉落 = int.Parse(Config.IniReadValue("GameServer", "红包雨开启掉落").Trim());
			天降红包奖励物品 = Config.IniReadValue("GameServer", "天降红包奖励物品").Trim();
			快速连接限制次数 = ((Config.IniReadValue("GameServer", "快速连接限制次数") == "") ? 快速连接限制次数 : int.Parse(Config.IniReadValue("GameServer", "快速连接限制次数")));
			快速连接限制时间 = ((Config.IniReadValue("GameServer", "快速连接限制时间") == "") ? 快速连接限制时间 : int.Parse(Config.IniReadValue("GameServer", "快速连接限制时间")));
			开启快速连接 = Config.IniReadValue("GameServer", "开启快速连接").Trim().ToLower() == "true";
			二人组队暴率百分比 = ((Config.IniReadValue("GameServer", "二人组队暴率百分比").Trim().Length == 0) ? 二人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "二人组队暴率百分比").Trim()));
			三人组队暴率百分比 = ((Config.IniReadValue("GameServer", "三人组队暴率百分比").Trim().Length == 0) ? 三人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "三人组队暴率百分比").Trim()));
			四人组队暴率百分比 = ((Config.IniReadValue("GameServer", "四人组队暴率百分比").Trim().Length == 0) ? 四人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "四人组队暴率百分比").Trim()));
			五人组队暴率百分比 = ((Config.IniReadValue("GameServer", "五人组队暴率百分比").Trim().Length == 0) ? 五人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "五人组队暴率百分比").Trim()));
			六人组队暴率百分比 = ((Config.IniReadValue("GameServer", "六人组队暴率百分比").Trim().Length == 0) ? 六人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "六人组队暴率百分比").Trim()));
			七人组队暴率百分比 = ((Config.IniReadValue("GameServer", "七人组队暴率百分比").Trim().Length == 0) ? 七人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "七人组队暴率百分比").Trim()));
			text = "850";
			八人组队暴率百分比 = ((Config.IniReadValue("GameServer", "八人组队暴率百分比").Trim().Length == 0) ? 八人组队暴率百分比 : double.Parse(Config.IniReadValue("GameServer", "八人组队暴率百分比").Trim()));
			南林钟离开关 = int.Parse(Config.IniReadValue("GameServer", "南林钟离开关").Trim());
			南林钟离总时间 = int.Parse(Config.IniReadValue("GameServer", "南林钟离总时间").Trim());
			南林钟离时间 = Config.IniReadValue("GameServer", "南林钟离时间").Trim();
			南林钟离一参数 = Config.IniReadValue("GameServer", "南林钟离一参数").Trim();
			攻城战胜利占领时间 = int.Parse(Config.IniReadValue("GameServer", "攻城战胜利占领时间").Trim());
			公告刷新时间 = int.Parse(Config.IniReadValue("GameServer", "公告刷新时间").Trim());
			排名刷新时间 = int.Parse(Config.IniReadValue("GameServer", "排名刷新时间").Trim());
			属性二替换成功率 = ((Config.IniReadValue("GameServer", "属性二替换成功率").Trim().Length == 0) ? 属性二替换成功率 : int.Parse(Config.IniReadValue("GameServer", "属性二替换成功率").Trim()));
			属性三替换成功率 = ((Config.IniReadValue("GameServer", "属性三替换成功率").Trim().Length == 0) ? 属性三替换成功率 : int.Parse(Config.IniReadValue("GameServer", "属性三替换成功率").Trim()));
			属性四替换成功率 = ((Config.IniReadValue("GameServer", "属性四替换成功率").Trim().Length == 0) ? 属性四替换成功率 : int.Parse(Config.IniReadValue("GameServer", "属性四替换成功率").Trim()));
			地下密路副本时间 = Config.IniReadValue("GameServer", "地下密路副本时间").Trim().Split('-');
			地下密路副本是否开启 = ((Config.IniReadValue("GameServer", "地下密路副本是否开启").Trim().Length != 0) ? int.Parse(Config.IniReadValue("GameServer", "地下密路副本是否开启").Trim()) : 0);
			大乱斗是否开启 = int.Parse(Config.IniReadValue("GameServer", "大乱斗是否开启").Trim());
			大乱斗开启小时 = int.Parse(Config.IniReadValue("GameServer", "大乱斗开启小时").Trim());
			大乱斗开启分 = int.Parse(Config.IniReadValue("GameServer", "大乱斗开启分").Trim());
			大乱斗开启秒 = int.Parse(Config.IniReadValue("GameServer", "大乱斗开启秒").Trim());
			大乱斗倒计时 = int.Parse(Config.IniReadValue("GameServer", "大乱斗倒计时").Trim());
			大乱斗总时间 = int.Parse(Config.IniReadValue("GameServer", "大乱斗总时间").Trim());
			大乱斗进入需要武勋 = int.Parse(Config.IniReadValue("GameServer", "大乱斗进入需要武勋").Trim());
			text = "900";
			大乱斗开启时间星期 = Config.IniReadValue("GameServer", "大乱斗开启时间星期").Trim();
			大乱斗参与奖励物品 = Config.IniReadValue("GameServer", "大乱斗参与奖励物品").Trim();
			限时行囊 = int.Parse(Config.IniReadValue("GameServer", "限时行囊").Trim());
			交易中物品时间 = int.Parse(Config.IniReadValue("GameServer", "交易中物品时间").Trim());
			阎王爆范围爆炸是否开启 = int.Parse(Config.IniReadValue("GameServer", "阎王爆范围爆炸是否开启").Trim());
			阎王爆爆炸距离 = int.Parse(Config.IniReadValue("GameServer", "阎王爆爆炸距离").Trim());
			阎王爆伤害降低百分比 = int.Parse(Config.IniReadValue("GameServer", "阎王爆伤害降低百分比").Trim());
			首爆记录 = ((Config.IniReadValue("GameServer", "首爆记录").Trim().Length == 0) ? 首爆记录 : int.Parse(Config.IniReadValue("GameServer", "首爆记录").Trim()));
			物品交易后锁定时间 = int.Parse(Config.IniReadValue("GameServer", "物品交易后锁定时间").Trim());
			仓库存取需时间 = int.Parse(Config.IniReadValue("GameServer", "仓库存取需时间").Trim());
			邮件物品数量开关 = int.Parse(Config.IniReadValue("GameServer", "邮件物品数量开关").Trim());
			邮件物品交易数量 = int.Parse(Config.IniReadValue("GameServer", "邮件物品交易数量").Trim());
			邮件物品强化数量 = int.Parse(Config.IniReadValue("GameServer", "邮件物品强化数量").Trim());
			邮件传书杀人次数 = int.Parse(Config.IniReadValue("GameServer", "邮件传书杀人次数").Trim());
			人物越级怪物经验差 = ((Config.IniReadValue("GameServer", "人物越级怪物经验差").Trim().Length == 0) ? 20 : int.Parse(Config.IniReadValue("GameServer", "人物越级怪物经验差").Trim()));
			怪物越级人物经验差 = ((Config.IniReadValue("GameServer", "怪物越级人物经验差").Trim().Length == 0) ? 20 : int.Parse(Config.IniReadValue("GameServer", "怪物越级人物经验差").Trim()));
			灵宠神灵普通进化率 = ((Config.IniReadValue("GameServer", "灵宠神灵普通进化率").Trim().Length == 0) ? 灵宠神灵普通进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠神灵普通进化率").Trim()));
			灵宠神灵高级进化率 = ((Config.IniReadValue("GameServer", "灵宠神灵高级进化率").Trim().Length == 0) ? 灵宠神灵高级进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠神灵高级进化率").Trim()));
			灵宠神灵宝物进化率 = ((Config.IniReadValue("GameServer", "灵宠神灵宝物进化率").Trim().Length == 0) ? 灵宠神灵宝物进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠神灵宝物进化率").Trim()));
			灵宠神灵传说进化率 = ((Config.IniReadValue("GameServer", "灵宠神灵传说进化率").Trim().Length == 0) ? 灵宠神灵传说进化率 : double.Parse(Config.IniReadValue("GameServer", "灵宠神灵传说进化率").Trim()));
			text = "950";
			武勋记录 = ((Config.IniReadValue("GameServer", "武勋记录").Trim().Length == 0) ? 武勋记录 : int.Parse(Config.IniReadValue("GameServer", "武勋记录").Trim()));
			内置百宝阁开关 = int.Parse(Config.IniReadValue("GameServer", "内置百宝阁开关").Trim());
			极限比武是否开启 = int.Parse(Config.IniReadValue("GameServer", "极限比武是否开启").Trim());
			极限比武开启小时 = int.Parse(Config.IniReadValue("GameServer", "极限比武开启小时").Trim());
			极限比武开启分 = int.Parse(Config.IniReadValue("GameServer", "极限比武开启分").Trim());
			极限比武开启秒 = int.Parse(Config.IniReadValue("GameServer", "极限比武开启秒").Trim());
			极限比武最大人数 = int.Parse(Config.IniReadValue("GameServer", "极限比武最大人数").Trim());
			极限比武开启时间星期 = Config.IniReadValue("GameServer", "极限比武开启时间星期").Trim();
			极限比武胜利奖励物品 = Config.IniReadValue("GameServer", "极限比武胜利奖励物品").Trim();
			极限比武参与奖励物品 = Config.IniReadValue("GameServer", "极限比武参与奖励物品").Trim();
			开启数据包限制 = int.Parse(Config.IniReadValue("GameServer", "开启数据包限制").Trim());
			假人帮派数据一 = Config.IniReadValue("GameServer", "假人帮派数据一").Trim().Split(';');
			假人帮派数据二 = Config.IniReadValue("GameServer", "假人帮派数据二").Trim().Split(';');
			假人帮派数据三 = Config.IniReadValue("GameServer", "假人帮派数据三").Trim().Split(';');
			假人帮派数据四 = Config.IniReadValue("GameServer", "假人帮派数据四").Trim().Split(';');
			假人帮派数据五 = Config.IniReadValue("GameServer", "假人帮派数据五").Trim().Split(';');
			武林血战准备时间 = int.Parse(Config.IniReadValue("GameServer", "武林血战准备时间").Trim());
			武林血战第一轮人物 = int.Parse(Config.IniReadValue("GameServer", "武林血战第一轮人物").Trim());
			武林血战第一轮时间 = int.Parse(Config.IniReadValue("GameServer", "武林血战第一轮时间").Trim());
			武林血战第二轮人物 = int.Parse(Config.IniReadValue("GameServer", "武林血战第二轮人物").Trim());
			text = "1000";
			武林血战第二轮时间 = int.Parse(Config.IniReadValue("GameServer", "武林血战第二轮时间").Trim());
			武林血战第三轮人物 = int.Parse(Config.IniReadValue("GameServer", "武林血战第三轮人物").Trim());
			武林血战第三轮时间 = int.Parse(Config.IniReadValue("GameServer", "武林血战第三轮时间").Trim());
			try
			{
				禁创职业.Clear();
				string text2 = Config.IniReadValue("GameServer", "禁创职业").Trim();
				if (text2.Length > 0)
				{
					string[] array = text2.Split(',');
					for (int i = 0; i < array.Length; i++)
					{
						if (array[i].Length > 0 && !禁创职业.Contains(int.Parse(array[i])))
						{
							禁创职业.Add(int.Parse(array[i]));
						}
					}
				}
				过滤数据包类型.Clear();
				string text3 = Config.IniReadValue("GameServer", "过滤数据包类型").Trim();
				if (text3.Length > 0)
				{
					string[] array2 = text3.Split(';');
					for (int j = 0; j < array2.Length; j++)
					{
						if (array2[j].Length > 0)
						{
							int item = int.Parse(array2[j]);
							if (!过滤数据包类型.Contains(item))
							{
								过滤数据包类型.Add(item);
							}
						}
					}
				}
			}
			catch
			{
				Form1.WriteLine(1, "禁止创建角色/封包过滤出错");
			}
			if (随机BOSS出现时间表.Length != 0)
			{
				BOSSListTime.Clear();
				string text4 = 随机BOSS出现时间表;
				char[] separator = new char[1] { ';' };
				string[] array3 = text4.Split(separator);
                string[] array4 = array3;
                foreach (string s in array4)
				{
					int item2 = int.Parse(s);
					BOSSListTime.Add(item2);
				}
			}
			else
			{
				BOSSListTime.Clear();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "配置文件config.ini配置错误:" + ex.Message);
			Form1.WriteLine(100, "在设置项:" + text);
		}
		try
		{
			string text5 = ((Config.IniReadValue("GameServer", "限制PK地图").Trim() == string.Empty) ? string.Empty : Config.IniReadValue("GameServer", "限制PK地图").Trim());
			限制PK地图列表.Clear();
			if (text5 != string.Empty)
			{
				string text6 = text5;
				char[] separator2 = new char[1] { ';' };
                string[] array5 = text6.Split(separator2);
                string[] array6 = array5;
                foreach (string s2 in array6)
				{
					if (!限制PK地图列表.Contains(int.Parse(s2)))
					{
						限制PK地图列表.Add(int.Parse(s2));
					}
				}
			}
			string text7 = ((Config.IniReadValue("GameServer", "限时间PK地图").Trim() == string.Empty) ? string.Empty : Config.IniReadValue("GameServer", "限时间PK地图").Trim());
			限时PK地图列表.Clear();
			if (text7 != string.Empty)
			{
				string text8 = text7;
				char[] separator3 = new char[1] { ';' };
                string[] array7 = text8.Split(separator3);
                string[] array8 = array7;
                foreach (string s3 in array8)
				{
					if (!限时PK地图列表.Contains(int.Parse(s3)))
					{
						限时PK地图列表.Add(int.Parse(s3));
					}
				}
			}
			限时地图开PK时间 = ((Config.IniReadValue("GameServer", "限时地图开PK时间").Trim() == string.Empty) ? 限时地图开PK时间 : int.Parse(Config.IniReadValue("GameServer", "限时地图开PK时间").Trim()));
			工作日限时地图开PK时间 = ((Config.IniReadValue("GameServer", "工作日限时地图开PK时间").Trim() == string.Empty) ? 工作日限时地图开PK时间 : int.Parse(Config.IniReadValue("GameServer", "工作日限时地图开PK时间").Trim()));
			限时地图关PK时间 = ((Config.IniReadValue("GameServer", "限时地图关PK时间").Trim() == string.Empty) ? 限时地图关PK时间 : int.Parse(Config.IniReadValue("GameServer", "限时地图关PK时间").Trim()));
			工作日限时地图关PK时间 = ((Config.IniReadValue("GameServer", "工作日限时地图关PK时间").Trim() == string.Empty) ? 工作日限时地图关PK时间 : int.Parse(Config.IniReadValue("GameServer", "工作日限时地图关PK时间").Trim()));
			周末全天PK是否开启 = ((Config.IniReadValue("GameServer", "周末全天PK是否开启").Trim() == string.Empty) ? 周末全天PK是否开启 : int.Parse(Config.IniReadValue("GameServer", "周末全天PK是否开启").Trim()));
			string text9 = ((Config.IniReadValue("GameServer", "全天PK地图").Trim() == string.Empty) ? "1301;2001;5001" : Config.IniReadValue("GameServer", "全天PK地图").Trim());
			周末全天PK地图列表.Clear();
			if (!(text9 != string.Empty))
			{
				return;
			}
			string text10 = text9;
			char[] separator4 = new char[1] { ';' };
            string[] array9 = text10.Split(separator4);
            string[] array10 = array9;
            foreach (string s4 in array10)
			{
				if (!周末全天PK地图列表.Contains(int.Parse(s4)))
				{
					周末全天PK地图列表.Add(int.Parse(s4));
				}
			}
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(100, " 配置文件加载挂机地图表错误:" + ex2.Message);
		}
	}

	public static void 加载势力战场次()
	{
		string[] array = 势力战设置.Split('/');
		所有势力战场次.Clear();
        string[] array2 = array;
        string[] array3 = array2;
        foreach (string text in array3)
		{
            if (text.Length > 0)
            {
                string[] array4 = text.Split(':');
                if (array4.Length > 3)
                {
                    势力战场次 势力战场次2 = new 势力战场次
                    {
                        开启时间 = int.Parse(array4[0]),
                        最小转职 = int.Parse(array4[1]),
                        最大转职 = int.Parse(array4[2]),
                        势力战类型 = int.Parse(array4[3])
                    };
                    所有势力战场次.TryAdd(势力战场次2.开启时间, 势力战场次2);
                }
            }
		}
	}

    public static void 加载势力战踢人设置()
    {
        string[] array = 势力战踢人设置.Split('/');
        势力战踢人方案.Clear();
        string[] array2 = array;
        string[] array3 = array2;
        foreach (string text in array3)
        {
            if (text.Length > 0)
            {
                string[] array4 = text.Split(':');
                if (array4.Length > 1)
                {
                    KeyValuePair<int, int> item = new KeyValuePair<int, int>(int.Parse(array4[0]), int.Parse(array4[1]));
                    势力战踢人方案.Add(item);
                }
            }
        }
    }

	public static bool 检查数据库配置()
	{
		using (SqlConnection sqlConnection = new SqlConnection(DBA.getstrConnection("rxjhaccount")))
		{
			try
			{
				sqlConnection.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库账号库配置错误，无法连接");
				return false;
			}
			finally
			{
				sqlConnection.Close();
			}
		}
		using (SqlConnection sqlConnection2 = new SqlConnection(DBA.getstrConnection("GameServer")))
		{
			try
			{
				sqlConnection2.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库角色库配置错误，无法连接");
				return false;
			}
			finally
			{
				sqlConnection2.Close();
			}
		}
		using (SqlConnection sqlConnection3 = new SqlConnection(DBA.getstrConnection("PublicDb")))
		{
			try
			{
				sqlConnection3.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库设置库配置错误，无法连接");
				return false;
			}
			finally
			{
				sqlConnection3.Close();
			}
		}
		using (SqlConnection sqlConnection4 = new SqlConnection(DBA.getstrConnection("WebDb")))
		{
			try
			{
				sqlConnection4.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库网站库配置错误，无法连接"); // 2025-0618 EVIAS 此处无需优化，已是常量字符串
				return false;
			}
			finally
			{
				sqlConnection4.Close();
			}
		}
		using (SqlConnection sqlConnection5 = new SqlConnection(DBA.getstrConnection("BBG")))
		{
			try
			{
				sqlConnection5.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库百宝阁配置错误，无法连接"); // 2025-0618 EVIAS 此处无需优化，已是常量字符串
				return false;
			}
			finally
			{
				sqlConnection5.Close();
			}
		}
		using (SqlConnection sqlConnection6 = new SqlConnection(DBA.getstrConnection("GameLog")))
		{
			try
			{
				sqlConnection6.Open();
			}
			catch
			{
				Form1.WriteLine(1, "数据库Gamelog配置错误，无法连接"); // 2025-0618 EVIAS 此处无需优化，已是常量字符串
				return false;
			}
			finally
			{
				sqlConnection6.Close();
			}
		}
		return true;
	}

	public void SetConfig2()
	{
		Db.TryAdd("rxjhaccount", new DbClass
		{
			ServerDb = "rxjhaccount",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("rxjhaccount", "Server").Trim(), Config.IniReadValue("rxjhaccount", "UserName").Trim(), Config.IniReadValue("rxjhaccount", "PassWord").Trim(), Config.IniReadValue("rxjhaccount", "DataName").Trim())
		});
		Db.TryAdd("GameServer", new DbClass
		{
			ServerDb = "GameServer",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("GameServer", "Server").Trim(), Config.IniReadValue("GameServer", "UserName").Trim(), Config.IniReadValue("GameServer", "PassWord").Trim(), Config.IniReadValue("GameServer", "DataName").Trim())
		});
		Db.TryAdd("PublicDb", new DbClass
		{
			ServerDb = "PublicDb",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("PublicDb", "Server").Trim(), Config.IniReadValue("PublicDb", "UserName").Trim(), Config.IniReadValue("PublicDb", "PassWord").Trim(), Config.IniReadValue("PublicDb", "DataName").Trim())
		});
		Db.TryAdd("WebDb", new DbClass
		{
			ServerDb = "WebDb",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("WebDb", "Server").Trim(), Config.IniReadValue("WebDb", "UserName").Trim(), Config.IniReadValue("WebDb", "PassWord").Trim(), Config.IniReadValue("WebDb", "DataName").Trim())
		});
		Db.TryAdd("BBG", new DbClass
		{
			ServerDb = "BBG",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("BBG", "Server").Trim(), Config.IniReadValue("BBG", "UserName").Trim(), Config.IniReadValue("BBG", "PassWord").Trim(), Config.IniReadValue("BBG", "DataName").Trim())
		});
		Db.TryAdd("GameLog", new DbClass
		{
			ServerDb = "GameLog",
			SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("GameLog", "Server").Trim(), Config.IniReadValue("GameLog", "UserName").Trim(), Config.IniReadValue("GameLog", "PassWord").Trim(), Config.IniReadValue("GameLog", "DataName").Trim())
		});
		百宝阁数据库连接信息 = new SqlConnectInfo
		{
			ServerAddress = Config.IniReadValue("BBG", "Server").Trim(),
			UserId = Config.IniReadValue("BBG", "UserName").Trim(),
			PassWord = Config.IniReadValue("BBG", "PassWord").Trim(),
			DatabaseName = Config.IniReadValue("BBG", "DataName").Trim()
		};
	}

	public void SetWxLever()
	{
    	string sqlCommand = $"SELECT * FROM TBL_武勋加成";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载武勋列表出错----没有武勋数据");
			}
			else
			{
				Wxlever.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					武勋加成类 value = new 武勋加成类
					{
						武勋气功防御 = double.Parse(dBToDataTable.Rows[i]["FLD_武勋气功防御"].ToString()),
						武勋点 = double.Parse(dBToDataTable.Rows[i]["FLD_武勋点数"].ToString()),
						增加攻击 = (int)dBToDataTable.Rows[i]["FLD_攻击"],
						增加防御 = (int)dBToDataTable.Rows[i]["FLD_防御"],
						增加HP = (int)dBToDataTable.Rows[i]["FLD_血量"],
						增加MP = (int)dBToDataTable.Rows[i]["FLD_蓝量"],
						增加气功 = (int)dBToDataTable.Rows[i]["FLD_所有气功增加"],
						增加阶段 = (int)dBToDataTable.Rows[i]["FLD_武勋阶段"]
					};
					Wxlever.TryAdd((int)dBToDataTable.Rows[i]["ID"], value);
				}
			}
			dBToDataTable.Dispose();
		}
		Form1.WriteLine(2, "加载武勋阶段表完成");
	}

	public void Set安全区()
	{
		对练区.Add(new 坐标Class
		{
			Rxjh_name = "对练区",
			Rxjh_Map = 2301,
			Rxjh_X = 120f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		势力战区域.Add(new 坐标Class
		{
			Rxjh_name = "势力战区域",
			Rxjh_Map = 801,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		仙魔大战区域.Add(new 坐标Class
		{
			Rxjh_name = "仙魔大战区域",
			Rxjh_Map = 41001,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		攻城战区域.Add(new 坐标Class
		{
			Rxjh_name = "攻城战区域",
			Rxjh_Map = 42001,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		帮战区.Add(new 坐标Class
		{
			Rxjh_name = "帮战区",
			Rxjh_Map = 7301,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
	}

	public void SetJianc()
	{
		if (查非法物品 != 2)
		{
			return;
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 装备检测", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			装备检测list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				装备检测list.TryAdd((int)dBToDataTable.Rows[i]["物品类型"], new 装备检测类
				{
					物品最高攻击值 = (int)dBToDataTable.Rows[i]["物品最高攻击值"],
					物品最高防御值 = (int)dBToDataTable.Rows[i]["物品最高防御值"],
					物品最高生命值 = (int)dBToDataTable.Rows[i]["物品最高生命值"],
					物品最高内功值 = (int)dBToDataTable.Rows[i]["物品最高内功值"],
					物品最高命中值 = (int)dBToDataTable.Rows[i]["物品最高命中值"],
					物品最高回避值 = (int)dBToDataTable.Rows[i]["物品最高回避值"],
					物品最高武功值 = (int)dBToDataTable.Rows[i]["物品最高武功值"],
					物品最高气功值 = (int)dBToDataTable.Rows[i]["物品最高气功值"],
					物品最高附魂值 = (int)dBToDataTable.Rows[i]["物品最高附魂值"]
				});
			}
			Form1.WriteLine(2, "加载装备检测数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 安全地图区域()
	{
    	string sqlCommand = $"SELECT * FROM TBL_安全区";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载安全区出错----没有数据");
		}
		else
		{
			地图安全区.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				安全区Class 安全区Class2 = new 安全区Class
				{
					地图编号 = (int)dBToDataTable.Rows[i]["地图编号"],
					FLD_X = float.Parse(dBToDataTable.Rows[i]["FLD_X"].ToString()),
					FLD_Y = float.Parse(dBToDataTable.Rows[i]["FLD_Y"].ToString()),
					坐标_X = float.Parse(dBToDataTable.Rows[i]["坐标_X"].ToString()),
					坐标_Y = float.Parse(dBToDataTable.Rows[i]["坐标_Y"].ToString())
				};
				地图安全区.TryAdd(安全区Class2.地图编号, 安全区Class2);
			}
			Form1.WriteLine(2, "加载安全区地图完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载物品回收()
	{
    	DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM 物品回收", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载物品回收出错----没有物品回收数据");
		}
		else
		{
			物品回收数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				物品回收类 value = new 物品回收类
				{
					需要物品 = dBToDataTable.Rows[i]["需要物品"].ToString(),
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					钻石 = (int)dBToDataTable.Rows[i]["钻石"],
					累充 = (int)dBToDataTable.Rows[i]["累充"]
				};
				物品回收数据.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载物品回收数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public static void 删除指定地图指定类型怪物(int 参数地图ID, int 参数怪物系统编号)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value in MapClass.GetnpcTemplate(参数地图ID).Values)
			{
				if (value.FLD_PID == 参数怪物系统编号)
				{
					list.Add(value);
				}
			}
			if (list == null)
			{
				return;
			}
			foreach (NpcClass item in list)
			{
				item.发送怪物一次性死亡数据();
			}
			list.Clear();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "删除怪 [" + 参数怪物系统编号 + "]出错：" + ex);
		}
	}

	public void 加载装备洗髓()
	{
    	DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM TBL_装备洗髓", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载装备洗髓出错----没有装备洗髓数据");
		}
		else
		{
			装备洗髓系统.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				装备洗髓 value = new 装备洗髓
				{
					装备ID = (int)dBToDataTable.Rows[i]["装备ID"],
					类型 = (int)dBToDataTable.Rows[i]["类型"],
					属性一最小 = (int)dBToDataTable.Rows[i]["属性一最小"],
					属性一最大 = (int)dBToDataTable.Rows[i]["属性一最大"],
					属性二最小 = (int)dBToDataTable.Rows[i]["属性二最小"],
					属性二最大 = (int)dBToDataTable.Rows[i]["属性二最大"],
					属性三最小 = (int)dBToDataTable.Rows[i]["属性三最小"],
					属性三最大 = (int)dBToDataTable.Rows[i]["属性三最大"],
					属性四最小 = (int)dBToDataTable.Rows[i]["属性四最小"],
					属性四最大 = (int)dBToDataTable.Rows[i]["属性四最大"],
					模式 = (int)dBToDataTable.Rows[i]["模式"]
				};
				装备洗髓系统.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载装备洗髓数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载装备首爆()
	{
        DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM 装备首爆", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载装备首爆出错----没有装备首爆数据");
		}
		else
		{
			装备首爆.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				首爆 value = new 首爆
				{
					物品ID = (int)dBToDataTable.Rows[i]["物品ID"],
					物品属性 = (int)dBToDataTable.Rows[i]["物品属性"],
					FLD_奖励一 = (int)dBToDataTable.Rows[i]["奖励一"],
					FLD_奖励二 = (int)dBToDataTable.Rows[i]["奖励二"],
					FLD_奖励三 = (int)dBToDataTable.Rows[i]["奖励三"],
					FLD_奖励四 = (int)dBToDataTable.Rows[i]["奖励四"],
					FLD_奖励五 = (int)dBToDataTable.Rows[i]["奖励五"]
				};
				装备首爆.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载装备首爆数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载英雄职业武器()
	{
    	DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM TBL_英雄职业武器", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载英雄职业武器出错----没有英雄职业武器");
		}
		else
		{
			英雄职业武器系统.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				英雄职业武器 value = new 英雄职业武器
				{
					装备ID = (int)dBToDataTable.Rows[i]["装备ID"],
					职业 = (int)dBToDataTable.Rows[i]["职业"],
					几转 = (int)dBToDataTable.Rows[i]["几转"]
				};
				英雄职业武器系统.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载英雄职业武器数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetLjcz()
	{
		string sqlCommand = "SELECT * FROM 累充系统";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载累充系统完成----没有抽奖数据");
		}
		else
		{
			累充数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				累充系统 item = new 累充系统
				{
					FLD_累充金额 = (int)dBToDataTable.Rows[i]["FLD_累充金额"],
					FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					FLD_NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"],
					FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					是否绑定 = (int)dBToDataTable.Rows[i]["是否绑定"],
					是否披风 = (int)dBToDataTable.Rows[i]["是否披风"],
					FLD_DES = dBToDataTable.Rows[i]["FLD_DES"].ToString(),
					使用天数 = (int)dBToDataTable.Rows[i]["使用天数"]
				};
				累充数据.Add(item);
			}
			Form1.WriteLine(2, "加载累充数据 " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetChouJiang()
	{
		string sqlCommand = "SELECT * FROM 抽奖系统";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载抽奖系统完成----没有抽奖数据");
		}
		else
		{
			ChouJiang.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				ChouJiangClass item = new ChouJiangClass
				{
					FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					FLD_PP = (int)dBToDataTable.Rows[i]["FLD_PP"],
					FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					FLD_NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"],
					FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					是否提示 = (int)dBToDataTable.Rows[i]["是否提示"],
					是否绑定 = (int)dBToDataTable.Rows[i]["是否绑定"],
					是否展示 = (int)dBToDataTable.Rows[i]["是否展示"],
					使用天数 = (int)dBToDataTable.Rows[i]["使用天数"]
				};
				ChouJiang.Add(item);
			}
			Form1.WriteLine(2, "加载新百宝阁抽奖数据完成 " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载累计充值称号()
	{
        DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM 累计充值称号", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载累计充值称号出错----没有累计充值礼包");
		}
		else
		{
			累计充值称号.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				累计充值礼包 value = new 累计充值礼包
				{
					累计最小 = (int)dBToDataTable.Rows[i]["累计最小"],
					累计最大 = (int)dBToDataTable.Rows[i]["累计最大"],
					礼包编号 = (int)dBToDataTable.Rows[i]["礼包编号"],
					档次 = (int)dBToDataTable.Rows[i]["档次"],
					攻击 = (int)dBToDataTable.Rows[i]["攻击"],
					防御 = (int)dBToDataTable.Rows[i]["防御"],
					血量 = (int)dBToDataTable.Rows[i]["血量"],
					上线提示 = dBToDataTable.Rows[i]["上线提示"].ToString(),
					颜色 = (int)dBToDataTable.Rows[i]["上线提示颜色"],
					自定开头内容 = dBToDataTable.Rows[i]["自定开头内容"].ToString(),
					战斗力 = (int)dBToDataTable.Rows[i]["对人战斗力"]
				};
				累计充值称号.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载累计充值称号数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set冲关地图()
	{
		try
		{
			冲关地图list.Clear();
			string[] array = 冲关地图.Split(';');
			if (array.Length > 1)
			{
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(',');
					if (array2.Length > 1 && !冲关地图list.ContainsKey(array2[0]))
					{
						冲关地图类 冲关地图类2 = new 冲关地图类
						{
							地图名 = array2[0],
							ItmeID = int.Parse(array2[1])
						};
						冲关地图list.TryAdd(冲关地图类2.地图名, 冲关地图类2);
					}
				}
			}
			Form1.WriteLine(2, "加载冲关地图成功");
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "加载冲关地图出错---" + ex.Message);
		}
	}

	public void SetQG()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_SKILL", "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count != 0)
			{
				气功加成.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					气功加成.TryAdd(i, new 气功加成属性
					{
						FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
						FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"],
						FLD_JOB = (int)dBToDataTable.Rows[i]["FLD_JOB"],
						FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						FLD_每点加成比率值1 = (double)dBToDataTable.Rows[i]["FLD_每点加成比率值1"],
						FLD_每点加成比率值2 = (double)dBToDataTable.Rows[i]["FLD_每点加成比率值2"]
					});
				}
				Form1.WriteLine(2, "加载气功加成比率数据完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		普通气功ID.Clear();
		for (int j = 0; j < 12; j++)
		{
			for (int k = 1; k < 14; k++)
			{
				普通气功ID.Add(得到气功ID(j, k));
			}
		}
		普通气功ID.Sort();
		Set反气功();		//24.0 EVIAS 新增反气功初始化
		Set反气功List(); //24.0 EVIAS 新增反气功配置加载
	}

	public void Set反气功List() //24.0 EVIAS 新增反气功配置加载方法
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 反气功 ORDER BY 气功ID", "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			
			反气功List.Clear();
			if (dBToDataTable.Rows.Count > 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					反气功类 反气功 = new 反气功类();
					反气功.气功ID = (int)dBToDataTable.Rows[i]["气功ID"];
					反气功.FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
					反气功.气功名 = dBToDataTable.Rows[i]["气功名"].ToString();
					反气功.ID = (int)dBToDataTable.Rows[i]["ID"];
					反气功.FLD_每点加成比率值 = (double)dBToDataTable.Rows[i]["FLD_每点加成比率值"];
					反气功List[反气功.气功ID] = 反气功;
				}
				Form1.WriteLine(15, "加载反气功 " + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "加载反气功配置异常: " + ex.Message);
		}
	}

    public void Set反气功() //24.0 EVIAS 新增反气功ID初始化方法
    {
        反气功ID.Clear();

        if (反气功List.Count > 0)
        {
            foreach (var kvp in 反气功List)
            {
                反气功ID.Add(kvp.Key);
            }
            反气功ID.Sort();
        }
        else
        {
            for (int i = 2001; i < 2028; i++)
            {
                反气功ID.Add(i);
            }
        }
        Form1.WriteLine(15, $"加载反气功ID完成，共{反气功ID.Count}个");
    }


    public void SetKill()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  XWWL_kill", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载过滤出错----没有过滤数据");
		}
		else
		{
			Kill.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Kill.Add(new KillClass
				{
					Txt = dBToDataTable.Rows[i]["txt"].ToString(),
					Sffh = (int)dBToDataTable.Rows[i]["sffh"]
				});
			}
			Form1.WriteLine(2, "加载过滤数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set等级奖励()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  等级奖励", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载等级奖励出错----没有等级奖励数据");
		}
		else
		{
			等级奖励.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				等级奖励.TryAdd(i, new 等级奖励类
				{
					等级 = (int)dBToDataTable.Rows[i]["级别"],
					武勋 = (int)dBToDataTable.Rows[i]["武勋"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					套装 = (int)dBToDataTable.Rows[i]["套装ID"],
					金钱 = dBToDataTable.Rows[i]["金钱"].ToString(),
					单件物品 = dBToDataTable.Rows[i]["单件物品"].ToString(),
					档次 = (int)dBToDataTable.Rows[i]["档次"]
				});
			}
			Form1.WriteLine(2, "加载等级奖励数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set假人等级奖励()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  假人等级奖励", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载假人等级奖励出错----没有假人等级奖励数据");
		}
		else
		{
			假人等级奖励.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				假人等级奖励.TryAdd(i, new 假人等级奖励
				{
					等级 = (int)dBToDataTable.Rows[i]["级别"],
					武勋 = (int)dBToDataTable.Rows[i]["武勋"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					套装 = (int)dBToDataTable.Rows[i]["套装ID"],
					金钱 = dBToDataTable.Rows[i]["金钱"].ToString(),
					单件物品 = dBToDataTable.Rows[i]["单件物品"].ToString(),
					档次 = (int)dBToDataTable.Rows[i]["档次"]
				});
			}
			Form1.WriteLine(2, "加载假人等级奖励数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set比武泡点奖励()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  比武泡点奖励", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载比武泡点奖励出错----没有比武泡点奖励数据");
		}
		else
		{
			比武泡点奖励.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				比武泡点奖励.TryAdd(i, new 比武泡点奖励
				{
					排名 = (int)dBToDataTable.Rows[i]["排名"],
					名次 = (int)dBToDataTable.Rows[i]["名次"],
					武勋 = (int)dBToDataTable.Rows[i]["武勋"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					钻石 = (int)dBToDataTable.Rows[i]["钻石"],
					套装 = (int)dBToDataTable.Rows[i]["套装ID"],
					金钱 = dBToDataTable.Rows[i]["金钱"].ToString(),
					单件物品 = dBToDataTable.Rows[i]["单件物品"].ToString()
				});
			}
			Form1.WriteLine(2, "加载比武泡点奖励数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set大乱斗奖励()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  大乱斗奖励", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载大乱斗奖励出错----没有大乱斗奖励数据");
		}
		else
		{
			大乱斗奖励.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				大乱斗奖励.TryAdd(i, new 大乱斗奖励
				{
					排名 = (int)dBToDataTable.Rows[i]["排名"],
					名次 = (int)dBToDataTable.Rows[i]["名次"],
					武勋 = (int)dBToDataTable.Rows[i]["武勋"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					钻石 = (int)dBToDataTable.Rows[i]["钻石"],
					套装 = (int)dBToDataTable.Rows[i]["套装ID"],
					金钱 = dBToDataTable.Rows[i]["金钱"].ToString(),
					单件物品 = dBToDataTable.Rows[i]["单件物品"].ToString()
				});
			}
			Form1.WriteLine(2, "加载大乱斗奖励数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set石头属性()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_STONE", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载石头属性出错----没有石头数据");
		}
		else
		{
			石头属性调整.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				石头属性调整类 石头属性调整类2 = new 石头属性调整类();
				石头属性调整类2.类型 = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
				石头属性调整类2.数量 = (int)dBToDataTable.Rows[i]["FLD_VALUE"];
				石头属性调整类2.加减 = (int)dBToDataTable.Rows[i]["FLD_增减"];
				石头属性调整.TryAdd(i, 石头属性调整类2);
			}
			Form1.WriteLine(2, "加载石头属性效果数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载职业系数()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_XWWL_职业参数", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载职业系数出错----没有职业系数数据");
		}
		else
		{
			职业系数数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				职业系数类 value = new 职业系数类
				{
					ID = (int)dBToDataTable.Rows[i]["id"],
					FLD_职业 = (int)dBToDataTable.Rows[i]["FLD_职业"],
					FLD_几转 = (int)dBToDataTable.Rows[i]["FLD_几转"],
					物理对怪伤害 = (double)dBToDataTable.Rows[i]["物理对怪伤害"],
					物理对人伤害 = (double)dBToDataTable.Rows[i]["物理对人伤害"],
					技能对怪伤害 = (double)dBToDataTable.Rows[i]["技能对怪伤害"],
					技能对人伤害 = (double)dBToDataTable.Rows[i]["技能对人伤害"]
				};
				职业系数数据.TryAdd(i, value);
			}
			Form1.WriteLine(2, "加载职业系数数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 强化概率参数()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_强化参数", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载强化参数出错----没有数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				强化概率((int)dBToDataTable.Rows[i]["FLD_强化数"], (double)dBToDataTable.Rows[i]["FLD_首饰加工"], (double)dBToDataTable.Rows[i]["FLD_属性阶段"], (double)dBToDataTable.Rows[i]["FLD_水晶属性阶段"], (double)dBToDataTable.Rows[i]["FLD_NPC强化"], (double)dBToDataTable.Rows[i]["FLD_至尊符强化"], (double)dBToDataTable.Rows[i]["FLD_灵宠强化"], (double)dBToDataTable.Rows[i]["FLD_披风强化"]);
			}
			Form1.WriteLine(2, "加载强化参数数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 强化概率(int 强化数, double 首饰加工, double 属性阶段, double 水晶属性阶段, double NPC强化, double 至尊符强化, double 灵宠强化, double 披风强化)
	{
		try
		{
			switch (强化数)
			{
			case 1:
				首饰加工一成功率 = 首饰加工;
				属性阶段一合成率 = 属性阶段;
				水晶属性阶段一合成率 = 水晶属性阶段;
				强化一合成率 = NPC强化;
				水晶取玉符强1 = 至尊符强化;
				灵宠强化一阶段概率 = 灵宠强化;
				披风强化一阶段概率 = 披风强化;
				break;
			case 2:
				首饰加工二成功率 = 首饰加工;
				属性阶段二合成率 = 属性阶段;
				水晶属性阶段二合成率 = 水晶属性阶段;
				强化二合成率 = NPC强化;
				水晶取玉符强2 = 至尊符强化;
				灵宠强化二阶段概率 = 灵宠强化;
				披风强化二阶段概率 = 披风强化;
				break;
			case 3:
				首饰加工三成功率 = 首饰加工;
				属性阶段三合成率 = 属性阶段;
				水晶属性阶段三合成率 = 水晶属性阶段;
				强化三合成率 = NPC强化;
				水晶取玉符强3 = 至尊符强化;
				灵宠强化三阶段概率 = 灵宠强化;
				披风强化三阶段概率 = 披风强化;
				break;
			case 4:
				首饰加工四成功率 = 首饰加工;
				属性阶段四合成率 = 属性阶段;
				水晶属性阶段四合成率 = 水晶属性阶段;
				强化四合成率 = NPC强化;
				水晶取玉符强4 = 至尊符强化;
				灵宠强化四阶段概率 = 灵宠强化;
				披风强化四阶段概率 = 披风强化;
				break;
			case 5:
				首饰加工五成功率 = 首饰加工;
				属性阶段五合成率 = 属性阶段;
				水晶属性阶段五合成率 = 水晶属性阶段;
				强化五合成率 = NPC强化;
				水晶取玉符强5 = 至尊符强化;
				灵宠强化五阶段概率 = 灵宠强化;
				披风强化五阶段概率 = 披风强化;
				break;
			case 6:
				首饰加工六成功率 = 首饰加工;
				属性阶段六合成率 = 属性阶段;
				水晶属性阶段六合成率 = 水晶属性阶段;
				强化六合成率 = NPC强化;
				水晶取玉符强6 = 至尊符强化;
				灵宠强化六阶段概率 = 灵宠强化;
				披风强化六阶段概率 = 披风强化;
				break;
			case 7:
				首饰加工七成功率 = 首饰加工;
				属性阶段七合成率 = 属性阶段;
				水晶属性阶段七合成率 = 水晶属性阶段;
				强化七合成率 = NPC强化;
				水晶取玉符强7 = 至尊符强化;
				灵宠强化七阶段概率 = 灵宠强化;
				披风强化七阶段概率 = 披风强化;
				break;
			case 8:
				首饰加工八成功率 = 首饰加工;
				属性阶段八合成率 = 属性阶段;
				水晶属性阶段八合成率 = 水晶属性阶段;
				强化八合成率 = NPC强化;
				水晶取玉符强8 = 至尊符强化;
				灵宠强化八阶段概率 = 灵宠强化;
				披风强化八阶段概率 = 披风强化;
				break;
			case 9:
				首饰加工九成功率 = 首饰加工;
				属性阶段九合成率 = 属性阶段;
				水晶属性阶段九合成率 = 水晶属性阶段;
				强化九合成率 = NPC强化;
				水晶取玉符强9 = 至尊符强化;
				灵宠强化九阶段概率 = 灵宠强化;
				披风强化九阶段概率 = 披风强化;
				break;
			case 10:
				首饰加工十成功率 = 首饰加工;
				属性阶段十合成率 = 属性阶段;
				水晶属性阶段十合成率 = 水晶属性阶段;
				强化十合成率 = NPC强化;
				水晶取玉符强10 = 至尊符强化;
				灵宠强化十阶段概率 = 灵宠强化;
				披风强化十阶段概率 = 披风强化;
				break;
			case 11:
				强化十一合成率 = NPC强化;
				至尊取玉符强11 = 至尊符强化;
				灵宠强化十一阶段概率 = 灵宠强化;
				披风强化十一阶段概率 = 披风强化;
				break;
			case 12:
				强化十二合成率 = NPC强化;
				至尊取玉符强12 = 至尊符强化;
				灵宠强化十二阶段概率 = 灵宠强化;
				披风强化十二阶段概率 = 披风强化;
				break;
			case 13:
				强化十三合成率 = NPC强化;
				至尊取玉符强13 = 至尊符强化;
				灵宠强化十三阶段概率 = 灵宠强化;
				披风强化十三阶段概率 = 披风强化;
				break;
			case 14:
				强化十四合成率 = NPC强化;
				至尊取玉符强14 = 至尊符强化;
				灵宠强化十四阶段概率 = 灵宠强化;
				披风强化十四阶段概率 = 披风强化;
				break;
			case 15:
				强化十五合成率 = NPC强化;
				至尊取玉符强15 = 至尊符强化;
				灵宠强化十五阶段概率 = 灵宠强化;
				披风强化十五阶段概率 = 披风强化;
				break;
			}
		}
		catch
		{
			Form1.WriteLine(2, "设置强化参数出错");
		}
	}

	public static bool 检查物品是否被锁定(int int_0)
	{
		string[] array = 拍卖物品锁定.Split(','); // 2025-05-19: 寄售改为拍卖
		int num = 0;
		while (true)
		{
			if (num < array.Length)
			{
				if (array[num] == int_0.ToString())
				{
					break;
				}
				num++;
				continue;
			}
			return false;
		}
		return true;
	}

	public void Set物品兑换()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  物品兑换", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载物品兑换出错----没有物品兑换数据");
		}
		else
		{
			物品兑换.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				物品兑换.TryAdd(i, new 物品兑换类
				{
					需要物品 = dBToDataTable.Rows[i]["需要物品"].ToString(),
					武勋 = (int)dBToDataTable.Rows[i]["武勋"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					积分 = (int)dBToDataTable.Rows[i]["积分"],
					套装 = (int)dBToDataTable.Rows[i]["套装ID"],
					金钱 = dBToDataTable.Rows[i]["金钱"].ToString(),
					命令 = dBToDataTable.Rows[i]["命令"].ToString(),
					增加 = (int)dBToDataTable.Rows[i]["增加"],
					颜色 = (int)dBToDataTable.Rows[i]["颜色"],
					单件物品 = dBToDataTable.Rows[i]["单件物品"].ToString(),
					系统公告 = dBToDataTable.Rows[i]["系统公告"].ToString()
				});
			}
			Form1.WriteLine(2, "加载物品兑换数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set神器兑换()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  神器兑换", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载神器兑换出错----没有神器兑换数据");
		}
		else
		{
			神器兑换.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				神器兑换.TryAdd(i, new 神器兑换类
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					需要物品 = dBToDataTable.Rows[i]["需要物品"].ToString(),
					需要武勋 = (int)dBToDataTable.Rows[i]["需要武勋"],
					需要元宝 = (int)dBToDataTable.Rows[i]["需要元宝"],
					需要钻石 = (int)dBToDataTable.Rows[i]["需要钻石"],
					需要武皇币 = (int)dBToDataTable.Rows[i]["需要武皇币"],
					获得物品 = dBToDataTable.Rows[i]["获得物品"].ToString(),
					是否开启 = (int)dBToDataTable.Rows[i]["是否开启"],
					系统公告 = dBToDataTable.Rows[i]["系统公告"].ToString(),
					颜色 = (int)dBToDataTable.Rows[i]["颜色"],
					需要充值点 = (int)dBToDataTable.Rows[i]["需要充值点"]
				});
			}
			Form1.WriteLine(2, "加载神器兑换数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set公告()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Gg", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载公告出错----没有公告数据");
		}
		else
		{
			公告.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				公告.TryAdd(i, new 公告类
				{
					msg = dBToDataTable.Rows[i]["txt"].ToString(),
					type = (int)dBToDataTable.Rows[i]["type"]
				});
			}
			Form1.WriteLine(2, FastString.Concat("加载公告数据完成", dBToDataTable.Rows.Count)); // 2025-0618 EVIAS 优化字符串拼接
		}
		dBToDataTable.Dispose();
	}

	public void Set任务数据新()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_XWWL_MISSION WHERE FLD_ON = 1", "PublicDb");
		int rwpid = 0;
		try
		{
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载任务数据新出错----没有任务传书数据");
			}
			else
			{
				任务list.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					任务类 任务类2 = new 任务类();
					任务类2.RwID = (int)dBToDataTable.Rows[i]["FLD_PID"];
					rwpid = 任务类2.RwID;
					任务类2.任务名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					任务类2.任务等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
					任务类2.任务正邪 = (int)dBToDataTable.Rows[i]["FLD_ZX"];
					任务类2.职业 = (int)dBToDataTable.Rows[i]["FLD_JOB"];
					任务类2.NpcID = (int)dBToDataTable.Rows[i]["FLD_NPCID"];
					任务类2.NPCNAME = dBToDataTable.Rows[i]["FLD_NPCNAME"].ToString();
					任务类2.Npc坐标.地图ID = (int)dBToDataTable.Rows[i]["FLD_MAP"];
					任务类2.Npc坐标.坐标X = (int)dBToDataTable.Rows[i]["FLD_X"];
					任务类2.Npc坐标.坐标Y = (int)dBToDataTable.Rows[i]["FLD_Y"];
					任务类2.任务开关 = (int)dBToDataTable.Rows[i]["FLD_ON"];
					任务类2.任务类型 = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
					int num = (int)dBToDataTable.Rows[i]["FLD_ZHMR"];
					任务类2.是否账号每日 = ((num != 0) ? true : false);
					任务类2.ZJPID = (int)dBToDataTable.Rows[i]["FLD_ZJPID"];
					任务类2.WPPID = (int)dBToDataTable.Rows[i]["FLD_WPPID"];
					任务类2.WPSL = (int)dBToDataTable.Rows[i]["FLD_WPSL"];
					int num2 = (int)dBToDataTable.Rows[i]["FLD_ZDHD"];
					任务类2.是否组队获得 = ((num2 != 0) ? true : false);
					DataTable dataTable = RxjhClass.得到自定义任务阶段(rwpid);
					if (dataTable != null)
					{
                        for (int j = 0; j < dataTable.Rows.Count; j++)
                        {
                            任务阶段类 任务阶段类2 = new 任务阶段类();
                            任务阶段类2.阶段ID = (int)dataTable.Rows[j]["任务阶段ID"];
                            任务阶段类2.NpcID = (int)dataTable.Rows[j]["NpcID"];
                            string text = dataTable.Rows[j]["FLD_NEED_ITEM"].ToString();
                            if (text.Length > 0)
                            {
                                string[] array = text.Split(';');
                                string[] array2 = array;
                                string[] array3 = array2;
                                string[] array4 = array3;
                                foreach (string text2 in array4)
                                {
                                    任务需要物品类 任务需要物品类2 = new 任务需要物品类();
                                    string[] array5 = text2.Split(',');
                                    任务需要物品类2.物品ID = int.Parse(array5[0]);
                                    任务需要物品类2.物品数量 = int.Parse(array5[1]);
                                    任务需要物品类2.NPCPID = int.Parse(array5[2]);
                                    任务需要物品类2.难度 = int.Parse(array5[3]);
                                    任务阶段类2.任务需要物品.Add(任务需要物品类2);
                                }
                            }
                            string text3 = dataTable.Rows[j]["FLD_GET_ITEM"].ToString();
                            if (text3.Length > 0)
                            {
                                string[] array6 = text3.Split(';');
                                string[] array7 = array6;
                                string[] array8 = array7;
                                string[] array9 = array8;
                                foreach (string text4 in array9)
                                {
                                    任务获得物品类 任务获得物品类2 = new 任务获得物品类();
                                    string[] array10 = text4.Split(',');
                                    任务获得物品类2.物品ID = int.Parse(array10[0]);
                                    任务获得物品类2.物品数量 = int.Parse(array10[1]);
                                    任务获得物品类2.是否绑定 = int.Parse(array10[2]);
                                    任务获得物品类2.奖励类型 = int.Parse(array10[3]);
                                    任务获得物品类2.时间 = int.Parse(array10[4]);
                                    任务阶段类2.任务获得物品.Add(任务获得物品类2);
                                }
                            }
                            任务类2.任务阶段.Add(任务阶段类2);
						}
					}
					任务类2.任务阶段数量 = 任务类2.任务阶段.Count;
					任务list.TryAdd(任务类2.RwID, 任务类2);
				}
				Form1.WriteLine(2, "加载任务数据新完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "加载任务数据新出错,任务ID-" + rwpid + "|" + ex.ToString());
		}
	}

	public void Set制药物品()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 制药物品列表 ORDER BY 物品ID", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载制作物品完成----没有制作物品数据");
			return;
		}
		制药物品列表.Clear();
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			制药物品类 制药物品类2 = new 制药物品类();
			try
			{
				制药物品类2.物品ID = (int)dBToDataTable.Rows[i]["物品ID"];
				制药物品类2.物品名 = dBToDataTable.Rows[i]["物品名"].ToString();
				制药物品类2.物品数量 = (int)dBToDataTable.Rows[i]["物品数量"];
				string value = dBToDataTable.Rows[i]["需要物品"].ToString();
				制药物品类2.需要物品 = JsonConvert.DeserializeObject<List<制药需要物品类>>(value);
				if (!制药物品列表.ContainsKey(制药物品类2.物品ID))
				{
					制药物品列表.TryAdd(制药物品类2.物品ID, 制药物品类2);
				}
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "加载制药物品 错误" + 制药物品类2.物品ID + "  " + ex.Message);
			}
		}
		Form1.WriteLine(2, "加载制药物品 " + dBToDataTable.Rows.Count);
	}

	public void Set移动()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_MAP  WHERE  (X  IS  NOT  NULL)", "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载自定义移动出错----没有移动数据");
			}
			else
			{
				移动.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					移动.Add(new 坐标Class
					{
						ID = (int)dBToDataTable.Rows[i]["ID"],
						Rxjh_name = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						Rxjh_Map = (int)dBToDataTable.Rows[i]["FLD_MID"],
						Rxjh_X = float.Parse(dBToDataTable.Rows[i]["X"].ToString()),
						Rxjh_Y = float.Parse(dBToDataTable.Rows[i]["Y"].ToString()),
						Rxjh_Z = 15f,
						别墅名字 = dBToDataTable.Rows[i]["别墅名字"].ToString(),
						是否别墅 = (int)dBToDataTable.Rows[i]["是否别墅"],
						暴率 = (int)dBToDataTable.Rows[i]["暴率"]
					});
				}
				Form1.WriteLine(2, "加载自定义移动数据完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		DataTable dBToDataTable2 = DBA.GetDBToDataTable("SELECT  FLD_MID,FLD_NAME  FROM  TBL_XWWL_MAP", "PublicDb");
		if (dBToDataTable2 == null)
		{
			return;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载自定义移动出错----没有移动数据");
		}
		else
		{
			Maplist.Clear();
			for (int j = 0; j < dBToDataTable2.Rows.Count; j++)
			{
				int key = (int)dBToDataTable2.Rows[j]["FLD_MID"];
				if (!Maplist.ContainsKey(key))
				{
					Maplist.TryAdd(key, dBToDataTable2.Rows[j]["FLD_NAME"].ToString());
				}
			}
		}
		dBToDataTable2.Dispose();
	}

	public static void Set别墅移动()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_MAP  WHERE  (X  IS  NOT  NULL)", "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载别墅移动出错----没有移动数据");
			}
			else
			{
				移动.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					移动.Add(new 坐标Class
					{
						ID = (int)dBToDataTable.Rows[i]["ID"],
						Rxjh_name = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						Rxjh_Map = (int)dBToDataTable.Rows[i]["FLD_MID"],
						Rxjh_X = float.Parse(dBToDataTable.Rows[i]["X"].ToString()),
						Rxjh_Y = float.Parse(dBToDataTable.Rows[i]["Y"].ToString()),
						Rxjh_Z = 15f,
						别墅名字 = dBToDataTable.Rows[i]["别墅名字"].ToString(),
						是否别墅 = (int)dBToDataTable.Rows[i]["是否别墅"],
						暴率 = (int)dBToDataTable.Rows[i]["暴率"]
					});
				}
				Form1.WriteLine(2, "加载别墅移动数据完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		DataTable dBToDataTable2 = DBA.GetDBToDataTable("SELECT  FLD_MID,FLD_NAME  FROM  TBL_XWWL_MAP", "PublicDb");
		if (dBToDataTable2 == null)
		{
			return;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载别墅移动出错----没有移动数据");
		}
		else
		{
			Maplist.Clear();
			for (int j = 0; j < dBToDataTable2.Rows.Count; j++)
			{
				int key = (int)dBToDataTable2.Rows[j]["FLD_MID"];
				if (!Maplist.ContainsKey(key))
				{
					Maplist.TryAdd(key, dBToDataTable2.Rows[j]["FLD_NAME"].ToString());
				}
			}
		}
		dBToDataTable2.Dispose();
	}

	public static void Set兑换码()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  兑换记录 ", "GameLog");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载CDK兑换码----没有移动数据");
		}
		else
		{
			兑换码.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				兑换码Class 兑换码Class2 = new 兑换码Class
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					FLD_CDK = dBToDataTable.Rows[i]["FLD_CDK"].ToString(),
					玩家名字 = dBToDataTable.Rows[i]["玩家名字"].ToString(),
                    玩家帐号 = dBToDataTable.Rows[i]["玩家帐号"].ToString(),
                    称号 = (int)dBToDataTable.Rows[i]["称号"],
					物品编号 = (int)dBToDataTable.Rows[i]["物品编号"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					IP地址 = dBToDataTable.Rows[i]["IP地址"].ToString(),
					时间 = DateTime.Parse(dBToDataTable.Rows[i]["时间"].ToString()),
					说明 = dBToDataTable.Rows[i]["说明"].ToString(),
					分区 = dBToDataTable.Rows[i]["分区"].ToString(),
					硬件指纹 = dBToDataTable.Rows[i]["硬件指纹"]?.ToString() ?? "0" // EVIAS 硬件指纹
				};
				兑换码.TryAdd(兑换码Class2.ID, 兑换码Class2);
			}
			Form1.WriteLine(2, "加载CDK兑换码完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set兑换码1()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  兑换记录 ", "GameLog");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载CDK兑换码----没有移动数据");
		}
		else
		{
			兑换码.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				兑换码Class 兑换码Class2 = new 兑换码Class
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					FLD_CDK = dBToDataTable.Rows[i]["FLD_CDK"].ToString(),
					玩家名字 = dBToDataTable.Rows[i]["玩家名字"].ToString(),
                    玩家帐号 = dBToDataTable.Rows[i]["玩家帐号"].ToString(),
                    称号 = (int)dBToDataTable.Rows[i]["称号"],
					物品编号 = (int)dBToDataTable.Rows[i]["物品编号"],
					元宝 = (int)dBToDataTable.Rows[i]["元宝"],
					IP地址 = dBToDataTable.Rows[i]["IP地址"].ToString(),
					时间 = DateTime.Parse(dBToDataTable.Rows[i]["时间"].ToString()),
					说明 = dBToDataTable.Rows[i]["说明"].ToString(),
					分区 = dBToDataTable.Rows[i]["分区"].ToString(),
					硬件指纹 = dBToDataTable.Rows[i]["硬件指纹"]?.ToString() ?? "0" // EVIAS 硬件指纹
				};
				兑换码.TryAdd(兑换码Class2.ID, 兑换码Class2);
			}
			Form1.WriteLine(2, "加载CDK兑换码完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetLever()
	{
		if (升级经验是否开启数据库 == 1)
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_XWWL_EXP", "PublicDb");
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载经验表出错----没有经验数据");
			}
			else
			{
				lever.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					lever.TryAdd((int)dBToDataTable.Rows[i]["FLD_LEVEL"], (double)dBToDataTable.Rows[i]["FLD_EXP"]);
				}
				Form1.WriteLine(2, "加载数据库经验表完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
			return;
		}
		lever.Clear();
		for (int j = 0; j < 256; j++)
		{
			if (j == 0)
			{
				lever.TryAdd(0, 升级经验表基数);
			}
			else if (j <= 20)
			{
				lever.TryAdd(j, lever[j - 1] * 1.3);
			}
			else if (j > 20 && j <= 40)
			{
				lever.TryAdd(j, lever[j - 1] * 1.2);
			}
			else if (j > 40 && j <= 80)
			{
				lever.TryAdd(j, lever[j - 1] * 1.17);
			}
			else if (j > 80)
			{
				lever.TryAdd(j, lever[j - 1] * 1.1);
			}
		}
		Form1.WriteLine(2, "加载GS自带经验表完成");
	}

	public void SetMover()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_VOME", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载移动出错----没有移动数据");
		}
		else
		{
			Mover.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Mover.Add(new MoveClass
				{
					MAP = (int)dBToDataTable.Rows[i]["MAP"],
					X = float.Parse(dBToDataTable.Rows[i]["X"].ToString()),
					Y = float.Parse(dBToDataTable.Rows[i]["Y"].ToString()),
					Z = float.Parse(dBToDataTable.Rows[i]["Z"].ToString()),
					ToMAP = (int)dBToDataTable.Rows[i]["ToMAP"],
					ToX = float.Parse(dBToDataTable.Rows[i]["ToX"].ToString()),
					ToY = float.Parse(dBToDataTable.Rows[i]["ToY"].ToString()),
					ToZ = float.Parse(dBToDataTable.Rows[i]["ToZ"].ToString())
				});
			}
			Form1.WriteLine(2, "加载移动数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetKONGFU()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_KONGFU", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载武功出错----没有武功数据");
		}
		else
		{
			TBL_KONGFU.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				武功类 武功类2 = new 武功类
				{
					FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					FLD_AT = (int)dBToDataTable.Rows[i]["FLD_AT"],
					FLD_EFFERT = (int)dBToDataTable.Rows[i]["FLD_EFFERT"],
					FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"],
					FLD_JOB = (int)dBToDataTable.Rows[i]["FLD_JOB"],
					FLD_JOBLEVEL = (int)dBToDataTable.Rows[i]["FLD_JOBLEVEL"],
					FLD_LEVEL = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
					FLD_MP = (int)dBToDataTable.Rows[i]["FLD_MP"],
					FLD_NEEDEXP = (int)dBToDataTable.Rows[i]["FLD_NEEDEXP"],
					FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					FLD_TYPE = (int)dBToDataTable.Rows[i]["FLD_TYPE"],
					FLD_ZX = (int)dBToDataTable.Rows[i]["FLD_ZX"],
					FLD_攻击数量 = (int)dBToDataTable.Rows[i]["FLD_攻击数量"],
					FLD_武功类型 = (int)dBToDataTable.Rows[i]["FLD_武功类型"],
					FLD_每级加MP = (int)dBToDataTable.Rows[i]["FLD_每级加MP"],
					FLD_每级加历练 = (int)dBToDataTable.Rows[i]["FLD_每级加历练"],
					FLD_每级危害 = dBToDataTable.Rows[i]["FLD_每级危害"].ToString(),
					FLD_每级加危害 = (int)dBToDataTable.Rows[i]["FLD_每级加危害"],
					FLD_每级武功点数 = (int)dBToDataTable.Rows[i]["FLD_每级武功点数"],
					FLD_TIME = (int)dBToDataTable.Rows[i]["FLD_TIME"],
					FLD_DEATHTIME = (int)dBToDataTable.Rows[i]["FLD_DEATHTIME"],
					FLD_CDTIME = (int)dBToDataTable.Rows[i]["FLD_CDTIME"],
					FLD_CDTIME2 = (int)dBToDataTable.Rows[i]["FLD_CDTIME2"],
					FLD_武功最高级别 = (int)dBToDataTable.Rows[i]["FLD_武功最高级别"],
					FLD_每级加修炼等级 = (int)dBToDataTable.Rows[i]["FLD_每级加修炼等级"]
				};
				TBL_KONGFU.TryAdd(武功类2.FLD_PID, 武功类2);
			}
			Form1.WriteLine(2, "加载武功数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载百宝阁抽奖()
	{
        string sqlCommand = $"SELECT * FROM tbl_award";
        DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "BBG");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载百宝阁物品----没有百宝阁抽奖数据");
		}
		else
		{
			百宝阁抽奖物品类list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				百宝阁类 百宝阁类2 = new 百宝阁类
				{
					PID = int.Parse(dBToDataTable.Rows[i]["id"].ToString()),
					NAME = dBToDataTable.Rows[i]["text"].ToString(),
					MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"],
					觉醒 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"],
					中级魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"],
					进化 = (int)dBToDataTable.Rows[i]["FLD_进化"],
					绑定 = (int)dBToDataTable.Rows[i]["FLD_是否绑定"],
					使用天数 = (int)dBToDataTable.Rows[i]["FLD_DAYS"]
				};
				百宝阁抽奖物品类list.TryAdd(百宝阁类2.PID, 百宝阁类2);
			}
			Form1.WriteLine(2, "加载老百宝阁抽奖数据完成 " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetBbgItem()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM ITEMSELL", "BBG");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载百宝阁物品----没有百宝阁数据");
		}
		else
		{
			百宝阁属性物品类list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				百宝阁类 百宝阁类2 = new 百宝阁类
				{
					PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					PRICE = int.Parse(dBToDataTable.Rows[i]["FLD_PRICE"].ToString()),
					DESC = dBToDataTable.Rows[i]["FLD_DESC"].ToString(),
					TYPE = (int)dBToDataTable.Rows[i]["FLD_TYPE"],
					RETURN = (int)dBToDataTable.Rows[i]["FLD_RETURN"],
					NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"],
					MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					觉醒 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"],
					中级魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"],
					进化 = (int)dBToDataTable.Rows[i]["FLD_进化"],
					绑定 = (int)dBToDataTable.Rows[i]["FLD_绑定"],
					使用天数 = (int)dBToDataTable.Rows[i]["FLD_DAYS"],
					时间 = DateTime.Parse(dBToDataTable.Rows[i]["FLD_TIME"].ToString())
				};
				百宝阁属性物品类list.TryAdd(百宝阁类2.PID, 百宝阁类2);
			}
			Form1.WriteLine(2, "加载百宝阁数据完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
		if (百宝阁服务 == null)
		{
			百宝阁服务 = NewWebShopService.Create(服务器ID, 服务器组ID, 老百宝阁地址, 百宝阁数据库连接信息);
		}
		else
		{
			百宝阁服务.ReloadSaleItemAndCustomMenu();
		}
	}

	public void SetItme()
	{
		int num = 0;
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT *  FROM  TBL_XWWL_ITEM", "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载物品出错----没有物品数据");
			}
			else
			{
				PVP装备.Clear();
				Itme.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					try
					{
						ItmeClass itmeClass = new ItmeClass();
						int num2 = (int)dBToDataTable.Rows[i]["FLD_NJ"];
						itmeClass.FLD_AT = (int)dBToDataTable.Rows[i]["FLD_AT1"];
						itmeClass.FLD_AT_Max = (int)dBToDataTable.Rows[i]["FLD_AT2"];
						itmeClass.FLD_DF = (int)dBToDataTable.Rows[i]["FLD_DF"];
						itmeClass.FLD_RESIDE1 = (int)dBToDataTable.Rows[i]["FLD_RESIDE1"];
						itmeClass.FLD_RESIDE2 = (int)dBToDataTable.Rows[i]["FLD_RESIDE2"];
						itmeClass.FLD_JOB_LEVEL = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"];
						itmeClass.FLD_LEVEL = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
						itmeClass.FLD_RECYCLE_MONEY = (int)dBToDataTable.Rows[i]["FLD_RECYCLE_MONEY"];
						itmeClass.FLD_SALE_MONEY = (int)dBToDataTable.Rows[i]["FLD_SALE_MONEY"];
						itmeClass.FLD_PID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
						itmeClass.FLD_SEX = (int)dBToDataTable.Rows[i]["FLD_SEX"];
						itmeClass.FLD_WEIGHT = (int)dBToDataTable.Rows[i]["FLD_WEIGHT"];
						itmeClass.FLD_ZX = (int)dBToDataTable.Rows[i]["FLD_ZX"];
						itmeClass.FLD_SIDE = (int)dBToDataTable.Rows[i]["FLD_SIDE"];
						itmeClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
						itmeClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
						itmeClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
						itmeClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
						itmeClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"];
						itmeClass.FLD_UP_LEVEL = (int)dBToDataTable.Rows[i]["FLD_UP_LEVEL"];
						itmeClass.FLD_XW = (int)dBToDataTable.Rows[i]["FLD_WX"];
						itmeClass.FLD_XWJD = (int)dBToDataTable.Rows[i]["FLD_WXJD"];
						itmeClass.FLD_TYPE = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
						itmeClass.FLD_QUESTITEM = (int)dBToDataTable.Rows[i]["FLD_QUESTITEM"];
						itmeClass.ItmeNAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
						itmeClass.FLD_NJ = num2;
						itmeClass.FLD_LOCK = (int)dBToDataTable.Rows[i]["FLD_LOCK"];
						itmeClass.FLD_NEED_MONEY = (double)dBToDataTable.Rows[i]["FLD_NEED_MONEY"];
						itmeClass.FLD_NEED_FIGHTEXP = (int)dBToDataTable.Rows[i]["FLD_NEED_FIGHTEXP"];
						itmeClass.FLD_INTEGRATION = (int)dBToDataTable.Rows[i]["FLD_INTEGRATION"];
						itmeClass.FLD_SERIES = (int)dBToDataTable.Rows[i]["FLD_SERIES"];
						itmeClass.FLD_HEAD_WEAR = (int)dBToDataTable.Rows[i]["FLD_HEAD_WEAR"];
						itmeClass.ItmeDES = dBToDataTable.Rows[i]["FLD_ZBSJ"].ToString();
						itmeClass.FLD_YCHP = (int)dBToDataTable.Rows[i]["FLD_YCHP"];
						itmeClass.FLD_YCAT = (int)dBToDataTable.Rows[i]["FLD_YCAT"];
						itmeClass.FLD_YCDF = (int)dBToDataTable.Rows[i]["FLD_YCDF"];
						itmeClass.FLD_YCJY = (int)dBToDataTable.Rows[i]["FLD_YCJY"];
						itmeClass.FLD_ATBFB = (int)dBToDataTable.Rows[i]["FLD_ATBFB"];
						itmeClass.FLD_DFBFB = (int)dBToDataTable.Rows[i]["FLD_DFBFB"];
						itmeClass.FLD_YCQG = (int)dBToDataTable.Rows[i]["FLD_YCQG"];
						itmeClass.回收字段 = dBToDataTable.Rows[i]["FLD_ZBHS"].ToString();
						itmeClass.FLD_SELL_TYPE = (int)dBToDataTable.Rows[i]["FLD_SELL_TYPE"];
						itmeClass.FLD_DEAL_LOCK = (int)dBToDataTable.Rows[i]["FLD_EL"];
						num = itmeClass.FLD_PID;
						Itme.TryAdd(itmeClass.FLD_PID, itmeClass);
						if (num2 == 1000)
						{
							PVP类 pVP类 = new PVP类
							{
								物品ID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString()),
								物品类型 = (int)dBToDataTable.Rows[i]["FLD_RESIDE2"],
								物品等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"]
							};
							PVP装备.TryAdd(pVP类.物品ID, pVP类);
						}
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, num + "|" + ex.Message);
					}
				}
				Form1.WriteLine(2, "加载物品数据完成" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, num + "|" + ex2.Message);
		}
	}

    public void 分解装备()   //EVIAS
    {
        int num = 0;
        try
        {
            DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT * FROM  EVIAS_分解物品列表"), "PublicDb");
            if (dBToDataTable != null)
            {
                if (dBToDataTable.Rows.Count == 0)
                {
                    Form1.WriteLine(2, "加载分解物品----没有分解物品数据");
                }
                else
                {
                    装备分解.Clear();

                    for (int i = 0; i < dBToDataTable.Rows.Count; i++)
                    {
                        try
                        {
                            装备分解材料 分解 = new 装备分解材料();
                            分解.分解物品PID = int.Parse(dBToDataTable.Rows[i]["分解物品PID"].ToString());
                            分解.分解物品名称 = dBToDataTable.Rows[i]["分解物品名称"].ToString();
                            分解.分解材料1PID = (int)dBToDataTable.Rows[i]["分解材料1PID"];
                            分解.分解材料1名称 = dBToDataTable.Rows[i]["分解材料1名称"].ToString();
                            分解.分解材料1数量 = (int)dBToDataTable.Rows[i]["分解材料1数量"];
                            分解.分解材料2PID = (int)dBToDataTable.Rows[i]["分解材料2PID"];
                            分解.分解材料2名称 = dBToDataTable.Rows[i]["分解材料2名称"].ToString();
                            分解.分解材料2数量 = (int)dBToDataTable.Rows[i]["分解材料2数量"];
                            分解.分解模式 = (int)dBToDataTable.Rows[i]["分解模式"];
                            装备分解.Add(分解.分解物品PID, 分解);
                        }
                        catch (Exception ex)
                        {
                            Form1.WriteLine(1, num + "|" + ex.Message);
                        }
                    }
                    Form1.WriteLine(2, "加载分解装备数据完成" + dBToDataTable.Rows.Count);
                }
                dBToDataTable.Dispose();
            }
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1, num + "|" + ex2.Message);
        }
    }

    public void 进阶装备()  //EVIAS
    {
        int num = 0;
        try
        {
            DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT * FROM  EVIAS_装备进阶列表"), "PublicDb");
            if (dBToDataTable != null)
            {
                if (dBToDataTable.Rows.Count == 0)
                {
                    Form1.WriteLine(2, "加载装备进阶列表----没有装备进阶数据");
                }
                else
                {
                    装备进阶.Clear();

                    for (int i = 0; i < dBToDataTable.Rows.Count; i++)
                    {
                        try
                        {
                            材料进阶装备 进阶 = new 材料进阶装备();
                            进阶.需要装备PID = int.Parse(dBToDataTable.Rows[i]["需要装备PID"].ToString());
                            进阶.需要装备名称 = dBToDataTable.Rows[i]["需要装备名称"].ToString();
                            进阶.需要装备强化等级 = (int)dBToDataTable.Rows[i]["需要装备强化等级"];
                            进阶.需要装备属性1 = (int)dBToDataTable.Rows[i]["需要装备属性1"];
                            进阶.需要装备属性2 = (int)dBToDataTable.Rows[i]["需要装备属性2"];
                            进阶.需要装备属性3 = (int)dBToDataTable.Rows[i]["需要装备属性3"];
                            进阶.需要装备属性4 = (int)dBToDataTable.Rows[i]["需要装备属性4"];
                            进阶.进阶装备PID = (int)dBToDataTable.Rows[i]["进阶装备PID"];
                            进阶.进阶装备名称 = dBToDataTable.Rows[i]["进阶装备名称"].ToString();
                            进阶.进阶装备属性1 = (int)dBToDataTable.Rows[i]["进阶装备属性1"];
                            进阶.进阶装备属性2 = (int)dBToDataTable.Rows[i]["进阶装备属性2"];
                            进阶.进阶装备属性3 = (int)dBToDataTable.Rows[i]["进阶装备属性3"];
                            进阶.进阶装备属性4 = (int)dBToDataTable.Rows[i]["进阶装备属性4"];
                            进阶.需要材料1名称 = dBToDataTable.Rows[i]["需要材料1名称"].ToString();
                            进阶.需要材料1数量 = (int)dBToDataTable.Rows[i]["需要材料1数量"];
                            进阶.需要元宝数量 = (int)dBToDataTable.Rows[i]["需要元宝数量"];
                            进阶.需要材料2PID = int.Parse(dBToDataTable.Rows[i]["需要材料2PID"].ToString());
                            进阶.需要材料2名称 = dBToDataTable.Rows[i]["需要材料2名称"].ToString();
                            进阶.需要材料2数量 = (int)dBToDataTable.Rows[i]["需要材料2数量"];
                            进阶.是否继承属性 = (int)dBToDataTable.Rows[i]["是否继承属性"];
                            进阶.使用天数 = (int)dBToDataTable.Rows[i]["使用天数"];
                            进阶.是否提示 = (int)dBToDataTable.Rows[i]["是否提示"];
                            装备进阶.Add(进阶.需要装备PID, 进阶);
                        }
                        catch (Exception ex)
                        {
                            Form1.WriteLine(1, num + "|" + ex.Message);
                        }
                    }
                    Form1.WriteLine(2, "加载进阶装备数据完成" + dBToDataTable.Rows.Count);
                }
                dBToDataTable.Dispose();
            }
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1, FastString.Concat(num, "|", ex2.Message)); // 2025-0618 EVIAS 优化字符串拼接
        }
    }

    public void SetShop()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_SELL  ORDER  BY  FLD_INDEX", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载物品商店----没有物品数据");
		}
		else
		{
			Shop.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Shop.Add(new ShopClass
				{
					FLD_NID = int.Parse(dBToDataTable.Rows[i]["FLD_NID"].ToString()),
					FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"],
					FLD_PID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString()),
					FLD_MONEY = long.Parse(dBToDataTable.Rows[i]["FLD_MONEY"].ToString()),
					FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					武皇币 = (int)dBToDataTable.Rows[i]["FLD_武皇币"],
					冰魄水玉 = (int)dBToDataTable.Rows[i]["FLD_冰魄水玉"],
					FLD_累计充值 = (int)dBToDataTable.Rows[i]["FLD_累计充值"]
				});
			}
			Form1.WriteLine(2, "加载物品商店完成" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 加载门战数据()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  门战胜利门派 where 分区信息= '" + 分区编号 + "'", "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			门战数据list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				门战数据 门战数据2 = new 门战数据
				{
					ID = (int)dBToDataTable.Rows[i]["帮派ID"],
					门派名字 = dBToDataTable.Rows[i]["门派名字"].ToString(),
					门战时间 = DateTime.Parse(dBToDataTable.Rows[i]["胜利时间"].ToString()),
					门战胜利奖励时间 = DateTime.Parse(dBToDataTable.Rows[i]["胜利奖励时间"].ToString()),
					门主名字 = dBToDataTable.Rows[i]["门主名字"].ToString()
				};
				门战数据list.TryAdd(门战数据2.ID, 门战数据2);
			}
		}
		dBToDataTable.Dispose();
	}

	public void 加载门派战绩()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 门战记录 where 分区= '" + 分区编号 + "'", "GameLog");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			门派战绩list.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				门派战绩 门派战绩2 = new 门派战绩
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					战胜门派 = dBToDataTable.Rows[i]["战胜门派"].ToString(),
					战败门派 = dBToDataTable.Rows[i]["战败门派"].ToString(),
					时间 = DateTime.Parse(dBToDataTable.Rows[i]["时间"].ToString())
				};
				门派战绩list.TryAdd(门派战绩2.ID, 门派战绩2);
			}
		}
		dBToDataTable.Dispose();
	}

	public void Set制作物品()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  制作物品列表  ORDER  BY  物品ID", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载制作物品完成----没有制作物品数据");
			return;
		}
		制作物品列表.Clear();
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			制作物品类 制作物品类2 = new 制作物品类();
			try
			{
				制作物品类2.物品ID = (int)dBToDataTable.Rows[i]["物品ID"];
				制作物品类2.物品名 = dBToDataTable.Rows[i]["物品名"].ToString();
				制作物品类2.物品数量 = (int)dBToDataTable.Rows[i]["物品数量"];
				制作物品类2.制作类型 = (int)dBToDataTable.Rows[i]["制作类型"];
				制作物品类2.制作等级 = (int)dBToDataTable.Rows[i]["制作等级"];
				制作物品类2.消耗精力 = (int)dBToDataTable.Rows[i]["消耗精力"];
				制作物品类2.制作成功率 = (int)dBToDataTable.Rows[i]["制作成功率"];
				string[] array = dBToDataTable.Rows[i]["需要物品"].ToString().Split('|');
				制作物品类2.需要物品.Clear();
				for (int j = 0; j < array.Length; j++)
				{
					string[] array2 = array[j].Split(',');
					制作物品类2.需要物品.Add(new 制作需要物品类
					{
						Id = int.Parse(array2[0]),
						number = int.Parse(array2[1])
					});
				}
				if (!制作物品列表.ContainsKey(制作物品类2.物品ID))
				{
					制作物品列表.TryAdd(制作物品类2.物品ID, 制作物品类2);
				}
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "加载制作物品  错误" + 制作物品类2.物品ID + "    " + ex.Message);
			}
		}
	}

	public static NpcClass AddNpc2(int pid, float x, float y, int mapid, bool 一次性怪, int NEWTIME)
	{
		try
		{
			if (MonSter.TryGetValue(pid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapid,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = ((mapid == 101 || mapid == 41001 || mapid == 29000) ? 1 : value.FLD_BOSS),
					FLD_NEWTIME = NEWTIME,
					一次性怪 = 一次性怪
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				return npcClass;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪  [" + pid + "]出错：" + ex);
		}
		return null;
	}

	public void SetDrop()
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_DROP  ORDER  BY  FLD_LEVEL1,  FLD_LEVEL2", "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(2, "加载普通掉落物品完成----没有物品数据");
			}
			else
			{
				Drop.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					DropClass dropClass = new DropClass();
					try
					{
						dropClass.ID = (int)dBToDataTable.Rows[i]["ID"];
						dropClass.FLD_LEVEL1 = (int)dBToDataTable.Rows[i]["FLD_LEVEL1"];
						dropClass.FLD_LEVEL2 = (int)dBToDataTable.Rows[i]["FLD_LEVEL2"];
						dropClass.FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"];
						dropClass.FLD_PIDNew = (int)dBToDataTable.Rows[i]["FLD_PID"];
						dropClass.FLD_PP = (int)dBToDataTable.Rows[i]["FLD_PP"];
						dropClass.FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
						dropClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
						dropClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
						dropClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
						dropClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
						dropClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
						dropClass.FLD_初级附魂 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"];
						dropClass.FLD_中级附魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"];
						dropClass.FLD_进化 = (int)dBToDataTable.Rows[i]["FLD_进化"];
						dropClass.FLD_绑定 = (int)dBToDataTable.Rows[i]["FLD_绑定"];
						dropClass.FLD_MAGICNew0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
						dropClass.FLD_MAGICNew1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
						dropClass.FLD_MAGICNew2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
						dropClass.FLD_MAGICNew3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
						dropClass.FLD_MAGICNew4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
						dropClass.是否开启公告 = (int)dBToDataTable.Rows[i]["是否开启公告"];
						dropClass.会员掉落 = (int)dBToDataTable.Rows[i]["会员掉落"];
						dropClass.MAPID = (int)dBToDataTable.Rows[i]["掉落地图ID"];
						dropClass.NPCPID = (int)dBToDataTable.Rows[i]["掉落怪物ID"];
						dropClass.数量控制 = (int)dBToDataTable.Rows[i]["数量控制"];
						dropClass.当前数量 = (int)dBToDataTable.Rows[i]["当前数量"];
						dropClass.最大数量 = (int)dBToDataTable.Rows[i]["最大数量"];
						dropClass.FLD_SUNX = (int)dBToDataTable.Rows[i]["FLD_SUNX"];
						dropClass.FLD_SUND = (int)dBToDataTable.Rows[i]["FLD_SUND"];
						Drop.Add(dropClass);
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, "加载普通掉落物品错误" + dropClass.FLD_NAME + "丨" + ex.Message);
					}
				}
				Form1.WriteLine(2, "加载普通掉落物品" + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "加载普通掉落物品错误" + ex2.Message);
		}
	}

	public void Set_GSDrop()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_DROP_GS  ORDER  BY  FLD_LEVEL1,  FLD_LEVEL2", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			BossDrop.Clear();
			Form1.WriteLine(2, "加载高手怪掉落物品完成----没有物品数据");
		}
		else
		{
			Drop_GS.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DropClass dropClass = new DropClass();
				try
				{
					dropClass.FLD_LEVEL1 = (int)dBToDataTable.Rows[i]["FLD_LEVEL1"];
					dropClass.FLD_LEVEL2 = (int)dBToDataTable.Rows[i]["FLD_LEVEL2"];
					dropClass.FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"];
					dropClass.FLD_PP = (int)dBToDataTable.Rows[i]["FLD_PP"];
					dropClass.FLD_PIDNew = (int)dBToDataTable.Rows[i]["FLD_PID"];
					dropClass.FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					dropClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
					dropClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
					dropClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
					dropClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
					dropClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
					dropClass.FLD_初级附魂 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"];
					dropClass.FLD_中级附魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"];
					dropClass.FLD_进化 = (int)dBToDataTable.Rows[i]["FLD_进化"];
					dropClass.FLD_绑定 = (int)dBToDataTable.Rows[i]["FLD_绑定"];
					dropClass.FLD_MAGICNew0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
					dropClass.FLD_MAGICNew1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
					dropClass.FLD_MAGICNew2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
					dropClass.FLD_MAGICNew3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
					dropClass.FLD_MAGICNew4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
					dropClass.是否开启公告 = (int)dBToDataTable.Rows[i]["是否开启公告"];
					dropClass.会员掉落 = (int)dBToDataTable.Rows[i]["会员掉落"];
					dropClass.MAPID = (int)dBToDataTable.Rows[i]["掉落地图ID"];
					dropClass.NPCPID = (int)dBToDataTable.Rows[i]["掉落怪物ID"];
					dropClass.FLD_SUNX = (int)dBToDataTable.Rows[i]["FLD_SUNX"];
					dropClass.FLD_SUND = (int)dBToDataTable.Rows[i]["FLD_SUND"];
					dropClass.是否必掉 = (int)dBToDataTable.Rows[i]["是否必掉"];
					Drop_GS.Add(dropClass);
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载高手掉落物品错误" + dropClass.FLD_NAME + "丨" + ex.Message);
				}
			}
			Form1.WriteLine(2, "加载高手怪落物品" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetBossDrop()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_BossDROP  ORDER  BY  FLD_LEVEL1,  FLD_LEVEL2", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			BossDrop.Clear();
			Form1.WriteLine(2, "加载BOSS掉落物品完成----没有物品数据");
		}
		else
		{
			BossDrop.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DropClass dropClass = new DropClass();
				try
				{
					dropClass.FLD_LEVEL1 = (int)dBToDataTable.Rows[i]["FLD_LEVEL1"];
					dropClass.FLD_LEVEL2 = (int)dBToDataTable.Rows[i]["FLD_LEVEL2"];
					dropClass.FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"];
					dropClass.FLD_PIDNew = (int)dBToDataTable.Rows[i]["FLD_PID"];
					dropClass.FLD_PP = (int)dBToDataTable.Rows[i]["FLD_PP"];
					dropClass.FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					dropClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
					dropClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
					dropClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
					dropClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
					dropClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
					dropClass.FLD_初级附魂 = (int)dBToDataTable.Rows[i]["FLD_初级附魂"];
					dropClass.FLD_中级附魂 = (int)dBToDataTable.Rows[i]["FLD_中级附魂"];
					dropClass.FLD_进化 = (int)dBToDataTable.Rows[i]["FLD_进化"];
					dropClass.FLD_绑定 = (int)dBToDataTable.Rows[i]["FLD_绑定"];
					dropClass.FLD_MAGICNew0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
					dropClass.FLD_MAGICNew1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
					dropClass.FLD_MAGICNew2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
					dropClass.FLD_MAGICNew3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
					dropClass.FLD_MAGICNew4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
					dropClass.是否开启公告 = (int)dBToDataTable.Rows[i]["是否开启公告"];
					dropClass.会员掉落 = (int)dBToDataTable.Rows[i]["会员掉落"];
					dropClass.MAPID = (int)dBToDataTable.Rows[i]["掉落地图ID"];
					dropClass.NPCPID = (int)dBToDataTable.Rows[i]["掉落怪物ID"];
					dropClass.FLD_SUNX = (int)dBToDataTable.Rows[i]["FLD_SUNX"];
					dropClass.FLD_SUND = (int)dBToDataTable.Rows[i]["FLD_SUND"];
					dropClass.是否必掉 = (int)dBToDataTable.Rows[i]["是否必掉"];
					BossDrop.Add(dropClass);
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载BOSS掉落物品  错误" + dropClass.FLD_NAME + "丨" + ex.Message);
				}
			}
			Form1.WriteLine(2, "加载BOSS掉落物品" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetOpen()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_OPEN", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载开箱物品完成----没有开箱物品数据");
		}
		else
		{
			Open.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Open.Add(new OpenClass
				{
					FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					FLD_PIDX = (int)dBToDataTable.Rows[i]["FLD_PIDX"],
					FLD_NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					FLD_MAGIC5 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"],
					FLD_觉醒 = (int)dBToDataTable.Rows[i]["FLD_FJ_觉醒"],
					FLD_进化 = (int)dBToDataTable.Rows[i]["FLD_FJ_进化"],
					FLD_中级附魂 = (int)dBToDataTable.Rows[i]["FLD_FJ_中级附魂"],
					FLD_BD = (int)dBToDataTable.Rows[i]["FLD_BD"],
					FLD_DAYS = (int)dBToDataTable.Rows[i]["FLD_DAYS"],
					FLD_PP = (int)dBToDataTable.Rows[i]["FLD_PP"],
					FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					是否开启公告 = (int)dBToDataTable.Rows[i]["是否开启公告"],
					FLD_NAMEX = dBToDataTable.Rows[i]["FLD_NAMEX"].ToString(),
					FLD_SUNX = (int)dBToDataTable.Rows[i]["FLD_SUNX"],
					FLD_SUND = (int)dBToDataTable.Rows[i]["FLD_SUND"],
					装备等级 = (int)dBToDataTable.Rows[i]["装备等级"],
					是否随机属性 = (int)dBToDataTable.Rows[i]["是否随机属性"]
				});
			}
			Form1.WriteLine(2, "加载开箱物品  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set道具组合()
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 道具组合 ORDER BY 物品ID", "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(1, "加载道具组合完成----没有道具组合数据");
				return;
			}
			所有可组合道具.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				int num = (int)dBToDataTable.Rows[i]["物品ID"];
				string text = dBToDataTable.Rows[i]["物品名"].ToString();
				string wp = dBToDataTable.Rows[i]["需要物品"].ToString();
				if (!所有可组合道具.ContainsKey(num))
				{
					道具组合类 道具组合类2 = new 道具组合类(num, wp);
					所有可组合道具.Add(道具组合类2.物品ID, 道具组合类2);
				}
			}
			Form1.WriteLine(2, "加载道具组合 " + dBToDataTable.Rows.Count);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "加载道具组合出错:" + ex.ToString());
		}
	}

	public void 充值排行()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM TBL_XWWL_Char where FLD_FQID='" + 分区编号 + "' and FLD_CZPH !=0  Order By FLD_CZPH Desc"), "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(1, "加载充值排行----没有充值排行数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				switch (i)
				{
				case 0:
					充值榜第一名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 1:
					充值榜第二名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 2:
					充值榜第三名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 3:
					充值榜第四名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 4:
					充值榜第五名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 5:
					充值榜第六名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 6:
					充值榜第七名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 7:
					充值榜第八名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 8:
					充值榜第九名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				case 9:
					充值榜第十名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					break;
				}
			}
		}
		dBToDataTable.Dispose();
	}

	public void 荣誉门派排行()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM 荣誉门派排行 where FLD_FQ='" + 分区编号 + "' Order By FLD_RY Desc"), "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			门派排名数据.Clear();
			Form1.WriteLine(1, "加载荣誉门派排行----没有荣誉门派排行数据");
		}
		else
		{
			门派排名数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				门派排名 item = new 门派排名
				{
					门派帮派名 = dBToDataTable.Rows[i]["FLD_BP"].ToString(),
					门派人物名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					门派正邪 = (int)dBToDataTable.Rows[i]["FLD_ZX"],
					门派职业 = (int)dBToDataTable.Rows[i]["FLD_JOB"],
					门派转职 = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"],
					门派人物等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
					门派荣誉点 = (int)dBToDataTable.Rows[i]["FLD_RY"],
					门派分区ID = dBToDataTable.Rows[i]["FLD_FQ"].ToString()
				};
				门派排名数据.Add(item);
			}
		}
		dBToDataTable.Dispose();
	}

	public void 荣誉势力排行()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM 荣誉势力排行 where FLD_FQ='" + 分区编号 + "' Order By FLD_RY Desc"), "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			势力排名数据.Clear();
			Form1.WriteLine(1, "加载荣誉势力排行----没有荣誉势力排行数据");
		}
		else
		{
			势力排名数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				势力排名 item = new 势力排名
				{
					势力帮派名 = dBToDataTable.Rows[i]["FLD_BP"].ToString(),
					势力人物名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					势力正邪 = (int)dBToDataTable.Rows[i]["FLD_ZX"],
					势力职业 = (int)dBToDataTable.Rows[i]["FLD_JOB"],
					势力人物等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
					势力转职 = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"],
					势力荣誉点 = (int)dBToDataTable.Rows[i]["FLD_RY"],
					势力分区ID = dBToDataTable.Rows[i]["FLD_FQ"].ToString()
				};
				势力排名数据.Add(item);
			}
		}
		dBToDataTable.Dispose();
	}

	public void 荣誉武林排行()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM 荣誉武林排行 where FLD_FQ='" + 分区编号 + "'  Order By FLD_RY Desc"), "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			武林排名数据.Clear();
			Form1.WriteLine(1, "加载荣誉武林排行----没有荣誉武林排行数据");
		}
		else
		{
			武林排名数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				武林排名 item = new 武林排名
				{
					武林帮派名 = dBToDataTable.Rows[i]["FLD_BP"].ToString(),
					武林人物名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					武林正邪 = (int)dBToDataTable.Rows[i]["FLD_ZX"],
					武林职业 = (int)dBToDataTable.Rows[i]["FLD_JOB"],
					武林人物等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
					武林转职 = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"],
					武林荣誉点 = (int)dBToDataTable.Rows[i]["FLD_RY"],
					武林分区ID = dBToDataTable.Rows[i]["FLD_FQ"].ToString()
				};
				武林排名数据.Add(item);
			}
		}
		dBToDataTable.Dispose();
	}

	public void 荣誉讨伐排行()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT TOP 10  * FROM 荣誉讨伐排行 where FLD_FQ='" + 分区编号 + "'  Order By FLD_RY Desc"), "GameServer");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			讨伐排名数据.Clear();
			Form1.WriteLine(1, "加载荣誉讨伐排行----没有荣誉讨伐排行数据");
		}
		else
		{
			讨伐排名数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				讨伐排名 item = new 讨伐排名
				{
					讨伐帮派名 = dBToDataTable.Rows[i]["FLD_BP"].ToString(),
					讨伐人物名 = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					讨伐正邪 = (int)dBToDataTable.Rows[i]["FLD_ZX"],
					讨伐职业 = (int)dBToDataTable.Rows[i]["FLD_JOB"],
					讨伐人物等级 = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
					讨伐转职 = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"],
					讨伐荣誉点 = (int)dBToDataTable.Rows[i]["FLD_RY"],
					讨伐分区ID = dBToDataTable.Rows[i]["FLD_FQ"].ToString()
				};
				讨伐排名数据.Add(item);
			}
		}
		dBToDataTable.Dispose();
	}

	public void boss攻城怪物()
	{
		攻城怪物列表.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_攻城BOSS", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载攻城BOSS数据出错----没有攻城BOSS数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				try
				{
					int num = (int)dBToDataTable.Rows[i]["FLD_PID"];
					if (MonSter.TryGetValue(num, out var _) && num >= 10000)
					{
						攻城怪物 攻城怪物2 = new 攻城怪物
						{
							FLD_PID = num,
							FLD_MID = (int)dBToDataTable.Rows[i]["FLD_MID"],
							FLD_X = (int)dBToDataTable.Rows[i]["FLD_X"],
							FLD_Y = (int)dBToDataTable.Rows[i]["FLD_Y"]
						};
						攻城怪物 item = 攻城怪物2;
						攻城怪物列表.Add(item);
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载攻城BOSS数据  出错：" + ex);
				}
			}
			Form1.WriteLine(2, "加载攻城BOSS数据完成  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public static double 获得队伍加成(组队Class 组队a)
	{
		double result = 0.0;
		switch (组队a.组队列表.Count)
		{
		case 1:
			result = 0.0;
			break;
		case 2:
			result = 二人组队增加百分比;
			break;
		case 3:
			result = 三人组队增加百分比;
			break;
		case 4:
			result = 四人组队增加百分比;
			break;
		case 5:
			result = 五人组队增加百分比;
			break;
		case 6:
			result = 六人组队增加百分比;
			break;
		case 7:
			result = 七人组队增加百分比;
			break;
		case 8:
			result = 八人组队增加百分比;
			break;
		}
		Players players = 检查玩家name(组队a.队长);
		if (players != null)
		{
			foreach (Players value in 组队a.组队列表.Values)
			{
				if (!value.查找范围玩家(组队范围距离, players))
				{
					return 0.0;
				}
			}
		}
		return result;
	}

	public static double 获得队伍暴率加成(组队Class 组队a)
	{
		double result = 0.0;
		switch (组队a.组队列表.Count)
		{
		case 1:
			result = 0.0;
			break;
		case 2:
			result = 二人组队暴率百分比;
			break;
		case 3:
			result = 三人组队暴率百分比;
			break;
		case 4:
			result = 四人组队暴率百分比;
			break;
		case 5:
			result = 五人组队暴率百分比;
			break;
		case 6:
			result = 六人组队暴率百分比;
			break;
		case 7:
			result = 七人组队暴率百分比;
			break;
		case 8:
			result = 八人组队暴率百分比;
			break;
		}
		Players players = 检查玩家name(组队a.队长);
		if (players != null)
		{
			foreach (Players value in 组队a.组队列表.Values)
			{
				if (!value.查找范围玩家(组队范围距离, players))
				{
					return 0.0;
				}
			}
		}
		return result;
	}

	public void boss伏魔洞怪物()
	{
		伏魔洞怪物列表.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_伏魔洞", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载伏魔洞数据出错----没有伏魔洞数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				try
				{
					int num = (int)dBToDataTable.Rows[i]["FLD_PID"];
					if (MonSter.TryGetValue(num, out var _) && num >= 10000)
					{
						伏魔洞怪物 伏魔洞怪物2 = new 伏魔洞怪物
						{
							ID = (int)dBToDataTable.Rows[i]["ID"],
							FLD_PID = num,
							FLD_MID = (int)dBToDataTable.Rows[i]["FLD_MID"],
							FLD_X = (int)dBToDataTable.Rows[i]["FLD_X"],
							FLD_Y = (int)dBToDataTable.Rows[i]["FLD_Y"]
						};
						伏魔洞怪物 item = 伏魔洞怪物2;
						伏魔洞怪物列表.Add(item);
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载伏魔洞数据  出错：" + ex);
				}
			}
			Form1.WriteLine(2, "加载伏魔洞数据完成  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void boss冰宫内城怪物()
	{
		冰宫内城怪物列表.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_冰宫内城", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载冰宫内城数据出错----没有冰宫内城数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				try
				{
					int num = (int)dBToDataTable.Rows[i]["FLD_PID"];
					if (MonSter.TryGetValue(num, out var _) && num >= 10000)
					{
						冰宫内城怪物 冰宫内城怪物2 = new 冰宫内城怪物
						{
							ID = (int)dBToDataTable.Rows[i]["ID"],
							FLD_PID = num,
							FLD_MID = (int)dBToDataTable.Rows[i]["FLD_MID"],
							FLD_X = (int)dBToDataTable.Rows[i]["FLD_X"],
							FLD_Y = (int)dBToDataTable.Rows[i]["FLD_Y"]
						};
						冰宫内城怪物 item = 冰宫内城怪物2;
						冰宫内城怪物列表.Add(item);
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载冰宫内城数据  出错：" + ex);
				}
			}
			Form1.WriteLine(2, "加载冰宫内城数据完成  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void boss活动副本怪物()
	{
		活动副本怪物列表.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_活动副本", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载活动副本数据出错----没有冰宫内城数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				try
				{
					int num = (int)dBToDataTable.Rows[i]["FLD_PID"];
					if (MonSter.TryGetValue(num, out var _) && num >= 10000)
					{
						活动副本怪物 活动副本怪物2 = new 活动副本怪物
						{
							ID = (int)dBToDataTable.Rows[i]["ID"],
							FLD_PID = num,
							FLD_MID = (int)dBToDataTable.Rows[i]["FLD_MID"],
							FLD_X = (int)dBToDataTable.Rows[i]["FLD_X"],
							FLD_Y = (int)dBToDataTable.Rows[i]["FLD_Y"]
						};
						活动副本怪物 item = 活动副本怪物2;
						活动副本怪物列表.Add(item);
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载活动副本数据  出错：" + ex);
				}
			}
			Form1.WriteLine(2, "加载活动副本数据完成  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set套装()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  ITMECLSS", "WebDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载套装物品完成----没有套装物品数据");
		}
		else
		{
			套装数据.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				套装数据.Add(new ItemSellClass
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					Type = (int)dBToDataTable.Rows[i]["FLD_TYPE"],
					Reside = (int)dBToDataTable.Rows[i]["FLD_RESIDE"],
					name = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					sql = dBToDataTable.Rows[i]["FLD_SQL"].ToString(),
					Magic0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					Magic1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					Magic2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					Magic3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					Magic4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					Magic5 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"],
					NJ = (int)dBToDataTable.Rows[i]["FLD_FJ_NJ"],
					DAYS = (int)dBToDataTable.Rows[i]["FLD_DAYS"],
					进化 = (int)dBToDataTable.Rows[i]["FLD_FJ_进化"],
					觉醒 = (int)dBToDataTable.Rows[i]["FLD_FJ_觉醒"],
					中级附魂 = (int)dBToDataTable.Rows[i]["FLD_FJ_中级附魂"],
					BD = (int)dBToDataTable.Rows[i]["FLD_BD"]
				});
			}
			Form1.WriteLine(2, "加载套装物品  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void Set升天气功()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  升天气功  ORDER  BY  气功ID", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载升天气功完成----没有升天气功数据");
		}
		else
		{
			升天气功List.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				升天气功总类 升天气功总类2 = new 升天气功总类
				{
					气功ID = (int)dBToDataTable.Rows[i]["气功ID"],
					气功名 = dBToDataTable.Rows[i]["气功名"].ToString(),
					物品ID = (int)dBToDataTable.Rows[i]["物品ID"],
					人物职业1 = (int)dBToDataTable.Rows[i]["人物职业1"],
					人物职业2 = (int)dBToDataTable.Rows[i]["人物职业2"],
					人物职业3 = (int)dBToDataTable.Rows[i]["人物职业3"],
					人物职业4 = (int)dBToDataTable.Rows[i]["人物职业4"],
					人物职业5 = (int)dBToDataTable.Rows[i]["人物职业5"],
					人物职业6 = (int)dBToDataTable.Rows[i]["人物职业6"],
					人物职业7 = (int)dBToDataTable.Rows[i]["人物职业7"],
					人物职业8 = (int)dBToDataTable.Rows[i]["人物职业8"],
					人物职业9 = (int)dBToDataTable.Rows[i]["人物职业9"],
					人物职业10 = (int)dBToDataTable.Rows[i]["人物职业10"],
					人物职业11 = (int)dBToDataTable.Rows[i]["人物职业11"],
					人物职业12 = (int)dBToDataTable.Rows[i]["人物职业12"],
					人物职业13 = (int)dBToDataTable.Rows[i]["人物职业13"],
					FLD_每点加成比率值 = (double)dBToDataTable.Rows[i]["FLD_每点加成比率值"]
				};
				升天气功List.TryAdd(升天气功总类2.气功ID, 升天气功总类2);
			}
			Form1.WriteLine(2, "加载升天气功  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void 假人商店出售物品()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("Select * from TBL_假人出售物品", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载假人出售物品列表完成----没有假人出售物品列表数据");
		}
		else
		{
			假人出售物品列表.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				假人出售物品 假人出售物品 = new 假人出售物品
				{
					Id = (int)dBToDataTable.Rows[i]["Id"],
					ItemPid = (int)dBToDataTable.Rows[i]["ItemPid"],
					金币最低 = long.Parse(dBToDataTable.Rows[i]["金钱最低"].ToString()),
					金币最高 = long.Parse(dBToDataTable.Rows[i]["金钱最高"].ToString()),
					FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					ItemTitle = dBToDataTable.Rows[i]["ItemTitle"].ToString(),
					数量最小值 = (int)dBToDataTable.Rows[i]["数量最小值"],
					数量最大值 = (int)dBToDataTable.Rows[i]["数量最大值"],
					元宝最低 = (int)dBToDataTable.Rows[i]["元宝最低"],
					元宝最高 = (int)dBToDataTable.Rows[i]["元宝最高"]
				};
				假人出售物品 item = 假人出售物品;
				假人出售物品列表.Add(item);
			}
			Form1.WriteLine(2, "假人出售物品列表  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetMonSter()
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_MONSTER", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载怪物数据完成----没有怪物数据");
		}
		else
		{
			MonSter.Clear();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				try
				{
					MonSterClss monSterClss = new MonSterClss
					{
						FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
						FLD_AT = (int)dBToDataTable.Rows[i]["FLD_AT"],
						FLD_AUTO = (int)dBToDataTable.Rows[i]["FLD_AUTO"],
						FLD_BOSS = (int)dBToDataTable.Rows[i]["FLD_BOSS"],
						FLD_DF = (int)dBToDataTable.Rows[i]["FLD_DF"],
						Level = (int)dBToDataTable.Rows[i]["FLD_LEVEL"],
						Name = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						Rxjh_Exp = (int)dBToDataTable.Rows[i]["FLD_EXP"],
						Rxjh_HP = (int)dBToDataTable.Rows[i]["FLD_HP"],
						FLD_NPC = (int)dBToDataTable.Rows[i]["FLD_NPC"],
						FLD_最小等级 = (int)dBToDataTable.Rows[i]["FLD_桃木等级小"],
						FLD_最大等级 = (int)dBToDataTable.Rows[i]["FLD_桃木等级大"]
					};
					MonSter.TryAdd(monSterClss.FLD_PID, monSterClss);
					if (Map == null || Map.Count <= 0)
					{
						continue;
					}
					foreach (MapClass value in Map.Values)
					{
						foreach (NpcClass value2 in value.npcTemplate.Values)
						{
							if (value2.FLD_PID == monSterClss.FLD_PID)
							{
								value2.FLD_AT = monSterClss.FLD_AT;
								value2.FLD_DF = monSterClss.FLD_DF;
								value2.Rxjh_Exp = monSterClss.Rxjh_Exp;
								value2.FLD_AUTO = monSterClss.FLD_AUTO;
								value2.FLD_BOSS = monSterClss.FLD_BOSS;
								value2.Level = monSterClss.Level;
								value2.Rxjh_HP = monSterClss.Rxjh_HP;
							}
						}
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "加载怪物数据  出错：" + ex);
				}
			}
			Form1.WriteLine(2, "加载怪物数据完成  " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

    public void SetNpc()  //Evias  
    {
        DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_NPC", "PublicDb");
        if (dBToDataTable == null)
        {
            return;
        }
        if (dBToDataTable.Rows.Count == 0)
        {
            Form1.WriteLine(2, "加载NPC数据出错----没有NPC数据");
        }
        else
        {
            Map.Clear();
            NpcList.Clear();
            for (int i = 0; i < dBToDataTable.Rows.Count; i++)
            {
                bool skipNpc = false;
                try
                {
                    NpcClass npcClass = new NpcClass();
                    npcClass.FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"];
                    if (MonSter.TryGetValue(npcClass.FLD_PID, out var value) && npcClass.FLD_PID >= 10000)
                    {
                        npcClass.Name = value.Name;
                        npcClass.Level = value.Level;
                        npcClass.Rxjh_Exp = value.Rxjh_Exp;
                        npcClass.IsNpc = value.FLD_NPC;
                        npcClass.Max_Rxjh_HP = value.Rxjh_HP;
                        npcClass.Rxjh_HP = value.Rxjh_HP;
                        npcClass.FLD_AT = value.FLD_AT;
                        npcClass.FLD_DF = value.FLD_DF;
                        npcClass.FLD_AUTO = value.FLD_AUTO;
                        npcClass.FLD_BOSS = value.FLD_BOSS;
                        npcClass.FLD_NEWTIME = (int)dBToDataTable.Rows[i]["FLD_NEWTIME"];
                    }
                    else
                    {
                        npcClass.Name = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
                        npcClass.Level = 0;
                        npcClass.Rxjh_Exp = 0;
                        npcClass.IsNpc = 1;
                        npcClass.Max_Rxjh_HP = 32000;
                        npcClass.Rxjh_HP = 32000;
                        npcClass.FLD_AT = 0.0;
                        npcClass.FLD_DF = 0.0;
                        npcClass.FLD_AUTO = 0;
                        npcClass.FLD_BOSS = 0;
                        npcClass.FLD_NEWTIME = 0;
                    }
                    npcClass.Rxjh_Map = (int)dBToDataTable.Rows[i]["FLD_MID"];
                    npcClass.FLD_FACE1 = float.Parse(dBToDataTable.Rows[i]["FLD_FACE0"].ToString());
                    npcClass.FLD_FACE2 = float.Parse(dBToDataTable.Rows[i]["FLD_FACE"].ToString());
                    npcClass.Rxjh_X = float.Parse(dBToDataTable.Rows[i]["FLD_X"].ToString());
                    npcClass.Rxjh_Y = float.Parse(dBToDataTable.Rows[i]["FLD_Y"].ToString());
                    npcClass.Rxjh_Z = float.Parse(dBToDataTable.Rows[i]["FLD_Z"].ToString());
                    npcClass.Rxjh_cs_X = float.Parse(dBToDataTable.Rows[i]["FLD_X"].ToString());
                    npcClass.Rxjh_cs_Y = float.Parse(dBToDataTable.Rows[i]["FLD_Y"].ToString());
                    npcClass.Rxjh_cs_Z = float.Parse(dBToDataTable.Rows[i]["FLD_Z"].ToString());
                    npcClass.FLD_SFYJ = (int)dBToDataTable.Rows[i]["FLD_SFYJ"];

                    if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
                    {
                        value2.add(npcClass);
                    }
                    else
                    {
                        value2 = new MapClass
                        {
                            MapID = npcClass.Rxjh_Map
                        };
                        value2.add(npcClass);
                        Map.TryAdd(value2.MapID, value2);
                    }

                    if (npcClass.IsNpc == 0)
                    {
                        switch (npcClass.Rxjh_Map)
                        {
                            case 101:
                                if (npcClass.Level > 0 && npcClass.Level < 30)
                                {
                                    一转地图.Add(npcClass);
                                }
                                break;
                            case 201:
                            case 301:
                            case 601:
                            case 701:
                                二转地图.Add(npcClass);
                                break;
                            case 1001:
                            case 1101:
                            case 1301:
                            case 1701:
                                三转地图.Add(npcClass);
                                break;
                            case 1401:
                            case 1501:
                            case 1801:
                            case 1901:
                                四转地图.Add(npcClass);
                                break;
                            case 2101:
                            case 2201:
                                五转地图.Add(npcClass);
                                break;
                            case 5001:
                                六转地图.Add(npcClass);
                                break;
                            case 6001:
                                七转地图.Add(npcClass);
                                break;
                            case 25100:
                                八转地图.Add(npcClass);
                                break;
                            case 26000:
                                九转地图.Add(npcClass);
                                break;
                            case 26100:
                                十转地图.Add(npcClass);
                                break;
                            case 26200:
                                十一转地图.Add(npcClass);
                                break;
                            case 26300:
                                十二转地图.Add(npcClass);
                                break;
                        }
                    }

                    if (npcClass.FLD_PID >= 10000)
                    {
                        try
                        {
                            if (!NpcList.ContainsKey(npcClass.FLD_PID))
                            {
                                NpcList.TryAdd(npcClass.FLD_PID, npcClass);
                            }
                        }
                        catch
                        {
                            skipNpc = true;
                        }
                    }

                    if (npcClass.IsNpc == 1 && npcClass.FLD_PID != 5 && npcClass.FLD_PID != 3)
                    {
                        if (npcClass.自动移动 != null)
                        {
                            npcClass.自动移动.Elapsed -= npcClass.自动移动事件; // 解绑 EVIAS
                            npcClass.自动移动.Enabled = false;
                            npcClass.自动移动.Close();
                            npcClass.自动移动.Dispose();
                            npcClass.自动移动 = null;
                        }
                        if (npcClass.自动攻击 != null)
                        {
                            npcClass.自动攻击.Elapsed -= npcClass.自动攻击事件; // 解绑 EVIAS
                            npcClass.自动攻击.Enabled = false;
                            npcClass.自动攻击.Close();
                            npcClass.自动攻击.Dispose();
                            npcClass.自动攻击 = null;
                        }
                        if (npcClass.自动复活 != null)
                        {
                            npcClass.自动复活.Enabled = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, "加载NPC数据出错：" + ex);
                    skipNpc = true;
                }

                if (skipNpc) continue;
            }
            Form1.WriteLine(2, "加载NPC数据完成" + dBToDataTable.Rows.Count);
        }
        dBToDataTable.Dispose();
    }

    public static void delNpc(int mapp, int Npcid)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value in MapClass.GetnpcTemplate(mapp).Values)
			{
				if (value.FLD_PID == Npcid)
				{
					list.Add(value);
				}
			}
			if (list == null)
			{
				return;
			}
			foreach (NpcClass item in list)
			{
				item.Dispose();
			}
			list.Clear();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "删除怪  [" + Npcid + "]出错：" + ex);
		}
	}

	public static void delBoss(int mapp, int Npcid)
	{
		try
		{
			List<NpcClass> list = new List<NpcClass>();
			foreach (NpcClass value in MapClass.GetnpcTemplate(mapp).Values)
			{
				if (value.FLD_PID == Npcid)
				{
					list.Add(value);
				}
			}
			if (list == null)
			{
				return;
			}
			foreach (NpcClass item in list)
			{
				item.发送怪物一次性死亡数据();
			}
			list.Clear();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "删除怪  [" + Npcid + "]出错：" + ex);
		}
	}

	public static NpcClass AddBossEveNpc(int Npcid, float x, float y, int mapp)
	{
		try
		{
			if (MonSter.TryGetValue(Npcid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapp,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = value.FLD_BOSS,
					一次性怪 = true
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					value2 = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					value2.add(npcClass);
					Map.TryAdd(value2.MapID, value2);
				}
				npcClass.获取范围玩家发送增加数据包();
				Thread.Sleep(1);
				return npcClass;
			}
			return null;
		}
		catch (Exception ex)
		{
			string text = "增加BOSS攻城怪 [";
			string text2 = Npcid.ToString();
			string text3 = "]出错：";
			Form1.WriteLine(2, text + text2 + text3 + ex);
			return null;
		}
	}

	public static void AddNpc(int Npcid, float x, float y, int mapp)
	{
		try
		{
			if (MonSter.TryGetValue(Npcid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapp,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = ((mapp == 101 || mapp == 41001 || mapp == 29000) ? 1 : value.FLD_BOSS),
					FLD_NEWTIME = 10,
					一次性怪 = true
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				npcClass.获取范围玩家发送增加数据包();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪  [" + Npcid + "]出错：" + ex);
		}
	}

	public static void AddNpcboss(int Npcid, float x, float y, int mapp)
	{
		try
		{
			if (MonSter.TryGetValue(Npcid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapp,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = ((mapp == 101 || mapp == 801 || mapp == 41001) ? 1 : value.FLD_BOSS),
					FLD_NEWTIME = 10,
					一次性怪 = true
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				npcClass.获取范围玩家发送增加数据包();
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪  [" + Npcid + "]出错：" + ex);
		}
	}

	public static NpcClass AddNpc(int FLD_PID, float Rxjh_cs_X, float Rxjh_cs_Y, int Rxjh_Map, int FLD_FACE1, int FLD_FACE2, bool 一次性怪, int 刷新时间)
	{
		try
		{
			if (MonSter.TryGetValue(FLD_PID, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = Rxjh_cs_X,
					Rxjh_Y = Rxjh_cs_Y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = Rxjh_cs_X,
					Rxjh_cs_Y = Rxjh_cs_Y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = Rxjh_Map,
					IsNpc = 0,
					FLD_FACE1 = FLD_FACE1,
					FLD_FACE2 = FLD_FACE2,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = value.FLD_BOSS,
					一次性怪 = 一次性怪,
					FLD_NEWTIME = 刷新时间
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				npcClass.获取范围玩家发送增加数据包();
				return npcClass;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪  [" + FLD_PID + "]出错：" + ex.ToString());
		}
		return null;
	}

	public static void SerNpc(int Npcid, float x, float y, int mapp)
	{
		try
		{
			string text = "在MONSTER表,修改血,攻,防,等级,经验,主动BOSS";
			if (MonSter.TryGetValue(Npcid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapp,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = value.FLD_BOSS,
					FLD_NEWTIME = 10,
					FLD_SFYJ = 0
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				npcClass.获取范围玩家发送增加数据包();
				DBA.ExeSqlCommand($"INSERT INTO TBL_XWWL_NPC(FLD_PID,FLD_X,FLD_Y,FLD_Z,FLD_FACE0,FLD_FACE,FLD_MID,FLD_NAME,FLD_HP,FLD_AT,FLD_DF,FLD_NPC,FLD_NEWTIME,FLD_LEVEL,FLD_EXP,FLD_AUTO,FLD_BOSS,FLD_SFYJ,FLD_备注)VALUES ({npcClass.FLD_PID},{x},{y},{15},{0},{0},{npcClass.Rxjh_Map},'{npcClass.Name}',{npcClass.Max_Rxjh_HP},{npcClass.FLD_AT},{npcClass.FLD_DF},{0},{10},{npcClass.Level},{npcClass.Rxjh_Exp},{npcClass.FLD_AUTO},{npcClass.FLD_BOSS},{0},'{text}')", "PublicDb");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪[" + Npcid + "]出错：" + ex);
		}
	}

	public static int SerNpc刷怪(int Npcid, float x, float y, int mapp)
	{
		try
		{
			string text = "在MONSTER表,修改血,攻,防,等级,经验,主动BOSS";
			if (MonSter.TryGetValue(Npcid, out var value))
			{
				NpcClass npcClass = new NpcClass
				{
					FLD_PID = value.FLD_PID,
					Name = value.Name,
					Level = value.Level,
					Rxjh_Exp = value.Rxjh_Exp,
					Rxjh_X = x,
					Rxjh_Y = y,
					Rxjh_Z = 15f,
					Rxjh_cs_X = x,
					Rxjh_cs_Y = y,
					Rxjh_cs_Z = 15f,
					Rxjh_Map = mapp,
					IsNpc = 0,
					FLD_FACE1 = 0f,
					FLD_FACE2 = 0f,
					Max_Rxjh_HP = value.Rxjh_HP,
					Rxjh_HP = value.Rxjh_HP,
					FLD_AT = value.FLD_AT,
					FLD_DF = value.FLD_DF,
					FLD_AUTO = value.FLD_AUTO,
					FLD_BOSS = value.FLD_BOSS,
					FLD_NEWTIME = 10,
					FLD_SFYJ = 0
				};
				if (Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new MapClass
					{
						MapID = npcClass.Rxjh_Map
					};
					mapClass.add(npcClass);
					Map.TryAdd(mapClass.MapID, mapClass);
				}
				npcClass.获取范围玩家发送增加数据包();
				DBA.ExeSqlCommand($"INSERT INTO TBL_XWWL_NPC(FLD_PID,FLD_X,FLD_Y,FLD_Z,FLD_FACE0,FLD_FACE,FLD_MID,FLD_NAME,FLD_HP,FLD_AT,FLD_DF,FLD_NPC,FLD_NEWTIME,FLD_LEVEL,FLD_EXP,FLD_AUTO,FLD_BOSS,FLD_SFYJ,FLD_备注)VALUES ({npcClass.FLD_PID},{x},{y},{15},{0},{0},{npcClass.Rxjh_Map},'{npcClass.Name}',{npcClass.Max_Rxjh_HP},{npcClass.FLD_AT},{npcClass.FLD_DF},{0},{10},{npcClass.Level},{npcClass.Rxjh_Exp},{npcClass.FLD_AUTO},{npcClass.FLD_BOSS},{0},'{text}')", "PublicDb");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "增加怪[" + Npcid + "]出错：" + ex);
		}
		return -1;
	}

	public static void ProcessSqlQueue()
	{
		try
		{
			if (jlMsg == 1)
			{
				Form1.WriteLine(0, "ProcessSqlQueue111()");
			}
			while (SqlPool.Count > 0)
			{
				if (jlMsg == 1)
				{
					Form1.WriteLine(0, "ProcessSqlQueue");
				}
				DbPoolClass dbPoolClass = (DbPoolClass)SqlPool.Dequeue();
				try
				{
					// 2025-0618 EVIAS 使用安全的数据库连接管理，确保连接正确释放
					if (DbPoolClass.DbPoolClassRunSafe(dbPoolClass.Conn, dbPoolClass.Sql, dbPoolClass.Prams, dbPoolClass.Type, dbPoolClass.名字) == -1)
					{
						Players players = 检查玩家name(dbPoolClass.名字);
						if (players != null)
						{
							RxjhClass.卡号记录(players.Userid, players.UserName, "卡数据队列5", players.Player_Job, 分区编号);
							players.Client.Dispose();
						}
						Form1.WriteLine(1, "ProcessSqlQueue()2 数据库连接出错 " + SqlPool.Count);
					}
				}
				catch (Exception ex)
				{
					// 2025-0618 EVIAS 改进异常处理，记录更详细的错误信息
					RxjhClass.HandleDatabaseException(ex, "处理SQL队列项", $"SQL: {dbPoolClass.Sql}, 玩家: {dbPoolClass.名字}");
					Form1.WriteLine(1, FastString.Concat("ProcessSqlQueue()1出错 ", ex)); // 2025-0618 EVIAS 优化字符串拼接
				}
			}
		}
		catch (Exception ex2)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleDatabaseException(ex2, "处理SQL队列", "");
			Form1.WriteLine(1, FastString.Concat("ProcessSqlQueue()出错 ", ex2)); // 2025-0618 EVIAS 优化字符串拼接
		}
	}

	public static bool 是否讨伐副本危险区域(Players player)
	{
		if (player.人物坐标_地图 == 43001 && player.人物坐标_X < 270f && player.人物坐标_X > -270f && player.人物坐标_Y < 110f && player.人物坐标_Y > -450f)
		{
			return true;
		}
		return false;
	}

	public static int GetRandomSeed()
	{
		byte[] array = new byte[4];
		new RNGCryptoServiceProvider().GetBytes(array);
		return BitConverter.ToInt32(array, 0);
	}

	public static void 异常状态效果(Players player, int 人物全服ID, int 异常ID, int 开关, int 异常数量, int 时间)
	{
		if (jlMsg == 1)
		{
			Form1.WriteLine(17, "异常状态类-状态效果");
		}
		byte[] array = Converter.hexStringToByte("AA553E00D3044015380000000000D30400001C000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
		if (人物全服ID < 10000)
		{
			Players players = 检查玩家世界ID(人物全服ID);
			if (players != null)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 14, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
				if (players.Client != null)
				{
					players.Client.Send多包(array, array.Length);
				}
				players.发送当前范围广播数据多包(array, array.Length);
			}
		}
		else
		{
			NpcClass npc = MapClass.GetNpc(player.人物坐标_地图, 人物全服ID);
			if (npc != null)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(npc.FLD_INDEX), 0, array, 14, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(npc.FLD_INDEX), 0, array, 4, 2);
				npc.广播数据(array, array.Length);
			}
		}
	}

    public static int GetValue(int pid, int type, ref int cnt)
    {
        switch (pid)
        {
            case 800000028:
                int num25 = RNG.Next(2, 6);
                if (cnt >= 10 + RNG.Next(-1, 3))
                {
                    num25 = 1;
                    cnt = 0;
                }
                cnt++;
                string text98 = "200";
                string text99 = "000";
                return int.Parse(text98 + num25 + text99);
            default:
                return GetValue(pid, type, cnt);
        }
    }

    public static int GetValue(int pid, int type, int cnt = 0)
    {
        int result = 0;
        try
        {
            switch (pid)
            {
                case 800000062:
                    {
                        int num18 = RNG.Next(0, 110);
                        int num19 = 1;
                        string text7 = ((type != 2) ? ((num18 >= 0 && num18 < 40) ? "2" : ((num18 >= 40 && num18 < 50) ? "3" : ((num18 >= 50 && num18 < 60) ? "4" : ((num18 < 60 || num18 >= 70) ? "11" : "6")))) : ((num18 < 0 || num18 >= 40) ? "11" : "2"));
                        switch (type)
                        {
                            case 1:
                                if (text7 == null)
                                {
                                    break;
                                }
                                switch (text7)
                                {
                                    case "11":
                                        num19 = RNG.Next(23, 45);
                                        if (num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "6":
                                        num19 = RNG.Next(1, 10);
                                        break;
                                    case "4":
                                        num19 = RNG.Next(17, 36);
                                        if (num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "3":
                                        num19 = RNG.Next(30, 60);
                                        if (num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "2":
                                        num19 = RNG.Next(5, 9);
                                        break;
                                }
                                break;
                            case 2:
                                switch (text7)
                                {
                                    case "11":
                                        num19 = RNG.Next(80, 100);
                                        if (num19 >= 80 && num19 < 83)
                                        {
                                            num19 = 80;
                                        }
                                        else if (num19 >= 83 && num19 < 86)
                                        {
                                            num19 = 80;
                                        }
                                        else if (num19 >= 86 && num19 < 89)
                                        {
                                            num19 = 85;
                                        }
                                        else if (num19 >= 89 && num19 < 92)
                                        {
                                            num19 = 85;
                                        }
                                        else if (num19 >= 92 && num19 < 95)
                                        {
                                            num19 = 90;
                                        }
                                        else if (num19 >= 95 && num19 < 98)
                                        {
                                            num19 = 95;
                                        }
                                        else if (num19 >= 98 && num19 <= 100)
                                        {
                                            num19 = 100;
                                        }
                                        if (num19 != 85 && num19 != 95 && num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "2":
                                        num19 = RNG.Next(11, 15);
                                        break;
                                }
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                if (text7 == null)
                                {
                                    break;
                                }
                                switch (text7)
                                {
                                    case "11":
                                        num19 = RNG.Next(76, 100);
                                        if (num19 > 80 && num19 <= 83)
                                        {
                                            num19 = 80;
                                        }
                                        else if (num19 > 83 && num19 <= 86)
                                        {
                                            num19 = 80;
                                        }
                                        else if (num19 > 86 && num19 <= 89)
                                        {
                                            num19 = 85;
                                        }
                                        else if (num19 > 89 && num19 <= 92)
                                        {
                                            num19 = 90;
                                        }
                                        else if (num19 > 92 && num19 <= 95)
                                        {
                                            num19 = 90;
                                        }
                                        else if (num19 > 95 && num19 <= 98)
                                        {
                                            num19 = 95;
                                        }
                                        else if (num19 > 98 && num19 <= 100)
                                        {
                                            num19 = 95;
                                        }
                                        if (num19 != 85 && num19 != 95 && num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "6":
                                        num19 = RNG.Next(3, 10);
                                        break;
                                    case "4":
                                        num19 = RNG.Next(35, 50);
                                        if (num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "3":
                                        num19 = RNG.Next(50, 80);
                                        if (num19 % 2 != 0)
                                        {
                                            num19--;
                                        }
                                        break;
                                    case "2":
                                        num19 = RNG.Next(10, 14);
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text7) * 100000 + num19;
                        break;
                    }
                case 800000061:
                    {
                        int num7 = RNG.Next(0, 120);
                        int num8 = 1;
                        string text2 = ((type != 2) ? ((num7 >= 0 && num7 < 30) ? "1" : ((num7 >= 30 && num7 < 40) ? "3" : ((num7 >= 40 && num7 < 45) ? "4" : ((num7 >= 45 && num7 < 50) ? "5" : ((num7 >= 50 && num7 < 110) ? "7" : ((num7 < 110 || num7 >= 115) ? "10" : "8")))))) : ((num7 >= 0 && num7 < 55) ? "1" : ((num7 < 55 || num7 >= 110) ? "8" : "7")));
                        switch (type)
                        {
                            case 1:
                                switch (text2)
                                {
                                    case "4":
                                        num8 = RNG.Next(20, 25);
                                        if (num8 % 2 != 0)
                                        {
                                            num8--;
                                        }
                                        break;
                                    case "5":
                                        num8 = RNG.Next(1, 10);
                                        break;
                                    case "10":
                                        num8 = RNG.Next(10, 25);
                                        break;
                                    case "1":
                                        num8 = RNG.Next(10, 20);
                                        break;
                                    case "7":
                                        num8 = RNG.Next(15, 25);
                                        break;
                                    case "8":
                                        num8 = 1;
                                        break;
                                    case "3":
                                        num8 = RNG.Next(50, 70);
                                        if (num8 % 2 != 0)
                                        {
                                            num8--;
                                        }
                                        break;
                                }
                                break;
                            case 2:
                                if (text2 != null)
                                {
                                    switch (text2)
                                    {
                                        case "8":
                                            num8 = 2;
                                            break;
                                        case "7":
                                            num8 = RNG.Next(30, 35);
                                            break;
                                        case "1":
                                            num8 = RNG.Next(20, 25);
                                            break;
                                    }
                                }
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                switch (text2)
                                {
                                    case "4":
                                        num8 = RNG.Next(25, 50);
                                        if (num8 % 2 != 0)
                                        {
                                            num8--;
                                        }
                                        break;
                                    case "5":
                                        num8 = RNG.Next(5, 10);
                                        break;
                                    case "10":
                                        num8 = RNG.Next(15, 25);
                                        break;
                                    case "1":
                                        num8 = RNG.Next(15, 24);
                                        break;
                                    case "7":
                                        num8 = RNG.Next(15, 34);
                                        break;
                                    case "8":
                                        num8 = RNG.Next(1, 2);
                                        break;
                                    case "3":
                                        num8 = RNG.Next(50, 80);
                                        if (num8 % 2 != 0)
                                        {
                                            num8--;
                                        }
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text2) * 100000 + num8;
                        break;
                    }
                case 800000001:
                case 800000011:
                    {
                        int num13 = RNG.Next(0, 120);
                        int num14 = 1;
                        string text5 = ((num13 >= 0 && num13 < 50) ? "1" : ((num13 >= 50 && num13 < 55) ? "3" : ((num13 >= 55 && num13 < 60) ? "4" : ((num13 >= 60 && num13 < 65) ? "5" : ((num13 >= 65 && num13 < 100) ? "7" : ((num13 < 100 || num13 >= 110) ? "10" : "8"))))));
                        switch (type)
                        {
                            case 2:
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                switch (text5)
                                {
                                    case "4":
                                        num14 = RNG.Next(25, 36);
                                        if (num14 % 2 != 0)
                                        {
                                            num14--;
                                        }
                                        break;
                                    case "5":
                                        num14 = RNG.Next(5, 10);
                                        break;
                                    case "10":
                                        num14 = RNG.Next(10, 25);
                                        break;
                                    case "1":
                                        num14 = RNG.Next(8, 15);
                                        break;
                                    case "7":
                                        num14 = RNG.Next(20, 25);
                                        break;
                                    case "8":
                                        num14 = RNG.Next(1, 2);
                                        break;
                                    case "3":
                                        num14 = RNG.Next(20, 40);
                                        if (num14 % 2 != 0)
                                        {
                                            num14--;
                                        }
                                        break;
                                }
                                break;
                            case 1:
                                switch (text5)
                                {
                                    case "4":
                                        num14 = RNG.Next(15, 25);
                                        if (num14 % 2 != 0)
                                        {
                                            num14--;
                                        }
                                        break;
                                    case "5":
                                        num14 = RNG.Next(1, 10);
                                        break;
                                    case "10":
                                        num14 = RNG.Next(20, 25);
                                        break;
                                    case "1":
                                        num14 = RNG.Next(5, 10);
                                        break;
                                    case "7":
                                        num14 = RNG.Next(15, 22);
                                        break;
                                    case "8":
                                        num14 = 1;
                                        break;
                                    case "3":
                                        num14 = RNG.Next(25, 30);
                                        if (num14 % 2 != 0)
                                        {
                                            num14--;
                                        }
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text5) * 100000 + num14;
                        break;
                    }
                case 800000002:
                case 800000012:
                    {
                        int num11 = RNG.Next(0, 100);
                        int num12 = 1;
                        string text4 = ((num11 >= 0 && num11 < 75) ? "2" : ((num11 >= 75 && num11 < 80) ? "3" : ((num11 >= 80 && num11 < 85) ? "4" : ((num11 < 85 || num11 >= 90) ? "11" : "6"))));
                        switch (type)
                        {
                            case 2:
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                if (text4 == null)
                                {
                                    break;
                                }
                                switch (text4)
                                {
                                    case "11":
                                        num12 = RNG.Next(35, 45);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "6":
                                        num12 = RNG.Next(5, 10);
                                        break;
                                    case "4":
                                        num12 = RNG.Next(25, 50);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "3":
                                        num12 = RNG.Next(25, 50);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "2":
                                        num12 = RNG.Next(6, 10);
                                        break;
                                }
                                break;
                            case 1:
                                if (text4 == null)
                                {
                                    break;
                                }
                                switch (text4)
                                {
                                    case "11":
                                        num12 = RNG.Next(2, 35);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "6":
                                        num12 = RNG.Next(1, 10);
                                        break;
                                    case "4":
                                        num12 = RNG.Next(20, 36);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "3":
                                        num12 = RNG.Next(20, 40);
                                        if (num12 % 2 != 0)
                                        {
                                            num12--;
                                        }
                                        break;
                                    case "2":
                                        num12 = RNG.Next(4, 8);
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text4) * 100000 + num12;
                        break;
                    }
                case 800000013:
                    {
                        int num15 = 0;
                        string text6 = "0000";
                        int num16 = RNG.Next(0, 125);
                        int num17 = ((num16 >= 0 && num16 <= 40) ? 8 : ((num16 > 40 && num16 <= 70) ? 9 : ((num16 > 70 && num16 <= 90) ? 12 : ((num16 <= 90 || num16 > 110) ? 15 : 13))));
                        switch (num17)
                        {
                            case 8:
                                num15 = 1;
                                text6 = "0000";
                                break;
                            case 9:
                                num15 = 1;
                                break;
                            case 12:
                                num15 = 10;
                                break;
                            case 13:
                                num15 = 5;
                                break;
                            case 15:
                                num15 = 1;
                                break;
                        }
                        result = int.Parse((num17 != 12) ? (num17 + text6 + num15) : (num17 + "000" + num15));
                        break;
                    }
                case 800000023:
                    {
                        int num9 = RNG.Next(0, 120);
                        int num10 = 1;
                        string text3 = ((type != 2) ? ((num9 >= 0 && num9 < 20) ? "1" : ((num9 >= 20 && num9 < 30) ? "3" : ((num9 >= 30 && num9 < 35) ? "4" : ((num9 >= 35 && num9 < 40) ? "5" : ((num9 >= 40 && num9 < 110) ? "7" : ((num9 < 110 || num9 >= 115) ? "10" : "8")))))) : ((num9 < 0 || num9 >= 30) ? "7" : "1"));
                        switch (type)
                        {
                            case 1:
                                switch (text3)
                                {
                                    case "4":
                                        num10 = RNG.Next(15, 25);
                                        if (num10 % 2 != 0)
                                        {
                                            num10--;
                                        }
                                        break;
                                    case "5":
                                        num10 = RNG.Next(1, 10);
                                        break;
                                    case "10":
                                        num10 = RNG.Next(10, 25);
                                        break;
                                    case "1":
                                        num10 = RNG.Next(8, 15);
                                        break;
                                    case "7":
                                        num10 = RNG.Next(15, 24);
                                        break;
                                    case "8":
                                        num10 = 1;
                                        break;
                                    case "3":
                                        num10 = RNG.Next(45, 70);
                                        if (num10 % 2 != 0)
                                        {
                                            num10--;
                                        }
                                        break;
                                }
                                break;
                            case 2:
                                switch (text3)
                                {
                                    case "7":
                                        num10 = RNG.Next(27, 31);
                                        break;
                                    case "1":
                                        num10 = RNG.Next(21, 24);
                                        break;
                                }
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                switch (text3)
                                {
                                    case "4":
                                        num10 = RNG.Next(15, 50);
                                        if (num10 % 2 != 0)
                                        {
                                            num10--;
                                        }
                                        break;
                                    case "5":
                                        num10 = RNG.Next(5, 10);
                                        break;
                                    case "10":
                                        num10 = RNG.Next(10, 25);
                                        break;
                                    case "1":
                                        num10 = RNG.Next(13, 20);
                                        break;
                                    case "7":
                                        num10 = RNG.Next(24, 31);
                                        break;
                                    case "8":
                                        num10 = RNG.Next(1, 2);
                                        break;
                                    case "3":
                                        num10 = RNG.Next(25, 80);
                                        if (num10 % 2 != 0)
                                        {
                                            num10--;
                                        }
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text3) * 100000 + num10;
                        break;
                    }
                case 800000024:
                    {
                        int num5 = RNG.Next(0, 100);
                        int num6 = 1;
                        string text = ((type != 2) ? ((num5 >= 0 && num5 < 45) ? "2" : ((num5 >= 45 && num5 < 50) ? "3" : ((num5 >= 50 && num5 < 55) ? "4" : ((num5 < 55 || num5 >= 60) ? "11" : "6")))) : ((num5 < 0 || num5 >= 50) ? "11" : "2"));
                        switch (type)
                        {
                            case 1:
                                if (text == null)
                                {
                                    break;
                                }
                                switch (text)
                                {
                                    case "11":
                                        num6 = RNG.Next(45, 70);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "6":
                                        num6 = RNG.Next(1, 10);
                                        break;
                                    case "4":
                                        num6 = RNG.Next(20, 36);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "3":
                                        num6 = RNG.Next(20, 36);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "2":
                                        num6 = RNG.Next(5, 9);
                                        break;
                                }
                                break;
                            case 2:
                                switch (text)
                                {
                                    case "11":
                                        num6 = RNG.Next(74, 80);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "2":
                                        num6 = RNG.Next(8, 11);
                                        break;
                                }
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                if (text == null)
                                {
                                    break;
                                }
                                switch (text)
                                {
                                    case "11":
                                        num6 = RNG.Next(64, 80);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "6":
                                        num6 = RNG.Next(3, 10);
                                        break;
                                    case "4":
                                        num6 = RNG.Next(15, 30);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "3":
                                        num6 = RNG.Next(30, 50);
                                        if (num6 % 2 != 0)
                                        {
                                            num6--;
                                        }
                                        break;
                                    case "2":
                                        num6 = RNG.Next(8, 11);
                                        break;
                                }
                                break;
                        }
                        result = int.Parse(text) * 100000 + num6;
                        break;
                    }
                case 800000025:
                    result = 1000000 + RNG.Next(15, 20);
                    break;
                case 800000026:
                    result = 700000 + RNG.Next(15, 25);
                    break;
                case 800000028:
                    //result = int.Parse("200" + RNG.Next(1, 6) + "000");
                    int num25 = RNG.Next(2, 6);
                    if (cnt >= 10 + RNG.Next(-2, 3))
                    {
                        num25 = 1;
                        cnt = 0;
                    }
                    string text98 = "200";
                    string text99 = "000";
                    result = int.Parse(text98 + num25 + text99);
                    break;
                case 800000030:
                case 800000034:
                    switch (type)
                    {
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            {
                                int num4 = RNG.Next(0, 100);
                                result = ((num4 >= 0 && num4 < 20) ? RNG.Next(100005, 100015) : ((num4 >= 20 && num4 < 40) ? RNG.Next(700008, 700025) : ((num4 >= 40 && num4 < 60) ? RNG.Next(1000008, 1000025) : ((num4 < 60 || num4 >= 80) ? 1500001 : RNG.Next(200001, 200020)))));
                                break;
                            }
                        case 1:
                            {
                                int num3 = RNG.Next(0, 100);
                                result = ((num3 >= 0 && num3 < 20) ? RNG.Next(100005, 100010) : ((num3 >= 20 && num3 < 40) ? RNG.Next(700008, 700020) : ((num3 >= 40 && num3 < 60) ? RNG.Next(1000008, 1000020) : ((num3 < 60 || num3 >= 80) ? 1500001 : RNG.Next(200001, 200010)))));
                                break;
                            }
                    }
                    break;
                case 800000031:
                case 800000035:
                    switch (type)
                    {
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            {
                                int num2 = RNG.Next(0, 100);
                                result = ((num2 >= 0 && num2 < 20) ? RNG.Next(100010, 100030) : ((num2 >= 20 && num2 < 40) ? RNG.Next(700015, 700035) : ((num2 >= 40 && num2 < 60) ? RNG.Next(500015, 500025) : ((num2 < 60 || num2 >= 80) ? RNG.Next(1500002, 1500005) : RNG.Next(200005, 200020)))));
                                break;
                            }
                        case 1:
                            {
                                int num = RNG.Next(0, 100);
                                result = ((num >= 0 && num < 20) ? RNG.Next(100005, 100020) : ((num >= 20 && num < 40) ? RNG.Next(700008, 700020) : ((num >= 40 && num < 60) ? RNG.Next(500008, 500020) : ((num < 60 || num >= 80) ? RNG.Next(1500001, 1500003) : RNG.Next(200001, 200010)))));
                                break;
                            }
                    }
                    break;
                case 800000032:
                case 800000036:
                    switch (type)
                    {
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            result = RNG.Next(100005, 100030);
                            break;
                        case 1:
                            result = RNG.Next(100005, 100020);
                            break;
                    }
                    break;
                case 800000033:
                case 800000037:
                    switch (type)
                    {
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            result = RNG.Next(100010, 100030);
                            break;
                        case 1:
                            result = RNG.Next(100005, 100020);
                            break;
                    }
                    break;
                case 800000080: //24.0  EVIAS 最高级玛瑙石
                case 800000082:
                    switch (type)
                    {
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            result = RNG.Next(100020, 100050);
                            break;
                        case 1:
                            result = RNG.Next(100010, 100030);
                            break;
                    }
                    break;
            }
        }
        catch
        {
            return 1;
        }
        return result;
    }
}

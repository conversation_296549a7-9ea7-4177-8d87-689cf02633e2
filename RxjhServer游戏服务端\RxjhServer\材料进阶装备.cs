﻿namespace RxjhServer
{
    // 新增于1209
    public class 材料进阶装备
    {
        private int _需要装备PID;
        private string _需要装备名称;
        private int _需要装备强化等级;
        private int _需要装备属性1;
        private int _需要装备属性2;
        private int _需要装备属性3;
        private int _需要装备属性4;

        private int _进阶装备PID;
        private string _进阶装备名称;
        private int _进阶装备属性1;
        private int _进阶装备属性2;
        private int _进阶装备属性3;
        private int _进阶装备属性4;

        private string _需要材料1名称;
        private int _需要材料1数量;

        private int _需要材料2PID;
        private string _需要材料2名称;
        private int _需要材料2数量;

        private int _需要元宝数量;

        private int _是否继承属性;
        private int _是否提示;
        private int _使用天数;

        public int 需要装备PID
        {
            get { return _需要装备PID; }
            set { _需要装备PID = value; }
        }

        public string 需要装备名称
        {
            get { return _需要装备名称; }
            set { _需要装备名称 = value; }
        }

        public int 需要装备强化等级
        {
            get { return _需要装备强化等级; }
            set { _需要装备强化等级 = value; }
        }

        public int 需要装备属性1
        {
            get { return _需要装备属性1; }
            set { _需要装备属性1 = value; }
        }

        public int 需要装备属性2
        {
            get { return _需要装备属性2; }
            set { _需要装备属性2 = value; }
        }

        public int 需要装备属性3
        {
            get { return _需要装备属性3; }
            set { _需要装备属性3 = value; }
        }

        public int 需要装备属性4
        {
            get { return _需要装备属性4; }
            set { _需要装备属性4 = value; }
        }

        public int 进阶装备PID
        {
            get { return _进阶装备PID; }
            set { _进阶装备PID = value; }
        }

        public string 进阶装备名称
        {
            get { return _进阶装备名称; }
            set { _进阶装备名称 = value; }
        }

        public int 进阶装备属性1
        {
            get { return _进阶装备属性1; }
            set { _进阶装备属性1 = value; }
        }

        public int 进阶装备属性2
        {
            get { return _进阶装备属性2; }
            set { _进阶装备属性2 = value; }
        }

        public int 进阶装备属性3
        {
            get { return _进阶装备属性3; }
            set { _进阶装备属性3 = value; }
        }

        public int 进阶装备属性4
        {
            get { return _进阶装备属性4; }
            set { _进阶装备属性4 = value; }
        }

        public string 需要材料1名称
        {
            get { return _需要材料1名称; }
            set { _需要材料1名称 = value; }
        }

        public int 需要材料1数量
        {
            get { return _需要材料1数量; }
            set { _需要材料1数量 = value; }
        }

        public int 需要材料2PID
        {
            get { return _需要材料2PID; }
            set { _需要材料2PID = value; }
        }

        public string 需要材料2名称
        {
            get { return _需要材料2名称; }
            set { _需要材料2名称 = value; }
        }

        public int 需要材料2数量
        {
            get { return _需要材料2数量; }
            set { _需要材料2数量 = value; }
        }

        public int 需要元宝数量
        {
            get { return _需要元宝数量; }
            set { _需要元宝数量 = value; }
        }

        public int 是否继承属性
        {
            get { return _是否继承属性; }
            set { _是否继承属性 = value; }
        }

        public int 是否提示
        {
            get { return _是否提示; }
            set { _是否提示 = value; }
        }

        public int 使用天数
        {
            get { return _使用天数; }
            set { _使用天数 = value; }
        }
    }
}
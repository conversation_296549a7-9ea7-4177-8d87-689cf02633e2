using System;
using System.IO;

namespace RxjhServer;

public class logo
{
	public static void kickid(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\踢号日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void WGTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\外挂检测_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\错误日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void pzTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\帮战日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void cfzTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\非法物品日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void zhtfTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\wep抓包日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void zhjsTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\道具加锁解锁_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileCQTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\CQLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileLoninTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\登陆日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void sjhdLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\自动活动_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void bbcjLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\百宝抽奖_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void ljczLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\累计充值_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void jshdLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\活动结束_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void deleteLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\删除日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileDropItmeTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\DropItme_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileItmeTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\ItmeLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileBugTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\BugLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FilePakTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\封包_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void SeveTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\存档_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void 拍卖记录(string ErrTxt) // 2025-05-19: 寄售改为拍卖
	{
		try
		{
			// 2025-06-14: 修复目录路径一致性问题
			if (!Directory.Exists("拍卖物品记录"))
			{
				Directory.CreateDirectory("拍卖物品记录");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("拍卖物品记录\\拍卖日志_" + DateTime.Now.ToString("yyyy_MMdd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	// 2025-05-19: 保留原方法名以兼容旧代码
	public static void 寄售记录(string ErrTxt)
	{
		拍卖记录(ErrTxt);
	}
}

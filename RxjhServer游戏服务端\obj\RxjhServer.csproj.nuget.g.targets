﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.resources.extensions\9.0.6\buildTransitive\net462\System.Resources.Extensions.targets" Condition="Exists('$(NuGetPackageRoot)system.resources.extensions\9.0.6\buildTransitive\net462\System.Resources.Extensions.targets')" />
    <Import Project="$(NuGetPackageRoot)keralua\1.4.4\build\net46\KeraLua.targets" Condition="Exists('$(NuGetPackageRoot)keralua\1.4.4\build\net46\KeraLua.targets')" />
    <Import Project="$(NuGetPackageRoot)hpsocket.net\6.0.3.1\build\HPSocket.Net.targets" Condition="Exists('$(NuGetPackageRoot)hpsocket.net\6.0.3.1\build\HPSocket.Net.targets')" />
  </ImportGroup>
</Project>
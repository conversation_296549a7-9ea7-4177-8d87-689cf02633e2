﻿namespace RxjhServer
{
    // 新增于0929
    public class 装备分解材料
    {
        private int _分解物品PID;
        private int _分解材料1PID;
        private int _分解材料1数量;
        private int _分解材料2PID;
        private int _分解材料2数量;
        private int _分解模式;

        private string _分解物品名称;
        private string _分解材料1名称;
        private string _分解材料2名称;

        public int 分解物品PID
        {
            get { return _分解物品PID; }
            set { _分解物品PID = value; }
        }

        public string 分解物品名称
        {
            get { return _分解物品名称; }
            set { _分解物品名称 = value; }
        }

        public int 分解材料1PID
        {
            get { return _分解材料1PID; }
            set { _分解材料1PID = value; }
        }

        public string 分解材料1名称
        {
            get { return _分解材料1名称; }
            set { _分解材料1名称 = value; }
        }

        public int 分解材料1数量
        {
            get { return _分解材料1数量; }
            set { _分解材料1数量 = value; }
        }

        public int 分解材料2PID
        {
            get { return _分解材料2PID; }
            set { _分解材料2PID = value; }
        }

        public string 分解材料2名称
        {
            get { return _分解材料2名称; }
            set { _分解材料2名称 = value; }
        }

        public int 分解材料2数量
        {
            get { return _分解材料2数量; }
            set { _分解材料2数量 = value; }
        }

        public int 分解模式
        {
            get { return _分解模式; }
            set { _分解模式 = value; }
        }
    }
}
namespace RxjhVer23;

public interface ICompetitionPlayer
{
	int 人物全服ID { get; }

	string UserName { get; }

	int Player_Level { get; }

	int Player_Job_leve { get; }

	int Player_WuXun { get; }

	int Player_Job { get; }

	int Player_Zx { get; }

	int Player_Sex { get; }

	int 帮派Id { get; }

	string 帮派名字 { get; }

	int 人物坐标_地图 { get; }

	bool ExtremeArenaIsDeath { get; }

	int ExtremeArenaPoints { get; set; }

	void 系统提示(string msg, int msgType, string name);

	void 移动(float x, float y, float z, int map);

	void 设置任务物品(int 物品ID, int 物品数量);

	void ExtremeArenaReviveInPlace();

	void ExtremeArenaCentralRevive();

	void ExtremeArenaSetRewards(int competitionResult, int totalDamage, int killCount, int deathCount, int participantCount, ref int points, ref int stampCount);

	void SendFullPacket(byte[] fullPacket);
}

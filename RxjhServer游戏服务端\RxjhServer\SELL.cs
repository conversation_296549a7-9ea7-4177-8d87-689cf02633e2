using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class SELL : Form
{
	private IContainer components = null;

	private ListView listView2;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader14;

	private ColumnHeader columnHeader15;

	private ColumnHeader columnHeader16;

	private ColumnHeader columnHeader17;

	private Button button43;

	private Panel panel1;

	private Button button1;

	private TextBox textBox6;

	private Label label6;

	private TextBox textBox5;

	private Label label2;

	private TextBox textBox4;

	private Label label5;

	private TextBox textBox3;

	private Label label4;

	private TextBox textBox2;

	private Label label3;

	private TextBox textBox1;

	private Label label1;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 编辑ToolStripMenuItem;

	private ToolStripMenuItem 删除ToolStripMenuItem;

	private ComboBox comboBox11;

	private Label label40;

	private ListBox listBox3;

	private Button button2;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private Label label11;

	private Label label10;

	private Label label9;

	private Label label8;

	private TextBox textBox10;

	private TextBox textBox9;

	private TextBox textBox8;

	private TextBox textBox7;

	private StatusStrip statusStrip1;

	private ToolStripStatusLabel toolStripStatusLabel1;

	private ToolStripStatusLabel toolStripStatusLabel2;

	private Label label7;

	private TextBox textBox11;

	private ColumnHeader columnHeader5;

	private ComboBox comboBox1;

	private Label label20;

	private TextBox textBox13;

	private Label label12;

	private TextBox textBoxPAGE;

	private Label label13;

	private ColumnHeader columnHeader6;

	private ColumnHeader columnHeader7;

	private TextBox textBox14;

	private Label label14;

	private Button button3;

	public string s_id { get; set; }

	public SELL()
	{
		InitializeComponent();
		listView2.ContextMenuStrip = contextMenuStrip1;
		contextMenuStrip1.Closed += contextMenuStrip1_Closed;
		toolStripStatusLabel2.Text = "修改后请重新读取数据库即可。";
		button1.Enabled = false;
	}

	private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
	{
		listView2.ContextMenuStrip = contextMenuStrip1;
	}

	private void button43_Click(object sender, EventArgs e)
	{
		刷新();
	}

	private void panel1_Paint(object sender, PaintEventArgs e)
	{
		ControlPaint.DrawBorder(e.Graphics, panel1.ClientRectangle, Color.Black, 1, ButtonBorderStyle.Solid, Color.Black, 1, ButtonBorderStyle.Solid, Color.Black, 1, ButtonBorderStyle.Solid, Color.Black, 1, ButtonBorderStyle.Solid);
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			if (listView2.Items.Count == 0)
			{
				toolStripStatusLabel1.Text = "请选择数据后在修改。";
			}
			string text = textBox1.Text;
			int num = int.Parse(textBox2.Text);
			int num2 = int.Parse(textBox3.Text);
			int num3 = int.Parse(textBox4.Text);
			int num4 = int.Parse(textBox5.Text);
			int num5 = int.Parse(textBox6.Text);
			int num6 = int.Parse(textBox7.Text);
			int num7 = int.Parse(textBox8.Text);
			int num8 = int.Parse(textBox9.Text);
			int num9 = int.Parse(textBox10.Text);
			int num10 = int.Parse(textBox11.Text);
			int num11 = int.Parse(textBoxPAGE.Text);
			int num12 = int.Parse(textBox14.Text);
			string text2 = "";
			text2 = string.Format("UPDATE TBL_XWWL_SELL  SET FLD_NPCNAME='{1}',FLD_NID={2}, FLD_INDEX={3},FLD_PID={4},FLD_MAGIC0={5},FLD_MAGIC1={6},FLD_MAGIC2={7},FLD_MAGIC3={8},FLD_MAGIC4={9},FLD_武皇币={10},FLD_MONEY={11},FLD_冰魄水玉={12},FLD_累计充值={13} WHERE ID={0}", s_id, text, num, num2, num3, num5, num6, num7, num8, num9, num10, num4, num11, num12);
			DBA.ExeSqlCommand(text2, "PublicDb");
			SetShop();
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox1.Text;
			listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox2.Text;
			listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox3.Text;
			listView2.Items[selectedIndices[0]].SubItems[4].Text = textBox4.Text;
			listView2.Items[selectedIndices[0]].SubItems[5].Text = textBox5.Text;
			listView2.Items[selectedIndices[0]].SubItems[6].Text = textBox6.Text;
			listView2.Items[selectedIndices[0]].SubItems[7].Text = textBox7.Text;
			listView2.Items[selectedIndices[0]].SubItems[8].Text = textBox8.Text;
			listView2.Items[selectedIndices[0]].SubItems[9].Text = textBox9.Text;
			listView2.Items[selectedIndices[0]].SubItems[10].Text = textBox10.Text;
			listView2.Items[selectedIndices[0]].SubItems[11].Text = textBox11.Text;
			listView2.Items[selectedIndices[0]].SubItems[12].Text = textBoxPAGE.Text;
			listView2.Items[selectedIndices[0]].SubItems[13].Text = textBox14.Text;
			MessageBox.Show("修改完成");
		}
		catch (Exception)
		{
		}
	}

	private void listView2_MouseClick(object sender, MouseEventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text2 = (s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text);
			string text3 = text2;
			string text4 = text3;
			string text5 = text4;
			textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
			textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
			textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
			textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
			textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
			textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
			textBox7.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
			textBox8.Text = listView2.Items[selectedIndices[0]].SubItems[8].Text;
			textBox9.Text = listView2.Items[selectedIndices[0]].SubItems[9].Text;
			textBox10.Text = listView2.Items[selectedIndices[0]].SubItems[10].Text;
			textBox11.Text = listView2.Items[selectedIndices[0]].SubItems[11].Text;
			textBoxPAGE.Text = listView2.Items[selectedIndices[0]].SubItems[12].Text;
			textBox14.Text = listView2.Items[selectedIndices[0]].SubItems[12].Text;
			if (!button1.Enabled)
			{
				button1.Enabled = true;
			}
			textBox13.Text = ItmeClass.得到物品名称(int.Parse(textBox4.Text));
		}
	}

	private void 编辑ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text2 = (s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text);
			string text3 = text2;
			string text4 = text3;
			string text5 = text4;
			textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
			textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
			textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
			textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
			textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
			textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
			textBox7.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
			textBox8.Text = listView2.Items[selectedIndices[0]].SubItems[8].Text;
			textBox9.Text = listView2.Items[selectedIndices[0]].SubItems[9].Text;
			textBox10.Text = listView2.Items[selectedIndices[0]].SubItems[10].Text;
			textBox11.Text = listView2.Items[selectedIndices[0]].SubItems[11].Text;
			textBoxPAGE.Text = listView2.Items[selectedIndices[0]].SubItems[12].Text;
			textBox14.Text = listView2.Items[selectedIndices[0]].SubItems[12].Text;
		}
	}

	private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text = listView2.Items[selectedIndices[0]].Text;
			string sqlCommand = "delete from tbl_xwwl_sell where id =" + text;
			DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			刷新();
		}
	}

	public void 刷新()
	{
		try
		{
			listView2.Items.Clear();
			string sqlCommand = "select * from tbl_xwwl_sell";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				ListViewItem listViewItem = new ListViewItem();
				listViewItem.SubItems.Clear();
				listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["id"].ToString();
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NPCNAME"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NID"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_INDEX"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_PID"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MONEY"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC0"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC1"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC2"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC3"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC4"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_武皇币"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_冰魄水玉"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_累计充值"].ToString());
				listView2.Items.Add(listViewItem);
			}
		}
		catch (Exception)
		{
			throw;
		}
	}

	private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = ((ItemDef.MyItem)comboBox11.SelectedItem).Value.ToString();
		listBox3.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			int key = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
			string value = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
			KeyValuePair<int, string> keyValuePair = new KeyValuePair<int, string>(key, value);
			listBox3.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
	}

	private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
	{
		KeyValuePair<int, string> keyValuePair = (KeyValuePair<int, string>)listBox3.SelectedItem;
		textBox4.Text = keyValuePair.Key.ToString();
		textBox13.Text = keyValuePair.Value;
	}

	public void SetShop()
	{
		try
		{
			string sqlCommand = "SELECT * FROM TBL_XWWL_SELL ORDER BY FLD_INDEX";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				Form1.WriteLine(0, "加载物品商店----没有物品数据");
			}
			else
			{
				World.Shop.Clear();
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					ShopClass shopClass = new ShopClass();
					shopClass.FLD_NID = int.Parse(dBToDataTable.Rows[i]["FLD_NID"].ToString());
					shopClass.FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
					shopClass.FLD_PID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
					shopClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"];
					shopClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
					shopClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
					shopClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
					shopClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
					shopClass.FLD_MONEY = long.Parse(dBToDataTable.Rows[i]["FLD_MONEY"].ToString());
					shopClass.武皇币 = (int)dBToDataTable.Rows[i]["FLD_武皇币"];
					shopClass.冰魄水玉 = (int)dBToDataTable.Rows[i]["FLD_冰魄水玉"];
					shopClass.FLD_累计充值 = (int)dBToDataTable.Rows[i]["FLD_累计充值"];
					World.Shop.Add(shopClass);
				}
			}
			dBToDataTable.Dispose();
		}
		catch (Exception)
		{
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
		string text = textBoxPAGE.Text;
		string sqlCommand = $"INSERT INTO tbl_xwwl_sell  (FLD_NPCNAME,FLD_NID, FLD_INDEX,FLD_PID,FLD_MONEY,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_武皇币,FLD_冰魄水玉,FLD_累计充值)VALUES('{textBox1.Text}',{int.Parse(textBox2.Text)},{int.Parse(textBox3.Text)},{int.Parse(textBox4.Text)},{int.Parse(textBox5.Text)},{int.Parse(textBox6.Text)},{int.Parse(textBox7.Text)},{int.Parse(textBox8.Text)},{int.Parse(textBox9.Text)},{int.Parse(textBox10.Text)},{int.Parse(textBox11.Text)},{text},{int.Parse(textBox14.Text)})";
		DBA.ExeSqlCommand(sqlCommand, "PublicDb");
		SetShop();
		加载指定NPC商店物品(((KeyValuePair<int, string>)comboBox1.SelectedItem).Key);
		MessageBox.Show("增加完成");
	}

	private void button3_Click(object sender, EventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text = listView2.Items[selectedIndices[0]].Text;
			string sqlCommand = "delete from tbl_xwwl_sell where id =" + text;
			DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			SetShop();
			加载指定NPC商店物品(((KeyValuePair<int, string>)comboBox1.SelectedItem).Key);
			MessageBox.Show("删除完成");
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SELL));
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader15 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader16 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader17 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.button43 = new System.Windows.Forms.Button();
            this.panel1 = new System.Windows.Forms.Panel();
            this.textBox14 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.textBoxPAGE = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.textBox10 = new System.Windows.Forms.TextBox();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.编辑ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.删除ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.comboBox11 = new System.Windows.Forms.ComboBox();
            this.label40 = new System.Windows.Forms.Label();
            this.listBox3 = new System.Windows.Forms.ListBox();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel2 = new System.Windows.Forms.ToolStripStatusLabel();
            this.button3 = new System.Windows.Forms.Button();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label20 = new System.Windows.Forms.Label();
            this.panel1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14,
            this.columnHeader15,
            this.columnHeader16,
            this.columnHeader17,
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader7});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(14, 43);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(911, 445);
            this.listView2.TabIndex = 1;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listView2_MouseClick);
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "id";
            this.columnHeader11.Width = 40;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "NPC名字";
            this.columnHeader12.Width = 84;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "NPCID";
            this.columnHeader13.Width = 68;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "商店位置";
            this.columnHeader14.Width = 76;
            // 
            // columnHeader15
            // 
            this.columnHeader15.Text = "物品ID";
            this.columnHeader15.Width = 73;
            // 
            // columnHeader16
            // 
            this.columnHeader16.Text = "物品价格";
            this.columnHeader16.Width = 73;
            // 
            // columnHeader17
            // 
            this.columnHeader17.Text = "属性1";
            this.columnHeader17.Width = 71;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "属性2";
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "属性3";
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "属性4";
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "属性5";
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "武皇币";
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "冰魄水玉";
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "充值点";
            // 
            // button43
            // 
            this.button43.Location = new System.Drawing.Point(16, 10);
            this.button43.Name = "button43";
            this.button43.Size = new System.Drawing.Size(75, 23);
            this.button43.TabIndex = 3;
            this.button43.Text = "查看全部";
            this.button43.UseVisualStyleBackColor = true;
            this.button43.Click += new System.EventHandler(this.button43_Click);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.textBox14);
            this.panel1.Controls.Add(this.label14);
            this.panel1.Controls.Add(this.textBoxPAGE);
            this.panel1.Controls.Add(this.label13);
            this.panel1.Controls.Add(this.textBox13);
            this.panel1.Controls.Add(this.label12);
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.textBox11);
            this.panel1.Controls.Add(this.label11);
            this.panel1.Controls.Add(this.label10);
            this.panel1.Controls.Add(this.label9);
            this.panel1.Controls.Add(this.label8);
            this.panel1.Controls.Add(this.textBox10);
            this.panel1.Controls.Add(this.textBox9);
            this.panel1.Controls.Add(this.textBox8);
            this.panel1.Controls.Add(this.textBox7);
            this.panel1.Controls.Add(this.textBox6);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.textBox5);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.textBox4);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.textBox3);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.textBox2);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.textBox1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(582, 506);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(343, 199);
            this.panel1.TabIndex = 4;
            this.panel1.Paint += new System.Windows.Forms.PaintEventHandler(this.panel1_Paint);
            // 
            // textBox14
            // 
            this.textBox14.Location = new System.Drawing.Point(236, 170);
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new System.Drawing.Size(100, 21);
            this.textBox14.TabIndex = 55;
            this.textBox14.Text = "0";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(184, 174);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(41, 12);
            this.label14.TabIndex = 54;
            this.label14.Text = "充值点";
            // 
            // textBoxPAGE
            // 
            this.textBoxPAGE.Location = new System.Drawing.Point(234, 60);
            this.textBoxPAGE.Name = "textBoxPAGE";
            this.textBoxPAGE.Size = new System.Drawing.Size(100, 21);
            this.textBoxPAGE.TabIndex = 53;
            this.textBoxPAGE.Text = "0";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(176, 65);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(53, 12);
            this.label13.TabIndex = 52;
            this.label13.Text = "冰魄水玉";
            // 
            // textBox13
            // 
            this.textBox13.Location = new System.Drawing.Point(234, 33);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(100, 21);
            this.textBox13.TabIndex = 51;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(186, 36);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(41, 12);
            this.label12.TabIndex = 50;
            this.label12.Text = "物品名";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 175);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 24;
            this.label7.Text = "武皇币";
            // 
            // textBox11
            // 
            this.textBox11.Location = new System.Drawing.Point(71, 168);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(100, 21);
            this.textBox11.TabIndex = 23;
            this.textBox11.Text = "0";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(12, 150);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(35, 12);
            this.label11.TabIndex = 22;
            this.label11.Text = "属性5";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(190, 123);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(35, 12);
            this.label10.TabIndex = 21;
            this.label10.Text = "属性4";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 123);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(35, 12);
            this.label9.TabIndex = 20;
            this.label9.Text = "属性3";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(191, 96);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(35, 12);
            this.label8.TabIndex = 19;
            this.label8.Text = "属性2";
            // 
            // textBox10
            // 
            this.textBox10.Location = new System.Drawing.Point(71, 141);
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new System.Drawing.Size(100, 21);
            this.textBox10.TabIndex = 18;
            this.textBox10.Text = "0";
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(234, 114);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(100, 21);
            this.textBox9.TabIndex = 17;
            this.textBox9.Text = "0";
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(71, 114);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(100, 21);
            this.textBox8.TabIndex = 16;
            this.textBox8.Text = "0";
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(234, 87);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(100, 21);
            this.textBox7.TabIndex = 15;
            this.textBox7.Text = "0";
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(71, 87);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(100, 21);
            this.textBox6.TabIndex = 12;
            this.textBox6.Text = "0";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(12, 96);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(35, 12);
            this.label6.TabIndex = 11;
            this.label6.Text = "属性1";
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(236, 144);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(100, 21);
            this.textBox5.TabIndex = 10;
            this.textBox5.Text = "0";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(195, 148);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 9;
            this.label2.Text = "价格";
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(71, 33);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(100, 21);
            this.textBox4.TabIndex = 8;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(12, 42);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 7;
            this.label5.Text = "物品ID";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(71, 60);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(100, 21);
            this.textBox3.TabIndex = 6;
            this.textBox3.Text = "0";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 67);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "位置";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(71, 7);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(100, 21);
            this.textBox2.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(29, 14);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "NPCid";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(234, 7);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(100, 21);
            this.textBox1.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(181, 14);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(47, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "NPC名字";
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(709, 734);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(100, 23);
            this.button2.TabIndex = 14;
            this.button2.Text = "增加(立即生效)";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(582, 734);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(121, 23);
            this.button1.TabIndex = 13;
            this.button1.Text = "保存修改(立即生效)";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.编辑ToolStripMenuItem,
            this.删除ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(101, 48);
            // 
            // 编辑ToolStripMenuItem
            // 
            this.编辑ToolStripMenuItem.Name = "编辑ToolStripMenuItem";
            this.编辑ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.编辑ToolStripMenuItem.Text = "编辑";
            this.编辑ToolStripMenuItem.Click += new System.EventHandler(this.编辑ToolStripMenuItem_Click);
            // 
            // 删除ToolStripMenuItem
            // 
            this.删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
            this.删除ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.删除ToolStripMenuItem.Text = "删除";
            this.删除ToolStripMenuItem.Click += new System.EventHandler(this.删除ToolStripMenuItem_Click);
            // 
            // comboBox11
            // 
            this.comboBox11.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox11.FormattingEnabled = true;
            this.comboBox11.Items.AddRange(new object[] {
            "衣服",
            "护手",
            "武器",
            "鞋子",
            "内甲",
            "项链",
            "耳环",
            "戒指",
            "披风",
            "弓箭",
            "门甲",
            "宝宝",
            "石头",
            "宝盒",
            "幸运符",
            "气功书",
            "百宝",
            "武功书",
            "其他"});
            this.comboBox11.Location = new System.Drawing.Point(97, 506);
            this.comboBox11.MaxDropDownItems = 20;
            this.comboBox11.Name = "comboBox11";
            this.comboBox11.Size = new System.Drawing.Size(142, 20);
            this.comboBox11.TabIndex = 40;
            this.comboBox11.SelectedIndexChanged += new System.EventHandler(this.comboBox11_SelectedIndexChanged);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(14, 509);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(77, 12);
            this.label40.TabIndex = 39;
            this.label40.Text = "物品种类选择";
            // 
            // listBox3
            // 
            this.listBox3.FormattingEnabled = true;
            this.listBox3.ItemHeight = 12;
            this.listBox3.Location = new System.Drawing.Point(12, 532);
            this.listBox3.Name = "listBox3";
            this.listBox3.Size = new System.Drawing.Size(549, 208);
            this.listBox3.TabIndex = 38;
            this.listBox3.SelectedIndexChanged += new System.EventHandler(this.listBox3_SelectedIndexChanged);
            // 
            // statusStrip1
            // 
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.toolStripStatusLabel2});
            this.statusStrip1.Location = new System.Drawing.Point(0, 760);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(934, 22);
            this.statusStrip1.TabIndex = 43;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.ForeColor = System.Drawing.Color.Black;
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(68, 17);
            this.toolStripStatusLabel1.Text = "信息提示：";
            // 
            // toolStripStatusLabel2
            // 
            this.toolStripStatusLabel2.ForeColor = System.Drawing.Color.Red;
            this.toolStripStatusLabel2.Name = "toolStripStatusLabel2";
            this.toolStripStatusLabel2.Size = new System.Drawing.Size(131, 17);
            this.toolStripStatusLabel2.Text = "toolStripStatusLabel2";
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(815, 734);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(100, 23);
            this.button3.TabIndex = 44;
            this.button3.Text = "删除(立即生效)";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(166, 12);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(183, 20);
            this.comboBox1.TabIndex = 46;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(107, 14);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(53, 12);
            this.label20.TabIndex = 45;
            this.label20.Text = "NPC选择:";
            // 
            // SELL
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(934, 782);
            this.Controls.Add(this.comboBox1);
            this.Controls.Add(this.label20);
            this.Controls.Add(this.button3);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.comboBox11);
            this.Controls.Add(this.label40);
            this.Controls.Add(this.listBox3);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.button43);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "SELL";
            this.Text = "商店数据修改";
            this.Load += new System.EventHandler(this.SELL_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void SELL_Load(object sender, EventArgs e)
	{
		foreach (MonSterClss value in World.MonSter.Values)
		{
			if (value.FLD_PID < 10000)
			{
				try
				{
					KeyValuePair<int, string> keyValuePair = new KeyValuePair<int, string>(value.FLD_PID, value.Name);
					comboBox1.Items.Add(keyValuePair);
				}
				catch
				{
					MessageBox.Show(value.FLD_PID + "|" + value.Name);
				}
			}
		}
		ItemDef.AddComBoxItemReside2(comboBox11);
	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		textBox2.Text = ((KeyValuePair<int, string>)comboBox1.SelectedItem).Key.ToString();
		textBox1.Text = ((KeyValuePair<int, string>)comboBox1.SelectedItem).Value;
		加载指定NPC商店物品(((KeyValuePair<int, string>)comboBox1.SelectedItem).Key);
	}

	private void 加载指定NPC商店物品(int NPCID)
	{
		try
		{
			listView2.Items.Clear();
			string sqlCommand = "select * from tbl_xwwl_sell where fld_nid=" + NPCID;
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				ListViewItem listViewItem = new ListViewItem();
				listViewItem.SubItems.Clear();
				listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["id"].ToString();
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NPCNAME"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NID"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_INDEX"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_PID"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MONEY"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC0"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC1"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC2"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC3"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC4"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_武皇币"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_冰魄水玉"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_累计充值"].ToString());
				listView2.Items.Add(listViewItem);
			}
		}
		catch (Exception)
		{
			throw;
		}
	}
}

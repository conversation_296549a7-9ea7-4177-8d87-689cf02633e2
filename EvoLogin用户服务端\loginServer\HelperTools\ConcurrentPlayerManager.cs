using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using loginServer.DbClss;

namespace loginServer.HelperTools
{
    // 2025-0618 EVIAS 高性能并发安全的玩家管理器
    public static class ConcurrentPlayerManager
    {
        // 使用ConcurrentDictionary提供线程安全的集合操作
        private static readonly ConcurrentDictionary<string, playerS> Players = new ConcurrentDictionary<string, playerS>(StringComparer.OrdinalIgnoreCase);
        private static readonly ConcurrentDictionary<string, playerS> PlayersTemp = new ConcurrentDictionary<string, playerS>(StringComparer.OrdinalIgnoreCase);
        
        // 读写锁，用于需要遍历操作的场景
        private static readonly ReaderWriterLockSlim PlayersLock = new ReaderWriterLockSlim();
        
        // 性能计数器
        private static long _totalOperations = 0;
        private static long _cacheHits = 0;
        
        // 缓存最近查询的玩家，提升性能
        private static readonly ConcurrentDictionary<string, (playerS Player, DateTime CacheTime)> QueryCache = 
            new ConcurrentDictionary<string, (playerS, DateTime)>();
        private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(5);
        
        // 获取玩家总数
        public static int PlayerCount => Players.Count;

        // 2025-0618 EVIAS 获取真正在线的玩家数量（排除离线挂机）
        public static int OnlinePlayerCount
        {
            get
            {
                try
                {
                    return Players.Values.Count(p => p?.离线挂机 == "0");
                }
                catch (Exception ex)
                {
                    ExceptionHandler.LogSecureException(ex, "获取在线玩家数量", "");
                    return 0;
                }
            }
        }

        // 2025-0618 EVIAS 获取离线挂机玩家数量
        public static int OfflinePlayerCount
        {
            get
            {
                try
                {
                    return Players.Values.Count(p => p?.离线挂机 == "1");
                }
                catch (Exception ex)
                {
                    ExceptionHandler.LogSecureException(ex, "获取离线挂机数量", "");
                    return 0;
                }
            }
        }

        // 2025-0618 EVIAS 获取假人数量
        public static int DummyPlayerCount
        {
            get
            {
                try
                {
                    return Players.Values.Count(p => p?.离线挂机 == "2");
                }
                catch (Exception ex)
                {
                    ExceptionHandler.LogSecureException(ex, "获取假人数量", "");
                    return 0;
                }
            }
        }

        // 2025-0618 EVIAS 获取云挂机数量
        public static int CloudPlayerCount
        {
            get
            {
                try
                {
                    return Players.Values.Count(p => p?.离线挂机 == "3");
                }
                catch (Exception ex)
                {
                    ExceptionHandler.LogSecureException(ex, "获取云挂机数量", "");
                    return 0;
                }
            }
        }
        
        // 获取临时玩家总数
        public static int TempPlayerCount => PlayersTemp.Count;

        // 获取唯一IP数量 - 2025-0618 EVIAS 用于状态栏显示
        public static int GetUniqueIpCount()
        {
            try
            {
                var uniqueIps = new HashSet<string>();
                foreach (var player in Players.Values)
                {
                    if (!string.IsNullOrWhiteSpace(player.UserIp))
                    {
                        uniqueIps.Add(player.UserIp);
                    }
                }
                return uniqueIps.Count;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "获取唯一IP数量", "");
                return 0;
            }
        }

        // 获取所有玩家列表 - 2025-0618 EVIAS 用于多开检测
        public static IEnumerable<playerS> GetAllPlayers()
        {
            try
            {
                return Players.Values.ToList(); // 返回副本避免并发修改
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "获取所有玩家", "");
                return new List<playerS>();
            }
        }
        
        // 获取性能统计
        public static (long Operations, long CacheHits, double HitRate) GetPerformanceStats()
        {
            long ops = Interlocked.Read(ref _totalOperations);
            long hits = Interlocked.Read(ref _cacheHits);
            double hitRate = ops > 0 ? (double)hits / ops * 100 : 0;
            return (ops, hits, hitRate);
        }
        
        // 添加玩家（线程安全）
        public static bool AddPlayer(string userId, playerS player)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId) || player == null)
                    return false;
                
                Interlocked.Increment(ref _totalOperations);
                
                bool added = Players.TryAdd(userId.ToLower(), player);
                if (added)
                {
                    // 清除相关缓存
                    InvalidateCache(userId);
                    Form1.WriteLine(3, $"玩家添加成功: {userId}");
                }
                
                return added;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "添加玩家", userId);
                return false;
            }
        }
        
        // 添加临时玩家
        public static bool AddTempPlayer(string userId, playerS player)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId) || player == null)
                    return false;
                
                return PlayersTemp.TryAdd(userId.ToLower(), player);
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "添加临时玩家", userId);
                return false;
            }
        }
        
        // 移除玩家（线程安全）
        public static bool RemovePlayer(string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                    return false;
                
                Interlocked.Increment(ref _totalOperations);
                
                bool removed = Players.TryRemove(userId.ToLower(), out var removedPlayer);
                if (removed)
                {
                    // 清除相关缓存
                    InvalidateCache(userId);
                    
                    // 清理资源
                    try
                    {
                        removedPlayer?.npcyd?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        ExceptionHandler.LogSecureException(ex, "清理玩家资源", userId);
                    }
                    
                    Form1.WriteLine(3, $"玩家移除成功: {userId}");
                }
                
                return removed;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "移除玩家", userId);
                return false;
            }
        }
        
        // 查询玩家（高性能，带缓存）
        public static playerS GetPlayer(string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                    return null;
                
                Interlocked.Increment(ref _totalOperations);
                string lowerUserId = userId.ToLower();
                
                // 先检查缓存
                if (QueryCache.TryGetValue(lowerUserId, out var cached))
                {
                    if (DateTime.Now - cached.CacheTime < CacheExpiry)
                    {
                        Interlocked.Increment(ref _cacheHits);
                        return cached.Player;
                    }
                    else
                    {
                        // 缓存过期，移除
                        QueryCache.TryRemove(lowerUserId, out _);
                    }
                }
                
                // 从主集合查询
                if (Players.TryGetValue(lowerUserId, out var player))
                {
                    // 更新缓存
                    QueryCache.TryAdd(lowerUserId, (player, DateTime.Now));
                    return player;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "查询玩家", userId);
                return null;
            }
        }
        
        // 查询在线玩家（排除离线挂机）
        public static playerS GetOnlinePlayer(string userId)
        {
            try
            {
                var player = GetPlayer(userId);
                return player?.离线挂机 != "1" ? player : null;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "查询在线玩家", userId);
                return null;
            }
        }
        
        // 查询离线玩家（仅离线挂机）
        public static playerS GetOfflinePlayer(string userId)
        {
            try
            {
                var player = GetPlayer(userId);
                return player?.离线挂机 == "1" ? player : null;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "查询离线玩家", userId);
                return null;
            }
        }
        
        // 查询临时玩家
        public static playerS GetTempPlayer(string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userId))
                    return null;
                
                PlayersTemp.TryGetValue(userId.ToLower(), out var player);
                return player;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "查询临时玩家", userId);
                return null;
            }
        }
        
        // 批量操作：根据绑定账号查询玩家
        public static List<playerS> GetPlayersByBindAccount(string bindAccount)
        {
            var result = new List<playerS>();
            
            try
            {
                if (string.IsNullOrWhiteSpace(bindAccount))
                    return result;
                
                PlayersLock.EnterReadLock();
                try
                {
                    result.AddRange(Players.Values.Where(p => 
                        RxjhClass.IsEquals(p.绑定账号, bindAccount)));
                }
                finally
                {
                    PlayersLock.ExitReadLock();
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "根据绑定账号查询玩家", bindAccount);
            }
            
            return result;
        }
        
        // 批量操作：根据服务器ID查询玩家
        public static List<playerS> GetPlayersByServerId(string serverId)
        {
            var result = new List<playerS>();
            
            try
            {
                if (string.IsNullOrWhiteSpace(serverId))
                    return result;
                
                PlayersLock.EnterReadLock();
                try
                {
                    result.AddRange(Players.Values.Where(p => 
                        p.ServerID == serverId && p.conn == 0));
                }
                finally
                {
                    PlayersLock.ExitReadLock();
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "根据服务器ID查询玩家", serverId);
            }
            
            return result;
        }
        
        // 获取所有玩家的快照（用于遍历）
        public static List<playerS> GetAllPlayersSnapshot()
        {
            var result = new List<playerS>();
            
            try
            {
                PlayersLock.EnterReadLock();
                try
                {
                    result.AddRange(Players.Values);
                }
                finally
                {
                    PlayersLock.ExitReadLock();
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "获取所有玩家快照", null);
            }
            
            return result;
        }
        
        // 清理过期缓存
        public static void CleanupCache()
        {
            try
            {
                var expiredKeys = new List<string>();
                var cutoff = DateTime.Now - CacheExpiry;
                
                foreach (var kvp in QueryCache)
                {
                    if (kvp.Value.CacheTime < cutoff)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }
                
                foreach (var key in expiredKeys)
                {
                    QueryCache.TryRemove(key, out _);
                }
                
                if (expiredKeys.Count > 0)
                {
                    Form1.WriteLine(3, $"清理过期缓存: {expiredKeys.Count} 项");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "清理缓存", null);
            }
        }
        
        // 使缓存失效
        private static void InvalidateCache(string userId)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    QueryCache.TryRemove(userId.ToLower(), out _);
                }
            }
            catch
            {
                // 忽略缓存清理错误
            }
        }
        
        // 获取统计信息
        public static string GetStatistics()
        {
            try
            {
                var stats = GetPerformanceStats();
                int onlineCount = OnlinePlayerCount;
                int offlineCount = OfflinePlayerCount;
                int dummyCount = DummyPlayerCount;
                int cloudCount = CloudPlayerCount;
                int totalCount = PlayerCount;

                return $"在线: {onlineCount}, 离线挂机: {offlineCount}, 假人: {dummyCount}, 云挂机: {cloudCount}, 总数: {totalCount}, 临时: {TempPlayerCount}, " +
                       $"查询操作: {stats.Operations}, 缓存命中率: {stats.HitRate:F1}%";
            }
            catch
            {
                return "统计信息获取失败";
            }
        }
    }
}

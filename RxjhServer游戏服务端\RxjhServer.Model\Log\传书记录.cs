﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;

namespace RxjhServer.Model.Log {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class 传书记录 {

		[JsonProperty]
		public int 传书类型 { get; set; } = 0;

		[JsonProperty, Column(DbType = "varchar(2000)", IsNullable = false)]
		public string 传书内容 { get; set; }

		[JsonProperty, Column(DbType = "smalldatetime", InsertValueSql = "getdate()")]
		public DateTime 传书时间 { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)", IsNullable = false)]
		public string 发送人物名 { get; set; }

		[JsonProperty]
		public int 发送NPC { get; set; }

		[JsonProperty, Column(StringLength = 50)]
		public string 分区 { get; set; }

		[JsonProperty, Column(DbType = "varchar(50)", IsNullable = false)]
		public string 接收人物名 { get; set; }

		[JsonProperty]
		public int 阅读标识 { get; set; }

		[JsonProperty, Column(IsIdentity = true)]
		public int ID { get; set; }

	}

}

using System;
using System.Data;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 比武泡点系统 : IDisposable
{
	private System.Timers.Timer 时间1;

	private System.Timers.Timer 时间2;

	private DateTime kssj;

	private int kssjint;

	private DateTime kssj1;

	private int kssjint1;

	public 比武泡点系统()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "比武泡点错误1");
			}
			World.比武泡点Top.Clear();
			kssj = DateTime.Now.AddMinutes(World.比武泡点倒计时);
			时间1 = new System.Timers.Timer(10000.0);
			时间1.Elapsed += 时间结束事件1;
			时间1.Enabled = true;
			时间1.AutoReset = true;
			时间结束事件1(null, null);
		}
		catch (Exception ex)
		{
			RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "比武泡点系统", "初始化", "比武泡点系统启动");
			Form1.WriteLine(1, "比武泡点错误2" + ex);
		}
	}

	public void 时间结束事件1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "比武泡点错误3");
		}
		try
		{
			int num = (kssjint = (int)kssj.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 != 101 || !value.检查玩家是否在挂机双倍区域(value))
				{
					value.发送其他活动开始倒计时(num);
					value.系统提示("要参加比武泡点的请速去泫勃派南门擂台", 10, "系统提示");
					value.系统提示("要参加比武泡点的请速去泫勃派南门擂台", 3, "系统提示");
					value.系统提示("要参加比武泡点的请速去泫勃派南门擂台", 7, "系统提示");
				}
			}
			if (kssjint <= 0)
			{
				时间1.Enabled = false;
				时间1.Close();
				时间1.Dispose();
				World.比武泡点进程 = 1;
				kssj1 = DateTime.Now.AddMinutes(World.比武泡点总时间);
				时间2 = new System.Timers.Timer(60000.0);
				时间2.Elapsed += 时间结束事件2;
				时间2.Enabled = true;
				时间2.AutoReset = true;
				时间结束事件2(null, null);
			}
		}
		catch (Exception)
		{
		}
	}

	public void 时间结束事件2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "比武泡点错误4");
		}
		try
		{
			int num = (kssjint1 = (int)kssj1.Subtract(DateTime.Now).TotalSeconds);
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.人物坐标_地图 == 101 && value.检查玩家是否在挂机双倍区域(value))
				{
					value.发送其他活动开始倒计时(num);
				}
			}
			if (kssjint1 > 0)
			{
				return;
			}
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
			foreach (比武泡点TopClass value2 in World.比武泡点Top.Values)
			{
				string text = "正";
				text = ((value2.势力 != 1) ? "邪" : "正");
				DBA.ExeSqlCommand($"INSERT INTO 荣誉比武排行 (人物名,帮派,势力,等级,杀人数,分区信息)values('{value2.人物名}','{value2.帮派}','{text}',{value2.等级},{value2.杀人数},'{World.分区编号}')");
			}
			string sqlCommand = string.Format("Select TOP 10 * from 荣誉比武排行 where 分区信息='" + World.分区编号 + "'  Order By 杀人数 Desc");
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "GameServer");
			if (dBToDataTable != null)
			{
				if (dBToDataTable.Rows.Count > 0)
				{
					for (int i = 0; i < dBToDataTable.Rows.Count; i++)
					{
						foreach (Players value3 in World.allConnectedChars.Values)
						{
							if (value3.UserName == dBToDataTable.Rows[i]["人物名"].ToString())
							{
								value3.比武泡点奖励(i);
							}
						}
					}
				}
				dBToDataTable.Dispose();
			}
			Dispose();
		}
		catch (Exception)
		{
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "比武泡点错误5");
		}
		World.比武泡点进程 = 0;
		World.发送特殊公告("比武活泡点动已结束，期待下次比武泡点！", 10, "系统提示");
		if (时间1 != null)
		{
			时间1.Enabled = false;
			时间1.Close();
			时间1.Dispose();
		}
		if (时间2 != null)
		{
			时间2.Enabled = false;
			时间2.Close();
			时间2.Dispose();
		}
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.检查玩家是否在挂机双倍区域(value))
			{
				value.移动(401f, 1366f, 15f, 101);
				value.切换PK模式(0);
			}
			value.比武追加经验值 = 0.0;
		}
		if (World.比武泡点Top != null)
		{
			World.比武泡点Top.Clear();
		}
		World.比武泡点 = null;
		Form1.WriteLine(22, "比武泡点活动结束");
	}
}

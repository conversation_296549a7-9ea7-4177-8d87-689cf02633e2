namespace RxjhServer;

public class 势力排名
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private string string_0;

	private string string_1;

	private string string_2;

	public string 势力帮派名
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public string 势力分区ID
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public int 势力人物等级
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public string 势力人物名
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int 势力荣誉点
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int 势力正邪
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int 势力职业
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int 势力转职
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}
}

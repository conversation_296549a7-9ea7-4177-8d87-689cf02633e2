using System;
using System.Collections;
using System.Net;
using HPSocket;
using HPSocket.Base;
using HPSocket.Tcp;
using RxjhServer.DbClss;

namespace RxjhServer.Network;

public class Listener
{
	private AppState appState = AppState.Stoped;

	public TcpServer Server = new TcpServer();

	private bool m_Disposed;

	private void SetServerState()
	{
		Server.OnPrepareListen += OnPrepareListen;
		Server.OnAccept += OnAccept;
		Server.OnSend += OnSend;
		Server.OnReceive += OnReceive;
		Server.OnClose += OnClose;
		Server.OnShutdown += OnShutdown;
		Server.SocketBufferSize = 8192u;
		Server.FreeBufferObjPool = 1600u;
		Server.FreeBufferObjHold = 3u;
		SetAppState(AppState.Stoped);
	}

	public Listener(ushort port)
	{
		try
		{
			m_Disposed = false;
			SetServerState();
			Start(port);
		}
		catch (Exception ex)
		{
			SetAppState(AppState.Error);
			AddMsg(ex.Message, 1);
		}
	}

	private void Start(ushort port)
	{
		try
		{
			SetAppState(AppState.Starting);
			Server.Address = "0.0.0.0";
			Server.Port = port;
			Server.SendPolicy = ((World.MainServer == 1) ? SendPolicy.Safe : SendPolicy.Direct);
			if (Server.Start())
			{
				World.主Socket = true;
				World.游戏服务器端口2 = port;
				AddMsg(string.Format("开始监听端口 {0}:{1}", "0.0.0.0", port), 1);
				World.conn.发送("更新服务器端口|" + World.服务器ID + "|" + port);
				SetAppState(AppState.Started);
				return;
			}
			SetAppState(AppState.Stoped);
			throw new Exception(string.Format("监听端口失败 -> {0}({1})", Server.ErrorMessage, Server.ErrorCode, 1));
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, $"0.0.0.0:{port}", "启动监听端口");
			AddMsg(ex.Message, 1);
		}
	}

	public void Stop()
	{
		try
		{
			SetAppState(AppState.Stoping);
			World.主Socket = false;
			if (!m_Disposed)
			{
				if (Server.Stop())
				{
					SetAppState(AppState.Stoped);
					m_Disposed = true;
				}
				else
				{
					AddMsg("停止通信服务出错1", 1);
				}
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, "服务器", "停止通信服务");
			AddMsg("停止通信服务出错2 " + ex.Message, 1);
		}
	}

	public void Dispose()
	{
		try
		{
			Server.Stop();
			Server.Dispose();
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, "服务器", "终止通信服务");
			AddMsg("终止通信服务出错3" + ex.Message, 1);
		}
	}

	private HandleResult OnShutdown(IServer sender)
	{
		AddMsg("停止通信组件服务4", 3);
		return HandleResult.Ok;
	}

	private void Disconnect(IntPtr connId)
	{
		try
		{
			if (!Server.Disconnect(connId))
			{
				throw new Exception("IP黑名单断开连接({connId}) 错误");
			}
			AddMsg($"$({connId}) 断开连接", 3);
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, connId.ToString(), "断开连接");
			AddMsg(ex.Message, 1);
		}
	}

	private HandleResult OnPrepareListen(IServer sender, IntPtr listen)
	{
		return HandleResult.Ok;
	}

	private static long IpToInt(string ip)
	{
		char[] separator = new char[1] { '.' };
		string[] array = ip.Split(separator);
		return (long.Parse(array[0]) << 24) | (long.Parse(array[1]) << 16) | (long.Parse(array[2]) << 8) | long.Parse(array[3]);
	}

	private HandleResult OnAccept(IServer sender, IntPtr connId, IntPtr pClient)
	{
		try
		{
			if (((Server)sender).GetRemoteAddress(connId, out var ip, out var port))
			{
				if (World.开启快速连接 && !ConnInfo.Check(ip, port))
				{
					ConnInfo.Add(new ConnInfo
					{
						Ip = ip,
						Port = port,
						Time = DateTime.Now,
						Kill = true
					});
					Form1.WriteLine(1, $"限制连接 IP={ip} Port={port}");
					return HandleResult.Error;
				}
				if (World.封IP)
				{
					IPAddress iPAddress = new IPAddress((uint)IPAddress.HostToNetworkOrder((int)IpToInt(ip)));
					if (World.BipList.Contains(iPAddress) && World.断开连接)
					{
						Disconnect(connId);
						return HandleResult.Error;
					}
					DateTime value = DateTime.Now;
					int num = 0;
					foreach (NetState value2 in World.list.Values)
					{
						if (value2.ToString() == iPAddress.ToString())
						{
							value = value2.Ljtime;
							num++;
						}
					}
					if (num >= World.游戏登陆端口最大连接数 && (int)DateTime.Now.Subtract(value).TotalMilliseconds < World.游戏登陆端口最大连接时间数)
					{
						Form1.WriteLine(1, $"达到IP最大连接数: {iPAddress}");
						if (World.加入过滤列表 && !World.BipList.Contains(iPAddress))
						{
							World.BipList.Add(iPAddress);
						}
						Disconnect(connId);
						try
						{
							Queue queue = Queue.Synchronized(new Queue());
							foreach (NetState value3 in World.list.Values)
							{
								if (value3.ToString() == iPAddress.ToString())
								{
									queue.Enqueue(value3);
								}
							}
							while (queue.Count > 0)
							{
								((NetState)queue.Dequeue()).Dispose();
							}
						}
						catch (Exception ex)
						{
							Form1.WriteLine(1, $"处理IP最大连接数时发生异常: {iPAddress}, {ex.Message}");
						}
						return HandleResult.Error;
					}
				}
				ClientInfo clientInfo = SetClientData(connId, ip, port);
				if (((Server)sender).SetExtra(connId, clientInfo))
				{
					new NetState(clientInfo).Start();
				}
				else
				{
					AddMsg($" > [{connId}, OnAccept] -> SetConnectionExtra(connId, clientInfo) Error", 1);
				}
				ConnInfo.Add(new ConnInfo
				{
					Ip = ip,
					Port = port,
					Time = DateTime.Now,
					Kill = false
				});
			}
			else
			{
				AddMsg($" > [{connId}, OnAccept] -> 获取远程地址失败", 1);
			}
		}
		catch (Exception ex2)
		{
			AddMsg($" > [{connId}, OnAccept] -> 接受连接时发生异常: {ex2.Message}", 1);
		}
		return HandleResult.Ok;
	}

	public NetState 生成一个假人连接()
	{
		NetState netState = null;
		IntPtr connId = new IntPtr(CreateIdService.GetUserConnId());
		int num = RNG.Next(45000, 65000);
		ClientInfo ci = SetClientData(connId, "127.0.0.1", (ushort)num);
		netState = new NetState(ci);
		netState.版本验证 = true;
		netState.Start();
		return netState;
	}

	private ClientInfo SetClientData(IntPtr connId, string ip, ushort port)
	{
		try
		{
			return new ClientInfo
			{
				ConnId = connId,
				IpAddress = ip,
				Port = port,
				Server = Server,
				WorldId = addWorldIdd()
			};
		}
		catch
		{
		}
		return null;
	}

	private int addWorldIdd()
	{
		for (int i = 300; i < 10000; i++)
		{
			if (!World.list.TryGetValue(i, out var _))
			{
				return i;
			}
		}
		return 0;
	}

	private HandleResult OnSend(IServer sender, IntPtr connId, byte[] bytes)
	{
		World.发送速度 += bytes.Length;
		return HandleResult.Ok;
	}

	private HandleResult OnReceive(IServer sender, IntPtr connId, byte[] bytes)
	{
		try
		{
			ClientInfo extra = Server.GetExtra<ClientInfo>(connId);
			if (extra != null)
			{
				int num = bytes.Length;
				if (num <= 0)
				{
					return HandleResult.Error;
				}
				World.接收速度 += num;
				using (new Lock(extra.Client.m_Buffer, "this.m_Buffer"))
				{
					extra.Client.m_Buffer.Enqueue(bytes, 0, num);
				}
				extra.Client.HandleReceive(extra.Client);
			}
			return HandleResult.Ok;
		}
		catch (Exception)
		{
			return HandleResult.Error;
		}
	}

	private HandleResult OnClose(IServer sender, IntPtr connId, SocketOperation enOperation, int errorCode)
	{
		int num = 0;
		try
		{
			ClientInfo extra = ((Server)sender).GetExtra<ClientInfo>(connId);
			if (extra != null)
			{
				num = 1;
				extra.Client.logout(enOperation, errorCode);
				num = 2;
			}
			if (errorCode == 0)
			{
				AddMsg($"[断开 连接ID:{connId} ExceStep:{num}]", 1);
			}
			else
			{
				AddMsg($"[断开 连接ID:{connId} ExceStep:{num}] -> OP:{enOperation}, CODE:{errorCode}", 3);
			}
			num = 3;
			if (!((Server)sender).SetExtra(connId, null))
			{
				num = 4;
				AddMsg($"[断开错误: SetExtra({connId}, null) 失败 ExceStep:{num}] -> OP:{enOperation}, CODE:{errorCode}", 3);
			}
		}
		catch (Exception ex)
		{
			RxjhClass.HandleNetworkException(ex, connId.ToString(), "连接关闭处理");
			AddMsg($"断开错误 连接ID:{connId} OP:{enOperation} CODE:{errorCode} ExceStep:{num} ExceptionMsg:{ex.Message}", 3);
		}
		return HandleResult.Ok;
	}

	private void SetAppState(AppState state)
	{
		appState = state;
		World.SocketState = appState.ToString();
	}

	private void AddMsg(string msg, int type)
	{
		Form1.WriteLine(type, msg);
	}
}

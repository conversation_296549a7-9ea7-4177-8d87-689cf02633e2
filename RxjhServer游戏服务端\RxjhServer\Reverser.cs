using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;

namespace RxjhServer;

public class Reverser<T> : IComparer<T>
{
	private readonly Type type = null;

	private ReverserInfo info;

	public Reverser(Type type, string name, ReverserInfo.Direction direction)
	{
		this.type = type;
		info.name = name;
		if (direction != 0)
		{
			info.direction = direction;
		}
	}

	int IComparer<T>.Compare(T t1, T t2)
	{
		object x = type.InvokeMember(info.name, BindingFlags.Instance | BindingFlags.Public | BindingFlags.GetProperty, null, t1, null);
		object y = type.InvokeMember(info.name, BindingFlags.Instance | BindingFlags.Public | BindingFlags.GetProperty, null, t2, null);
		if (info.direction != 0)
		{
			Swap(ref x, ref y);
		}
		return new CaseInsensitiveComparer().Compare(x, y);
	}

	private void Swap(ref object x, ref object y)
	{
		object obj = null;
		obj = x;
		x = y;
		y = obj;
	}
}

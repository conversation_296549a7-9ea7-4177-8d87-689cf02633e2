using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace loginServer.HelperTools
{
    // 2025-0618 EVIAS 资源管理器，防止内存泄漏和资源未释放
    public static class ResourceManager
    {
        // 跟踪需要释放的资源
        private static readonly ConcurrentQueue<IDisposable> DisposalQueue = new ConcurrentQueue<IDisposable>();
        private static readonly ConcurrentDictionary<string, WeakReference> TrackedObjects = new ConcurrentDictionary<string, WeakReference>();
        
        // 定时清理器
        private static readonly System.Timers.Timer CleanupTimer = new System.Timers.Timer(30000); // 30秒清理一次
        
        // 统计信息
        private static long _totalDisposed = 0;
        private static long _totalTracked = 0;
        private static long _cleanupRuns = 0;
        
        static ResourceManager()
        {
            CleanupTimer.Elapsed += OnCleanupTimer;
            CleanupTimer.AutoReset = true;
            CleanupTimer.Start();
            
            // 应用程序退出时清理所有资源
            AppDomain.CurrentDomain.ProcessExit += OnProcessExit;
        }
        
        // 安全释放IDisposable对象
        public static void SafeDispose(IDisposable resource, string resourceName = null)
        {
            if (resource == null) return;
            
            try
            {
                resource.Dispose();
                Interlocked.Increment(ref _totalDisposed);
                
                if (!string.IsNullOrEmpty(resourceName))
                {
                    Form1.WriteLine(3, $"资源已释放: {resourceName}");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "释放资源", resourceName);
            }
        }
        
        // 延迟释放资源（用于异步场景）
        public static void DelayedDispose(IDisposable resource, string resourceName = null)
        {
            if (resource == null) return;
            
            DisposalQueue.Enqueue(resource);
            
            if (!string.IsNullOrEmpty(resourceName))
            {
                Form1.WriteLine(3, $"资源已加入延迟释放队列: {resourceName}");
            }
        }
        
        // 跟踪对象生命周期
        public static void TrackObject(object obj, string objectId)
        {
            if (obj == null || string.IsNullOrEmpty(objectId)) return;
            
            try
            {
                TrackedObjects.TryAdd(objectId, new WeakReference(obj));
                Interlocked.Increment(ref _totalTracked);
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "跟踪对象", objectId);
            }
        }
        
        // 停止跟踪对象
        public static void UntrackObject(string objectId)
        {
            if (string.IsNullOrEmpty(objectId)) return;
            
            TrackedObjects.TryRemove(objectId, out _);
        }
        
        // 安全关闭Socket连接
        public static void SafeCloseSocket(Socket socket, string connectionInfo = null)
        {
            if (socket == null) return;
            
            try
            {
                if (socket.Connected)
                {
                    socket.Shutdown(SocketShutdown.Both);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "关闭Socket连接", connectionInfo);
            }
            finally
            {
                try
                {
                    socket.Close();
                    socket.Dispose();
                    
                    if (!string.IsNullOrEmpty(connectionInfo))
                    {
                        Form1.WriteLine(3, $"Socket连接已关闭: {connectionInfo}");
                    }
                }
                catch (Exception ex)
                {
                    ExceptionHandler.LogSecureException(ex, "释放Socket资源", connectionInfo);
                }
            }
        }
        
        // 安全关闭Timer
        public static void SafeCloseTimer(System.Timers.Timer timer, string timerName = null)
        {
            if (timer == null) return;
            
            try
            {
                timer.Stop();
                timer.Enabled = false;
                timer.Dispose();
                
                if (!string.IsNullOrEmpty(timerName))
                {
                    Form1.WriteLine(3, $"Timer已关闭: {timerName}");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "关闭Timer", timerName);
            }
        }
        
        // 批量释放资源
        public static void BatchDispose(IEnumerable<IDisposable> resources, string batchName = null)
        {
            if (resources == null) return;
            
            int count = 0;
            foreach (var resource in resources)
            {
                SafeDispose(resource);
                count++;
            }
            
            if (!string.IsNullOrEmpty(batchName))
            {
                Form1.WriteLine(3, $"批量释放资源完成: {batchName}, 数量: {count}");
            }
        }
        
        // 强制垃圾回收（谨慎使用）
        public static void ForceGarbageCollection(string reason = null)
        {
            try
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                if (!string.IsNullOrEmpty(reason))
                {
                    Form1.WriteLine(3, $"强制垃圾回收完成: {reason}");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "强制垃圾回收", reason);
            }
        }
        
        // 获取内存使用情况
        public static (long WorkingSet, long PrivateMemory, long GCMemory) GetMemoryUsage()
        {
            try
            {
                var process = System.Diagnostics.Process.GetCurrentProcess();
                return (
                    process.WorkingSet64,
                    process.PrivateMemorySize64,
                    GC.GetTotalMemory(false)
                );
            }
            catch
            {
                return (0, 0, 0);
            }
        }
        
        // 获取资源统计信息
        public static string GetResourceStatistics()
        {
            try
            {
                var memory = GetMemoryUsage();
                var aliveObjects = 0;
                
                foreach (var kvp in TrackedObjects)
                {
                    if (kvp.Value.IsAlive)
                        aliveObjects++;
                }
                
                return $"已释放资源: {_totalDisposed}, 跟踪对象: {aliveObjects}/{_totalTracked}, " +
                       $"清理次数: {_cleanupRuns}, 内存: {memory.WorkingSet / 1024 / 1024}MB";
            }
            catch
            {
                return "统计信息获取失败";
            }
        }
        
        // 定时清理事件
        private static void OnCleanupTimer(object sender, ElapsedEventArgs e)
        {
            try
            {
                Interlocked.Increment(ref _cleanupRuns);
                
                // 处理延迟释放队列
                int disposedCount = 0;
                while (DisposalQueue.TryDequeue(out var resource))
                {
                    SafeDispose(resource);
                    disposedCount++;
                    
                    // 限制每次处理的数量，避免阻塞
                    if (disposedCount >= 100) break;
                }
                
                // 清理已死亡的弱引用
                var deadKeys = new List<string>();
                foreach (var kvp in TrackedObjects)
                {
                    if (!kvp.Value.IsAlive)
                    {
                        deadKeys.Add(kvp.Key);
                    }
                }
                
                foreach (var key in deadKeys)
                {
                    TrackedObjects.TryRemove(key, out _);
                }
                
                // 清理玩家管理器缓存
                ConcurrentPlayerManager.CleanupCache();
                
                if (disposedCount > 0 || deadKeys.Count > 0)
                {
                    Form1.WriteLine(3, $"资源清理完成: 释放{disposedCount}个资源, 清理{deadKeys.Count}个死亡引用");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "定时资源清理", null);
            }
        }
        
        // 应用程序退出时的清理
        private static void OnProcessExit(object sender, EventArgs e)
        {
            try
            {
                CleanupTimer?.Stop();
                CleanupTimer?.Dispose();
                
                // 清理所有延迟释放的资源
                while (DisposalQueue.TryDequeue(out var resource))
                {
                    SafeDispose(resource);
                }
                
                Form1.WriteLine(2, "应用程序退出，资源清理完成");
            }
            catch
            {
                // 退出时忽略异常
            }
        }
        
        // 手动触发清理
        public static void ManualCleanup()
        {
            OnCleanupTimer(null, null);
        }
    }
}

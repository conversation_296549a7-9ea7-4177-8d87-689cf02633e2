using System;
using System.IO;

namespace loginServer;

public class logo
{
	public static void FileTxtLog(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\错误日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileTxtLog1(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\多开日志_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileTxtLog2(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\同进在线_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileTxtLog3(string ErrTxt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			using StreamWriter streamWriter = new StreamWriter(new FileStream("logs\\复制注入_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now.ToString() + " " + ErrTxt + "\r\n");
		}
		catch
		{
		}
	}
}

using System.Collections.Generic;

namespace RxjhVer23;

internal class NewWebShopAccount : INewWebShopAccount
{
	public INewWebShopUser NewWebShopUser { get; private set; }

	public string AccountName => NewWebShopUser.Userid;

	public int UdefineFunds { get; private set; }

	public int RxPiont => NewWebShopUser.FLD_RXPIONT;

	public int GiftedRxPiont => NewWebShopUser.FLD_RXPIONTX;

	public List<INewWebShopSaleItem> PurchasedItemList { get; private set; }

	public NewWebShopAccount(INewWebShopUser user)
	{
		NewWebShopUser = user;
		PurchasedItemList = new List<INewWebShopSaleItem>();
	}
}

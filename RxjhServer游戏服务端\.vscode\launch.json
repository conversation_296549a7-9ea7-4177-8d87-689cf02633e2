{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask":  "build",
            "name": ".NET Core Launch (console)",
            "program": "${workspaceFolder}/ConsleVSCode/bin/Debug/net8.0/ConsleVSCode.dll",
            "args": [],
            "cwd": "${workspaceFolder}",
            "stopAtEntry": false,
            "internalConsoleOptions": "openOnSessionStart",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
        
                "pipeProgram": "cmd",
                "pipeArgs": ["/c"],
                "debuggerPath": "C:\\netcoredbg\\netcoredbg.exe",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
              },
        }
    ]
}
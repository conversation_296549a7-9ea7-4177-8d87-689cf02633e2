using System;
using System.Collections.Generic;
using RxjhServer.DbClss;

namespace RxjhServer;

public class UserNameGene
{
	private static List<string> _names = new List<string>();

	public UserNameGene(List<string> Names)
	{
		_names = Names;
	}

	public string GetName()
	{
		if (_names.Count < 1)
		{
			return string.Empty;
		}
		Random random = new Random();
		int index = random.Next(0, _names.Count - 1);
		string text = _names[index] ?? "";
		if (RxjhClass.GetUserName1(text) < 0)
		{
			text = GetName();
		}
		return text;
	}
}

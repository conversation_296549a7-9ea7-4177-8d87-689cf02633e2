using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

public class userlist : Form
{
	private Dictionary<string, playerS> Players = new Dictionary<string, playerS>(4000);

	private IContainer components;

	private SplitContainer splitContainer1;

	private ListView listView1;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private Button button1;

	private TextBox textBox1;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 踢IDToolStripMenuItem;

	private ColumnHeader columnHeader6;

	private ColumnHeader columnHeader7;

	private ToolStripMenuItem 封号ToolStripMenuItem;

	private ToolStripMenuItem 天ToolStripMenuItem;

	private ToolStripMenuItem 天ToolStripMenuItem1;

	private ToolStripMenuItem 天ToolStripMenuItem2;

	private ToolStripMenuItem 月ToolStripMenuItem;

	private ToolStripMenuItem 永久ToolStripMenuItem;

	private ColumnHeader columnHeader8;

	private TextBox textBox2;

	private Button button2;

	private ToolStripMenuItem 封IPToolStripMenuItem;

	private ToolStripMenuItem 封绑定帐号ToolStripMenuItem;

	private ColumnHeader columnHeader9;

	private ToolStripMenuItem 加入限制ToolStripMenuItem;

	private ToolStripMenuItem 降低爆率ToolStripMenuItem;

	private ToolStripMenuItem toolStripMenuItem32;

	private ToolStripMenuItem toolStripMenuItem33;

	private ToolStripMenuItem toolStripMenuItem34;

	private ToolStripMenuItem toolStripMenuItem35;

	private ToolStripMenuItem toolStripMenuItem36;

	private ToolStripMenuItem toolStripMenuItem37;

	private ToolStripMenuItem toolStripMenuItem38;

	private ToolStripMenuItem toolStripMenuItem39;

	private ToolStripMenuItem toolStripMenuItem40;

	private ToolStripMenuItem toolStripMenuItem41;

	private ToolStripMenuItem 降低经验ToolStripMenuItem;

	private ToolStripMenuItem toolStripMenuItem22;

	private ToolStripMenuItem toolStripMenuItem23;

	private ToolStripMenuItem toolStripMenuItem24;

	private ToolStripMenuItem toolStripMenuItem25;

	private ToolStripMenuItem toolStripMenuItem26;

	private ToolStripMenuItem toolStripMenuItem27;

	private ToolStripMenuItem toolStripMenuItem28;

	private ToolStripMenuItem toolStripMenuItem29;

	private ToolStripMenuItem toolStripMenuItem30;

	private ToolStripMenuItem toolStripMenuItem31;

	private ToolStripMenuItem 降低金钱ToolStripMenuItem;

	private ToolStripMenuItem toolStripMenuItem12;

	private ToolStripMenuItem toolStripMenuItem13;

	private ToolStripMenuItem toolStripMenuItem14;

	private ToolStripMenuItem toolStripMenuItem15;

	private ToolStripMenuItem toolStripMenuItem16;

	private ToolStripMenuItem toolStripMenuItem17;

	private ToolStripMenuItem toolStripMenuItem18;

	private ToolStripMenuItem toolStripMenuItem19;

	private ToolStripMenuItem toolStripMenuItem20;

	private ToolStripMenuItem toolStripMenuItem21;

	private ToolStripMenuItem 降低历练ToolStripMenuItem;

	private ToolStripMenuItem toolStripMenuItem2;

	private ToolStripMenuItem toolStripMenuItem3;

	private ToolStripMenuItem toolStripMenuItem4;

	private ToolStripMenuItem toolStripMenuItem5;

	private ToolStripMenuItem toolStripMenuItem6;

	private ToolStripMenuItem toolStripMenuItem7;

	private ToolStripMenuItem toolStripMenuItem8;

	private ToolStripMenuItem toolStripMenuItem9;

	private ToolStripMenuItem toolStripMenuItem10;

	private ToolStripMenuItem toolStripMenuItem11;

	private ColumnHeader columnHeader10;

	private ColumnHeader columnHeader11;

	private ToolStripMenuItem 启动teamViewerToolStripMenuItem;

	private ToolStripMenuItem 关闭登陆器ToolStripMenuItem;

	private ToolStripMenuItem 关闭客户端ToolStripMenuItem;

	private ToolStripMenuItem 禁用自动登陆ToolStripMenuItem;

	private ToolStripMenuItem 关机ToolStripMenuItem;

	private ToolStripMenuItem 重启ToolStripMenuItem;

	private ToolStripMenuItem 停止挂机ToolStripMenuItem;

	private ToolStripMenuItem 允许10开ToolStripMenuItem;

	private ToolStripMenuItem 允许20开ToolStripMenuItem;

	private ToolStripMenuItem 允许30开ToolStripMenuItem;

	private ToolStripMenuItem 允许50开ToolStripMenuItem;

	private ToolStripMenuItem 开始挂机ToolStripMenuItem;

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(userlist));
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.踢IDToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.天ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.天ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.天ToolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.月ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.永久ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封IPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封绑定帐号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.加入限制ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.降低爆率ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem32 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem33 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem34 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem35 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem36 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem37 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem38 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem39 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem40 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem41 = new System.Windows.Forms.ToolStripMenuItem();
            this.降低经验ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem22 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem23 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem24 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem25 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem26 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem27 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem28 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem29 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem30 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem31 = new System.Windows.Forms.ToolStripMenuItem();
            this.降低金钱ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem12 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem13 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem14 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem15 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem16 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem17 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem18 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem19 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem20 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem21 = new System.Windows.Forms.ToolStripMenuItem();
            this.降低历练ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem10 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem11 = new System.Windows.Forms.ToolStripMenuItem();
            this.启动teamViewerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.允许10开ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.允许20开ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.允许30开ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.允许50开ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关闭登陆器ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关闭客户端ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.禁用自动登陆ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关机ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.重启ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.停止挂机ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.开始挂机ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.textBox1 = new System.Windows.Forms.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(4);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.listView1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.textBox2);
            this.splitContainer1.Panel2.Controls.Add(this.button2);
            this.splitContainer1.Panel2.Controls.Add(this.button1);
            this.splitContainer1.Panel2.Controls.Add(this.textBox1);
            this.splitContainer1.Size = new System.Drawing.Size(1328, 574);
            this.splitContainer1.SplitterDistance = 1109;
            this.splitContainer1.SplitterWidth = 6;
            this.splitContainer1.TabIndex = 4;
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader5,
            this.columnHeader9,
            this.columnHeader1,
            this.columnHeader7,
            this.columnHeader8,
            this.columnHeader6,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader10,
            this.columnHeader11});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(0, 0);
            this.listView1.Margin = new System.Windows.Forms.Padding(4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(1109, 574);
            this.listView1.TabIndex = 2;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "id";
            this.columnHeader5.Width = 95;
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "人物";
            this.columnHeader9.Width = 139;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "用户IP";
            this.columnHeader1.Width = 124;
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "绑定帐号";
            this.columnHeader7.Width = 124;
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "人物状态";
            this.columnHeader8.Width = 72;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "地区";
            this.columnHeader6.Width = 104;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "所在服务器";
            this.columnHeader2.Width = 72;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "在线时长";
            this.columnHeader3.Width = 75;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "conn";
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "VIP";
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "职业";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.踢IDToolStripMenuItem,
            this.封号ToolStripMenuItem,
            this.封IPToolStripMenuItem,
            this.封绑定帐号ToolStripMenuItem,
            this.加入限制ToolStripMenuItem,
            this.启动teamViewerToolStripMenuItem,
            this.关闭登陆器ToolStripMenuItem,
            this.关闭客户端ToolStripMenuItem,
            this.禁用自动登陆ToolStripMenuItem,
            this.关机ToolStripMenuItem,
            this.重启ToolStripMenuItem,
            this.停止挂机ToolStripMenuItem,
            this.开始挂机ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(189, 394);
            // 
            // 踢IDToolStripMenuItem
            // 
            this.踢IDToolStripMenuItem.Name = "踢IDToolStripMenuItem";
            this.踢IDToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.踢IDToolStripMenuItem.Text = "踢ID";
            this.踢IDToolStripMenuItem.Click += new System.EventHandler(this.踢IDToolStripMenuItem_Click);
            // 
            // 封号ToolStripMenuItem
            // 
            this.封号ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.天ToolStripMenuItem,
            this.天ToolStripMenuItem1,
            this.天ToolStripMenuItem2,
            this.月ToolStripMenuItem,
            this.永久ToolStripMenuItem});
            this.封号ToolStripMenuItem.Name = "封号ToolStripMenuItem";
            this.封号ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.封号ToolStripMenuItem.Text = "封ID";
            // 
            // 天ToolStripMenuItem
            // 
            this.天ToolStripMenuItem.Name = "天ToolStripMenuItem";
            this.天ToolStripMenuItem.Size = new System.Drawing.Size(146, 34);
            this.天ToolStripMenuItem.Text = "1天";
            this.天ToolStripMenuItem.Click += new System.EventHandler(this.天ToolStripMenuItem_Click);
            // 
            // 天ToolStripMenuItem1
            // 
            this.天ToolStripMenuItem1.Name = "天ToolStripMenuItem1";
            this.天ToolStripMenuItem1.Size = new System.Drawing.Size(146, 34);
            this.天ToolStripMenuItem1.Text = "3天";
            this.天ToolStripMenuItem1.Click += new System.EventHandler(this.天ToolStripMenuItem1_Click);
            // 
            // 天ToolStripMenuItem2
            // 
            this.天ToolStripMenuItem2.Name = "天ToolStripMenuItem2";
            this.天ToolStripMenuItem2.Size = new System.Drawing.Size(146, 34);
            this.天ToolStripMenuItem2.Text = "7天";
            this.天ToolStripMenuItem2.Click += new System.EventHandler(this.天ToolStripMenuItem2_Click);
            // 
            // 月ToolStripMenuItem
            // 
            this.月ToolStripMenuItem.Name = "月ToolStripMenuItem";
            this.月ToolStripMenuItem.Size = new System.Drawing.Size(146, 34);
            this.月ToolStripMenuItem.Text = "1月";
            this.月ToolStripMenuItem.Click += new System.EventHandler(this.月ToolStripMenuItem_Click);
            // 
            // 永久ToolStripMenuItem
            // 
            this.永久ToolStripMenuItem.Name = "永久ToolStripMenuItem";
            this.永久ToolStripMenuItem.Size = new System.Drawing.Size(146, 34);
            this.永久ToolStripMenuItem.Text = "永久";
            this.永久ToolStripMenuItem.Click += new System.EventHandler(this.永久ToolStripMenuItem_Click);
            // 
            // 封IPToolStripMenuItem
            // 
            this.封IPToolStripMenuItem.Name = "封IPToolStripMenuItem";
            this.封IPToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.封IPToolStripMenuItem.Text = "封IP";
            this.封IPToolStripMenuItem.Click += new System.EventHandler(this.封IPToolStripMenuItem_Click);
            // 
            // 封绑定帐号ToolStripMenuItem
            // 
            this.封绑定帐号ToolStripMenuItem.Name = "封绑定帐号ToolStripMenuItem";
            this.封绑定帐号ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.封绑定帐号ToolStripMenuItem.Text = "封绑定帐号";
            this.封绑定帐号ToolStripMenuItem.Click += new System.EventHandler(this.封绑定帐号ToolStripMenuItem_Click);
            // 
            // 加入限制ToolStripMenuItem
            // 
            this.加入限制ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.降低爆率ToolStripMenuItem,
            this.降低经验ToolStripMenuItem,
            this.降低金钱ToolStripMenuItem,
            this.降低历练ToolStripMenuItem});
            this.加入限制ToolStripMenuItem.Name = "加入限制ToolStripMenuItem";
            this.加入限制ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.加入限制ToolStripMenuItem.Text = "加入限制";
            // 
            // 降低爆率ToolStripMenuItem
            // 
            this.降低爆率ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem32,
            this.toolStripMenuItem33,
            this.toolStripMenuItem34,
            this.toolStripMenuItem35,
            this.toolStripMenuItem36,
            this.toolStripMenuItem37,
            this.toolStripMenuItem38,
            this.toolStripMenuItem39,
            this.toolStripMenuItem40,
            this.toolStripMenuItem41});
            this.降低爆率ToolStripMenuItem.Name = "降低爆率ToolStripMenuItem";
            this.降低爆率ToolStripMenuItem.Size = new System.Drawing.Size(182, 34);
            this.降低爆率ToolStripMenuItem.Text = "降低爆率";
            // 
            // toolStripMenuItem32
            // 
            this.toolStripMenuItem32.Name = "toolStripMenuItem32";
            this.toolStripMenuItem32.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem32.Text = "解除";
            this.toolStripMenuItem32.Click += new System.EventHandler(this.toolStripMenuItem32_Click);
            // 
            // toolStripMenuItem33
            // 
            this.toolStripMenuItem33.Name = "toolStripMenuItem33";
            this.toolStripMenuItem33.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem33.Text = "10%";
            this.toolStripMenuItem33.Click += new System.EventHandler(this.toolStripMenuItem33_Click);
            // 
            // toolStripMenuItem34
            // 
            this.toolStripMenuItem34.Name = "toolStripMenuItem34";
            this.toolStripMenuItem34.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem34.Text = "20%";
            this.toolStripMenuItem34.Click += new System.EventHandler(this.toolStripMenuItem34_Click);
            // 
            // toolStripMenuItem35
            // 
            this.toolStripMenuItem35.Name = "toolStripMenuItem35";
            this.toolStripMenuItem35.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem35.Text = "30%";
            this.toolStripMenuItem35.Click += new System.EventHandler(this.toolStripMenuItem35_Click);
            // 
            // toolStripMenuItem36
            // 
            this.toolStripMenuItem36.Name = "toolStripMenuItem36";
            this.toolStripMenuItem36.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem36.Text = "40%";
            this.toolStripMenuItem36.Click += new System.EventHandler(this.toolStripMenuItem36_Click);
            // 
            // toolStripMenuItem37
            // 
            this.toolStripMenuItem37.Name = "toolStripMenuItem37";
            this.toolStripMenuItem37.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem37.Text = "50%";
            this.toolStripMenuItem37.Click += new System.EventHandler(this.toolStripMenuItem37_Click);
            // 
            // toolStripMenuItem38
            // 
            this.toolStripMenuItem38.Name = "toolStripMenuItem38";
            this.toolStripMenuItem38.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem38.Text = "60%";
            this.toolStripMenuItem38.Click += new System.EventHandler(this.toolStripMenuItem38_Click);
            // 
            // toolStripMenuItem39
            // 
            this.toolStripMenuItem39.Name = "toolStripMenuItem39";
            this.toolStripMenuItem39.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem39.Text = "70%";
            this.toolStripMenuItem39.Click += new System.EventHandler(this.toolStripMenuItem39_Click);
            // 
            // toolStripMenuItem40
            // 
            this.toolStripMenuItem40.Name = "toolStripMenuItem40";
            this.toolStripMenuItem40.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem40.Text = "80%";
            this.toolStripMenuItem40.Click += new System.EventHandler(this.toolStripMenuItem40_Click);
            // 
            // toolStripMenuItem41
            // 
            this.toolStripMenuItem41.Name = "toolStripMenuItem41";
            this.toolStripMenuItem41.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem41.Text = "90%";
            this.toolStripMenuItem41.Click += new System.EventHandler(this.toolStripMenuItem41_Click);
            // 
            // 降低经验ToolStripMenuItem
            // 
            this.降低经验ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem22,
            this.toolStripMenuItem23,
            this.toolStripMenuItem24,
            this.toolStripMenuItem25,
            this.toolStripMenuItem26,
            this.toolStripMenuItem27,
            this.toolStripMenuItem28,
            this.toolStripMenuItem29,
            this.toolStripMenuItem30,
            this.toolStripMenuItem31});
            this.降低经验ToolStripMenuItem.Name = "降低经验ToolStripMenuItem";
            this.降低经验ToolStripMenuItem.Size = new System.Drawing.Size(182, 34);
            this.降低经验ToolStripMenuItem.Text = "降低经验";
            // 
            // toolStripMenuItem22
            // 
            this.toolStripMenuItem22.Name = "toolStripMenuItem22";
            this.toolStripMenuItem22.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem22.Text = "解除";
            this.toolStripMenuItem22.Click += new System.EventHandler(this.toolStripMenuItem22_Click);
            // 
            // toolStripMenuItem23
            // 
            this.toolStripMenuItem23.Name = "toolStripMenuItem23";
            this.toolStripMenuItem23.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem23.Text = "10%";
            this.toolStripMenuItem23.Click += new System.EventHandler(this.toolStripMenuItem23_Click);
            // 
            // toolStripMenuItem24
            // 
            this.toolStripMenuItem24.Name = "toolStripMenuItem24";
            this.toolStripMenuItem24.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem24.Text = "20%";
            this.toolStripMenuItem24.Click += new System.EventHandler(this.toolStripMenuItem24_Click);
            // 
            // toolStripMenuItem25
            // 
            this.toolStripMenuItem25.Name = "toolStripMenuItem25";
            this.toolStripMenuItem25.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem25.Text = "30%";
            this.toolStripMenuItem25.Click += new System.EventHandler(this.toolStripMenuItem25_Click);
            // 
            // toolStripMenuItem26
            // 
            this.toolStripMenuItem26.Name = "toolStripMenuItem26";
            this.toolStripMenuItem26.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem26.Text = "40%";
            this.toolStripMenuItem26.Click += new System.EventHandler(this.toolStripMenuItem26_Click);
            // 
            // toolStripMenuItem27
            // 
            this.toolStripMenuItem27.Name = "toolStripMenuItem27";
            this.toolStripMenuItem27.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem27.Text = "50%";
            this.toolStripMenuItem27.Click += new System.EventHandler(this.toolStripMenuItem27_Click);
            // 
            // toolStripMenuItem28
            // 
            this.toolStripMenuItem28.Name = "toolStripMenuItem28";
            this.toolStripMenuItem28.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem28.Text = "60%";
            this.toolStripMenuItem28.Click += new System.EventHandler(this.toolStripMenuItem28_Click);
            // 
            // toolStripMenuItem29
            // 
            this.toolStripMenuItem29.Name = "toolStripMenuItem29";
            this.toolStripMenuItem29.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem29.Text = "70%";
            this.toolStripMenuItem29.Click += new System.EventHandler(this.toolStripMenuItem29_Click);
            // 
            // toolStripMenuItem30
            // 
            this.toolStripMenuItem30.Name = "toolStripMenuItem30";
            this.toolStripMenuItem30.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem30.Text = "80%";
            this.toolStripMenuItem30.Click += new System.EventHandler(this.toolStripMenuItem30_Click);
            // 
            // toolStripMenuItem31
            // 
            this.toolStripMenuItem31.Name = "toolStripMenuItem31";
            this.toolStripMenuItem31.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem31.Text = "90%";
            this.toolStripMenuItem31.Click += new System.EventHandler(this.toolStripMenuItem31_Click);
            // 
            // 降低金钱ToolStripMenuItem
            // 
            this.降低金钱ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem12,
            this.toolStripMenuItem13,
            this.toolStripMenuItem14,
            this.toolStripMenuItem15,
            this.toolStripMenuItem16,
            this.toolStripMenuItem17,
            this.toolStripMenuItem18,
            this.toolStripMenuItem19,
            this.toolStripMenuItem20,
            this.toolStripMenuItem21});
            this.降低金钱ToolStripMenuItem.Name = "降低金钱ToolStripMenuItem";
            this.降低金钱ToolStripMenuItem.Size = new System.Drawing.Size(182, 34);
            this.降低金钱ToolStripMenuItem.Text = "降低金钱";
            // 
            // toolStripMenuItem12
            // 
            this.toolStripMenuItem12.Name = "toolStripMenuItem12";
            this.toolStripMenuItem12.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem12.Text = "解除";
            this.toolStripMenuItem12.Click += new System.EventHandler(this.toolStripMenuItem12_Click);
            // 
            // toolStripMenuItem13
            // 
            this.toolStripMenuItem13.Name = "toolStripMenuItem13";
            this.toolStripMenuItem13.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem13.Text = "10%";
            this.toolStripMenuItem13.Click += new System.EventHandler(this.toolStripMenuItem13_Click);
            // 
            // toolStripMenuItem14
            // 
            this.toolStripMenuItem14.Name = "toolStripMenuItem14";
            this.toolStripMenuItem14.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem14.Text = "20%";
            this.toolStripMenuItem14.Click += new System.EventHandler(this.toolStripMenuItem14_Click);
            // 
            // toolStripMenuItem15
            // 
            this.toolStripMenuItem15.Name = "toolStripMenuItem15";
            this.toolStripMenuItem15.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem15.Text = "30%";
            this.toolStripMenuItem15.Click += new System.EventHandler(this.toolStripMenuItem15_Click);
            // 
            // toolStripMenuItem16
            // 
            this.toolStripMenuItem16.Name = "toolStripMenuItem16";
            this.toolStripMenuItem16.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem16.Text = "40%";
            this.toolStripMenuItem16.Click += new System.EventHandler(this.toolStripMenuItem16_Click);
            // 
            // toolStripMenuItem17
            // 
            this.toolStripMenuItem17.Name = "toolStripMenuItem17";
            this.toolStripMenuItem17.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem17.Text = "50%";
            this.toolStripMenuItem17.Click += new System.EventHandler(this.toolStripMenuItem17_Click);
            // 
            // toolStripMenuItem18
            // 
            this.toolStripMenuItem18.Name = "toolStripMenuItem18";
            this.toolStripMenuItem18.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem18.Text = "60%";
            this.toolStripMenuItem18.Click += new System.EventHandler(this.toolStripMenuItem18_Click);
            // 
            // toolStripMenuItem19
            // 
            this.toolStripMenuItem19.Name = "toolStripMenuItem19";
            this.toolStripMenuItem19.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem19.Text = "70%";
            this.toolStripMenuItem19.Click += new System.EventHandler(this.toolStripMenuItem19_Click);
            // 
            // toolStripMenuItem20
            // 
            this.toolStripMenuItem20.Name = "toolStripMenuItem20";
            this.toolStripMenuItem20.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem20.Text = "80%";
            this.toolStripMenuItem20.Click += new System.EventHandler(this.toolStripMenuItem20_Click);
            // 
            // toolStripMenuItem21
            // 
            this.toolStripMenuItem21.Name = "toolStripMenuItem21";
            this.toolStripMenuItem21.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem21.Text = "90%";
            this.toolStripMenuItem21.Click += new System.EventHandler(this.toolStripMenuItem21_Click);
            // 
            // 降低历练ToolStripMenuItem
            // 
            this.降低历练ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem2,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripMenuItem6,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8,
            this.toolStripMenuItem9,
            this.toolStripMenuItem10,
            this.toolStripMenuItem11});
            this.降低历练ToolStripMenuItem.Name = "降低历练ToolStripMenuItem";
            this.降低历练ToolStripMenuItem.Size = new System.Drawing.Size(182, 34);
            this.降低历练ToolStripMenuItem.Text = "降低历练";
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem2.Text = "解除";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem3.Text = "10%";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.toolStripMenuItem3_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem4.Text = "20%";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.toolStripMenuItem4_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem5.Text = "30%";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.toolStripMenuItem5_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem6.Text = "40%";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.toolStripMenuItem6_Click);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem7.Text = "50%";
            this.toolStripMenuItem7.Click += new System.EventHandler(this.toolStripMenuItem7_Click);
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem8.Text = "60%";
            this.toolStripMenuItem8.Click += new System.EventHandler(this.toolStripMenuItem8_Click);
            // 
            // toolStripMenuItem9
            // 
            this.toolStripMenuItem9.Name = "toolStripMenuItem9";
            this.toolStripMenuItem9.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem9.Text = "70%";
            this.toolStripMenuItem9.Click += new System.EventHandler(this.toolStripMenuItem9_Click);
            // 
            // toolStripMenuItem10
            // 
            this.toolStripMenuItem10.Name = "toolStripMenuItem10";
            this.toolStripMenuItem10.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem10.Text = "80%";
            this.toolStripMenuItem10.Click += new System.EventHandler(this.toolStripMenuItem10_Click);
            // 
            // toolStripMenuItem11
            // 
            this.toolStripMenuItem11.Name = "toolStripMenuItem11";
            this.toolStripMenuItem11.Size = new System.Drawing.Size(148, 34);
            this.toolStripMenuItem11.Text = "90%";
            this.toolStripMenuItem11.Click += new System.EventHandler(this.toolStripMenuItem11_Click);
            // 
            // 启动teamViewerToolStripMenuItem
            // 
            this.启动teamViewerToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.允许10开ToolStripMenuItem,
            this.允许20开ToolStripMenuItem,
            this.允许30开ToolStripMenuItem,
            this.允许50开ToolStripMenuItem});
            this.启动teamViewerToolStripMenuItem.Name = "启动teamViewerToolStripMenuItem";
            this.启动teamViewerToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.启动teamViewerToolStripMenuItem.Text = "加入无限多开";
            // 
            // 允许10开ToolStripMenuItem
            // 
            this.允许10开ToolStripMenuItem.Name = "允许10开ToolStripMenuItem";
            this.允许10开ToolStripMenuItem.Size = new System.Drawing.Size(186, 34);
            this.允许10开ToolStripMenuItem.Text = "允许10开";
            this.允许10开ToolStripMenuItem.Click += new System.EventHandler(this.允许10开ToolStripMenuItem_Click);
            // 
            // 允许20开ToolStripMenuItem
            // 
            this.允许20开ToolStripMenuItem.Name = "允许20开ToolStripMenuItem";
            this.允许20开ToolStripMenuItem.Size = new System.Drawing.Size(186, 34);
            this.允许20开ToolStripMenuItem.Text = "允许20开";
            this.允许20开ToolStripMenuItem.Click += new System.EventHandler(this.允许20开ToolStripMenuItem_Click);
            // 
            // 允许30开ToolStripMenuItem
            // 
            this.允许30开ToolStripMenuItem.Name = "允许30开ToolStripMenuItem";
            this.允许30开ToolStripMenuItem.Size = new System.Drawing.Size(186, 34);
            this.允许30开ToolStripMenuItem.Text = "允许30开";
            this.允许30开ToolStripMenuItem.Click += new System.EventHandler(this.允许30开ToolStripMenuItem_Click);
            // 
            // 允许50开ToolStripMenuItem
            // 
            this.允许50开ToolStripMenuItem.Name = "允许50开ToolStripMenuItem";
            this.允许50开ToolStripMenuItem.Size = new System.Drawing.Size(186, 34);
            this.允许50开ToolStripMenuItem.Text = "允许50开";
            this.允许50开ToolStripMenuItem.Click += new System.EventHandler(this.允许50开ToolStripMenuItem_Click);
            // 
            // 关闭登陆器ToolStripMenuItem
            // 
            this.关闭登陆器ToolStripMenuItem.Name = "关闭登陆器ToolStripMenuItem";
            this.关闭登陆器ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.关闭登陆器ToolStripMenuItem.Text = "关闭登陆器";
            this.关闭登陆器ToolStripMenuItem.Click += new System.EventHandler(this.关闭登陆器ToolStripMenuItem_Click);
            // 
            // 关闭客户端ToolStripMenuItem
            // 
            this.关闭客户端ToolStripMenuItem.Name = "关闭客户端ToolStripMenuItem";
            this.关闭客户端ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.关闭客户端ToolStripMenuItem.Text = "关闭客户端";
            this.关闭客户端ToolStripMenuItem.Click += new System.EventHandler(this.关闭客户端ToolStripMenuItem_Click);
            // 
            // 禁用自动登陆ToolStripMenuItem
            // 
            this.禁用自动登陆ToolStripMenuItem.Name = "禁用自动登陆ToolStripMenuItem";
            this.禁用自动登陆ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.禁用自动登陆ToolStripMenuItem.Text = "禁用自动登陆";
            this.禁用自动登陆ToolStripMenuItem.Click += new System.EventHandler(this.禁用自动登陆ToolStripMenuItem_Click);
            // 
            // 关机ToolStripMenuItem
            // 
            this.关机ToolStripMenuItem.Name = "关机ToolStripMenuItem";
            this.关机ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.关机ToolStripMenuItem.Text = "关机";
            this.关机ToolStripMenuItem.Click += new System.EventHandler(this.关机ToolStripMenuItem_Click);
            // 
            // 重启ToolStripMenuItem
            // 
            this.重启ToolStripMenuItem.Name = "重启ToolStripMenuItem";
            this.重启ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.重启ToolStripMenuItem.Text = "重启";
            this.重启ToolStripMenuItem.Click += new System.EventHandler(this.重启ToolStripMenuItem_Click);
            // 
            // 停止挂机ToolStripMenuItem
            // 
            this.停止挂机ToolStripMenuItem.Name = "停止挂机ToolStripMenuItem";
            this.停止挂机ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.停止挂机ToolStripMenuItem.Text = "停止挂机";
            this.停止挂机ToolStripMenuItem.Click += new System.EventHandler(this.停止挂机ToolStripMenuItem_Click);
            // 
            // 开始挂机ToolStripMenuItem
            // 
            this.开始挂机ToolStripMenuItem.Name = "开始挂机ToolStripMenuItem";
            this.开始挂机ToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.开始挂机ToolStripMenuItem.Text = "开始挂机";
            this.开始挂机ToolStripMenuItem.Click += new System.EventHandler(this.开始挂机ToolStripMenuItem_Click);
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(16, 162);
            this.textBox2.Margin = new System.Windows.Forms.Padding(4);
            this.textBox2.MaxLength = 1;
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(134, 28);
            this.textBox2.TabIndex = 3;
            this.textBox2.Text = "3";
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(16, 214);
            this.button2.Margin = new System.Windows.Forms.Padding(4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(134, 34);
            this.button2.TabIndex = 2;
            this.button2.Text = "查绑定数";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(16, 104);
            this.button1.Margin = new System.Windows.Forms.Padding(4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(134, 34);
            this.button1.TabIndex = 1;
            this.button1.Text = "查询帐号";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(16, 52);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(134, 28);
            this.textBox1.TabIndex = 0;
            // 
            // userlist
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1328, 574);
            this.Controls.Add(this.splitContainer1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "userlist";
            this.Text = "userlist";
            this.Load += new System.EventHandler(this.userlist_Load);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

	}

	public userlist()
	{
		InitializeComponent();
	}

	private void userlist_Load(object sender, EventArgs e)
	{
		try
		{
			listView1.ListViewItemSorter = new ListViewColumnSorter();
			listView1.ColumnClick += ListViewHelper.ListView_ColumnClick;
			// 2025-0618 EVIAS 使用新的玩家管理器
			var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
			foreach (playerS item in allPlayers)
			{
				string 状态 = string.Empty;
				if (item.离线挂机 == "0")
				{
					状态 = "在线";
				}
				else if (item.离线挂机 == "1")
				{
					状态 = "挂店";
				}
				else if (item.离线挂机 == "2")
				{
					状态 = "假人";
				}
				else if (item.离线挂机 == "3")
				{
					状态 = "云挂机";
				}
				if (item != null)
				{
					string[] array = new string[13]
					{
						item.UserId.ToString(),
						item.UserNmae,
						item.UserIp,
						item.绑定账号,
						状态,
						RxjhClass.GetUserIpadds(item.UserIp),
						item.ServerID + "线",
						item.在线时间.ToString().Remove(item.在线时间.ToString().Length - 8, 8),
						item.conn.ToString(),
						(!(item.金符 == "0")) ? "有" : "无",
						null,
						null,
						null
					};
					if (item.职业 == "0")
					{
						array[10] = "0";
					}
					else if (item.职业 == "1")
					{
						array[10] = "刀";
					}
					else if (item.职业 == "2")
					{
						array[10] = "剑";
					}
					else if (item.职业 == "3")
					{
						array[10] = "枪";
					}
					else if (item.职业 == "4")
					{
						array[10] = "弓";
					}
					else if (item.职业 == "5")
					{
						array[10] = "医";
					}
					else if (item.职业 == "6")
					{
						array[10] = "刺";
					}
					else if (item.职业 == "7")
					{
						array[10] = "琴";
					}
					else if (item.职业 == "8")
					{
						array[10] = "韩";
					}
					else if (item.职业 == "9")
					{
						array[10] = "谭";
					}
					else if (item.职业 == "10")
					{
						array[10] = "拳";
					}
					else if (item.职业 == "11")
					{
						array[10] = "梅";
					}
					else if (item.职业 == "12")
					{
						array[10] = "卢";
					}
					else if (item.职业 == "13")
					{
						array[10] = "神";
					}
					listView1.Items.Insert(listView1.Items.Count, new ListViewItem(array));
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		listView1.Items.Clear();
		// 2025-0618 EVIAS 使用新的玩家管理器
		var value = HelperTools.ConcurrentPlayerManager.GetPlayer(textBox1.Text);
		if (value != null)
		{
			string 状态 = string.Empty;
			if (value.离线挂机 == "0")
			{
				状态 = "在线";
			}
			else if (value.离线挂机 == "1")
			{
				状态 = "挂店";
			}
			else if (value.离线挂机 == "2")
			{
				状态 = "假人";
			}
			else if (value.离线挂机 == "3")
			{
				状态 = "云挂机";
			}
			string 职业 = string.Empty;
			if (value.职业 == "0")
			{
				职业 = "0";
			}
			else if (value.职业 == "1")
			{
				职业 = "刀";
			}
			else if (value.职业 == "2")
			{
				职业 = "剑";
			}
			else if (value.职业 == "3")
			{
				职业 = "枪";
			}
			else if (value.职业 == "4")
			{
				职业 = "弓";
			}
			else if (value.职业 == "5")
			{
				职业 = "医";
			}
			else if (value.职业 == "6")
			{
				职业 = "刺";
			}
			else if (value.职业 == "7")
			{
				职业 = "琴";
			}
			else if (value.职业 == "8")
			{
				职业 = "韩";
			}
			else if (value.职业 == "9")
			{
				职业 = "谭";
			}
			else if (value.职业 == "10")
			{
				职业 = "拳";
			}
			else if (value.职业 == "11")
			{
				职业 = "梅";
			}
			else if (value.职业 == "12")
			{
				职业 = "卢";
			}
			else if (value.职业 == "13")
			{
				职业 = "神";
			}
			string VIP = string.Empty;
			if (value.金符 == "0")
			{
				VIP = "无";
			}
			else if (value.金符 == "1")
			{
				VIP = "有";
			}
			string[] array = new string[11]
			{
				value.UserId.ToString(),
				value.UserNmae,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null
			};
			NetState netState = World.得到帐号数据(int.Parse(value.WorldID));
			array[2] = ((netState == null) ? value.UserIp : netState.ToString());
			array[3] = value.绑定账号;
			array[4] = 状态;
			array[5] = RxjhClass.GetUserIpadds(value.UserIp);
			array[6] = value.ServerID + "线";
			string obj = value.在线时间.ToString();
			int startIndex = value.在线时间.ToString().Length - 8;
			array[7] = obj.Remove(startIndex, 8);
			array[8] = value.conn.ToString();
			array[9] = VIP;
			array[10] = 职业;
			listView1.Items.Insert(listView1.Items.Count, new ListViewItem(array));
		}
	}

	private void 踢IDToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			string text = listView1.SelectedItems[0].SubItems[0].Text;
			playerS playerS2 = World.查询玩家(text);
			if (playerS2 != null)
			{
				World.登出玩家(text);
				World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
				playerS2.npcyd.Dispose();
				RxjhClass.SetUserIdONLINE(text);
			}
		}
	}

	private void 天ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
		}
		// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
		SecureDBA.UpdateAccountStatus(text, 24, "封号24小时", 0);
	}

	private void 天ToolStripMenuItem1_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
		}
		// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
		SecureDBA.UpdateAccountStatus(text, 72, "封号3天", 0);
	}

	private void 天ToolStripMenuItem2_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
		}
		// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
		SecureDBA.UpdateAccountStatus(text, 168, "封号7天", 0);
	}

	private void 月ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
		}
		// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
		SecureDBA.UpdateAccountStatus(text, 720, "封号30天", 0);
	}

	private void 永久ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
		}
		// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
		SecureDBA.UpdateAccountStatus(text, 99999, "永久封号", 0);
	}

	private void button2_Click(object sender, EventArgs e)
	{
		try
		{
			int num = int.Parse(textBox2.Text);
			listView1.Items.Clear();
			Players.Clear();
			// 2025-0618 EVIAS 使用新的玩家管理器
			var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
			foreach (playerS item in allPlayers)
			{
				if (查数量(item.绑定账号) >= num)
				{
					Players.Add(item.UserId, item);
				}
			}
			if (Players.Count <= 0)
			{
				return;
			}
			foreach (playerS value in Players.Values)
			{
				string 状态 = string.Empty;
				if (value.离线挂机 == "0")
				{
					状态 = "在线";
				}
				else if (value.离线挂机 == "1")
				{
					状态 = "挂店";
				}
				else if (value.离线挂机 == "2")
				{
					状态 = "假人";
				}
				else if (value.离线挂机 == "3")
				{
					状态 = "云挂机";
				}
				string 职业 = string.Empty;
				if (value.职业 == "0")
				{
					职业 = "0";
				}
				else if (value.职业 == "1")
				{
					职业 = "刀";
				}
				else if (value.职业 == "2")
				{
					职业 = "剑";
				}
				else if (value.职业 == "3")
				{
					职业 = "枪";
				}
				else if (value.职业 == "4")
				{
					职业 = "弓";
				}
				else if (value.职业 == "5")
				{
					职业 = "医";
				}
				else if (value.职业 == "6")
				{
					职业 = "刺";
				}
				else if (value.职业 == "7")
				{
					职业 = "琴";
				}
				else if (value.职业 == "8")
				{
					职业 = "韩";
				}
				else if (value.职业 == "9")
				{
					职业 = "谭";
				}
				else if (value.职业 == "10")
				{
					职业 = "拳";
				}
				else if (value.职业 == "11")
				{
					职业 = "梅";
				}
				else if (value.职业 == "12")
				{
					职业 = "卢";
				}
				else if (value.职业 == "13")
				{
					职业 = "神";
				}
				string VIP = string.Empty;
				if (value.金符 == "0")
				{
					VIP = "无";
				}
				else if (value.金符 == "1")
				{
					VIP = "有";
				}
				string[] obj = new string[11]
				{
					value.UserId.ToString(),
					value.UserNmae,
					value.UserIp,
					value.绑定账号,
					状态,
					RxjhClass.GetUserIpadds(value.UserIp),
					value.ServerID + "线",
					null,
					null,
					null,
					null
				};
				string text = value.在线时间.ToString();
				int startIndex = value.在线时间.ToString().Length - 8;
				obj[7] = text.Remove(startIndex, 8);
				obj[8] = value.conn.ToString();
				obj[9] = VIP;
				obj[10] = 职业;
				string[] array = obj;
				listView1.Items.Insert(listView1.Items.Count, new ListViewItem(array));
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show("查询错误 " + ex.Message);
		}
	}

	private int 查数量(string 绑定帐号)
	{
		// 2025-0618 EVIAS 使用新的玩家管理器
		int num = 0;
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (RxjhClass.IsEquals(item.绑定账号, 绑定帐号))
			{
				num++;
			}
		}
		return num;
	}

	private void 封IPToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			if (RxjhClass.GetUserIP(playerS2.UserIp) == 0)
			{
				// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
				SecureDBA.BanIP(listView1.SelectedItems[0].SubItems[2].Text);
			}
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
			MessageBox.Show("操作成功");
		}
	}

	private void 封绑定帐号ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		string text = listView1.SelectedItems[0].SubItems[0].Text;
		playerS playerS2 = World.查询玩家(text);
		if (playerS2 != null)
		{
			if (RxjhClass.GetUserIP(playerS2.UserIp) == 0)
			{
				// 2025-0618 EVIAS 使用安全的参数化查询防止SQL注入
				SecureDBA.BanIP(playerS2.UserIp);
			}
			// 2025-0618 EVIAS 使用新的玩家管理器
			HelperTools.ConcurrentPlayerManager.RemovePlayer(text);
			World.服务器踢出ID(playerS2.ServerID, playerS2.WorldID);
			playerS2.npcyd.Dispose();
			MessageBox.Show("操作成功");
		}
	}

	private void toolStripMenuItem32_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 0);
			}
		}
	}

	private void toolStripMenuItem33_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 10);
			}
		}
	}

	private void toolStripMenuItem34_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 20);
			}
		}
	}

	private void toolStripMenuItem35_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 30);
			}
		}
	}

	private void toolStripMenuItem36_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 40);
			}
		}
	}

	private void toolStripMenuItem37_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 50);
			}
		}
	}

	private void toolStripMenuItem38_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 60);
			}
		}
	}

	private void toolStripMenuItem39_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 70);
			}
		}
	}

	private void toolStripMenuItem40_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 80);
			}
		}
	}

	private void toolStripMenuItem41_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 0, 90);
			}
		}
	}

	private void toolStripMenuItem22_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 0);
			}
		}
	}

	private void toolStripMenuItem23_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 10);
			}
		}
	}

	private void toolStripMenuItem24_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 20);
			}
		}
	}

	private void toolStripMenuItem25_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 30);
			}
		}
	}

	private void toolStripMenuItem26_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 40);
			}
		}
	}

	private void toolStripMenuItem27_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 50);
			}
		}
	}

	private void toolStripMenuItem28_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 60);
			}
		}
	}

	private void toolStripMenuItem29_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 70);
			}
		}
	}

	private void toolStripMenuItem30_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 80);
			}
		}
	}

	private void toolStripMenuItem31_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 1, 90);
			}
		}
	}

	private void toolStripMenuItem12_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 0);
			}
		}
	}

	private void toolStripMenuItem13_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 10);
			}
		}
	}

	private void toolStripMenuItem14_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 20);
			}
		}
	}

	private void toolStripMenuItem15_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 30);
			}
		}
	}

	private void toolStripMenuItem16_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 40);
			}
		}
	}

	private void toolStripMenuItem17_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 50);
			}
		}
	}

	private void toolStripMenuItem18_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 60);
			}
		}
	}

	private void toolStripMenuItem19_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 70);
			}
		}
	}

	private void toolStripMenuItem20_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 80);
			}
		}
	}

	private void toolStripMenuItem21_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 2, 90);
			}
		}
	}

	private void toolStripMenuItem2_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 0);
			}
		}
	}

	private void toolStripMenuItem3_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 10);
			}
		}
	}

	private void toolStripMenuItem4_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 20);
			}
		}
	}

	private void toolStripMenuItem5_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 30);
			}
		}
	}

	private void toolStripMenuItem6_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 40);
			}
		}
	}

	private void toolStripMenuItem7_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 50);
			}
		}
	}

	private void toolStripMenuItem8_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 60);
			}
		}
	}

	private void toolStripMenuItem9_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 70);
			}
		}
	}

	private void toolStripMenuItem10_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 80);
			}
		}
	}

	private void toolStripMenuItem11_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				World.加入限制用户(playerS2.ServerID, playerS2.WorldID, 3, 90);
			}
		}
	}

	private void OpClient(string ServerID, string WorldID, string OpCode)
	{
		World.OpClient(ServerID, WorldID, OpCode);
	}

	private void 关闭登陆器ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "2");
			}
		}
	}

	private void 关闭客户端ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "0");
			}
		}
	}

	private void 禁用自动登陆ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "1");
			}
		}
	}

	private void 关机ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "4");
			}
		}
	}

	private void 重启ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "5");
			}
		}
	}

	private void 停止挂机ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "3");
			}
		}
	}

	private void 开始挂机ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count > 0)
		{
			playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
			if (playerS2 != null)
			{
				OpClient(playerS2.ServerID, playerS2.WorldID, "7");
			}
		}
	}

	private void 允许10开ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
		if (playerS2 != null)
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM 限制IP多开表  WHERE FLD_PID= '" + playerS2.绑定账号 + "' ", "rxjhaccount");
			if (dBToDataTable.Rows.Count == 0)
			{
				DBA.ExeSqlCommand("INSERT INTO 限制IP多开表 (FLD_PID,FLD_NUMBER)values('" + playerS2.绑定账号 + "','10')", "rxjhaccount");
			}
			else
			{
				DBA.ExeSqlCommand($"UPDATE 限制IP多开表 SET FLD_NUMBER=10 WHERE FLD_PID='{playerS2.绑定账号}'", "rxjhaccount");
			}
		}
		MessageBox.Show("操作成功");
	}

	private void 允许20开ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
		if (playerS2 != null)
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_MORE_RUN]  where FLD_PID= '" + playerS2.绑定账号 + "' ", "rxjhaccount");
			if (dBToDataTable.Rows.Count == 0)
			{
				DBA.ExeSqlCommand("INSERT INTO 限制IP多开表 (FLD_PID,FLD_NUMBER)values('" + playerS2.绑定账号 + "','20')", "rxjhaccount");
			}
			else
			{
				DBA.ExeSqlCommand($"UPDATE 限制IP多开表 SET FLD_NUMBER=20 WHERE FLD_PID='{playerS2.绑定账号}'", "rxjhaccount");
			}
		}
		MessageBox.Show("操作成功");
	}

	private void 允许30开ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
		if (playerS2 != null)
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [限制IP多开表]  where FLD_PID= '" + playerS2.绑定账号 + "' ", "rxjhaccount");
			if (dBToDataTable.Rows.Count == 0)
			{
				DBA.ExeSqlCommand("INSERT INTO 限制IP多开表 (FLD_PID,FLD_NUMBER)values('" + playerS2.绑定账号 + "','30')", "rxjhaccount");
			}
			else
			{
				DBA.ExeSqlCommand($"UPDATE 限制IP多开表 SET FLD_NUMBER=30 WHERE FLD_PID='{playerS2.绑定账号}'", "rxjhaccount");
			}
		}
		MessageBox.Show("操作成功");
	}

	private void 允许50开ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView1.SelectedItems.Count <= 0)
		{
			return;
		}
		playerS playerS2 = World.查询玩家(listView1.SelectedItems[0].SubItems[0].Text);
		if (playerS2 != null)
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [限制IP多开表]  where FLD_PID= '" + playerS2.绑定账号 + "' ", "rxjhaccount");
			if (dBToDataTable.Rows.Count == 0)
			{
				DBA.ExeSqlCommand("INSERT INTO 限制IP多开表 (FLD_PID,FLD_NUMBER)values('" + playerS2.绑定账号 + "','50')", "rxjhaccount");
			}
			else
			{
				DBA.ExeSqlCommand($"UPDATE 限制IP多开表 SET FLD_NUMBER=50 WHERE FLD_PID='{playerS2.绑定账号}'", "rxjhaccount");
			}
		}
		MessageBox.Show("操作成功");
	}
}

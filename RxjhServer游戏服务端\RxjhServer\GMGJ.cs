using System;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class GMGJ : Form
{
	private string string_0;

	private string string_1;

	private string string_2;

	private string string_3;

	private string string_4;

	private string string_5;

	private string string_6;

	private string string_7;

	private GroupBox groupBox1;

	private Button button1;

	private Label label3;

	private Label label1;

	private ComboBox comboBox12;

	private ComboBox comboBox11;

	private ListBox listbox1;

	private StatusStrip statusStrip1;

	private ToolStripStatusLabel toolStripStatusLabel1;

	private ToolStripStatusLabel tishi;

	private Label label5;

	private ComboBox comboBox19;

	private TextBox textBox68;

	private TextBox textBox67;

	private TextBox textBox66;

	private TextBox textBox65;

	private TextBox textBox63;

	private ComboBox comboBox18;

	private ComboBox comboBox17;

	private ComboBox comboBox16;

	private ComboBox comboBox15;

	private ComboBox comboBox14;

	private ComboBox comboBox13;

	private Label label46;

	private Label label45;

	private Label label44;

	private Label label43;

	private Label label41;

	private Label label7;

	private GroupBox groupBox2;

	private Button button2;

	private Label label9;

	private TextBox textBox2;

	private Button button5;

	private Button button3;

	private Button button6;

	private Label label12;

	private Label label11;

	private Label label10;

	private Button button10;

	private Button button9;

	private Button button8;

	private TextBox textBox6;

	private TextBox textBox5;

	private TextBox textBox4;

	private Button button13;

	private Button button12;

	private Label label14;

	private Button button18;

	private Button button17;

	private Button button16;

	private Button button15;

	private Button button14;

	private Button button19;

	private TextBox textBox8;

	private Label label6;

	private Button button32;

	private Button button31;

	private Button button30;

	private Button button29;

	private Button button28;

	private Button button27;

	private Button button26;

	private Button button25;

	private Button button24;

	private Button button23;

	private Button button21;

	private Button button34;

	private Button button33;

	private Button button38;

	private TextBox textBox11;

	private Label label17;

	private TextBox textBox13;

	private GroupBox groupBox3;

	private Button button35;

	private TextBox textBox3;

	private Button button4;

	private Label label8;

	private Button button7;

	private Label label15;

	private ListBox listBox2;

	private Label label18;

	private Button button11;

	private TextBox textBox1;

	private TextBox textBox7;

	private Label label4;

	private Button button_3;

	private TextBox textBox_9;

	private Label label_10;

	private Button button_1;

	private Label label_5;

	private TextBox textBox_4;

	private TextBox textBox64;

	private Label label16;

	private TextBox textBoxJH;

	private Label label22;

	private TextBox textBoxZH;

	private Label label20;

	private TextBox textBoxChuHun;
    private Label label21;
    private Label label19;
    
    // 新增控件
    private ComboBox comboBoxAttribute;
    private Label label2;
    private Button buttonConfirm;

	public string username
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string Player_Level
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public string Player_Job
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public string Player_Zx
	{
		get
		{
			return string_3;
		}
		set
		{
			string_3 = value;
		}
	}

	public string Player_Job_leve
	{
		get
		{
			return string_4;
		}
		set
		{
			string_4 = value;
		}
	}

	public string 转生次数
	{
		get
		{
			return string_5;
		}
		set
		{
			string_5 = value;
		}
	}

	public string Player_Sex
	{
		get
		{
			return string_6;
		}
		set
		{
			string_6 = value;
		}
	}

	public string 人物经验
	{
		get
		{
			return string_7;
		}
		set
		{
			string_7 = value;
		}
	}

	public GMGJ()
	{
		InitializeComponent();
		textBox1.Text = username;
		textBox6.Text = Player_Level;
		textBox8.Text = Player_Job;
		textBox4.Text = Player_Zx;
		textBox5.Text = Player_Job_leve;
		textBox3.Text = Player_Sex;
		tishi.Text = "请认真仔细填写，确认后即可生效！";
		comboBox13.Text = "无";
		comboBox14.Text = "无";
		comboBox15.Text = "无";
		comboBox16.Text = "无";
		comboBox17.Text = "无";
		comboBox18.Text = "无";
		comboBox19.Text = "不绑";
	}

	private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = "";
		switch (comboBox11.Text)
		{
		case "武器":
			text = "4";
			break;
		case "衣服":
			text = "1";
			break;
		case "护手":
			text = "2";
			break;
		case "鞋子":
			text = "5";
			break;
		case "内甲":
			text = "6";
			break;
		case "项链":
			text = "7";
			break;
		case "耳环":
			text = "8";
			break;
		case "戒指":
			text = "10";
			break;
		case "男/女披风":
			text = "12";
			break;
		case "门派战甲":
			text = "14";
			break;
		case "灵兽召唤符":
			text = "15";
			break;
		case "召唤书/碎片":
			text = "13";
			break;
		case "石头类":
			text = "16";
			break;
		case "幸运符":
			text = "18";
			break;
		case "盒子/箱子":
			text = "17";
			break;
		case "礼包传书卷":
			text = "20";
			break;
		case "其他":
			text = "0";
			break;
		}
		listbox1.Items.Clear();
		comboBox12.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			listbox1.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			comboBox12.Items.Add(dBToDataTable.Rows[i]["FLD_PID"].ToString());
		}
	}

	private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
	{
		comboBox12.SelectedIndex = listbox1.SelectedIndex;
	}

	private int method_0(string string_8, int int_0)
	{
		try
		{
			if (1 == 0)
			{
			}
			int result = string_8 switch
			{
				"升级成功率%增加" => 90000000 + int_0, 
				"攻击增加" => 10000000 + int_0, 
				"死亡损失经验减少%" => 130000000 + int_0, 
				"获得金钱%增加" => 120000000 + int_0, 
				"命中率增加" => 50000000 + int_0, 
				"防御增加" => 20000000 + int_0, 
				"武功防御力增加" => 110000000 + int_0, 
				"药品数量" => 2000000000 + int_0, 
				"精炼蓝红" => 2010000000 + int_0, 
				"回避率增加" => 60000000 + int_0, 
				"追加伤害值" => 100000000 + int_0, 
				"全部气功等级增加" => 80000000 + int_0, 
				"内功增加" => 40000000 + int_0, 
				"武功攻击力%增加" => 70000000 + int_0, 
				"经验值增加%" => 150000000 + int_0, 
				"精炼追加生命" => 1020000000 + int_0, 
				"生命增加" => 30000000 + int_0, 
				_ => 0, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		catch
		{
			return 0;
		}
	}

	private void textBox4_MouseClick(object sender, MouseEventArgs e)
	{
		textBox4.Text = Player_Zx;
	}

	private void textBox5_MouseClick(object sender, MouseEventArgs e)
	{
		textBox5.Text = Player_Job_leve;
	}

	private void textBox6_MouseClick(object sender, MouseEventArgs e)
	{
		textBox6.Text = Player_Level;
	}

	private void textBox8_MouseClick(object sender, MouseEventArgs e)
	{
		textBox8.Text = Player_Job;
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			if (textBox1.Text == string.Empty)
			{
				tishi.Text = "请输入玩家角色名";
				return;
			}
			if (comboBox12.Text == string.Empty)
			{
				tishi.Text = "请选择物品ID";
				return;
			}
			int num = 0;
			int num2 = 0;
			int num3 = 0;
			int num4 = 0;
			int num5 = 0;
			switch (comboBox13.Text)
			{
			case "武器/戒指/强化":
				num = 10000000 + int.Parse(textBox63.Text);
				break;
			case "防具/项链/强化":
				num = 20000000 + int.Parse(textBox63.Text);
				break;
			case "耳环强化":
				num = 30000000 + int.Parse(textBox63.Text);
				break;
			case "披风强化":
				num = 40000000 + int.Parse(textBox63.Text);
				break;
			case "灵宠强化":
				num = 190000000 + int.Parse(textBox63.Text);
				break;
			case "火属性石头":
				num = 2001000 + int.Parse(textBox63.Text);
				break;
			case "水属性石头":
				num = 2002000 + int.Parse(textBox63.Text);
				break;
			case "风属性石头":
				num = 2003000 + int.Parse(textBox63.Text);
				break;
			case "内功属性石头":
				num = 2004000 + int.Parse(textBox63.Text);
				break;
			case "外功属性石头":
				num = 2005000 + int.Parse(textBox63.Text);
				break;
			case "毒属性石头":
				num = 2006000 + int.Parse(textBox63.Text);
				break;
			case "攻击石头":
				num = 100000 + int.Parse(textBox63.Text);
				break;
			case "防御石头":
				num = 200000 + int.Parse(textBox63.Text);
				break;
			case "生命石头":
				num = 300000 + int.Parse(textBox63.Text);
				break;
			case "内功石头":
				num = 400000 + int.Parse(textBox63.Text);
				break;
			case "命中率石头":
				num = 500000 + int.Parse(textBox63.Text);
				break;
			case "回避率石头":
				num = 600000 + int.Parse(textBox63.Text);
				break;
			case "武功攻击力%石头":
				num = 700000 + int.Parse(textBox63.Text);
				break;
			case "全部气功等级石头":
				num = 800000 + int.Parse(textBox63.Text);
				break;
			case "升级成功率%石头":
				num = 900000 + int.Parse(textBox63.Text);
				break;
			case "追加伤害石头":
				num = 1000000 + int.Parse(textBox63.Text);
				break;
			case "武功防御力石头":
				num = 1100000 + int.Parse(textBox63.Text);
				break;
			case "获得金钱%石头":
				num = 1200000 + int.Parse(textBox63.Text);
				break;
			case "死亡经验减少%石头":
				num = 1300000 + int.Parse(textBox63.Text);
				break;
			case "经验值增加%石头":
				num = 1500000 + int.Parse(textBox63.Text);
				break;
			case "药品个数":
				num = 2000000000 + int.Parse(textBox63.Text);
				break;
			}
			int num6 = 0;
			num6 = int.Parse(textBox64.Text) * 100;
			switch (comboBox14.Text)
			{
			case "毒":
				num += 1000006000 + num6;
				break;
			case "外":
				num += 1000005000 + num6;
				break;
			case "内":
				num += 1000004000 + num6;
				break;
			case "风":
				num += 1000003000 + num6;
				break;
			case "水":
				num += 1000002000 + num6;
				break;
			case "火":
				num += 1000001000 + num6;
				break;
			}
			num2 = method_0(comboBox15.Text, int.Parse(textBox65.Text));
			num3 = method_0(comboBox16.Text, int.Parse(textBox66.Text));
			num4 = method_0(comboBox17.Text, int.Parse(textBox67.Text));
			num5 = method_0(comboBox18.Text, int.Parse(textBox68.Text));
			int 初级附魂 = int.Parse(textBoxChuHun.Text);
			int 中级附魂 = int.Parse(textBoxZH.Text);
			int 进化 = int.Parse(textBoxJH.Text);
			int 绑定 = ((!(comboBox19.Text == "不绑")) ? 1 : 0);
			Players players = World.检查玩家name(textBox1.Text);
			if (players != null)
			{
				int num7 = players.得到包裹空位(players);
				if (num7 == -1)
				{
					tishi.Text = "该玩家没有空位了,请通知玩家清理背包后再尝试";
					return;
				}
				players.增加物品带属性(int.Parse(comboBox12.Text), num7, 1, num, num2, num3, num4, num5, 初级附魂, 中级附魂, 进化, 绑定, 0);
				players.系统提示("获得 " + ItmeClass.得到物品名称(int.Parse(comboBox12.Text)), 10, "系统提示");
				RxjhClass.进化记录(players.Userid, players.UserName, int.Parse(comboBox12.Text), "GS内置工具2", 1, (int)RxjhClass.GetDBItmeId(), ItmeClass.得到物品名称(int.Parse(comboBox12.Text)), World.分区编号);
				tishi.Text = "玩家后台已经将物品成功刷入玩家[" + players.UserName + "]背包中";
			}
			else
			{
				tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "给玩家刷装备出错:" + ex.Message);
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
		int 元宝 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.检察元宝数据(元宝, 1, "在线GM工具");
			tishi.Text = "玩家后台刷取增加元宝成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button3_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.Player_Money += num;
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得游戏币" + num + "个", 50, "");
			tishi.Text = "玩家后台刷取增加金币成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button5_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.Player_ExpErience += num;
			players.更新经验和历练();
			players.保存人物的数据();
			tishi.Text = "玩家后台刷取增加历练成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button6_Click(object sender, EventArgs e)
	{
		int 武勋 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.武勋加减(武勋, 1, "系统提示");
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.保存人物的数据();
			tishi.Text = "玩家后台刷取增加武勋成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button12_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_攻击 += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.保存人物的数据();
			players.系统提示("获得攻击" + num);
			tishi.Text = "玩家后台刷取增加攻击成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button13_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_防御 += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得防御" + num);
			tishi.Text = "玩家后台刷取增加防御成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button14_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_武功防御力增加百分比 += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得武功防御" + num);
			tishi.Text = "玩家后台刷取增加武功防御成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button15_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.人物追加最大_HP += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得生命" + num);
			tishi.Text = "玩家后台刷取增加生命成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button16_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.人物追加最大_MP += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得内功" + num);
			tishi.Text = "玩家后台刷取增加内功成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button17_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_命中 += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得命中" + num);
			tishi.Text = "玩家后台刷取增加命中成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button18_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_回避 += num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("获得回避" + num);
			tishi.Text = "玩家后台刷取增加回避成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button19_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrEmpty(textBox8.Text))
        {
            tishi.Text = "等级不能为空";
            return;
        }
        int num = int.Parse(textBox8.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家职业不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			switch (num)
			{
			case 1:
				players.Player_Job = 1;
				players.系统提示("成功修改职业为[刀客],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 2:
				players.Player_Job = 2;
				players.系统提示("成功修改职业为[剑客],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 3:
				players.Player_Job = 3;
				players.系统提示("成功修改职业为[枪客],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 4:
				players.Player_Job = 4;
				players.系统提示("成功修改职业为[弓手],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 5:
				players.Player_Job = 5;
				players.系统提示("成功修改职业为[医生],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 6:
				players.Player_Job = 6;
				players.系统提示("成功修改职业为[刺客],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 7:
				players.Player_Job = 7;
				players.系统提示("成功修改职业为[琴师],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 8:
				players.Player_Job = 8;
				players.系统提示("成功修改职业为[韩飞官],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 9:
				players.Player_Job = 9;
				players.系统提示("成功修改职业为[谭花灵],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 10:
				players.Player_Job = 10;
				players.系统提示("成功修改职业为[拳师],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 11:
				players.Player_Job = 11;
				players.系统提示("成功修改职业为[梅柳真],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取职业修改成功";
				break;
			case 12:
				if (players.Player_Sex == 1)
				{
					players.Player_Job = 12;
					players.系统提示("成功修改职业为[卢风郎],请小退后重上!", 9, "系统提示");
					tishi.Text = "玩家后台刷取职业修改成功";
					break;
				}
				players.系统提示("卢风郎,职业必须是男性才可以申请转换职业", 10, "系统提示");
				return;
			case 13:
				if (players.Player_Sex == 2)
				{
					players.Player_Job = 13;
					players.Player_Job = 13;
					players.系统提示("成功修改职业为[东陵神女],请小退后重上!", 9, "系统提示");
					tishi.Text = "玩家后台刷取职业修改成功";
					break;
				}
				players.系统提示("东陵神女,职业必须是女性才可以申请转换职业", 10, "系统提示");
				return;
			}
		}
		else
		{
			tishi.Text = "变更职业的时候玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button8_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrEmpty(textBox4.Text))
        {
            tishi.Text = "ZX数值不能为空";
            return;
        }
        int num = int.Parse(textBox4.Text);
		int num2 = 1;
		int num3 = 2;
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			if (num == num2)
			{
				players.Player_Zx = num;
				players.系统提示("成功修改职业为[正/派],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取正邪修改成功";
			}
			else if (num == num3)
			{
				players.Player_Zx = num;
				players.系统提示("成功修改职业为[邪/派],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取正邪修改成功";
			}
		}
		else
		{
			tishi.Text = "变更职业的时候玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button9_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrEmpty(textBox5.Text))
        {
            tishi.Text = "职业不能为空";
            return;
        }
        int num = int.Parse(textBox5.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			int num2 = num;
			int num3 = num2;
			if ((uint)(num3 - 1) <= 10u)
			{
				players.Player_Job_leve = num;
				players.系统提示("您已成功修改为[" + num + "转],请小退后重上!", 9, "系统提示");
				tishi.Text = "玩家后台刷取转职修改成功";
			}
		}
		else
		{
			tishi.Text = "变更职业的时候玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
		players.更新气功();
	}

	private void button10_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrEmpty(textBox6.Text))
        {
            tishi.Text = "等级不能为空";
            return;
        }
        int player_Level = int.Parse(textBox6.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.Player_Level = player_Level;
			players.计算人物基本数据3();
			players.升级后的提示(1);
			players.人物经验 = 0L;
			players.更新气功();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.更新广播人物数据();
			players.系统提示("您已成功修改为[" + player_Level + "级],请小退后重上!", 9, "系统提示");
			tishi.Text = "玩家后台刷取等级修改成功";
		}
		else
		{
			tishi.Text = "变更职业的时候玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button21_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.Player_Money -= num;
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("被系统扣除" + num + "游戏币", 50, "");
			tishi.Text = "玩家后台刷取减少金币成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button23_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.Player_ExpErience -= num;
			players.系统提示("被系统扣除" + num + "历练", 50, "");
			tishi.Text = "玩家后台刷取减少历练成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button24_Click(object sender, EventArgs e)
	{
		int 武勋 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.武勋加减(武勋, 0, "GM工具2");
			tishi.Text = "玩家后台刷取减少武勋成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.保存人物的数据();
	}

	private void button25_Click(object sender, EventArgs e)
	{
		int 元宝 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.检察元宝数据(元宝, 0, "在线GM工具2");
			tishi.Text = "玩家后台刷取减少元宝成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button26_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_攻击 -= num;
			if (players.FLD_人物_追加_攻击 < 0)
			{
				players.FLD_人物_追加_攻击 = 0;
			}
			players.系统提示("被系统减少攻击" + num, 50, "");
			tishi.Text = "玩家后台刷取减少攻击成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button27_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_防御 -= num;
			if (players.FLD_人物_追加_防御 < 0)
			{
				players.FLD_人物_追加_防御 = 0;
			}
			players.系统提示("被系统减少防御" + num, 50, "");
			tishi.Text = "玩家后台刷取减少防御成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button28_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_武功防御力增加百分比 -= num;
			players.系统提示("被系统减少武功防御" + num, 50, "");
			tishi.Text = "玩家后台刷取减少武功防御成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.计算人物基本数据3();
		players.更新HP_MP_SP();
		players.更新武功和状态();
		players.更新经验和历练();
		players.更新金钱和负重();
		players.保存人物的数据();
	}

	private void button29_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.人物追加最大_HP -= num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("被系统减少生命" + num, 50, "");
			tishi.Text = "玩家后台刷取减少生命成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button30_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.人物追加最大_MP -= num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("被系统减少内功" + num, 50, "");
			tishi.Text = "玩家后台刷取减少内功成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button31_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_命中 -= num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("被系统减少命中" + num, 50, "");
			tishi.Text = "玩家后台刷取减少命中成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button32_Click(object sender, EventArgs e)
	{
		int num = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.FLD_人物_追加_回避 -= num;
			players.计算人物基本数据3();
			players.更新HP_MP_SP();
			players.更新武功和状态();
			players.更新经验和历练();
			players.更新金钱和负重();
			players.保存人物的数据();
			players.系统提示("被系统减少回避" + num, 50, "");
			tishi.Text = "玩家后台刷取减少回避成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button33_Click(object sender, EventArgs e)
	{
		int 元宝 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.检察钻石数据(元宝, 1, "在线GM工具");
			players.系统提示("获得" + 元宝 + "钻石", 50, "");
			tishi.Text = "玩家后台刷取增加钻石成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button34_Click(object sender, EventArgs e)
	{
		int 元宝 = int.Parse(textBox2.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			players.检察钻石数据(元宝, 0, "在线GM工具");
			players.系统提示("被系统扣除" + 元宝 + "钻石", 50, "");
			tishi.Text = "玩家后台刷取减少钻石成功";
		}
		else
		{
			tishi.Text = "刷取物品属性时玩家必须在线,请检查该玩家是否在线状态";
		}
	}

	private void button38_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrEmpty(textBox11.Text))
        {
            tishi.Text = "VIP天数不能为空";
            return;
        }
        int num = int.Parse(textBox11.Text);
        if (textBox1.Text.Trim() == string.Empty)
        {
            tishi.Text = "玩家名字不能为空";
            return;
        }
        Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			if (World.allConnectedChars.TryGetValue(players.人物全服ID, out var _))
			{
				DateTime now = DateTime.Now;
				now = DateTime.Now.AddDays(num);
				players.FLD_VIP = 1;
				players.FLD_VIPTIM = now;
				players.系统提示("恭喜您获得" + num + "天的VIP！", 9, "系统提示");
				players.系统提示("你的VIP结束时间是:" + players.FLD_VIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9, "系统提示");
				players.系统提示("续时成功,你的VIP结束时间是:" + players.FLD_VIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9, "系统提示");
				players.保存人物的数据();
				players.保存会员数据();
			}
			tishi.Text = "玩家后台刷会员成功";
		}
		else
		{
			tishi.Text = "刷取属性时玩家必须在线,请检查该玩家是否在线状态";
		}
		players.保存会员数据();
		players.保存人物的数据();
	}

	private void GMGJ_Load(object sender, EventArgs e)
	{
		tishi.Text = "请认真仔细填写，确认后即可生效！";
		comboBoxAttribute.SelectedIndex = 0; // 默认选择"增加元宝"选项
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GMGJ));
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBoxJH = new System.Windows.Forms.TextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.textBoxZH = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.textBoxChuHun = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.textBox64 = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.button_1 = new System.Windows.Forms.Button();
            this.button_3 = new System.Windows.Forms.Button();
            this.textBox_4 = new System.Windows.Forms.TextBox();
            this.label_5 = new System.Windows.Forms.Label();
            this.textBox_9 = new System.Windows.Forms.TextBox();
            this.label_10 = new System.Windows.Forms.Label();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.button35 = new System.Windows.Forms.Button();
            this.listBox2 = new System.Windows.Forms.ListBox();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.button7 = new System.Windows.Forms.Button();
            this.button4 = new System.Windows.Forms.Button();
            this.button38 = new System.Windows.Forms.Button();
            this.button11 = new System.Windows.Forms.Button();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.button19 = new System.Windows.Forms.Button();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.button10 = new System.Windows.Forms.Button();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.button9 = new System.Windows.Forms.Button();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.button8 = new System.Windows.Forms.Button();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.comboBoxAttribute = new System.Windows.Forms.ComboBox();
            this.buttonConfirm = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBox19 = new System.Windows.Forms.ComboBox();
            this.textBox68 = new System.Windows.Forms.TextBox();
            this.textBox67 = new System.Windows.Forms.TextBox();
            this.textBox66 = new System.Windows.Forms.TextBox();
            this.textBox65 = new System.Windows.Forms.TextBox();
            this.textBox63 = new System.Windows.Forms.TextBox();
            this.comboBox18 = new System.Windows.Forms.ComboBox();
            this.comboBox17 = new System.Windows.Forms.ComboBox();
            this.comboBox16 = new System.Windows.Forms.ComboBox();
            this.comboBox15 = new System.Windows.Forms.ComboBox();
            this.comboBox14 = new System.Windows.Forms.ComboBox();
            this.comboBox13 = new System.Windows.Forms.ComboBox();
            this.label46 = new System.Windows.Forms.Label();
            this.label45 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.label43 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.comboBox12 = new System.Windows.Forms.ComboBox();
            this.comboBox11 = new System.Windows.Forms.ComboBox();
            this.listbox1 = new System.Windows.Forms.ListBox();
            this.button34 = new System.Windows.Forms.Button();
            this.button33 = new System.Windows.Forms.Button();
            this.button32 = new System.Windows.Forms.Button();
            this.button31 = new System.Windows.Forms.Button();
            this.button30 = new System.Windows.Forms.Button();
            this.button29 = new System.Windows.Forms.Button();
            this.button28 = new System.Windows.Forms.Button();
            this.button27 = new System.Windows.Forms.Button();
            this.button26 = new System.Windows.Forms.Button();
            this.button25 = new System.Windows.Forms.Button();
            this.button24 = new System.Windows.Forms.Button();
            this.button23 = new System.Windows.Forms.Button();
            this.button21 = new System.Windows.Forms.Button();
            this.button18 = new System.Windows.Forms.Button();
            this.button17 = new System.Windows.Forms.Button();
            this.button16 = new System.Windows.Forms.Button();
            this.button15 = new System.Windows.Forms.Button();
            this.button14 = new System.Windows.Forms.Button();
            this.button13 = new System.Windows.Forms.Button();
            this.button12 = new System.Windows.Forms.Button();
            this.button6 = new System.Windows.Forms.Button();
            this.button5 = new System.Windows.Forms.Button();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.tishi = new System.Windows.Forms.ToolStripStatusLabel();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.textBoxJH);
            this.groupBox1.Controls.Add(this.label22);
            this.groupBox1.Controls.Add(this.textBoxZH);
            this.groupBox1.Controls.Add(this.label20);
            this.groupBox1.Controls.Add(this.textBoxChuHun);
            this.groupBox1.Controls.Add(this.label19);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.textBox64);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.textBox1);
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.label21);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.comboBox19);
            this.groupBox1.Controls.Add(this.textBox68);
            this.groupBox1.Controls.Add(this.textBox67);
            this.groupBox1.Controls.Add(this.textBox66);
            this.groupBox1.Controls.Add(this.textBox65);
            this.groupBox1.Controls.Add(this.textBox63);
            this.groupBox1.Controls.Add(this.comboBox18);
            this.groupBox1.Controls.Add(this.comboBox17);
            this.groupBox1.Controls.Add(this.comboBox16);
            this.groupBox1.Controls.Add(this.comboBox15);
            this.groupBox1.Controls.Add(this.comboBox14);
            this.groupBox1.Controls.Add(this.comboBox13);
            this.groupBox1.Controls.Add(this.label46);
            this.groupBox1.Controls.Add(this.label45);
            this.groupBox1.Controls.Add(this.label44);
            this.groupBox1.Controls.Add(this.label43);
            this.groupBox1.Controls.Add(this.label41);
            this.groupBox1.Controls.Add(this.button1);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.comboBox12);
            this.groupBox1.Controls.Add(this.comboBox11);
            this.groupBox1.Controls.Add(this.listbox1);
            this.groupBox1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.groupBox1.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(70)))), ((int)(((byte)(140)))));
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox1.Size = new System.Drawing.Size(1362, 832);
            this.groupBox1.TabIndex = 63;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "物品修改/添加";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.label2.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.ForeColor = System.Drawing.Color.Gray;
            this.label2.Location = new System.Drawing.Point(58, 104);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(312, 27);
            this.label2.TabIndex = 188;
            this.label2.Text = "提示：直接输入在线玩家名字更改";
            // 
            // textBoxJH
            // 
            this.textBoxJH.BackColor = System.Drawing.Color.White;
            this.textBoxJH.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxJH.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBoxJH.Location = new System.Drawing.Point(1204, 359);
            this.textBoxJH.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxJH.Name = "textBoxJH";
            this.textBoxJH.Size = new System.Drawing.Size(119, 31);
            this.textBoxJH.TabIndex = 184;
            this.textBoxJH.Text = "0";
            this.textBoxJH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label22.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label22.Location = new System.Drawing.Point(1092, 360);
            this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(84, 25);
            this.label22.TabIndex = 183;
            this.label22.Text = "进化阶段";
            // 
            // textBoxZH
            // 
            this.textBoxZH.BackColor = System.Drawing.Color.White;
            this.textBoxZH.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxZH.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBoxZH.Location = new System.Drawing.Point(1204, 306);
            this.textBoxZH.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxZH.Name = "textBoxZH";
            this.textBoxZH.Size = new System.Drawing.Size(119, 31);
            this.textBoxZH.TabIndex = 182;
            this.textBoxZH.Text = "0";
            this.textBoxZH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label20.Location = new System.Drawing.Point(1092, 307);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(84, 25);
            this.label20.TabIndex = 181;
            this.label20.Text = "中魂属性";
            // 
            // textBoxChuHun
            // 
            this.textBoxChuHun.BackColor = System.Drawing.Color.White;
            this.textBoxChuHun.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxChuHun.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBoxChuHun.Location = new System.Drawing.Point(936, 300);
            this.textBoxChuHun.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxChuHun.Name = "textBoxChuHun";
            this.textBoxChuHun.Size = new System.Drawing.Size(132, 31);
            this.textBoxChuHun.TabIndex = 180;
            this.textBoxChuHun.Text = "0";
            this.textBoxChuHun.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label19.Location = new System.Drawing.Point(814, 302);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(84, 25);
            this.label19.TabIndex = 179;
            this.label19.Text = "初魂阶段";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label16.Location = new System.Drawing.Point(814, 74);
            this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(89, 25);
            this.label16.TabIndex = 178;
            this.label16.Text = "装备属性:";
            // 
            // textBox64
            // 
            this.textBox64.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox64.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox64.Location = new System.Drawing.Point(1204, 74);
            this.textBox64.Margin = new System.Windows.Forms.Padding(4);
            this.textBox64.Name = "textBox64";
            this.textBox64.Size = new System.Drawing.Size(119, 31);
            this.textBox64.TabIndex = 0;
            this.textBox64.Text = "0";
            this.textBox64.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("微软雅黑", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.label4.Location = new System.Drawing.Point(21, 40);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(174, 47);
            this.label4.TabIndex = 177;
            this.label4.Text = "玩家名字:";
            // 
            // textBox1
            // 
            this.textBox1.BackColor = System.Drawing.Color.White;
            this.textBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBox1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox1.ForeColor = System.Drawing.Color.Black;
            this.textBox1.Location = new System.Drawing.Point(203, 47);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(229, 39);
            this.textBox1.TabIndex = 176;
            // 
            // groupBox3
            // 
            this.groupBox3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.groupBox3.Controls.Add(this.button_1);
            this.groupBox3.Controls.Add(this.button_3);
            this.groupBox3.Controls.Add(this.textBox_4);
            this.groupBox3.Controls.Add(this.label_5);
            this.groupBox3.Controls.Add(this.textBox_9);
            this.groupBox3.Controls.Add(this.label_10);
            this.groupBox3.Controls.Add(this.textBox7);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.button35);
            this.groupBox3.Controls.Add(this.listBox2);
            this.groupBox3.Controls.Add(this.textBox3);
            this.groupBox3.Controls.Add(this.button7);
            this.groupBox3.Controls.Add(this.button4);
            this.groupBox3.Controls.Add(this.button38);
            this.groupBox3.Controls.Add(this.button11);
            this.groupBox3.Controls.Add(this.textBox11);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.button19);
            this.groupBox3.Controls.Add(this.textBox8);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label11);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.button10);
            this.groupBox3.Controls.Add(this.textBox4);
            this.groupBox3.Controls.Add(this.button9);
            this.groupBox3.Controls.Add(this.textBox5);
            this.groupBox3.Controls.Add(this.button8);
            this.groupBox3.Controls.Add(this.textBox6);
            this.groupBox3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.groupBox3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(70)))), ((int)(((byte)(140)))));
            this.groupBox3.Location = new System.Drawing.Point(9, 146);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox3.Size = new System.Drawing.Size(423, 683);
            this.groupBox3.TabIndex = 175;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "查询玩家";
            // 
            // button_1
            // 
            this.button_1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button_1.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button_1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button_1.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button_1.ForeColor = System.Drawing.Color.Black;
            this.button_1.Location = new System.Drawing.Point(283, 95);
            this.button_1.Margin = new System.Windows.Forms.Padding(4);
            this.button_1.Name = "button_1";
            this.button_1.Size = new System.Drawing.Size(126, 40);
            this.button_1.TabIndex = 67;
            this.button_1.Text = "确定修改";
            this.button_1.UseVisualStyleBackColor = true;
            this.button_1.Click += new System.EventHandler(this.button_1_Click);
            // 
            // button_3
            // 
            this.button_3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button_3.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button_3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button_3.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button_3.ForeColor = System.Drawing.Color.Black;
            this.button_3.Location = new System.Drawing.Point(285, 317);
            this.button_3.Margin = new System.Windows.Forms.Padding(4);
            this.button_3.Name = "button_3";
            this.button_3.Size = new System.Drawing.Size(124, 38);
            this.button_3.TabIndex = 183;
            this.button_3.Text = "确定授权";
            this.button_3.UseVisualStyleBackColor = true;
            this.button_3.Click += new System.EventHandler(this.button_3_Click);
            // 
            // textBox_4
            // 
            this.textBox_4.Location = new System.Drawing.Point(156, 96);
            this.textBox_4.Margin = new System.Windows.Forms.Padding(4);
            this.textBox_4.Name = "textBox_4";
            this.textBox_4.Size = new System.Drawing.Size(116, 31);
            this.textBox_4.TabIndex = 65;
            // 
            // label_5
            // 
            this.label_5.AutoSize = true;
            this.label_5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.label_5.Location = new System.Drawing.Point(13, 98);
            this.label_5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label_5.Name = "label_5";
            this.label_5.Size = new System.Drawing.Size(117, 25);
            this.label_5.TabIndex = 66;
            this.label_5.Text = "1封号|0解封:";
            // 
            // textBox_9
            // 
            this.textBox_9.ForeColor = System.Drawing.Color.Red;
            this.textBox_9.Location = new System.Drawing.Point(124, 319);
            this.textBox_9.Margin = new System.Windows.Forms.Padding(4);
            this.textBox_9.Name = "textBox_9";
            this.textBox_9.Size = new System.Drawing.Size(150, 31);
            this.textBox_9.TabIndex = 182;
            this.textBox_9.Text = "19";
            this.textBox_9.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label_10
            // 
            this.label_10.AutoSize = true;
            this.label_10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label_10.Location = new System.Drawing.Point(13, 320);
            this.label_10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label_10.Name = "label_10";
            this.label_10.Size = new System.Drawing.Size(86, 25);
            this.label_10.TabIndex = 178;
            this.label_10.Text = "GM权限:";
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(123, 258);
            this.textBox7.Margin = new System.Windows.Forms.Padding(4);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(151, 31);
            this.textBox7.TabIndex = 177;
            this.textBox7.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label15.Location = new System.Drawing.Point(13, 150);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(89, 25);
            this.label15.TabIndex = 8;
            this.label15.Text = "角色列表:";
            // 
            // button35
            // 
            this.button35.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button35.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button35.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button35.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button35.ForeColor = System.Drawing.Color.Black;
            this.button35.Location = new System.Drawing.Point(285, 202);
            this.button35.Margin = new System.Windows.Forms.Padding(4);
            this.button35.Name = "button35";
            this.button35.Size = new System.Drawing.Size(124, 39);
            this.button35.TabIndex = 167;
            this.button35.Text = "查看积分";
            this.button35.UseVisualStyleBackColor = false;
            this.button35.Click += new System.EventHandler(this.button35_Click);
            // 
            // listBox2
            // 
            this.listBox2.BackColor = System.Drawing.Color.White;
            this.listBox2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBox2.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.listBox2.ForeColor = System.Drawing.Color.Black;
            this.listBox2.FormattingEnabled = true;
            this.listBox2.ItemHeight = 24;
            this.listBox2.Location = new System.Drawing.Point(123, 150);
            this.listBox2.Margin = new System.Windows.Forms.Padding(4);
            this.listBox2.Name = "listBox2";
            this.listBox2.Size = new System.Drawing.Size(151, 74);
            this.listBox2.TabIndex = 7;
            this.listBox2.Click += new System.EventHandler(this.listBox2_Click);
            // 
            // textBox3
            // 
            this.textBox3.BackColor = System.Drawing.Color.White;
            this.textBox3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBox3.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox3.ForeColor = System.Drawing.Color.Black;
            this.textBox3.Location = new System.Drawing.Point(89, 46);
            this.textBox3.Margin = new System.Windows.Forms.Padding(4);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(185, 34);
            this.textBox3.TabIndex = 168;
            // 
            // button7
            // 
            this.button7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button7.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button7.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button7.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button7.ForeColor = System.Drawing.Color.Black;
            this.button7.Location = new System.Drawing.Point(285, 46);
            this.button7.Margin = new System.Windows.Forms.Padding(4);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(124, 34);
            this.button7.TabIndex = 9;
            this.button7.Text = "查找";
            this.button7.UseVisualStyleBackColor = false;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // button4
            // 
            this.button4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button4.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button4.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button4.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button4.ForeColor = System.Drawing.Color.Black;
            this.button4.Location = new System.Drawing.Point(285, 150);
            this.button4.Margin = new System.Windows.Forms.Padding(4);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(124, 39);
            this.button4.TabIndex = 166;
            this.button4.Text = "查看元宝";
            this.button4.UseVisualStyleBackColor = false;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // button38
            // 
            this.button38.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button38.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button38.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button38.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button38.ForeColor = System.Drawing.Color.Black;
            this.button38.Location = new System.Drawing.Point(285, 624);
            this.button38.Margin = new System.Windows.Forms.Padding(4);
            this.button38.Name = "button38";
            this.button38.Size = new System.Drawing.Size(124, 39);
            this.button38.TabIndex = 161;
            this.button38.Text = "增加天数";
            this.button38.UseVisualStyleBackColor = false;
            this.button38.Click += new System.EventHandler(this.button38_Click);
            // 
            // button11
            // 
            this.button11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button11.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button11.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button11.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button11.ForeColor = System.Drawing.Color.Black;
            this.button11.Location = new System.Drawing.Point(285, 260);
            this.button11.Margin = new System.Windows.Forms.Padding(4);
            this.button11.Name = "button11";
            this.button11.Size = new System.Drawing.Size(124, 34);
            this.button11.TabIndex = 0;
            this.button11.Text = "确认更改";
            this.button11.UseVisualStyleBackColor = false;
            this.button11.Click += new System.EventHandler(this.button11_Click);
            // 
            // textBox11
            // 
            this.textBox11.BackColor = System.Drawing.Color.White;
            this.textBox11.Location = new System.Drawing.Point(124, 624);
            this.textBox11.Margin = new System.Windows.Forms.Padding(4);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(150, 31);
            this.textBox11.TabIndex = 154;
            this.textBox11.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label8.Location = new System.Drawing.Point(12, 46);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(60, 28);
            this.label8.TabIndex = 165;
            this.label8.Text = "帐号:";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label17.Location = new System.Drawing.Point(13, 625);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(84, 25);
            this.label17.TabIndex = 153;
            this.label17.Text = "VIP天数:";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label18.Location = new System.Drawing.Point(13, 260);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(89, 25);
            this.label18.TabIndex = 4;
            this.label18.Text = "改新名字:";
            // 
            // button19
            // 
            this.button19.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button19.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button19.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button19.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button19.ForeColor = System.Drawing.Color.Black;
            this.button19.Location = new System.Drawing.Point(285, 378);
            this.button19.Margin = new System.Windows.Forms.Padding(4);
            this.button19.Name = "button19";
            this.button19.Size = new System.Drawing.Size(124, 39);
            this.button19.TabIndex = 145;
            this.button19.Text = "修改职业";
            this.button19.UseVisualStyleBackColor = false;
            this.button19.Click += new System.EventHandler(this.button19_Click);
            // 
            // textBox8
            // 
            this.textBox8.BackColor = System.Drawing.Color.White;
            this.textBox8.Location = new System.Drawing.Point(124, 380);
            this.textBox8.Margin = new System.Windows.Forms.Padding(4);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(150, 31);
            this.textBox8.TabIndex = 144;
            this.textBox8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBox8.MouseClick += new System.Windows.Forms.MouseEventHandler(this.textBox8_MouseClick);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label10.Location = new System.Drawing.Point(13, 440);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(89, 25);
            this.label10.TabIndex = 127;
            this.label10.Text = "玩家正邪:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label6.Location = new System.Drawing.Point(13, 380);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 25);
            this.label6.TabIndex = 143;
            this.label6.Text = "玩家职业:";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label11.Location = new System.Drawing.Point(13, 502);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(89, 25);
            this.label11.TabIndex = 128;
            this.label11.Text = "玩家转职:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label12.Location = new System.Drawing.Point(13, 562);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(89, 25);
            this.label12.TabIndex = 129;
            this.label12.Text = "玩家等级:";
            // 
            // button10
            // 
            this.button10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button10.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button10.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button10.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button10.ForeColor = System.Drawing.Color.Black;
            this.button10.Location = new System.Drawing.Point(285, 562);
            this.button10.Margin = new System.Windows.Forms.Padding(4);
            this.button10.Name = "button10";
            this.button10.Size = new System.Drawing.Size(124, 39);
            this.button10.TabIndex = 139;
            this.button10.Text = "修改等级";
            this.button10.UseVisualStyleBackColor = false;
            this.button10.Click += new System.EventHandler(this.button10_Click);
            // 
            // textBox4
            // 
            this.textBox4.BackColor = System.Drawing.Color.White;
            this.textBox4.Location = new System.Drawing.Point(124, 441);
            this.textBox4.Margin = new System.Windows.Forms.Padding(4);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(150, 31);
            this.textBox4.TabIndex = 132;
            this.textBox4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBox4.MouseClick += new System.Windows.Forms.MouseEventHandler(this.textBox4_MouseClick);
            // 
            // button9
            // 
            this.button9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button9.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button9.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button9.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button9.ForeColor = System.Drawing.Color.Black;
            this.button9.Location = new System.Drawing.Point(285, 500);
            this.button9.Margin = new System.Windows.Forms.Padding(4);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(124, 39);
            this.button9.TabIndex = 138;
            this.button9.Text = "修改转职";
            this.button9.UseVisualStyleBackColor = false;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // textBox5
            // 
            this.textBox5.BackColor = System.Drawing.Color.White;
            this.textBox5.Location = new System.Drawing.Point(124, 502);
            this.textBox5.Margin = new System.Windows.Forms.Padding(4);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(150, 31);
            this.textBox5.TabIndex = 133;
            this.textBox5.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBox5.MouseClick += new System.Windows.Forms.MouseEventHandler(this.textBox5_MouseClick);
            // 
            // button8
            // 
            this.button8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.button8.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button8.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button8.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button8.ForeColor = System.Drawing.Color.Black;
            this.button8.Location = new System.Drawing.Point(285, 439);
            this.button8.Margin = new System.Windows.Forms.Padding(4);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(124, 39);
            this.button8.TabIndex = 137;
            this.button8.Text = "修改正邪";
            this.button8.UseVisualStyleBackColor = false;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // textBox6
            // 
            this.textBox6.BackColor = System.Drawing.Color.White;
            this.textBox6.Location = new System.Drawing.Point(124, 563);
            this.textBox6.Margin = new System.Windows.Forms.Padding(4);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(150, 31);
            this.textBox6.TabIndex = 134;
            this.textBox6.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBox6.MouseClick += new System.Windows.Forms.MouseEventHandler(this.textBox6_MouseClick);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label21.Location = new System.Drawing.Point(49, 199);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(0, 24);
            this.label21.TabIndex = 173;
            // 
            // groupBox2
            // 
            this.groupBox2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.groupBox2.Controls.Add(this.comboBoxAttribute);
            this.groupBox2.Controls.Add(this.buttonConfirm);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.textBox2);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.groupBox2.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBox2.Location = new System.Drawing.Point(817, 487);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox2.Size = new System.Drawing.Size(521, 336);
            this.groupBox2.TabIndex = 65;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "属性修改";
            // 
            // comboBoxAttribute
            // 
            this.comboBoxAttribute.BackColor = System.Drawing.Color.White;
            this.comboBoxAttribute.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxAttribute.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBoxAttribute.ForeColor = System.Drawing.Color.Black;
            this.comboBoxAttribute.FormattingEnabled = true;
            this.comboBoxAttribute.Items.AddRange(new object[] {
            "增加元宝",
            "减少元宝",
            "增加积分",
            "减少积分",
            "增加金币",
            "减少金币",
            "增加防御",
            "减少防御",
            "增加攻击",
            "减少攻击",
            "增加武勋",
            "减少武勋",
            "增加历练",
            "减少历练",
            "增加内功",
            "减少内功",
            "增加生命",
            "减少生命",
            "增加回避",
            "减少回避",
            "减少命中",
            "增加武防",
            "减少武防",
            "增加命中"});
            this.comboBoxAttribute.Location = new System.Drawing.Point(135, 133);
            this.comboBoxAttribute.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxAttribute.Name = "comboBoxAttribute";
            this.comboBoxAttribute.Size = new System.Drawing.Size(307, 36);
            this.comboBoxAttribute.TabIndex = 128;
            // 
            // buttonConfirm
            // 
            this.buttonConfirm.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(240)))), ((int)(((byte)(255)))));
            this.buttonConfirm.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.buttonConfirm.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonConfirm.Font = new System.Drawing.Font("微软雅黑", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonConfirm.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.buttonConfirm.Location = new System.Drawing.Point(135, 187);
            this.buttonConfirm.Margin = new System.Windows.Forms.Padding(4);
            this.buttonConfirm.Name = "buttonConfirm";
            this.buttonConfirm.Size = new System.Drawing.Size(307, 45);
            this.buttonConfirm.TabIndex = 129;
            this.buttonConfirm.Text = "确认修改";
            this.buttonConfirm.UseVisualStyleBackColor = false;
            this.buttonConfirm.Click += new System.EventHandler(this.buttonConfirm_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label9.Location = new System.Drawing.Point(55, 72);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(69, 31);
            this.label9.TabIndex = 130;
            this.label9.Text = "数量:";
            // 
            // textBox2
            // 
            this.textBox2.BackColor = System.Drawing.Color.White;
            this.textBox2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBox2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox2.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox2.Location = new System.Drawing.Point(135, 69);
            this.textBox2.Margin = new System.Windows.Forms.Padding(4);
            this.textBox2.MaxLength = 68888;
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(307, 39);
            this.textBox2.TabIndex = 127;
            this.textBox2.Text = "0";
            this.textBox2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.label14.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label14.ForeColor = System.Drawing.Color.Gray;
            this.label14.Location = new System.Drawing.Point(56, 270);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(412, 27);
            this.label14.TabIndex = 131;
            this.label14.Text = "操作说明：请选择修改类型，输入数量后确认";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.BackColor = System.Drawing.Color.WhiteSmoke;
            this.label7.Font = new System.Drawing.Font("等线", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label7.ForeColor = System.Drawing.Color.Red;
            this.label7.Location = new System.Drawing.Point(815, 406);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(279, 57);
            this.label7.TabIndex = 125;
            this.label7.Text = "提示：强化最高99、属性最高10、\r\n\r\n      其他四个属性最高2个9";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label5.Location = new System.Drawing.Point(814, 354);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 25);
            this.label5.TabIndex = 124;
            this.label5.Text = "是否绑定:";
            // 
            // comboBox19
            // 
            this.comboBox19.BackColor = System.Drawing.Color.White;
            this.comboBox19.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox19.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox19.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.comboBox19.FormattingEnabled = true;
            this.comboBox19.Items.AddRange(new object[] {
            "不绑",
            "绑定"});
            this.comboBox19.Location = new System.Drawing.Point(936, 354);
            this.comboBox19.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox19.Name = "comboBox19";
            this.comboBox19.Size = new System.Drawing.Size(132, 32);
            this.comboBox19.TabIndex = 123;
            // 
            // textBox68
            // 
            this.textBox68.BackColor = System.Drawing.SystemColors.Window;
            this.textBox68.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox68.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox68.Location = new System.Drawing.Point(1204, 254);
            this.textBox68.Margin = new System.Windows.Forms.Padding(4);
            this.textBox68.MaxLength = 7;
            this.textBox68.Name = "textBox68";
            this.textBox68.Size = new System.Drawing.Size(119, 31);
            this.textBox68.TabIndex = 122;
            this.textBox68.Text = "0";
            this.textBox68.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBox67
            // 
            this.textBox67.BackColor = System.Drawing.SystemColors.Window;
            this.textBox67.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox67.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox67.Location = new System.Drawing.Point(1204, 209);
            this.textBox67.Margin = new System.Windows.Forms.Padding(4);
            this.textBox67.MaxLength = 7;
            this.textBox67.Name = "textBox67";
            this.textBox67.Size = new System.Drawing.Size(119, 31);
            this.textBox67.TabIndex = 121;
            this.textBox67.Text = "0";
            this.textBox67.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBox66
            // 
            this.textBox66.BackColor = System.Drawing.SystemColors.Window;
            this.textBox66.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox66.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox66.Location = new System.Drawing.Point(1204, 164);
            this.textBox66.Margin = new System.Windows.Forms.Padding(4);
            this.textBox66.MaxLength = 7;
            this.textBox66.Name = "textBox66";
            this.textBox66.Size = new System.Drawing.Size(119, 31);
            this.textBox66.TabIndex = 120;
            this.textBox66.Text = "0";
            this.textBox66.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBox65
            // 
            this.textBox65.BackColor = System.Drawing.SystemColors.Window;
            this.textBox65.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox65.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox65.Location = new System.Drawing.Point(1204, 119);
            this.textBox65.Margin = new System.Windows.Forms.Padding(4);
            this.textBox65.MaxLength = 7;
            this.textBox65.Name = "textBox65";
            this.textBox65.Size = new System.Drawing.Size(119, 31);
            this.textBox65.TabIndex = 119;
            this.textBox65.Text = "0";
            this.textBox65.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBox63
            // 
            this.textBox63.BackColor = System.Drawing.SystemColors.Window;
            this.textBox63.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox63.ForeColor = System.Drawing.Color.DarkOliveGreen;
            this.textBox63.Location = new System.Drawing.Point(1204, 29);
            this.textBox63.Margin = new System.Windows.Forms.Padding(4);
            this.textBox63.MaxLength = 7;
            this.textBox63.Name = "textBox63";
            this.textBox63.Size = new System.Drawing.Size(119, 31);
            this.textBox63.TabIndex = 117;
            this.textBox63.Text = "0";
            this.textBox63.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // comboBox18
            // 
            this.comboBox18.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox18.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox18.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox18.ForeColor = System.Drawing.Color.Black;
            this.comboBox18.FormattingEnabled = true;
            this.comboBox18.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "精炼追加生命",
            "精炼蓝红",
            "药品数量"});
            this.comboBox18.Location = new System.Drawing.Point(936, 253);
            this.comboBox18.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox18.MaxDropDownItems = 18;
            this.comboBox18.Name = "comboBox18";
            this.comboBox18.Size = new System.Drawing.Size(260, 32);
            this.comboBox18.TabIndex = 116;
            // 
            // comboBox17
            // 
            this.comboBox17.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox17.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox17.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox17.ForeColor = System.Drawing.Color.Black;
            this.comboBox17.FormattingEnabled = true;
            this.comboBox17.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "精炼追加生命",
            "精炼蓝红",
            "药品数量"});
            this.comboBox17.Location = new System.Drawing.Point(936, 208);
            this.comboBox17.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox17.MaxDropDownItems = 18;
            this.comboBox17.Name = "comboBox17";
            this.comboBox17.Size = new System.Drawing.Size(260, 32);
            this.comboBox17.TabIndex = 115;
            // 
            // comboBox16
            // 
            this.comboBox16.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox16.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox16.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox16.ForeColor = System.Drawing.Color.Black;
            this.comboBox16.FormattingEnabled = true;
            this.comboBox16.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "精炼追加生命",
            "精炼蓝红",
            "药品数量"});
            this.comboBox16.Location = new System.Drawing.Point(936, 163);
            this.comboBox16.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox16.MaxDropDownItems = 18;
            this.comboBox16.Name = "comboBox16";
            this.comboBox16.Size = new System.Drawing.Size(260, 32);
            this.comboBox16.TabIndex = 114;
            // 
            // comboBox15
            // 
            this.comboBox15.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox15.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox15.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox15.ForeColor = System.Drawing.Color.Black;
            this.comboBox15.FormattingEnabled = true;
            this.comboBox15.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "精炼追加生命",
            "精炼蓝红",
            "药品数量"});
            this.comboBox15.Location = new System.Drawing.Point(936, 118);
            this.comboBox15.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox15.MaxDropDownItems = 18;
            this.comboBox15.Name = "comboBox15";
            this.comboBox15.Size = new System.Drawing.Size(260, 32);
            this.comboBox15.TabIndex = 113;
            // 
            // comboBox14
            // 
            this.comboBox14.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox14.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox14.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox14.ForeColor = System.Drawing.Color.Black;
            this.comboBox14.FormattingEnabled = true;
            this.comboBox14.Items.AddRange(new object[] {
            "无",
            "火",
            "水",
            "风",
            "内",
            "外",
            "毒"});
            this.comboBox14.Location = new System.Drawing.Point(936, 73);
            this.comboBox14.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox14.MaxDropDownItems = 18;
            this.comboBox14.Name = "comboBox14";
            this.comboBox14.Size = new System.Drawing.Size(260, 32);
            this.comboBox14.TabIndex = 112;
            // 
            // comboBox13
            // 
            this.comboBox13.BackColor = System.Drawing.SystemColors.Info;
            this.comboBox13.DisplayMember = "1";
            this.comboBox13.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox13.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox13.ForeColor = System.Drawing.Color.Black;
            this.comboBox13.FormattingEnabled = true;
            this.comboBox13.Items.AddRange(new object[] {
            "无",
            "武器/戒指/强化",
            "防具/项链/强化",
            "耳环强化",
            "披风强化",
            "灵宠强化",
            "药品个数",
            "-----------------",
            "火属性石头",
            "水属性石头",
            "风属性石头",
            "毒属性石头",
            "内功属性石头",
            "外功属性石头",
            "-----------------",
            "攻击石头",
            "防御石头",
            "生命石头",
            "内功石头",
            "命中率石头",
            "回避率石头",
            "追加伤害石头",
            "获得金钱%石头",
            "武功防御力石头",
            "武功攻击力%石头",
            "经验值增加%石头",
            "升级成功率%石头",
            "全部气功等级石头",
            "死亡经验减少%石头"});
            this.comboBox13.Location = new System.Drawing.Point(936, 28);
            this.comboBox13.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox13.MaxDropDownItems = 26;
            this.comboBox13.Name = "comboBox13";
            this.comboBox13.Size = new System.Drawing.Size(260, 32);
            this.comboBox13.TabIndex = 111;
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label46.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label46.Location = new System.Drawing.Point(814, 254);
            this.label46.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(89, 25);
            this.label46.TabIndex = 110;
            this.label46.Text = "第四属性:";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label45.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label45.Location = new System.Drawing.Point(814, 209);
            this.label45.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(89, 25);
            this.label45.TabIndex = 109;
            this.label45.Text = "第三属性:";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label44.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label44.Location = new System.Drawing.Point(814, 163);
            this.label44.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(89, 25);
            this.label44.TabIndex = 108;
            this.label44.Text = "第二属性:";
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label43.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label43.Location = new System.Drawing.Point(814, 119);
            this.label43.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(89, 25);
            this.label43.TabIndex = 107;
            this.label43.Text = "第一属性:";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label41.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label41.Location = new System.Drawing.Point(814, 28);
            this.label41.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(89, 25);
            this.label41.TabIndex = 105;
            this.label41.Text = "石头属性:";
            // 
            // button1
            // 
            this.button1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.button1.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button1.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button1.ForeColor = System.Drawing.Color.White;
            this.button1.Location = new System.Drawing.Point(1114, 411);
            this.button1.Margin = new System.Windows.Forms.Padding(4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(209, 50);
            this.button1.TabIndex = 71;
            this.button1.Text = "确定发送";
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label3.Location = new System.Drawing.Point(457, 81);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(84, 28);
            this.label3.TabIndex = 185;
            this.label3.Text = "物品ID:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label1.Location = new System.Drawing.Point(457, 38);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(102, 28);
            this.label1.TabIndex = 187;
            this.label1.Text = "物品分类:";
            // 
            // comboBox12
            // 
            this.comboBox12.BackColor = System.Drawing.Color.White;
            this.comboBox12.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox12.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox12.ForeColor = System.Drawing.Color.Black;
            this.comboBox12.FormattingEnabled = true;
            this.comboBox12.Items.AddRange(new object[] {
            "武器",
            "衣服",
            "护手",
            "鞋子",
            "内甲",
            "耳环",
            "项链",
            "戒指",
            "男/女披风",
            "门派战甲",
            "灵兽召唤符",
            "石头类",
            "幸运符",
            "盒子/箱子",
            "礼包传书卷",
            "召唤书/碎片/弓箭",
            "宠物甲",
            "宠物盔",
            "宠物环",
            "其他"});
            this.comboBox12.Location = new System.Drawing.Point(565, 83);
            this.comboBox12.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox12.MaxDropDownItems = 33;
            this.comboBox12.Name = "comboBox12";
            this.comboBox12.Size = new System.Drawing.Size(241, 32);
            this.comboBox12.TabIndex = 65;
            // 
            // comboBox11
            // 
            this.comboBox11.BackColor = System.Drawing.Color.White;
            this.comboBox11.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox11.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox11.ForeColor = System.Drawing.Color.Black;
            this.comboBox11.FormattingEnabled = true;
            this.comboBox11.Items.AddRange(new object[] {
            "武器",
            "衣服",
            "护手",
            "鞋子",
            "内甲",
            "耳环",
            "项链",
            "戒指",
            "男/女披风",
            "门派战甲",
            "灵兽召唤符",
            "石头类",
            "幸运符",
            "盒子/箱子",
            "礼包传书卷",
            "召唤书/碎片/弓箭",
            "宠物甲",
            "宠物盔",
            "宠物环",
            "其他"});
            this.comboBox11.Location = new System.Drawing.Point(565, 35);
            this.comboBox11.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox11.MaxDropDownItems = 20;
            this.comboBox11.Name = "comboBox11";
            this.comboBox11.Size = new System.Drawing.Size(241, 32);
            this.comboBox11.TabIndex = 64;
            this.comboBox11.SelectedIndexChanged += new System.EventHandler(this.comboBox11_SelectedIndexChanged);
            // 
            // listbox1
            // 
            this.listbox1.BackColor = System.Drawing.Color.WhiteSmoke;
            this.listbox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listbox1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.listbox1.ForeColor = System.Drawing.Color.Black;
            this.listbox1.FormattingEnabled = true;
            this.listbox1.ItemHeight = 24;
            this.listbox1.Location = new System.Drawing.Point(448, 125);
            this.listbox1.Margin = new System.Windows.Forms.Padding(4);
            this.listbox1.Name = "listbox1";
            this.listbox1.Size = new System.Drawing.Size(358, 698);
            this.listbox1.TabIndex = 63;
            this.listbox1.SelectedIndexChanged += new System.EventHandler(this.listBox3_SelectedIndexChanged);
            // 
            // button34
            // 
            this.button34.Location = new System.Drawing.Point(0, 0);
            this.button34.Name = "button34";
            this.button34.Size = new System.Drawing.Size(75, 23);
            this.button34.TabIndex = 0;
            // 
            // button33
            // 
            this.button33.Location = new System.Drawing.Point(0, 0);
            this.button33.Name = "button33";
            this.button33.Size = new System.Drawing.Size(75, 23);
            this.button33.TabIndex = 0;
            // 
            // button32
            // 
            this.button32.Location = new System.Drawing.Point(0, 0);
            this.button32.Name = "button32";
            this.button32.Size = new System.Drawing.Size(75, 23);
            this.button32.TabIndex = 0;
            // 
            // button31
            // 
            this.button31.Location = new System.Drawing.Point(0, 0);
            this.button31.Name = "button31";
            this.button31.Size = new System.Drawing.Size(75, 23);
            this.button31.TabIndex = 0;
            // 
            // button30
            // 
            this.button30.Location = new System.Drawing.Point(0, 0);
            this.button30.Name = "button30";
            this.button30.Size = new System.Drawing.Size(75, 23);
            this.button30.TabIndex = 0;
            // 
            // button29
            // 
            this.button29.Location = new System.Drawing.Point(0, 0);
            this.button29.Name = "button29";
            this.button29.Size = new System.Drawing.Size(75, 23);
            this.button29.TabIndex = 0;
            // 
            // button28
            // 
            this.button28.Location = new System.Drawing.Point(0, 0);
            this.button28.Name = "button28";
            this.button28.Size = new System.Drawing.Size(75, 23);
            this.button28.TabIndex = 0;
            // 
            // button27
            // 
            this.button27.Location = new System.Drawing.Point(0, 0);
            this.button27.Name = "button27";
            this.button27.Size = new System.Drawing.Size(75, 23);
            this.button27.TabIndex = 0;
            // 
            // button26
            // 
            this.button26.Location = new System.Drawing.Point(0, 0);
            this.button26.Name = "button26";
            this.button26.Size = new System.Drawing.Size(75, 23);
            this.button26.TabIndex = 0;
            // 
            // button25
            // 
            this.button25.Location = new System.Drawing.Point(0, 0);
            this.button25.Name = "button25";
            this.button25.Size = new System.Drawing.Size(75, 23);
            this.button25.TabIndex = 0;
            // 
            // button24
            // 
            this.button24.Location = new System.Drawing.Point(0, 0);
            this.button24.Name = "button24";
            this.button24.Size = new System.Drawing.Size(75, 23);
            this.button24.TabIndex = 0;
            // 
            // button23
            // 
            this.button23.Location = new System.Drawing.Point(0, 0);
            this.button23.Name = "button23";
            this.button23.Size = new System.Drawing.Size(75, 23);
            this.button23.TabIndex = 0;
            // 
            // button21
            // 
            this.button21.Location = new System.Drawing.Point(0, 0);
            this.button21.Name = "button21";
            this.button21.Size = new System.Drawing.Size(75, 23);
            this.button21.TabIndex = 0;
            // 
            // button18
            // 
            this.button18.Location = new System.Drawing.Point(0, 0);
            this.button18.Name = "button18";
            this.button18.Size = new System.Drawing.Size(75, 23);
            this.button18.TabIndex = 0;
            // 
            // button17
            // 
            this.button17.Location = new System.Drawing.Point(0, 0);
            this.button17.Name = "button17";
            this.button17.Size = new System.Drawing.Size(75, 23);
            this.button17.TabIndex = 0;
            // 
            // button16
            // 
            this.button16.Location = new System.Drawing.Point(0, 0);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(75, 23);
            this.button16.TabIndex = 0;
            // 
            // button15
            // 
            this.button15.Location = new System.Drawing.Point(0, 0);
            this.button15.Name = "button15";
            this.button15.Size = new System.Drawing.Size(75, 23);
            this.button15.TabIndex = 0;
            // 
            // button14
            // 
            this.button14.Location = new System.Drawing.Point(0, 0);
            this.button14.Name = "button14";
            this.button14.Size = new System.Drawing.Size(75, 23);
            this.button14.TabIndex = 0;
            // 
            // button13
            // 
            this.button13.Location = new System.Drawing.Point(0, 0);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(75, 23);
            this.button13.TabIndex = 0;
            // 
            // button12
            // 
            this.button12.Location = new System.Drawing.Point(0, 0);
            this.button12.Name = "button12";
            this.button12.Size = new System.Drawing.Size(75, 23);
            this.button12.TabIndex = 0;
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(0, 0);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(75, 23);
            this.button6.TabIndex = 0;
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(0, 0);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(75, 23);
            this.button5.TabIndex = 0;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(0, 0);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(75, 23);
            this.button3.TabIndex = 0;
            // 
            // button2
            // 
            this.button2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(240)))), ((int)(((byte)(255)))));
            this.button2.FlatAppearance.BorderColor = System.Drawing.Color.LightGray;
            this.button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.button2.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.button2.ForeColor = System.Drawing.Color.Black;
            this.button2.Location = new System.Drawing.Point(9, 105);
            this.button2.Margin = new System.Windows.Forms.Padding(4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(115, 45);
            this.button2.TabIndex = 126;
            this.button2.Text = "增加元宝";
            this.button2.UseVisualStyleBackColor = false;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // textBox13
            // 
            this.textBox13.BackColor = System.Drawing.Color.White;
            this.textBox13.Location = new System.Drawing.Point(125, 664);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(123, 28);
            this.textBox13.TabIndex = 171;
            // 
            // statusStrip1
            // 
            this.statusStrip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.tishi});
            this.statusStrip1.Location = new System.Drawing.Point(0, 827);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(2, 0, 21, 0);
            this.statusStrip1.Size = new System.Drawing.Size(1362, 31);
            this.statusStrip1.TabIndex = 64;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.toolStripStatusLabel1.ForeColor = System.Drawing.Color.Black;
            this.toolStripStatusLabel1.LinkColor = System.Drawing.Color.Red;
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(208, 24);
            this.toolStripStatusLabel1.Text = "属性信息修改成功提示：";
            // 
            // tishi
            // 
            this.tishi.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tishi.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.tishi.Name = "tishi";
            this.tishi.Size = new System.Drawing.Size(0, 24);
            // 
            // GMGJ
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.ClientSize = new System.Drawing.Size(1362, 858);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "GMGJ";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show;
            this.Text = "在线GM工具";
            this.Load += new System.EventHandler(this.GMGJ_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void button4_Click(object sender, EventArgs e)
	{
		if (textBox3.Text == string.Empty)
		{
			tishi.Text = "请输入玩家账号";
			return;
		}
		string sqlCommand = "select FLD_RXPIONT FROM [TBL_ACCOUNT] where FLD_ID = '" + textBox3.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
		if (dBToDataTable.Rows.Count >= 1)
		{
			tishi.Text = "当前玩家元宝数量为:" + dBToDataTable.Rows[0]["FLD_RXPIONT"].ToString();
		}
		else
		{
			tishi.Text = "你输入的账号错误!";
		}
	}

	private void button35_Click(object sender, EventArgs e)
	{
		if (textBox3.Text == string.Empty)
		{
			tishi.Text = "请输入玩家账号";
			return;
		}
		string sqlCommand = "select FLD_JF FROM [TBL_ACCOUNT] where FLD_ID = '" + textBox3.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
		if (dBToDataTable.Rows.Count >= 1)
		{
			tishi.Text = "当前玩家积分数量为:" + dBToDataTable.Rows[0]["FLD_JF"].ToString();
		}
		else
		{
			tishi.Text = "你输入的账号错误!";
		}
	}

	private void button11_Click(object sender, EventArgs e)
	{
		if (textBox1.Text.Trim() == string.Empty || textBox7.Text.Trim() == string.Empty)
		{
			tishi.Text = "玩家名字不能为空";
			return;
		}
		Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			tishi.Text = "当前玩家在线,请离线后在更改";
			return;
		}
		string sqlCommand = "SELECT * FROM TBL_XWWL_GuildMember WHERE name ='" + textBox1.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
		string sqlCommand2 = "SELECT * FROM TBL_XWWL_Guild WHERE G_Master ='" + textBox1.Text + "'";
		DataTable dBToDataTable2 = DBA.GetDBToDataTable(sqlCommand2);
		if (dBToDataTable.Rows.Count > 0 || dBToDataTable2.Rows.Count > 0)
		{
			tishi.Text = "请通知玩家退出门派后在更名";
			return;
		}
		string sqlCommand3 = "UPDATE TBL_XWWL_Char SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
		SqlParameter[] prams = new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
			SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox7.Text)
		};
		DBA.ExeSqlCommand(sqlCommand3, prams);
		sqlCommand3 = "UPDATE TBL_XWWL_Warehouse SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
		SqlParameter[] prams2 = new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
			SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox7.Text)
		};
		DBA.ExeSqlCommand(sqlCommand3, prams2);
		tishi.Text = "换名成功请进入游戏查看";
		listBox2.Items.Clear();
		string sqlCommand4 = "select * from TBL_XWWL_Char  where fld_id='" + textBox3.Text + "'";
		DataTable dBToDataTable3 = DBA.GetDBToDataTable(sqlCommand4);
		for (int i = 0; i < dBToDataTable3.Rows.Count; i++)
		{
			listBox2.Items.Add(dBToDataTable3.Rows[i]["FLD_NAME"].ToString());
		}
	}

	private void button7_Click(object sender, EventArgs e)
	{
		if (textBox3.Text == string.Empty)
		{
			tishi.Text = "请输入玩家账号";
			return;
		}
		listBox2.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_Char  where fld_id='" + textBox3.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			listBox2.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
		}
		tishi.Text = "查询完毕";
	}

	private void listBox2_Click(object sender, EventArgs e)
	{
		textBox1.Text = listBox2.Text;
	}

	private void button_3_Click(object sender, EventArgs e)
	{
		if (textBox1.Text.Trim() == string.Empty)
		{
			tishi.Text = "玩家名字不能为空";
			return;
		}
		Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			tishi.Text = "当前玩家在线,请离线后在更改";
			return;
		}
		string arg = textBox1.Text;
		string arg2 = textBox_9.Text;
		string sqlCommand = string.Format("UPDATE TBL_XWWL_Char SET FLD_J9 = '{1}' WHERE FLD_NAME = '{0}'", arg, arg2);
		DBA.GetDBToDataTable(sqlCommand, "GameServer");
		tishi.Text = "设置GM权限成功";
	}

	private void button_1_Click(object sender, EventArgs e)
	{
		if (textBox3.Text == string.Empty)
		{
			tishi.Text = "请输入玩家账号";
			return;
		}
		Players players = World.检查玩家name(textBox1.Text);
		if (players != null)
		{
			tishi.Text = "当前玩家在线,请离线后在更改";
			return;
		}
		string arg = textBox3.Text;
		string arg2 = textBox_4.Text;
		string sqlCommand = string.Format("UPDATE TBL_ACCOUNT SET FLD_ZT = '{1}' WHERE FLD_ID = '{0}'", arg, arg2);
		DBA.GetDBToDataTable(sqlCommand, "rxjhaccount");
		tishi.Text = "修改成功!";
	}

	private void buttonConfirm_Click(object sender, EventArgs e)
	{
	    if (comboBoxAttribute.SelectedIndex == -1)
	    {
	        tishi.Text = "请选择属性修改类型";
	        return;
	    }
	    
	    switch (comboBoxAttribute.SelectedIndex)
	    {
	        case 0: // 增加元宝
	            button2_Click(sender, e);
	            break;
	        case 1: // 减少元宝
	            button25_Click(sender, e);
	            break;
	        case 2: // 增加积分
	            button33_Click(sender, e);
	            break;
	        case 3: // 减少积分
	            button34_Click(sender, e);
	            break;
	        case 4: // 增加金币
	            button3_Click(sender, e);
	            break;
	        case 5: // 减少金币
	            button21_Click(sender, e);
	            break;
	        case 6: // 增加防御
	            button13_Click(sender, e);
	            break;
	        case 7: // 减少防御
	            button27_Click(sender, e);
	            break;
	        case 8: // 增加攻击
	            button12_Click(sender, e);
	            break;
	        case 9: // 减少攻击
	            button26_Click(sender, e);
	            break;
	        case 10: // 增加武勋
	            button6_Click(sender, e);
	            break;
	        case 11: // 减少武勋
	            button24_Click(sender, e);
	            break;
	        case 12: // 增加历练
	            button5_Click(sender, e);
	            break;
	        case 13: // 减少历练
	            button23_Click(sender, e);
	            break;
	        case 14: // 增加内功
	            button16_Click(sender, e);
	            break;
	        case 15: // 减少内功
	            button30_Click(sender, e);
	            break;
	        case 16: // 增加生命
	            button15_Click(sender, e);
	            break;
	        case 17: // 减少生命
	            button29_Click(sender, e);
	            break;
	        case 18: // 增加回避
	            button18_Click(sender, e);
	            break;
	        case 19: // 减少回避
	            button32_Click(sender, e);
	            break;
	        case 20: // 减少命中
	            button31_Click(sender, e);
	            break;
	        case 21: // 增加武防
	            button14_Click(sender, e);
	            break;
	        case 22: // 减少武防
	            button28_Click(sender, e);
	            break;
	        case 23: // 增加命中
	            button17_Click(sender, e);
	            break;
	        default:
	            tishi.Text = "未知操作类型";
	            break;
	    }
	}

}

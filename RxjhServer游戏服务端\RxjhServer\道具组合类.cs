using System.Collections.Generic;

namespace RxjhServer;

public class 道具组合类
{
	public int 物品ID;

	public string 物品名;

	public Dictionary<int, int> 需要物品 = new Dictionary<int, int>();

	public 道具组合类(int pid, string wp)
	{
		物品ID = pid;
		需要物品.Clear();
		if (wp.Length <= 0)
		{
			return;
		}
		string[] array = wp.Split('/');
		foreach (string text in array)
		{
			if (text.Length > 0)
			{
				string[] array2 = text.Split(',');
				if (array2.Length > 1)
				{
					int key = int.Parse(array2[0]);
					int value = int.Parse(array2[1]);
					需要物品.Add(key, value);
				}
			}
		}
	}
}

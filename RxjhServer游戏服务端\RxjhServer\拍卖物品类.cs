using System;
namespace RxjhServer;


public class 拍卖物品类
{

	public long ID;

	public long FLD_物品ID;

	public string FLD_物品名称;

	public int FLD_数量;

	public int FLD_物品类型;

	public int FLD_物品性别;

	public int FLD_物品基础防御;

	public int FLD_物品基础最小攻击;

	public int FLD_物品基础最大攻击;

	public int FLD_物品等级;

	public int FLD_物品正邪;

	public int FLD_需求转职等级;

	public int FLD_属性0;

	public int FLD_属性1;

	public int FLD_属性2;

	public int FLD_属性3;

	public int FLD_属性4;

	public int FLD_强化类型;

	public int FLD_强化数量;

	public int FLD_属性类型;

	public int FLD_属性数量;

	public DateTime FLD_物品上架时间;

	public DateTime FLD_物品下架时间;

	public int FLD_一口价;

	public int FLD_物品低价;

	public int FLD_每次加价;

	public int FLD_当前竞价;

	public string FLD_拍品状态;

	public string FLD_竞拍者;

	public string FLD_拍卖者;

	public string FLD_参与者;

	public int FLD_竞拍次数;

	public int FLD_觉醒;

	public int FLD_中级附魂;

	public int FLD_进化;

	public string FLD_FQ;
}

namespace RxjhVer23;

public interface INewWebShopSaleItem
{
	int FLD_SALE_ID { get; set; }

	int FLD_ITEM_ID { get; set; }

	string FLD_ITEM_NAME { get; set; }

	string FLD_ITEM_DESC { get; set; }

	int FLD_SALE_PRICE { get; set; }

	int FLD_SALE_PRICE_TYPE { get; set; }

	int FLD_SALE_TYPE1 { get; set; }

	int FLD_SALE_TYPE2 { get; set; }

	int FLD_ITEM_MAGIC { get; set; }

	int FLD_ITEM_MAGIC1 { get; set; }

	int FLD_ITEM_MAGIC2 { get; set; }

	int FLD_ITEM_MAGIC3 { get; set; }

	int FLD_ITEM_MAGIC4 { get; set; }

	int FLD_INTERMEDIATE_SOUL_ATTRIBUTE { get; set; }

	int FLD_SOUL_AWAKENING_STAGE { get; set; }

	int FLD_EVOLUTION_LEVEL { get; set; }

	int FLD_FOUR_GODS_POWER { get; set; }

	int FLD_IS_BOUND { get; set; }

	int FLD_AVAILABLE_DAYS { get; set; }
}

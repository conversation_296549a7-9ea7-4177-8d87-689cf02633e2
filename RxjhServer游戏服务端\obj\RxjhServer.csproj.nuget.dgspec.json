{"format": 1, "restore": {"E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj": {}}, "projects": {"E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj", "projectName": "RxjhServer", "projectPath": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\RxjhServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\24.0断线重连\\断线重连设计\\24服务端已升级\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"FreeSql.Provider.SqlServerForSystem": {"target": "Package", "version": "[3.5.202, )"}, "HPSocket.Net": {"target": "Package", "version": "[6.0.3.1, )"}, "KeraLua": {"target": "Package", "version": "[1.4.4, )"}, "NLua": {"target": "Package", "version": "[1.7.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Resources.Extensions": {"target": "Package", "version": "[9.0.6, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}
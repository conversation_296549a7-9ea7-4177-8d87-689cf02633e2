using System;
using System.Net.Sockets;
using System.Text;
using static System.Net.Mime.MediaTypeNames;

namespace RxjhServer.bbg;

public class ClientConnection : SockClienT
{
	public ClientConnection(Socket sock, RemoveClientDelegateE rcd)
		: base(sock, rcd)
	{
	}

	public override void ProcessDataReceived(byte[] data, int length)
	{
		int num = 0;
		if (170 != data[0] || 136 != data[1])
		{
			return;
		}
		byte[] array = new byte[4];
		Buffer.BlockCopy(data, 2, array, 0, 4);
		int num2 = BitConverter.ToInt32(array, 0);
		if (length < num2 + 6)
		{
			return;
		}
		while (true)
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "ProcessDataReceived");
			}
			byte[] array2 = new byte[num2];
			Buffer.BlockCopy(data, num + 6, array2, 0, num2);
			num += num2 + 6;
			DataReceived(array2, num2);
			if (num < length && data[num] == 170 && data[num + 1] == 136)
			{
				Buffer.BlockCopy(data, num + 2, array, 0, 4);
				num2 = BitConverter.ToInt16(array, 0);
				continue;
			}
			break;
		}
	}

	public void DataReceived(byte[] data, int length)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "ProcessDataReceived()");
		}
		try
		{
			byte[] array = new byte[length];
			for (int i = 0; i < length; i++)
			{
				array[i] = data[i];
			}
			string @string = Encoding.Default.GetString(array);
			string str = "-1";
			string[] array2 = @string.Split(',');
			switch (array2[0])
			{			
			// 2025-05-19: 添加前端需要的查询接口						
			case "EV用户登陆1": 
			{
				Players players11 = World.检查玩家(array2[1]);
				str = ((players11 != null) ? "登陆成功" : "登陆失败");
				break;
			}
			case "EV查询余额2": 
			{
				Players players_query = World.检查玩家(array2[1]);
				if (players_query == null)
				{
					str = "-1";
				}
				else
				{
					players_query.查百宝阁元宝数();
					str = players_query.UserName + "," + players_query.FLD_RXPIONT + "," +
							players_query.FLD_RXPIONTX + "," + players_query.得到包裹空位数() + "," +
							players_query.FLD_Coin;
				}
				break;
			}
			case "EV获取背包1":
			{
				Players players_bag1 = World.检查玩家(array2[1]);
				if (players_bag1 == null)
				{
					str = "-1";
				}
				else
				{
					str = players_bag1.获取背包数据(1);
				}
				break;
			}
			case "EV获取背包2":
			{
				Players players_bag2 = World.检查玩家(array2[1]);
				if (players_bag2 == null)
				{
					str = "-1";
				}
				else
				{
					str = players_bag2.获取背包数据(2);
				}
				break;
			}
			case "EV获取背包3":
			{
				Players players_bag3 = World.检查玩家(array2[1]);
				if (players_bag3 == null)
				{
					str = "-1";
				}
				else
				{
					str = players_bag3.获取背包数据(3);
				}
				break;
			}
			case "EV获取背包4":
			{
				Players players_bag4 = World.检查玩家(array2[1]);
				if (players_bag4 == null)
				{
					str = "-1";
				}
				else
				{
					str = players_bag4.获取背包数据(4);
				}
				break;
			}
			case "EV商城购买5":
			{
				Players players_buy = World.检查玩家(array2[1]);
				if (players_buy != null && array2.Length >= 6)
				{
					int 物品ID = int.Parse(array2[2]);
					int 数量 = int.Parse(array2[3]);
					int 总价 = int.Parse(array2[4]);
					int 类型 = int.Parse(array2[5]);

					str = players_buy.百宝阁买卖东西(物品ID, 数量, 总价, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
				else
				{
					str = "-1";
				}
				break;
			}
            case "EV拍卖上架6":
            {
                Players players_add = World.检查玩家(array2[1]);
                if (players_add != null && array2.Length >= 8)
                {
                    str = players_add.添加拍卖物品(int.Parse(array2[2]), int.Parse(array2[3]),
                        int.Parse(array2[4]), int.Parse(array2[5]), int.Parse(array2[6]), int.Parse(array2[7]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV一口价购买7":
            {
                Players players_buy = World.检查玩家(array2[1]);
                if (players_buy != null && array2.Length >= 3)
                {
                    str = players_buy.一口价购买(int.Parse(array2[2]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV一口价领取8":
            {
                Players players_get = World.检查玩家(array2[1]);
                if (players_get != null && array2.Length >= 3)
                {
                    str = players_get.一口价领取(int.Parse(array2[2]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV领取收益9":
            {
                Players players_profit = World.检查玩家(array2[1]);
                if (players_profit != null && array2.Length >= 3)
                {
                    str = players_profit.领取收益(int.Parse(array2[2]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV物品下架10":
            {
                Players players_remove = World.检查玩家(array2[1]);
                if (players_remove != null && array2.Length >= 3)
                {
                    str = players_remove.物品下架(int.Parse(array2[2]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV参与竞拍11":
            {
                // 参数: 用户ID,拍品ID,竞拍价格
                Players players_bid = World.检查玩家(array2[1]);
                if (players_bid != null && array2.Length >= 4)
                {
                    str = players_bid.参与竞拍(int.Parse(array2[2]), int.Parse(array2[3]));
                }
                else
                {
                    str = "-1";
                }
                break;
            }
            case "EV领取元宝12":
			{
				Players players_getpoint = World.检查玩家(array2[1]);
				if (players_getpoint != null && array2.Length >= 4)
				{
					int 类型 = int.Parse(array2[2]);
					int 数量 = int.Parse(array2[3]);
					str = "领取成功";
				}
				else
				{
					str = "-1";
				}
				break;
			}
			case "EV地图传送13":
				{
					Players players_move = World.检查玩家(array2[1]);
					if (players_move != null && array2.Length >= 5)
					{
						int 地图ID = int.Parse(array2[2]);
						int X坐标 = int.Parse(array2[3]);
						int Y坐标 = int.Parse(array2[4]);
						str = players_move.地图传送(地图ID, X坐标, Y坐标);
					}
					else
					{
						str = "-1";
					}
					break;
				}
			case "EV查询坐标14":
				{
					Players players_coord = World.检查玩家(array2[1]);
					if (players_coord != null)
					{
						string 地图名称 = 坐标Class.getmapname(players_coord.人物坐标_地图);
						if (string.IsNullOrEmpty(地图名称))
						{
							地图名称 = "未知地图";
						}
						str = $"{地图名称},{players_coord.人物坐标_X},{players_coord.人物坐标_Y},{players_coord.Player_Money},{players_coord.人物坐标_地图}";
					}
					else
					{
						str = "-1";
					}
					break;
				}

			case "EV查询累充15":
			{
				Players players4 = World.检查玩家(array2[1]);
				if (World.检查玩家(array2[1]) == null)
				{
					str = "-1";
					break;
				}
				players4.查累充数据();
				str = players4.FLD_账号累充.ToString();
				break;
			}
			case "EV百宝抽奖16":
			{
				Players players6 = World.检查玩家(array2[1]);
				str = ((players6 != null) ? players6.抽奖系统(array2[2], int.Parse(array2[3])) : "-1");
				break;
			}
			case "EV累充礼包17":
			{
				Players players9 = World.检查玩家(array2[1]);
				str = ((players9 == null) ? "-1" : ((players9 != null) ? players9.领取累充礼包(int.Parse(array2[2])) : "-1"));
				break;
			}
			case "EV兑换货币18":
			{
				str = World.检查玩家(array2[1]).货币兑换(int.Parse(array2[2]), int.Parse(array2[3]));
				break;
			}
			case "EV获取兑换配置19":
			{
				// 返回兑换配置：钻石兑换元宝开关,元宝兑换钻石开关,钻石兑换元宝比例,元宝兑换钻石比例
				str = World.钻石兑换元宝是否开启 + "," + World.元宝兑换钻石是否开启 + "," +
					  World.钻石兑换元宝比例 + "," + World.元宝兑换钻石比例;
				break;
			}
			case "EV获取抽奖配置21":
			{
				// 2025-06-14: 返回抽奖价格配置：单抽价格,十抽价格
				str = World.单抽价格 + "," + World.十抽价格;
				break;
			}
			case "EV获取拍卖配置22":
			{
				// 2025-06-16: 返回完整拍卖配置  EVIAS
				str = World.最大拍卖数量 + "," + World.拍卖持续天数 + "," + World.拍卖手续费比例 + "," +
					  World.拍卖自动结束倍数 + "," + World.拍卖超价结束时间 + "," + World.拍卖记录清理天数;
				break;
			}

			case "兑换披风一":
			case "兑换披风二":
			case "兑换披风三":
			case "兑换披风四":
			case "兑换披风五":
			case "兑换披风六":
			case "兑换披风七":
			case "兑换披风八":
			case "兑换披风九":
			case "兑换披风十":
			case "兑换披风十一":
			case "兑换披风十二":
			case "兑换披风十三":
			case "兑换披风十四":
			case "兑换披风十五":
			case "兑换披风十六":
			case "兑换披风十七":
			case "兑换披风十八":
			{
				int 档次 = 0;
				switch (array2[0])
				{
					case "兑换披风一":
						档次 = 1;
						break;
					case "兑换披风二":
						档次 = 2;
						break;
					case "兑换披风三":
						档次 = 3;
						break;
					case "兑换披风四":
						档次 = 4;
						break;
					case "兑换披风五":
						档次 = 5;
						break;
					case "兑换披风六":
						档次 = 6;
						break;
					case "兑换披风七":
						档次 = 7;
						break;
					case "兑换披风八":
						档次 = 8;
						break;
					case "兑换披风九":
						档次 = 9;
						break;
					case "兑换披风十":
						档次 = 10;
						break;
					case "兑换披风十一":
						档次 = 11;
						break;
					case "兑换披风十二":
						档次 = 12;
						break;
					case "兑换披风十三":
						档次 = 13;
						break;
					case "兑换披风十四":
						档次 = 14;
						break;
					case "兑换披风十五":
						档次 = 15;
						break;
					case "兑换披风十六":
						档次 = 16;
						break;
					case "兑换披风十七":
						档次 = 17;
						break;
					case "兑换披风十八":
						档次 = 18;
						break;
				}
				Players players = World.检查玩家(array2[1]);
				str = ((players != null) ? players.神器兑换礼包(档次) : "-1");
				break;
			}

			default:
			str = "-1";
			break;
			}
			Sendd(str);
		}
		catch (Exception)
		{
			Dispose();
		}
	}
}

using System;
using System.Collections.Generic;

namespace RxjhServer
{
    public class 披风鉴定系统
    {
        // 鉴定成功概率
        private const int 普通披风升级概率 = 20; // 20%概率
        private const int 斗战披风升级概率 = 5; // 5%概率
        
        // 奖励物品ID
        private const int 披风强化布ID = 1000000545;
        private const int 披风鉴定石ID = 900000224;

        private static readonly HashSet<int> 顶级披风IDs = new HashSet<int> { 16900464, 26900464 };

        public static bool 是斗战披风(int 物品ID)
        {
            if (World.Itme.TryGetValue(物品ID, out ItmeClass 物品) &&
                物品.ItmeDES != null &&
                物品.ItmeDES.Contains("激活斗战披风"))
            {
                return true;
            }
            return false;
        }

        public static bool 是顶级披风(int 物品ID)
        {
            if (顶级披风IDs.Contains(物品ID))
            {
                return true;
            }

            if (World.Itme.TryGetValue(物品ID, out ItmeClass 物品) &&
                物品.ItmeDES != null &&
                物品.ItmeDES.Contains("顶级斗战披风"))
            {
                return true;
            }

            return false;
        }
        
        public static bool 是普通披风(int 物品ID)
        {
            if (World.Itme.TryGetValue(物品ID, out ItmeClass 物品) &&
                物品.FLD_RESIDE2 == 12 &&
                !是斗战披风(物品ID) &&
                !是顶级披风(物品ID))
            {
                return true;
            }
            return false;
        }

        public static bool 检查披风(Players player, out string 错误信息)
        {
            错误信息 = string.Empty;
            
            if (player.装备栏包裹[0] == null)
            {
                错误信息 = "请将要鉴定的披风放到背包第一格！";
                return false;
            }
            
            int 物品ID = BitConverter.ToInt32(player.装备栏包裹[0].物品ID, 0);
            
            if (!World.Itme.TryGetValue(物品ID, out ItmeClass 物品) || 物品.FLD_RESIDE2 != 12)
            {
                错误信息 = "第一格物品不是披风，无法鉴定！";
                return false;
            }
            
            if (是顶级披风(物品ID))
            {
                错误信息 = "顶级披风已达最高级，无法继续鉴定！";
                return false;
            }
            
            if (player.装备栏包裹[0].物品绑定)
            {
                错误信息 = "绑定的披风不能鉴定！";
                return false;
            }            
            return true;
        }
        
        public static void 鉴定披风(Players player)
        {
            string 错误信息;
            if (!检查披风(player, out 错误信息))
            {
                player.系统提示(错误信息, 7, "披风鉴定");
                return;
            }

            int 物品ID = BitConverter.ToInt32(player.装备栏包裹[0].物品ID, 0);

            if (是顶级披风(物品ID))
            {
                player.系统提示("顶级披风已达最高级，无法继续鉴定！", 7, "披风鉴定");
                return; 
            }
                        
            int 鉴定石位置 = player.得到包裹物品位置(披风鉴定石ID);
            player.减去物品(鉴定石位置, 1);
            
            int 成功概率;
            bool 鉴定成功 = false;
                        
            if (是斗战披风(物品ID))
            {
                成功概率 = 斗战披风升级概率;
                
                if (World.披风鉴定随机数生成器.Next(1, 101) <= 成功概率)
                {
                    鉴定成功 = true;
                    处理斗战披风成功(player, 物品ID);
             
                }
                else
                {
                    处理斗战披风失败(player);
                    
                }
            }
            else
            {
                成功概率 = 普通披风升级概率;
                
                if (World.披风鉴定随机数生成器.Next(1, 101) <= 成功概率)
                {
                    鉴定成功 = true;
                    处理普通披风成功(player, 物品ID);
                }
                else
                {
                    处理普通披风失败(player);
                    
                }
            }
            
            if (!鉴定成功)
            {
                player.系统提示("鉴定失败！请再接再厉", 7, "披风鉴定");
            }
        }

        private static void 处理普通披风成功(Players player, int 原物品ID)
        {
            player.减去物品(0, 1);

            int 新物品ID = 随机获取披风(false); 
            int 空位 = player.得到包裹空位位置();
            player.增加物品带属性(新物品ID, 空位, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);


            player.系统提示($"恭喜！成功将披风鉴定为斗战披风", 10, "披风鉴定");
            World.全局提示("披风鉴定", 10, $"恭喜玩家「{player.UserName}」成功将披风鉴定为斗战披风，实力大增！");
        }

        private static void 处理斗战披风成功(Players player, int 原物品ID)
        {
            player.减去物品(0, 1);

            int 新物品ID = 随机获取顶级披风();
            int 空位 = player.得到包裹空位位置();
            player.增加物品带属性(新物品ID, 空位, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

            player.系统提示($"恭喜！成功将披风鉴定为顶级披风", 10, "披风鉴定");
            World.全局提示("披风鉴定", 10, $"恭喜玩家「{player.UserName}」成功将披风鉴定为顶级披风，实力大增！");
        }

        private static void 处理普通披风失败(Players player)
        {
            player.减去物品(0, 1);

            // 30%概率获得1个披风强化布
            bool 获得强化布 = false;
            if (World.披风鉴定随机数生成器.Next(1, 101) <= 30)
            {
                int 空位 = player.得到包裹空位位置();
                if (空位 >= 0)
                {
                    player.增加物品带属性(披风强化布ID, 空位, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    获得强化布 = true;
                    player.系统提示("鉴定失败，获得了1个披风强化布", 7, "披风鉴定");
                }
            }

            if (!获得强化布)
            {
                player.系统提示("鉴定失败，请再接再厉", 7, "披风鉴定");
            }
        }

        private static void 处理斗战披风失败(Players player)
        {
            player.减去物品(0, 1);

            bool 获得强化布 = false;
            int 强化布数量 = 0;
            
            int 随机值 = World.披风鉴定随机数生成器.Next(1, 101);
            
            // 40%概率获得1个强化布
            if (随机值 <= 40)
            {
                获得强化布 = true;
                强化布数量 = 1;
                
                int 空位 = player.得到包裹空位位置();
                if (空位 >= 0)
                {
                    player.增加物品带属性(披风强化布ID, 空位, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    player.系统提示("鉴定失败，获得了1个披风强化布", 7, "披风鉴定");
                }
            }
            // 30%概率获得2个强化布
            else if (随机值 <= 80)
            {
                获得强化布 = true;
                强化布数量 = 2;
                
                int 空位 = player.得到包裹空位位置();
                if (空位 >= 0)
                {
                    player.增加物品带属性(披风强化布ID, 空位, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    player.系统提示("鉴定失败，获得了2个披风强化布", 7, "披风鉴定");
                }
            }
            
            // 1%概率获得鉴定石
            if (World.披风鉴定随机数生成器.Next(1, 101) <= 1)
            {
                int 空位 = player.得到包裹空位位置();
                if (空位 >= 0)
                {
                    player.增加物品带属性(披风鉴定石ID, 空位, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    
                    if (获得强化布)
                    {
                        player.系统提示($"鉴定失败，但获得了{强化布数量}个披风强化布和1个披风鉴定石", 7, "披风鉴定");
                    }
                    else
                    {
                        player.系统提示("鉴定失败，但获得了1个披风鉴定石", 7, "披风鉴定");
                    }
                }
            }
            
            if (!获得强化布 && World.披风鉴定随机数生成器.Next(1, 101) > 1)
            {
                player.系统提示("鉴定失败，请再接再厉", 7, "披风鉴定");
            }
        }

        public static int 随机获取披风(bool 是否普通披风 = true)
        {
            List<int> 符合条件披风 = new List<int>();
            
            foreach (var item in World.Itme)
            {
  
                if (item.Value.FLD_RESIDE2 == 12)
                {

                    if (是否普通披风 && 是普通披风(item.Key))
                    {
                        符合条件披风.Add(item.Key);
                    }
                    else if (!是否普通披风 && 是斗战披风(item.Key) && !是顶级披风(item.Key))
                    {
                        符合条件披风.Add(item.Key);
                    }
                }
            }
            
            if (符合条件披风.Count > 0)
            {
                int index = World.披风鉴定随机数生成器.Next(0, 符合条件披风.Count);
                return 符合条件披风[index];
            }
            
            return 0; 
        }
        
        public static int 随机获取顶级披风()
        {
            List<int> 符合条件披风 = new List<int>();
            
            foreach (int id in 顶级披风IDs)
            {
                符合条件披风.Add(id);
            }

            foreach (var item in World.Itme)
            {
                // 检查是否为披风类型
                if (item.Value.FLD_RESIDE2 == 12)
                {
                    if (是顶级披风(item.Key) && !顶级披风IDs.Contains(item.Key)) 
                    {
                        符合条件披风.Add(item.Key);
                    }
                }
            }
            
            if (符合条件披风.Count > 0)
            {
                int index = World.披风鉴定随机数生成器.Next(0, 符合条件披风.Count);
                return 符合条件披风[index];
            }
            
            return 0; 
        }
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class ItemSel : Form
{
	public TextBox PID;

	public TextBox NAME;

	private IContainer components = null;

	private GroupBox groupBox3;

	private Label label1;

	private ComboBox comboBox1;

	private Label label22;

	private Button button1;

	private Button button2;

	private TextBox textBox15;

	private GroupBox groupBox1;

	private Label label28;

	private Label label27;

	private ListBox listBox1;

	public ItemSel()
	{
		InitializeComponent();
	}

	private void button2_Click(object sender, EventArgs e)
	{
		if (textBox15.Text == "")
		{
			MessageBox.Show("请先输入要查询的内容!", "提示");
			return;
		}
		if (comboBox1.SelectedIndex != -1)
		{
			try
			{
				listBox1.Items.Clear();
				string text = "";
				int result;
				switch (comboBox1.SelectedIndex)
				{
				case 0:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品ID!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) <= 0)
					{
						MessageBox.Show("必须是正整数");
						return;
					}
					int num3 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_PID={num3}";
					break;
				}
				case 1:
					text = $"select * from TBL_XWWL_ITEM where FLD_NAME like '%{textBox15.Text}%'";
					break;
				case 2:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num4 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_RESIDE2={num4}";
					break;
				}
				case 3:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num2 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_RESIDE1={num2}";
					break;
				}
				case 4:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num5 = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_SEX={num5}";
					break;
				}
				case 5:
				{
					if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out result))
					{
						MessageBox.Show("请输入正确的物品种类!", "提示");
						return;
					}
					if (int.Parse(textBox15.Text) < 0)
					{
						MessageBox.Show("不能是负数");
						return;
					}
					int num = Convert.ToInt32(textBox15.Text);
					text = $"select * from TBL_XWWL_ITEM where FLD_JOB_LEVEL={num}";
					break;
				}
				default:
					text = "select * from TBL_XWWL_ITEM";
					break;
				}
				DataTable dBToDataTable = DBA.GetDBToDataTable(text, "PublicDb");
				if (dBToDataTable.Rows.Count > 0)
				{
					for (int i = 0; i < dBToDataTable.Rows.Count; i++)
					{
						KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
						listBox1.Items.Add(keyValuePair);
					}
					label28.Text = dBToDataTable.Rows.Count.ToString();
				}
				else
				{
					MessageBox.Show("无此物品,请检查PID是否正确！", "提示");
				}
				dBToDataTable.Dispose();
				return;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.ToString(), "错误");
				return;
			}
		}
		MessageBox.Show("请选择查询的类型", "提示");
	}

	private void listBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBox1.SelectedItem;
		if (PID != null)
		{
			PID.Text = keyValuePair.Key;
		}
		if (NAME != null)
		{
			NAME.Text = keyValuePair.Value;
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ItemSel));
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label22 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.textBox15 = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label28 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.listBox1 = new System.Windows.Forms.ListBox();
            this.groupBox3.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.comboBox1);
            this.groupBox3.Controls.Add(this.label22);
            this.groupBox3.Controls.Add(this.button1);
            this.groupBox3.Controls.Add(this.button2);
            this.groupBox3.Controls.Add(this.textBox15);
            this.groupBox3.Location = new System.Drawing.Point(17, 8);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(296, 84);
            this.groupBox3.TabIndex = 24;
            this.groupBox3.TabStop = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(11, 46);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 57;
            this.label1.Text = "关键字";
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "PID",
            "物品名",
            "物品种类",
            "适用职业",
            "适用性别",
            "适用转职"});
            this.comboBox1.Location = new System.Drawing.Point(57, 16);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(121, 20);
            this.comboBox1.TabIndex = 56;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(23, 20);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(29, 12);
            this.label22.TabIndex = 55;
            this.label22.Text = "条件";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(195, 15);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(92, 23);
            this.button1.TabIndex = 54;
            this.button1.Text = "查看所有物品";
            this.button1.UseVisualStyleBackColor = true;
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(195, 40);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(92, 23);
            this.button2.TabIndex = 9;
            this.button2.Text = "查找";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // textBox15
            // 
            this.textBox15.Location = new System.Drawing.Point(57, 42);
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new System.Drawing.Size(121, 21);
            this.textBox15.TabIndex = 6;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label28);
            this.groupBox1.Controls.Add(this.label27);
            this.groupBox1.Controls.Add(this.listBox1);
            this.groupBox1.Location = new System.Drawing.Point(12, 98);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(301, 421);
            this.groupBox1.TabIndex = 23;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "物品列表";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.ForeColor = System.Drawing.Color.Red;
            this.label28.Location = new System.Drawing.Point(79, 397);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(47, 12);
            this.label28.TabIndex = 14;
            this.label28.Text = "label28";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(14, 397);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(59, 12);
            this.label27.TabIndex = 13;
            this.label27.Text = "物品总数:";
            // 
            // listBox1
            // 
            this.listBox1.FormattingEnabled = true;
            this.listBox1.ItemHeight = 12;
            this.listBox1.Location = new System.Drawing.Point(9, 28);
            this.listBox1.Name = "listBox1";
            this.listBox1.Size = new System.Drawing.Size(283, 352);
            this.listBox1.TabIndex = 1;
            this.listBox1.SelectedIndexChanged += new System.EventHandler(this.listBox1_SelectedIndexChanged);
            // 
            // ItemSel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(325, 526);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ItemSel";
            this.Text = "物品选择";
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

	}
}

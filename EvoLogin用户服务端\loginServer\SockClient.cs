using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using loginServer.DbClss;
using loginServer.Security;

namespace loginServer;

public class SockClient : IDisposable
{
	private SevConnClass SevConn;

	private ServerInfo ServerInfo;

	private bool disposed;

	private string _ServerId;

	private int _最大在线;

	private bool m_Running;

	// 添加安全通信服务
	private static readonly SecureCommunication _secureCommunication = new SecureCommunication("ServerComm");

	public ServerInfo _ServerInfo
	{
		get
		{
			return ServerInfo;
		}
		set
		{
			ServerInfo = value;
		}
	}

	public string ServerId
	{
		get
		{
			return _ServerId;
		}
		set
		{
			_ServerId = value;
		}
	}

	public int 最大在线
	{
		get
		{
			return _最大在线;
		}
		set
		{
			_最大在线 = value;
		}
	}

	public int 当前在线 => World.在线人数(ServerId);

	public bool Running => m_Running;

	public SockClient(ServerInfo SI)
	{
		disposed = false;
		m_Running = false;
		SI.Client = this;
		ServerInfo = SI;
		using (new Lock(World.ServerLst, "ServerlistLock"))
		{
			if (!InList(ServerInfo.ConnId))
			{
				World.ServerLst.TryAdd(ServerInfo.ConnId, this);
			}
			else
			{
				Dispose();
			}
		}
	}

	private bool InList(IntPtr key)
	{
		SockClient value;
		return World.ServerLst.TryGetValue(key, out value);
	}

	public void Logout(IntPtr connId)
	{
		try
		{
			World.服务器断开(ServerId);
			World.ServerLst.TryRemove(connId, out var _);
			Form1.WriteLine(3, "服务器已断开      ID：" + ServerId);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "服务器断开Logout()出错" + ex.Message);
		}
	}

	public void Dispose()
	{
		if (disposed)
		{
			return;
		}
		disposed = true;
		try
		{
			ServerInfo.Server.Disconnect(ServerInfo.ConnId);
			ServerInfo.Client = null;
			ServerInfo = null;
		}
		catch
		{
		}
	}

	public byte[] ProcessDataReceived(byte[] data, int length)
	{
		try
		{
			int num = 0;
			if (204 == data[0] && 153 == data[1])
			{
				byte[] array = new byte[4];
				Buffer.BlockCopy(data, 2, array, 0, 4);
				int num2 = BitConverter.ToInt32(array, 0);
				if (length < num2 + 6)
				{
					return null;
				}
				while (true)
				{
					byte[] array2 = new byte[num2];
					Buffer.BlockCopy(data, num + 6, array2, 0, num2);
					num += num2 + 6;
					DataReceived(array2, num2);
					if (num >= length || data[num] != 204 || data[num + 1] != 153)
					{
						break;
					}
					Buffer.BlockCopy(data, num + 2, array, 0, 4);
					num2 = BitConverter.ToInt16(array, 0);
				}
			}
			return null;
		}
		catch
		{
			return null;
		}
	}

	public byte[] DataReceived(byte[] data, int length)
	{
		int num = 0;
		try
		{
			string @string = Encoding.Default.GetString(data);
			string text = _secureCommunication.Decrypt(@string);
			string[] array = text.Split('|');
			switch (array[0])
			{
			case "KILLID":
				World.KillList.TryAdd(array[1], new KillClass
				{
					UserId = array[1],
					conn = 5,
					time = DateTime.Now.AddMinutes(5.0)
				});
				break;
			case "PVP":
			{
				for (int j = 1; j < array.Length; j++)
				{
					try
					{
						string[] array8 = array[j].Split(';');
						int value29 = 0;
						if (World.PVPList.TryGetValue(array8[0], out value29))
						{
							World.PVPList.TryRemove(array8[0], out var _);
						}
						World.PVPList.TryAdd(array8[0], int.Parse(array8[1]));
					}
					catch
					{
					}
				}
				if (World.PVPList.Count <= 0)
				{
					break;
				}
				int num2 = 0;
				string text6 = string.Empty;
				foreach (KeyValuePair<string, int> item3 in World.PVPList.OrderByDescending((KeyValuePair<string, int> pair) => pair.Value))
				{
					if (num2 < 20)
					{
						text6 = text6 + "|" + item3.Key;
						num2++;
						continue;
					}
					break;
				}
				foreach (SockClient value31 in World.ServerLst.Values)
				{
					value31.Sendd("PVP" + text6);
				}
				break;
			}
			case "仙魔大战掉线":
				num = 1;
				foreach (SockClient value27 in World.ServerLst.Values)
				{
					value27.Sendd("仙魔大战掉线|" + array[1] + "|" + array[2]);
				}
				break;
			case "移除仙魔大战掉线":
				num = 2;
				foreach (SockClient value28 in World.ServerLst.Values)
				{
					value28.Sendd("移除仙魔大战掉线|" + array[1] + "|" + array[2]);
				}
				break;
			case "仙魔大战进程":
				num = 3;
				foreach (SockClient value33 in World.ServerLst.Values)
				{
					value33.Sendd("仙魔大战进程|" + array[1]);
				}
				break;
			case "仙魔大战人数":
				num = 4;
				foreach (SockClient value25 in World.ServerLst.Values)
				{
					value25.Sendd("仙魔大战人数|" + array[1] + "|" + array[2]);
				}
				break;
			case "发送公告":
				num = 5;
				foreach (SockClient value24 in World.ServerLst.Values)
				{
					value24.Sendd("发送公告|" + array[1] + "|" + array[2]);
				}
				break;
			case "狮吼功":
				num = 6;
				foreach (SockClient value22 in World.ServerLst.Values)
				{
					value22.Sendd("狮吼功|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "刷怪掉宝":
				num = 7;
				foreach (SockClient value20 in World.ServerLst.Values)
				{
					value20.Sendd("刷怪掉宝|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "全线提示":
				num = 8;
				foreach (SockClient value32 in World.ServerLst.Values)
				{
					value32.Sendd("全线提示|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "情侣提示":
				num = 9;
				foreach (SockClient value18 in World.ServerLst.Values)
				{
					value18.Sendd("情侣提示|" + array[1] + "|" + array[2]);
				}
				break;
			case "PK提示":
				num = 10;
				foreach (SockClient value17 in World.ServerLst.Values)
				{
					value17.Sendd("PK提示|" + array[1] + "|" + array[2]);
				}
				break;
			case "开启宝盒":
				num = 11;
				foreach (SockClient value15 in World.ServerLst.Values)
				{
					value15.Sendd("开启宝盒|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "百宝抽奖":
				num = 12;
				foreach (SockClient value16 in World.ServerLst.Values)
				{
					value16.Sendd("百宝抽奖|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "更新原服务器":
			{
				num = 13;
				playerS playerS15 = World.查询玩家(array[1]);
				if (playerS15 != null)
				{
					playerS15.原服务器序号 = int.Parse(array[2]);
					playerS15.原服务器IP = array[3];
					playerS15.原服务器端口 = array[4];
					playerS15.原服务器ID = int.Parse(array[5]);
				}
				break;
			}
			case "获取服务器列表":
			{
				num = 14;
				string str = array[3];
				bool flag = false;
				ServerClass serverClass = new ServerClass();
				foreach (ServerClass server5 in World.ServerList)
				{
					foreach (ServerXlClass server6 in server5.ServerList)
					{
						if (RxjhClass.IsEquals(server6.SERVER_IP, str))
						{
							serverClass = server5;
							flag = true;
							break;
						}
					}
					if (flag)
					{
						break;
					}
				}
				if (serverClass == null)
				{
					break;
				}
				playerS playerS13 = World.查询玩家(array[1]);
				if (playerS13 != null)
				{
					string text5 = "获取服务器列表|" + playerS13.UserId + "|";
					for (int l = 0; l < serverClass.ServerList.Count; l++)
					{
						text5 = text5 + serverClass.ServerList[l].ServerId + "," + serverClass.ServerList[l].SERVER_IP + "," + serverClass.ServerList[l].SERVER_PORT + "," + serverClass.ServerList[l].ServerZId + "|";
					}
					Sendd(text5.TrimEnd('|'));
				}
				break;
			}
			case "复查用户登陆":
			{
				num = 15;
				Dictionary<string, playerS> dictionary = new Dictionary<string, playerS>();
				string[] array6 = array[1].Split(',');
				if (array6.Length < 1)
				{
					break;
				}
				for (int k = 0; k < array6.Length; k++)
				{
					try
					{
						string[] array7 = array6[k].Split('-');
						array7[0] = array7[0].Trim();
						playerS playerS10 = new playerS();
						playerS10.UserId = array7[0];
						if (array7.Length >= 2)
						{
							playerS10.UserIp = playerS10.UserIp;
						}
						playerS10.ServerID = ServerId;
						playerS10.conn = 0;
						playerS10.绑定账号 = array7[2];
						playerS10.离线挂机 = array7[3];
						playerS10.UserNmae = array7[4];
						playerS10.原服务器序号 = int.Parse(array7[5]);
						playerS10.原服务器IP = array7[6];
						playerS10.原服务器端口 = array7[7];
						playerS10.原服务器ID = int.Parse(array7[8]);
						playerS10.银币广场服务器IP = array7[9];
						playerS10.银币广场服务器端口 = array7[10];
						playerS10.WorldID = array7[11];
						playerS10.UserIp = array7[1];
						// 2025-0618 EVIAS 使用新的玩家管理器
						var value21 = HelperTools.ConcurrentPlayerManager.GetPlayer(playerS10.UserId);
						if (value21 != null)
						{
							value21.UserIp = array7[1];
							value21.绑定账号 = array7[2];
							value21.离线挂机 = array7[3];
							value21.UserNmae = array7[4];
							value21.原服务器序号 = int.Parse(array7[5]);
							value21.原服务器IP = array7[6];
							value21.原服务器端口 = array7[7];
							value21.原服务器ID = int.Parse(array7[8]);
							value21.银币广场服务器IP = array7[9];
							value21.银币广场服务器端口 = array7[10];
							value21.WorldID = array7[11];
							value21.金符 = array7[12];
							value21.职业 = array7[13];
						}
						playerS playerS11 = World.查询账号总表(array7[0]);
						if (playerS11 != null)
						{
							playerS11.UserIp = array7[1];
							playerS11.绑定账号 = array7[2];
							playerS11.离线挂机 = array7[3];
							playerS11.UserNmae = array7[4];
							playerS11.原服务器序号 = int.Parse(array7[5]);
							playerS11.原服务器IP = array7[6];
							playerS11.原服务器端口 = array7[7];
							playerS11.原服务器ID = int.Parse(array7[8]);
							playerS11.银币广场服务器IP = array7[9];
							playerS11.银币广场服务器端口 = array7[10];
							playerS11.WorldID = array7[11];
							playerS11.金符 = array7[12];
							playerS11.职业 = array7[13];
						}
						dictionary.Add(array7[0], playerS10);
					}
					catch
					{
					}
				}
				World.复查用户登陆(ServerId, dictionary);
				Form1.WriteLine(3, "帐号验证收到：复查用户登陆");
				break;
			}
			case "踢出玩家用户":
				try
				{
					num = 16;
					if (World.查询踢出玩家(array[1]))
					{
						Sendd("踢出玩家成功   ID：   " + array[1]);
					}
					else
					{
						Sendd("踢出玩家失败   ID：   " + array[1] + "   不在线");
					}
					Form1.WriteLine(3, "1帐号验证收到：" + text);
				}
				catch
				{
					Form1.WriteLine(3, "1踢出玩家用户   出错：" + text);
				}
				break;
			case "踢出玩家ID":
				try
				{
					num = 17;
					if (World.查询踢出玩家(array[2]))
					{
						Sendd("2踢出玩家成功   ID：   " + array[1]);
					}
					else
					{
						Sendd("2踢出玩家失败   ID：   " + array[1]);
					}
					Form1.WriteLine(3, "3帐号验证收到：" + text);
				}
				catch
				{
					Form1.WriteLine(3, "3踢出玩家ID   出错：" + text);
				}
				break;
			case "用户换线通知":
			{
				num = 18;
				if (array[2] != "1")
				{
					World.查询踢出玩家(array[1]);
					break;
				}
				playerS playerS5 = World.查询玩家(array[1]);
				if (playerS5 != null)
				{
					playerS5.封包换线 = int.Parse(array[2]);
				}
				if (World.玩家换线通知(array[1]) == 0)
				{
					Sendd("用户踢出|" + array[1]);
					World.查询踢出玩家(array[1]);
				}
				break;
			}
			case "用户换线登陆":
			{
				num = 20;
				playerS playerS7 = World.查询玩家(array[1]);
				if (playerS7 != null)
				{
					if (playerS7.离线挂机 == "1")
					{
						Sendd("OpClient|" + array[4] + "|1");
						return null;
					}
					if (playerS7.conn == 0)
					{
						if (playerS7.绑定账号 != "NULL")
						{
							Sendd("用户换线登陆|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS7.绑定账号 + "|" + playerS7.原服务器IP + "|" + playerS7.原服务器端口 + "|" + playerS7.银币广场服务器IP + "|" + playerS7.银币广场服务器端口 + "|" + playerS7.原服务器序号 + "|" + playerS7.原服务器ID + "|" + playerS7.封包换线);
						}
						else
						{
							Sendd("用户换线登陆|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS7.绑定账号 + "|" + array[5] + "|13000|" + array[5] + "|13000|" + playerS7.原服务器序号 + "|" + playerS7.原服务器ID + "|" + playerS7.封包换线);
						}
						return null;
					}
				}
				else
				{
					playerS playerS8 = World.查询账号总表(array[1]);
					if (playerS8 == null)
					{
						Sendd("用户换线登陆|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|NULL|" + array[5] + "|13000|127.0.0.1|13000|0|" + array[6] + "|" + playerS7.封包换线);
						playerS playerS9 = new playerS();
						playerS9.UserId = array[1];
						playerS9.ServerID = array[6];
						playerS9.WorldID = array[4];
						playerS9.绑定账号 = "NULL";
						playerS9.离线挂机 = "0";
						playerS9.UserNmae = "NULL";
						playerS9.UserIp = array[5];
						playerS9.conn = 0; // 2025-0618 EVIAS 设置conn=0表示游戏服务器连接
						// 2025-0618 EVIAS 修复并发安全问题，直接使用线程安全的添加方法
						HelperTools.ConcurrentPlayerManager.AddPlayer(array[1], playerS9);
						// 2025-0618 EVIAS 修复并发安全问题，使用新的玩家管理器
						HelperTools.ConcurrentPlayerManager.AddTempPlayer(array[1], playerS9);
						return null;
					}
					if (playerS8.离线挂机 == "0")
					{
						Sendd("用户换线登陆|" + array[1] + "|" + array[2] + "|" + array[3] + "|" + array[4] + "|" + array[5] + "|" + array[6] + "|" + playerS8.绑定账号 + "|" + playerS8.原服务器IP + "|" + playerS8.原服务器端口 + "|" + playerS8.银币广场服务器IP + "|" + playerS8.银币广场服务器端口 + "|" + playerS8.原服务器序号 + "|" + playerS8.原服务器ID + "|" + playerS7.封包换线);
						// 2025-0618 EVIAS 使用新的玩家管理器
						playerS8.UserIp = array[5];
						HelperTools.ConcurrentPlayerManager.AddPlayer(array[1], playerS8);
						return null;
					}
				}
				Sendd("用户踢出|" + array[4]);
				World.查询踢出玩家(array[1]);
				break;
			}
			case "换线登陆":
			{
				num = 21;
				playerS playerS16 = World.查询账号总表(array[1]);
				if (playerS16 != null)
				{
					playerS16.UserId = array[1];
					playerS16.绑定账号 = array[5];
					playerS16.离线挂机 = array[6];
					playerS16.UserIp = array[2];
					playerS16.ServerID = array[3];
					playerS16.WorldID = array[4];
				}
				playerS playerS17 = World.查询玩家(array[1]);
				if (playerS17 != null)
				{
					playerS17.UserId = array[1];
					playerS17.ServerID = array[3];
					playerS17.WorldID = array[4];
					playerS17.绑定账号 = array[5];
					playerS17.离线挂机 = array[6];
					playerS17.换线完成();
					if (array[7].Length > 12)
					{
						Sendd("更新配置|" + playerS17.UserId + "|" + array[7]);
					}
				}
				else
				{
					Sendd("用户踢出|" + array[4]);
					World.查询踢出玩家(array[1]);
				}
				break;
			}
			case "用户登陆":
				num = 22;
				if (array.Length == 8)
				{
					SevConn = new SevConnClass();
					SevConn.Server = this;
					SevConn.UserId = array[1];
					SevConn.ServerID = array[3];
					SevConn.WorldID = array[4];
					SevConn.绑定帐号 = array[5];
					SevConn.离线挂机 = array[6];
					// 2025-0618 EVIAS 使用新的玩家管理器
					var value26 = HelperTools.ConcurrentPlayerManager.GetPlayer(array[1]);
					if (value26 != null)
					{
						value26.绑定账号 = array[5];
						value26.离线挂机 = array[6];
						value26.UserNmae = array[7];
						SevConn.UserIp = value26.UserIp;
					}
					else
					{
						SevConn.UserIp = array[2];
					}
					playerS playerS14 = World.查询账号总表(array[1]);
					if (playerS14 != null)
					{
						playerS14.绑定账号 = array[5];
						playerS14.离线挂机 = array[6];
						playerS14.UserNmae = array[7];
						playerS14.UserIp = array[2];
						playerS14.ServerID = ServerId;
						playerS14.WorldID = array[4];
					}
					World.Connect.Enqueue(SevConn);
				}
				Form1.WriteLine(3, "4帐号验证收到：" + text);
				break;
			case "用户登出":
				num = 23;
				World.登出玩家(array[1]);
				Form1.WriteLine(3, "5帐号验证收到：" + text);
				break;
			case "更新服务器配置":
				num = 24;
				最大在线 = int.Parse(array[2]);
				Form1.WriteLine(3, "6帐号验证收到：" + text);
				break;
			case "服务器连接登陆":
				num = 25;
				Form1.WriteLine(3, "7帐号验证收到：" + text);
				num = 26;
				ServerId = array[1];
				num = 27;
				最大在线 = int.Parse(array[2]);
				num = 28;
				World.服务器断开(ServerId);
				num = 29;
				Form1.WriteLine(3, "服务器连接成功   ID：" + array[1]);
				break;
			case "狮子吼":
				num = 30;
				if (World.狮子吼List.Count < World.狮子吼最大数)
				{
					World.狮子吼List.Enqueue(new 狮子吼Class
					{
						FLD_INDEX = int.Parse(array[1]),
						UserName = array[2],
						TxtId = World.狮子吼ID,
						Txt = array[3],
						UserClientIP = array[4],
						线Id = int.Parse(array[5]),
						Map = int.Parse(array[6]),
						样式 = int.Parse(array[7])
					});
					if (World.狮子吼ID >= 127)
					{
						World.狮子吼ID = 0;
					}
					else
					{
						World.狮子吼ID++;
					}
				}
				else
				{
					Sendd("狮子吼|NO|" + array[1]);
				}
				break;
			case "传音消息":
				num = 31;
				World.发送传音消息(array[1], array[2], array[3], array[4], int.Parse(array[5]), array[6]);
				break;
			case "帮派消息":
				num = 32;
				World.发送帮派消息(array[1], array[3]);
				break;
			case "同盟聊天":
				num = 33;
				World.发送同盟消息(array[1], array[2], array[3], array[4]);
				break;
			case "全线公告":
				num = 34;
				foreach (SockClient value23 in World.ServerLst.Values)
				{
					value23.Sendd("全线公告|" + array[1] + "|" + array[2] + "|" + array[3]);
				}
				break;
			case "角色同进踢出":
				num = 36;
				Form1.WriteLine(6, "LS角色同进封号：[" + array[1] + "]");
				Thread.Sleep(2000);
				World.踢出非本线ID(array[1]);
				break;
			case "离线挂机":
			{
				num = 35;
				// 2025-0618 EVIAS 使用新的玩家管理器
				var value19 = HelperTools.ConcurrentPlayerManager.GetPlayer(array[1]);
				if (value19 != null)
				{
					value19.离线挂机 = "1";
				}
				value19 = World.查询账号总表(array[1]);
				if (value19 != null)
				{
					value19.离线挂机 = "1";
				}
				break;
			}
			case "查人物":
			{
				num = 37;
				string[] array3 = array[2].Split(';');
				string text3 = string.Empty;
				num = 38;
				string[] array4 = array3;
				string[] array5 = array4;
				foreach (string text4 in array5)
				{
					num = 39;
					if (text4.Length <= 0)
					{
						continue;
					}
					num = 40;
					// 2025-0618 EVIAS 使用新的玩家管理器
					var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
					foreach (playerS item2 in allPlayers)
					{
						num = 41;
						if (string.Compare(text4, item2.UserNmae) == 0)
						{
							num = 42;
							text3 = text3 + item2.UserNmae + ";";
						}
					}
				}
				num = 43;
				if (text3.Length > 0)
				{
					num = 44;
					Sendd("查人物|" + array[1] + "|" + text3);
				}
				break;
			}
			case "更新服务器端口":
			{
				num = 45;
				string s = array[1];
				string sERVER_PORT = array[2];
				foreach (ServerClass server3 in World.ServerList)
				{
					foreach (ServerXlClass server4 in server3.ServerList)
					{
						if (server4.ServerZId == int.Parse(s))
						{
							server4.SERVER_PORT = sERVER_PORT;
						}
					}
				}
				Form1.WriteLine(3, "更新服务器端口ID：" + array[1] + "丨" + array[2]);
				break;
			}
			case "StartTV":
			{
				num = 46;
				playerS playerS2 = World.查询玩家(array[1]);
				if (playerS2 != null)
				{
					World.OpClient(playerS2.ServerID, playerS2.WorldID, "6");
					Sendd("OK");
				}
				else
				{
					Sendd("账号" + array[1] + "不在线");
				}
				Dispose();
				break;
			}
			case "服务器维护状态":
			{
				num = 47;
				try
				{
					World.维护中 = (array[1] == "1");
					World.维护提示信息 = array.Length > 2 ? array[2] : "";
					string 状态文本 = World.维护中 ? "进入维护" : "退出维护";
					Form1.WriteLine(2, $"收到维护状态通知：{状态文本}，提示信息：{World.维护提示信息}");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, $"处理维护状态消息出错：{ex.Message}");
				}
				break;
			}
			}
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "帐号验证服务器DataReceived：" + ex.Message + "|" + num);
			return null;
		}
	}

	public void Sendd(string str)
	{
		byte[] bytes = Encoding.Default.GetBytes(str);
		发送(bytes, bytes.Length);
	}

	public unsafe void 发送(byte[] toSendBuff, int len)
	{
		try
		{
			byte[] array = new byte[len + 6];
			array[0] = 204;
			array[1] = 153;
			Buffer.BlockCopy(BitConverter.GetBytes(len), 0, array, 2, 4);
			Buffer.BlockCopy(toSendBuff, 0, array, 6, len);
			fixed (byte* ptr = array)
			{
				ServerInfo.Server.Send(ServerInfo.ConnId, (IntPtr)ptr, array.Length);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "帐号验证服务器      发送出错：" + ex.Message);
		}
	}

	public void Start()
	{
		m_Running = true;
	}

}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using RxjhServer.DbClss;
using RxjhServer.Network;

namespace RxjhServer;

public class 灵兽类 : IDisposable
{
	private readonly object thisLock = new object();

	public 武功类[,] 武功新 = new 武功类[2, 17];

	// 24.0 EVIAS 灵兽独立已完成任务系统 20250727
	public ConcurrentDictionary<int, int> 已完成任务 = new ConcurrentDictionary<int, int>();

	private float _人物坐标_X;

	private float _人物坐标_Y;

	private float _人物坐标_Z;

	private int _人物坐标_MAP;

	private long _Id;

	private int _Bs;

	private string _ZrName;

	private string _Name;

	private int _FLD_ZCD;

	private long _FLD_EXP;

	private long _最大经验;

	private long _FLD_EXP_MAX;

	private int _FLD_LEVEL;

	private int _FLD_JOB;

	private int _FLD_JOB_LEVEL;

	private int _FLD_HP;

	private int _FLD_HP_MAX;

	private int _FLD_MP;

	private int _FLD_MP_MAX;

	private int _FLD_物品_追加_HP;

	private int _FLD_物品_追加_MP;

	private int _FLD_负重;

	private int _FLD_负重_MAX;

	private int _FLD_攻击;

	private int _FLD_防御;

	private int _FLD_命中;

	private int _FLD_回避;

	private int _骑;

	private int _FLD_装备_追加_攻击;

	private int _FLD_装备_追加_防御;

	private int _FLD_装备_追加_命中;

	private int _FLD_装备_追加_回避;

	private int _FLD_装备_追加_HP;

	private int _FLD_装备_追加_MP;

	private double _FLD_装备_武功攻击力增加百分比;

	private double _FLD_装备_武功防御力增加百分比;

	private double _FLD_追加百分比_攻击;

	private double _FLD_追加百分比_防御;

	private double _FLD_追加百分比_命中;

	private double _FLD_追加百分比_回避;

	private double _FLD_追加百分比_HP上限;

	private double _FLD_追加百分比_MP上限;

	private double _FLD_灵兽_武功攻击力增加百分比;

	private double _FLD_灵兽_武功防御力增加百分比;

	private double _FLD_灵兽_获得经验增加百分比;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _FLD_MAGIC5;

	public int 全服ID;

	public 物品类[] 宠物装备栏;

	public 物品类[] 宠物以装备;

	public Players Playe;

	private NetState client;

	public List<攻击类> 攻击列表;

	public bool _死亡;

	public float 人物坐标_X
	{
		get
		{
			return _人物坐标_X;
		}
		set
		{
			_人物坐标_X = value;
		}
	}

	public float 人物坐标_Y
	{
		get
		{
			return _人物坐标_Y;
		}
		set
		{
			_人物坐标_Y = value;
		}
	}

	public float 人物坐标_Z
	{
		get
		{
			return _人物坐标_Z;
		}
		set
		{
			_人物坐标_Z = value;
		}
	}

	public int 人物坐标_MAP
	{
		get
		{
			return _人物坐标_MAP;
		}
		set
		{
			_人物坐标_MAP = value;
		}
	}

	public long Id
	{
		get
		{
			return _Id;
		}
		set
		{
			_Id = value;
		}
	}

	public int Bs
	{
		get
		{
			return _Bs;
		}
		set
		{
			_Bs = value;
		}
	}

	public string ZrName
	{
		get
		{
			return _ZrName;
		}
		set
		{
			_ZrName = value;
		}
	}

	public string Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = value;
		}
	}

	public int FLD_ZCD
	{
		get
		{
			return _FLD_ZCD;
		}
		set
		{
			_FLD_ZCD = value;
		}
	}

	public long FLD_EXP
	{
		get
		{
			return _FLD_EXP;
		}
		set
		{
			_FLD_EXP = value;
		}
	}

	public long 最大经验
	{
		get
		{
			return _最大经验;
		}
		set
		{
			_最大经验 = value;
		}
	}

	public long FLD_EXP_MAX
	{
		get
		{
			return _FLD_EXP_MAX;
		}
		set
		{
			_FLD_EXP_MAX = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_JOB
	{
		get
		{
			return _FLD_JOB;
		}
		set
		{
			_FLD_JOB = value;
		}
	}

	public int FLD_JOB_LEVEL
	{
		get
		{
			return _FLD_JOB_LEVEL;
		}
		set
		{
			_FLD_JOB_LEVEL = value;
		}
	}

	public int FLD_HP
	{
		get
		{
			return _FLD_HP;
		}
		set
		{
			_FLD_HP = value;
		}
	}

	public int FLD_HP_MAX
	{
		get
		{
			return _FLD_HP_MAX;
		}
		set
		{
			_FLD_HP_MAX = value;
		}
	}

	public int FLD_MP
	{
		get
		{
			return _FLD_MP;
		}
		set
		{
			_FLD_MP = value;
		}
	}

	public int FLD_MP_MAX
	{
		get
		{
			return _FLD_MP_MAX;
		}
		set
		{
			_FLD_MP_MAX = value;
		}
	}

	public int FLD_物品_追加_HP
	{
		get
		{
			return _FLD_物品_追加_HP;
		}
		set
		{
			_FLD_物品_追加_HP = value;
		}
	}

	public int FLD_物品_追加_MP
	{
		get
		{
			return _FLD_物品_追加_MP;
		}
		set
		{
			_FLD_物品_追加_MP = value;
		}
	}

	public int 灵兽基本最大_HP => (int)((double)(FLD_HP_MAX + FLD_物品_追加_HP + FLD_装备_追加_HP) * (1.0 + FLD_追加百分比_HP上限));

	public int 灵兽基本最大_MP => (int)((double)(FLD_MP_MAX + FLD_物品_追加_MP + FLD_装备_追加_MP) * (1.0 + FLD_追加百分比_MP上限));

	public int 灵兽基本攻击 => (int)((double)(FLD_攻击 + FLD_装备_追加_攻击) * (1.0 + FLD_追加百分比_攻击 + FLD_装备_武功攻击力增加百分比));

	public int 灵兽基本防御 => (int)((double)(FLD_防御 + FLD_装备_追加_防御) * (1.0 + FLD_追加百分比_防御));

	public int 灵兽基本命中 => (int)((double)(FLD_命中 + FLD_装备_追加_命中) * (1.0 + FLD_追加百分比_命中));

	public int FLD_负重
	{
		get
		{
			return _FLD_负重;
		}
		set
		{
			_FLD_负重 = value;
		}
	}

	public int FLD_负重_MAX
	{
		get
		{
			return _FLD_负重_MAX;
		}
		set
		{
			_FLD_负重_MAX = value;
		}
	}

	public int FLD_攻击
	{
		get
		{
			return _FLD_攻击;
		}
		set
		{
			_FLD_攻击 = value;
		}
	}

	public int FLD_防御
	{
		get
		{
			return _FLD_防御;
		}
		set
		{
			_FLD_防御 = value;
		}
	}

	public int FLD_命中
	{
		get
		{
			return _FLD_命中;
		}
		set
		{
			_FLD_命中 = value;
		}
	}

	public int FLD_回避
	{
		get
		{
			return _FLD_回避;
		}
		set
		{
			_FLD_回避 = value;
		}
	}

	public int 骑
	{
		get
		{
			return _骑;
		}
		set
		{
			_骑 = value;
		}
	}

	public int FLD_装备_追加_攻击
	{
		get
		{
			return _FLD_装备_追加_攻击;
		}
		set
		{
			_FLD_装备_追加_攻击 = value;
		}
	}

	public int FLD_装备_追加_防御
	{
		get
		{
			return _FLD_装备_追加_防御;
		}
		set
		{
			_FLD_装备_追加_防御 = value;
		}
	}

	public int FLD_装备_追加_命中
	{
		get
		{
			return _FLD_装备_追加_命中;
		}
		set
		{
			_FLD_装备_追加_命中 = value;
		}
	}

	public int FLD_装备_追加_回避
	{
		get
		{
			return _FLD_装备_追加_回避;
		}
		set
		{
			_FLD_装备_追加_回避 = value;
		}
	}

	public int FLD_装备_追加_HP
	{
		get
		{
			return _FLD_装备_追加_HP;
		}
		set
		{
			_FLD_装备_追加_HP = value;
		}
	}

	public int FLD_装备_追加_MP
	{
		get
		{
			return _FLD_装备_追加_MP;
		}
		set
		{
			_FLD_装备_追加_MP = value;
		}
	}

	public double FLD_装备_武功攻击力增加百分比
	{
		get
		{
			return _FLD_装备_武功攻击力增加百分比;
		}
		set
		{
			_FLD_装备_武功攻击力增加百分比 = value;
		}
	}

	public double FLD_装备_武功防御力增加百分比
	{
		get
		{
			return _FLD_装备_武功防御力增加百分比;
		}
		set
		{
			_FLD_装备_武功防御力增加百分比 = value;
		}
	}

	public double FLD_追加百分比_攻击
	{
		get
		{
			return _FLD_追加百分比_攻击;
		}
		set
		{
			_FLD_追加百分比_攻击 = value;
		}
	}

	public double FLD_追加百分比_防御
	{
		get
		{
			return _FLD_追加百分比_防御;
		}
		set
		{
			_FLD_追加百分比_防御 = value;
		}
	}

	public double FLD_追加百分比_命中
	{
		get
		{
			return _FLD_追加百分比_命中;
		}
		set
		{
			_FLD_追加百分比_命中 = value;
		}
	}

	public double FLD_追加百分比_回避
	{
		get
		{
			return _FLD_追加百分比_回避;
		}
		set
		{
			_FLD_追加百分比_回避 = value;
		}
	}

	public double FLD_追加百分比_HP上限
	{
		get
		{
			return _FLD_追加百分比_HP上限;
		}
		set
		{
			_FLD_追加百分比_HP上限 = value;
		}
	}

	public double FLD_追加百分比_MP上限
	{
		get
		{
			return _FLD_追加百分比_MP上限;
		}
		set
		{
			_FLD_追加百分比_MP上限 = value;
		}
	}

	public double FLD_灵兽_武功攻击力增加百分比
	{
		get
		{
			return _FLD_灵兽_武功攻击力增加百分比;
		}
		set
		{
			_FLD_灵兽_武功攻击力增加百分比 = value;
		}
	}

	public double FLD_灵兽_武功防御力增加百分比
	{
		get
		{
			return _FLD_灵兽_武功防御力增加百分比;
		}
		set
		{
			_FLD_灵兽_武功防御力增加百分比 = value;
		}
	}

	public double FLD_灵兽_获得经验增加百分比
	{
		get
		{
			return _FLD_灵兽_获得经验增加百分比;
		}
		set
		{
			_FLD_灵兽_获得经验增加百分比 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return _FLD_MAGIC5;
		}
		set
		{
			_FLD_MAGIC5 = value;
		}
	}

	public bool 死亡
	{
		get
		{
			if (FLD_HP <= 0)
			{
				_死亡 = false;
			}
			return _死亡;
		}
		set
		{
			_死亡 = value;
		}
	}

	public void Dispose()
	{
		Playe = null;
		client = null;
	}

	public 灵兽类(long id, NetState clien, DataTable table2, Players Playe)
	{
		this.Playe = Playe;
		client = clien;
		Id = id;
		ZrName = table2.Rows[0]["ZrName"].ToString();
		Name = table2.Rows[0]["Name"].ToString();
		FLD_ZCD = (int)table2.Rows[0]["FLD_ZCD"];
		FLD_EXP = long.Parse(table2.Rows[0]["FLD_EXP"].ToString());
		FLD_LEVEL = (int)table2.Rows[0]["FLD_LEVEL"];
		FLD_JOB = (int)table2.Rows[0]["FLD_JOB"];
		FLD_JOB_LEVEL = (int)table2.Rows[0]["FLD_JOB_LEVEL"];
		FLD_HP = (int)table2.Rows[0]["FLD_HP"];
		FLD_MP = (int)table2.Rows[0]["FLD_MP"];
		Bs = (int)table2.Rows[0]["FLD_BS"];
		FLD_MAGIC1 = (int)table2.Rows[0]["FLD_MAGIC1"];
		FLD_MAGIC2 = (int)table2.Rows[0]["FLD_MAGIC2"];
		FLD_MAGIC3 = (int)table2.Rows[0]["FLD_MAGIC3"];
		FLD_MAGIC4 = (int)table2.Rows[0]["FLD_MAGIC4"];
		FLD_MAGIC5 = (int)table2.Rows[0]["FLD_MAGIC5"];
		FLD_EXP_MAX = 100000L;
		FLD_攻击 = 0;
		FLD_防御 = 0;
		FLD_命中 = 0;
		FLD_回避 = 0;
		FLD_负重 = 0;
		FLD_负重_MAX = 100;
		FLD_物品_追加_HP = 0;
		FLD_物品_追加_MP = 0;
		FLD_装备_追加_攻击 = 0;
		FLD_装备_追加_防御 = 0;
		FLD_装备_追加_命中 = 0;
		FLD_装备_追加_回避 = 0;
		FLD_装备_追加_HP = 0;
		FLD_装备_追加_MP = 0;
		FLD_装备_武功攻击力增加百分比 = 0.0;
		FLD_装备_武功防御力增加百分比 = 0.0;
		FLD_追加百分比_命中 = 0.0;
		FLD_追加百分比_回避 = 0.0;
		FLD_追加百分比_攻击 = 0.0;
		FLD_追加百分比_防御 = 0.0;
		FLD_追加百分比_HP上限 = 0.0;
		FLD_追加百分比_MP上限 = 0.0;
		FLD_灵兽_武功防御力增加百分比 = 0.0;
		FLD_灵兽_武功攻击力增加百分比 = 0.0;
		FLD_灵兽_获得经验增加百分比 = 0.0;
		宠物装备栏 = new 物品类[16];
		宠物以装备 = new 物品类[5];
		武功新 = new 武功类[2, 17];
		攻击列表 = new List<攻击类>();
		byte[] src = (byte[])table2.Rows[0]["FLD_ITEM"];
		for (int i = 0; i < 16; i++)
		{
			byte[] array = new byte[World.数据库单个物品大小];
			try
			{
				Buffer.BlockCopy(src, i * World.数据库单个物品大小, array, 0, World.数据库单个物品大小);
			}
			catch (Exception value)
			{
				Console.WriteLine(value);
			}
			宠物装备栏[i] = new 物品类(array, i);
			FLD_负重 += 宠物装备栏[i].物品总重量;
		}
		byte[] src2 = (byte[])table2.Rows[0]["FLD_WEARITEM"];
		for (int j = 0; j < 5; j++)
		{
			byte[] array2 = new byte[World.数据库单个物品大小];
			try
			{
				Buffer.BlockCopy(src2, j * World.数据库单个物品大小, array2, 0, World.数据库单个物品大小);
			}
			catch (Exception value2)
			{
				Console.WriteLine(value2);
			}
			宠物以装备[j] = new 物品类(array2, j);
			FLD_负重 += 宠物以装备[j].物品总重量;
			byte[] array3 = new byte[4];
			Buffer.BlockCopy(宠物以装备[j].物品_byte, 60, array3, 0, 4);
			int num = BitConverter.ToInt32(array3, 0);
			if (num > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num)).TotalSeconds >= 0.0)
			{
				clien.Player.系统提示("背包有物品[" + 宠物以装备[j].得到物品名称() + "]已过期,已删除！", 9, "[系统]");
				宠物以装备[j].物品_byte = new byte[World.数据库单个物品大小];
			}
		}
		byte[] array4 = (byte[])table2.Rows[0]["FLD_KONGFU"];
		for (int k = 0; k < 32; k++)
		{
			byte[] array5 = new byte[4];
			try
			{
				if (array4.Length < k * 4 + 4)
				{
					break;
				}
				Buffer.BlockCopy(array4, k * 4, array5, 0, 4);
				int num2 = BitConverter.ToInt32(array5, 0);
				if (num2 == 0)
				{
					continue;
				}
				武功类 武功类2 = new 武功类(num2);
				if (武功类2.FLD_JOB != 0)
				{
					switch (武功类2.FLD_JOB)
					{
					case 7:
						if (FLD_JOB != 1)
						{
							continue;
						}
						break;
					case 8:
						if (FLD_JOB != 2)
						{
							continue;
						}
						break;
					case 9:
						if (FLD_JOB != 3)
						{
							continue;
						}
						break;
					case 10:
						if (FLD_JOB != 4)
						{
							continue;
						}
						break;
					case 15:
						if (FLD_JOB != 5)
						{
							continue;
						}
						break;
					case 20:
						if (FLD_JOB != 6)
						{
							continue;
						}
						break;
					case 21:
						if (FLD_JOB != 7)
						{
							continue;
						}
						break;
					case 22:
						if (FLD_JOB != 8)
						{
							continue;
						}
						break;
					}
				}
				if (FLD_JOB_LEVEL >= 武功类2.FLD_JOBLEVEL && FLD_LEVEL >= 武功类2.FLD_LEVEL)
				{
					武功新[武功类2.FLD_武功类型, 武功类2.FLD_INDEX] = 武功类2;
				}
				continue;
			}
			catch (Exception value3)
			{
				Console.WriteLine(value3);
				continue;
			}
		}
		// 24.0 EVIAS 加载灵兽已完成任务数据 20250727
		加载已完成任务数据(table2);
		
		计算基本数据();
		计算灵兽装备数据();
	}

	public void 计算基本数据() //24.0  计算基本数据  EVIAS
    {
        Playe.FLD_宠物_追加_最大HP = 0; 
        Playe.FLD_宠物_追加_武功攻击 = 0;
        Playe.FLD_宠物_追加_武功防御 = 0;
        Playe.FLD_宠物_追加_攻击 = 0;
        Playe.FLD_宠物_追加_防御 = 0;
        Playe.FLD_宠物_追加_经验百分比 = 0;

        if (FLD_LEVEL > 125)
		{
			FLD_LEVEL = 125;
		}
		最大经验 = (long)World.lever[FLD_LEVEL];
		int fLD_LEVEL = FLD_LEVEL;
		while (FLD_LEVEL < 125)
		{
			if (client == null || !client.Running)
			{
				return;
			}
			if (FLD_EXP < 最大经验)
			{
				break;
			}
			FLD_LEVEL++;
			最大经验 = (long)World.lever[FLD_LEVEL];
		}
		if (FLD_LEVEL - fLD_LEVEL != 0)
		{
			if (FLD_LEVEL - fLD_LEVEL > 0 && client.Player.人物灵兽 != null)
			{
				client.Player.灵兽升级后的提示();
			}
			if (client.Player.人物灵兽 != null)
			{
				client.Player.更新灵兽HP_MP_SP();
				client.Player.更新灵兽武功和状态();
			}
		}
		//24.0 EVIAS 灵兽效果 20250722 - 15级自动学习特殊效果武功
		if (FLD_LEVEL >= 15)
		{
			int 武功等级;
			if (FLD_LEVEL < 50)
			{
				武功等级 = 1; 
			}
			else
			{
				武功等级 = 2 + (FLD_LEVEL - 50) / 25; 
			}
			//24.0 EVIAS 根据灵兽等级计算武功等级 20250725
			if (武功新[1, 4] == null && World.TBL_KONGFU.ContainsKey(1401))
			{
				武功新[1, 4] = new 武功类(1401);
			}
			if (武功新[1, 4] != null)
			{
				int 最高等级 = 武功新[1, 4].FLD_武功最高级别;
				武功新[1, 4].武功_等级 = Math.Min(最高等级, 武功等级); 
			}

			if (武功新[1, 5] == null && World.TBL_KONGFU.ContainsKey(1402))
			{
				武功新[1, 5] = new 武功类(1402);
			}
			if (武功新[1, 5] != null)
			{
				int 最高等级 = 武功新[1, 5].FLD_武功最高级别;
				武功新[1, 5].武功_等级 = Math.Min(最高等级, 武功等级); 
			}

			if (武功新[1, 6] == null && World.TBL_KONGFU.ContainsKey(1403))
			{
				武功新[1, 6] = new 武功类(1403);
			}
			if (武功新[1, 6] != null)
			{
				int 最高等级 = 武功新[1, 6].FLD_武功最高级别;
				武功新[1, 6].武功_等级 = Math.Min(最高等级, 武功等级); 
			}
		}
		FLD_负重_MAX = 500 + 20 * FLD_LEVEL;
		switch (FLD_JOB)
		{
		case 1:
		{
			FLD_HP_MAX = 183 + FLD_LEVEL * 12;
			FLD_MP_MAX = 164 + FLD_LEVEL * 2;
			FLD_命中 = 75 + FLD_LEVEL * 2;
			FLD_回避 = 75 + FLD_LEVEL * 2;
			FLD_攻击 = 75;
			FLD_防御 = 75;
			for (int num2 = 2; num2 <= FLD_LEVEL; num2++)
			{
				if (num2 % 2 == 0)
				{
					FLD_攻击 += 4;
					FLD_防御 += 4;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 2:
		{
			FLD_HP_MAX = 233 + FLD_LEVEL * 14;
			FLD_MP_MAX = 214 + FLD_LEVEL * 3;
			FLD_命中 = 80 + FLD_LEVEL * 2;
			FLD_回避 = 80 + FLD_LEVEL * 2;
			FLD_攻击 = 80;
			FLD_防御 = 80;
			for (int l = 2; l <= FLD_LEVEL; l++)
			{
				if (l % 2 == 0)
				{
					FLD_攻击 += 6;
					FLD_防御 += 6;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 3:
		{
			FLD_HP_MAX = 283 + FLD_LEVEL * 16;
			FLD_MP_MAX = 264 + FLD_LEVEL * 4;
			FLD_命中 = 85 + FLD_LEVEL * 2;
			FLD_回避 = 85 + FLD_LEVEL * 2;
			FLD_攻击 = 85;
			FLD_防御 = 85;
			for (int n = 2; n <= FLD_LEVEL; n++)
			{
				if (n % 2 == 0)
				{
					FLD_攻击 += 8;
					FLD_防御 += 8;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 4:
		{
			FLD_HP_MAX = 333 + FLD_LEVEL * 18;
			FLD_MP_MAX = 314 + FLD_LEVEL * 5;
			FLD_命中 = 90 + FLD_LEVEL * 2;
			FLD_回避 = 90 + FLD_LEVEL * 2;
			FLD_攻击 = 90;
			FLD_防御 = 90;
			for (int j = 2; j <= FLD_LEVEL; j++)
			{
				if (j % 2 == 0)
				{
					FLD_攻击 += 10;
					FLD_防御 += 10;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 5:
		{
			FLD_HP_MAX = 383 + FLD_LEVEL * 20;
			FLD_MP_MAX = 364 + FLD_LEVEL * 6;
			FLD_命中 = 95 + FLD_LEVEL * 2;
			FLD_回避 = 95 + FLD_LEVEL * 2;
			FLD_攻击 = 95;
			FLD_防御 = 95;
			for (int num = 2; num <= FLD_LEVEL; num++)
			{
				if (num % 2 == 0)
				{
					FLD_攻击 += 12;
					FLD_防御 += 12;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 6:
		{
			FLD_HP_MAX = 433 + FLD_LEVEL * 22;
			FLD_MP_MAX = 414 + FLD_LEVEL * 7;
			FLD_命中 = 100 + FLD_LEVEL * 2;
			FLD_回避 = 100 + FLD_LEVEL * 2;
			FLD_攻击 = 100;
			FLD_防御 = 100;
			for (int m = 2; m <= FLD_LEVEL; m++)
			{
				if (m % 2 == 0)
				{
					FLD_攻击 += 14;
					FLD_防御 += 14;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 7:
		{
			FLD_HP_MAX = 483 + FLD_LEVEL * 24;
			FLD_MP_MAX = 464 + FLD_LEVEL * 8;
			FLD_命中 = 105 + FLD_LEVEL * 2;
			FLD_回避 = 105 + FLD_LEVEL * 2;
			FLD_攻击 = 105;
			FLD_防御 = 105;
			for (int k = 2; k <= FLD_LEVEL; k++)
			{
				if (k % 2 == 0)
				{
					FLD_攻击 += 16;
					FLD_防御 += 16;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		case 8:
		{
			FLD_HP_MAX = 543 + FLD_LEVEL * 26;
			FLD_MP_MAX = 514 + FLD_LEVEL * 9;
			FLD_命中 = 110 + FLD_LEVEL * 2;
			FLD_回避 = 110 + FLD_LEVEL * 2;
			FLD_攻击 = 110;
			FLD_防御 = 110;
			for (int i = 2; i <= FLD_LEVEL; i++)
			{
				if (i % 2 == 0)
				{
					FLD_攻击 += 18;
					FLD_防御 += 18;
				}
				else
				{
					FLD_攻击++;
					FLD_防御++;
				}
			}
			break;
		}
		}
		switch (_FLD_JOB_LEVEL)
		{
		case 1:
			FLD_攻击 += 5;
			FLD_防御 += 5;
			FLD_HP_MAX += 85;
			FLD_MP_MAX += 50;
			break;
		case 2:
			FLD_攻击 += 15;
			FLD_防御 += 15;
			FLD_HP_MAX += 200;
			FLD_MP_MAX += 150;
			break;
		case 3:
			FLD_攻击 += 35;
			FLD_防御 += 35;
			FLD_HP_MAX += 400;
			FLD_MP_MAX += 350;
			break;
		 case 4:
            FLD_攻击 += 100; //24.0  灵兽4转5转属性加成
            FLD_防御 += 35;
            FLD_命中 += 40;
            FLD_HP_MAX += 400;
            FLD_MP_MAX += 350;
            break;
        case 5:
            FLD_攻击 += 235;
            FLD_防御 += 35;
            FLD_命中 += 50;
            FLD_HP_MAX += 400;
            FLD_MP_MAX += 350;
            break;
		}
		if (FLD_JOB == 1)
		{
			Playe.FLD_宠物_追加_最大HP = (FLD_LEVEL + 1) * 3;
		}
		else if (FLD_JOB >= 5)
		{
			Playe.FLD_宠物_追加_最大HP = (FLD_LEVEL + 1) * 5;
			Playe.FLD_宠物_追加_防御 = (int)((double)(FLD_LEVEL + 1) * 0.8);
			Playe.FLD_宠物_追加_攻击 = (int)((double)(FLD_LEVEL + 1) * 0.8);
		}
		else
		{
			Playe.FLD_宠物_追加_最大HP = (FLD_LEVEL + 1) * 4;
			Playe.FLD_宠物_追加_防御 = (int)((double)(FLD_LEVEL + 1) * 0.5);
		}
		long get物品ID = 宠物以装备[4].Get物品ID;
		long num3 = get物品ID;
		long num4 = num3 - 601100003;
		if ((ulong)num4 <= 5uL)
		{
			long num5 = num4;
			long num6 = num5;
			if ((ulong)num6 <= 5uL)
			{
				switch (num6)
				{
				case 0L:
					Playe.FLD_宠物_追加_最大HP += 300;
					break;
				case 1L:
					Playe.FLD_宠物_追加_武功攻击 += 0.08;
					break;
				case 2L:
					Playe.FLD_宠物_追加_武功防御 += 60.0;
					break;
				case 3L:
					Playe.FLD_宠物_追加_攻击 += 40;
					break;
				case 4L:
					Playe.FLD_宠物_追加_防御 += 40;
					break;
				case 5L:
					Playe.FLD_宠物_追加_经验百分比 += 0.2;
					break;
				}
			}
		}
		Playe.更新HP_MP_SP();
		Playe.更新武功和状态();
	}

	public void 计算灵兽装备数据()
	{
		using (new Lock(thisLock, "计算宠物装备数据"))
		{
			FLD_装备_追加_攻击 = 0;
			FLD_装备_追加_防御 = 0;
			FLD_装备_追加_命中 = 0;
			FLD_装备_追加_回避 = 0;
			FLD_装备_追加_HP = 0;
			FLD_装备_追加_MP = 0;
			FLD_装备_武功攻击力增加百分比 = 0.0;
			FLD_装备_武功防御力增加百分比 = 0.0;
			FLD_灵兽_获得经验增加百分比 = 0.0;
			for (int i = 0; i < 5; i++)
			{
				if (宠物以装备[i].Get物品ID != 0)
				{
					宠物以装备[i].得到物品属性方法(0, 0);
					FLD_装备_追加_攻击 += (宠物以装备[i].前置物品攻击力 + 宠物以装备[i].物品攻击力后置) / 2;
					FLD_装备_追加_防御 += 宠物以装备[i].物品防御力;
					FLD_装备_追加_命中 += 宠物以装备[i].物品属性_命中率增加;
					FLD_装备_追加_回避 += 宠物以装备[i].物品属性_回避率增加;
					FLD_装备_追加_HP += 宠物以装备[i].物品属性_生命力增加;
					FLD_装备_追加_MP += 宠物以装备[i].物品属性_内功力增加;
					double num = 宠物以装备[i].物品属性_武功攻击力;
					double num2 = (double)宠物以装备[i].物品属性_武功防御力增加 * (1.0 - World.武功防降低百分比);
					FLD_装备_武功攻击力增加百分比 += num * 0.01;
					FLD_装备_武功防御力增加百分比 += num2 * 0.01;
				}
			}
			得到兽灵丹属性(FLD_MAGIC1.ToString());
			得到兽灵丹属性(FLD_MAGIC2.ToString());
			得到兽灵丹属性(FLD_MAGIC3.ToString());
			得到兽灵丹属性(FLD_MAGIC4.ToString());
			得到兽灵丹属性(FLD_MAGIC5.ToString());
		}
	}

	public void 得到兽灵丹属性(string ysqh)
	{
		try
		{
			if (!(ysqh == "0"))
			{
				string s;
				switch (ysqh.Length)
				{
				default:
					return;
				case 8:
					s = ysqh.Substring(0, 1);
					break;
				case 9:
					s = ysqh.Substring(0, 2);
					break;
				}
				int num = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
				switch (int.Parse(s))
				{
				case 1:
					Playe.FLD_宠物_追加_攻击 += num;
					FLD_装备_追加_攻击 += num;
					break;
				case 2:
					Playe.FLD_宠物_追加_防御 += num;
					FLD_装备_追加_防御 += num;
					break;
				case 3:
					Playe.FLD_宠物_追加_最大HP += num;
					FLD_装备_追加_HP += num;
					break;
				case 4:
					FLD_装备_追加_MP += num;
					break;
				case 5:
					FLD_装备_追加_命中 += num;
					break;
				case 6:
					FLD_装备_追加_回避 += num;
					break;
				case 7:
					Playe.FLD_宠物_追加_武功攻击 += (double)num * 0.01;
					FLD_装备_武功攻击力增加百分比 += (double)num * 0.01;
					break;
				case 11:
					Playe.FLD_宠物_追加_武功防御 += num;
					FLD_装备_武功防御力增加百分比 += (double)num * 0.01;
					break;
				case 15:
					Playe.FLD_宠物_追加_经验百分比 += (double)num * 0.01;
					FLD_灵兽_获得经验增加百分比 += (double)num * 0.01;
					break;
				case 8:
				case 9:
				case 10:
				case 12:
				case 13:
				case 14:
					break;
				}
			}
		}
		catch
		{
		}
	}

	public void 保存数据()
	{
        // 24.0 EVIAS 灵兽新增已完成任务参数
        SqlParameter[] prams = new SqlParameter[14]
		{
			SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 20, _Id.ToString()),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 20, Name),
			SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, FLD_LEVEL),
			SqlDBA.MakeInParam("@zcd", SqlDbType.Int, 10, FLD_ZCD),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, FLD_JOB),
			SqlDBA.MakeInParam("@job_level", SqlDbType.Int, 0, FLD_JOB_LEVEL),
			SqlDBA.MakeInParam("@exp", SqlDbType.VarChar, 50, FLD_EXP.ToString()),
			SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, FLD_HP),
			SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, FLD_MP),
			SqlDBA.MakeInParam("@strWearitem", SqlDbType.VarBinary, 200, GetWEARITEMCodes()),
			SqlDBA.MakeInParam("@strItem", SqlDbType.VarBinary, 600, GetFLD_ITEMCodes()),
			SqlDBA.MakeInParam("@strKongfu", SqlDbType.VarBinary, 130, GetFLD_KONGFUCodes()),
			SqlDBA.MakeInParam("@bs", SqlDbType.Int, 0, Bs),
			SqlDBA.MakeInParam("@strQuestFinish", SqlDbType.VarBinary, 1000, Get已完成任务byte()) //24.0  新增已完成任务参数
		};
		
		try
		{
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "XWWL_UPDATE_Cw_DATA_New" 
			});
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"保存灵兽数据出错 - 灵兽ID:{Id}, 错误:{ex.Message}");
		}
	}

	public byte[] GetWEARITEMCodes()
	{
		byte[] array = new byte[World.数据库单个物品大小 * 5];
		for (int i = 0; i < 5; i++)
		{
			byte[] src;
			try
			{
				src = 宠物以装备[i].物品_byte;
			}
			catch
			{
				src = new byte[World.数据库单个物品大小];
			}
			Buffer.BlockCopy(src, 0, array, i * World.数据库单个物品大小, World.数据库单个物品大小);
		}
		return array;
	}

	public byte[] GetFLD_ITEMCodes()
	{
		byte[] array = new byte[World.数据库单个物品大小 * 16];
		for (int i = 0; i < 16; i++)
		{
			byte[] src;
			try
			{
				src = 宠物装备栏[i].物品_byte;
			}
			catch
			{
				src = new byte[World.数据库单个物品大小];
			}
			Buffer.BlockCopy(src, 0, array, i * World.数据库单个物品大小, World.数据库单个物品大小);
		}
		return array;
	}

	public byte[] GetFLD_KONGFUCodes()
	{
		using 发包类 发包类 = new 发包类();
		try
		{
			for (int i = 0; i < 2; i++)
			{
				for (int j = 0; j < 17; j++)
				{
					if (武功新[i, j] != null)
					{
						发包类.Write4(武功新[i, j].FLD_PID);
					}
				}
			}
		}
		catch
		{
		}
		return 发包类.ToArray3();
	}

	// 24.0 EVIAS 灵兽独立已完成任务系统 20250727
	private void 加载已完成任务数据(DataTable table)
	{
		try
		{
			if (table.Rows.Count > 0 && table.Columns.Contains("FLD_QUEST_FINISH"))
			{
				object questFinishObj = table.Rows[0]["FLD_QUEST_FINISH"];
				if (questFinishObj != null && questFinishObj != DBNull.Value)
				{
					byte[] questFinishData = (byte[])questFinishObj;
					if (questFinishData != null && questFinishData.Length > 0)
					{
						for (int i = 0; i < questFinishData.Length / 2; i++)
						{
							if (i * 2 + 1 < questFinishData.Length)
							{
								byte[] taskIdBytes = new byte[2];
								Buffer.BlockCopy(questFinishData, i * 2, taskIdBytes, 0, 2);
								int taskId = BitConverter.ToInt16(taskIdBytes, 0);
								if (taskId != 0 && !已完成任务.ContainsKey(taskId))
								{
									已完成任务.TryAdd(taskId, taskId);
								}
							}
						}
					}
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"加载灵兽已完成任务数据出错 - 灵兽ID:{Id}, 错误:{ex.Message}");
		}
	}

	// 获取灵兽已完成任务的字节数据
	public byte[] Get已完成任务byte()
	{
		using 发包类 发包类 = new 发包类();
		try
		{
			foreach (int taskId in 已完成任务.Values)
			{
				发包类.Write2(taskId);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"获取灵兽已完成任务数据出错 - 灵兽ID:{Id}, 错误:{ex.Message}");
		}
		return 发包类.ToArray3();
	}

	// 添加已完成任务
	public void 添加已完成任务(int taskId)
	{
		try
		{
			if (!已完成任务.ContainsKey(taskId))
			{
				已完成任务.TryAdd(taskId, taskId);
				Form1.WriteLine(3, $"灵兽[{Name}]完成任务:{taskId}");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"添加灵兽已完成任务出错 - 灵兽ID:{Id}, 任务ID:{taskId}, 错误:{ex.Message}");
		}
	}

	// 检查任务是否已完成
	public bool 是否已完成任务(int taskId)
	{
		return 已完成任务.ContainsKey(taskId);
	}
}

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer.HelperTools
{
    // 2025-0618 EVIAS 配置文件验证工具
    public static class ConfigValidator
    {
        // 必需的配置项
        private static readonly Dictionary<string, string[]> RequiredConfigs = new Dictionary<string, string[]>
        {
            ["LoginServer"] = new[]
            {
                "帐号验证服务器端口", "游戏登陆服务器端口", "服务器名", "分区号",
                "Server", "UserName", "PassWord", "DataName", "serverCount"
            },
            ["server0"] = new[]
            {
                "servername", "serverCount"
            }
        };

        // 验证配置文件完整性
        public static bool ValidateConfig()
        {
            try
            {
                string configPath = Application.StartupPath + "\\config\\config.ini";
                
                if (!File.Exists(configPath))
                {
                    Form1.WriteLine(1, "配置文件不存在: " + configPath);
                    return false;
                }

                // 检查文件编码
                if (!CheckFileEncoding(configPath))
                {
                    Form1.WriteLine(1, "配置文件编码异常，可能包含乱码");
                }

                // 验证必需配置项
                var missingConfigs = new List<string>();
                
                foreach (var section in RequiredConfigs)
                {
                    foreach (var key in section.Value)
                    {
                        string value = Config.IniReadValue(section.Key, key);
                        if (string.IsNullOrWhiteSpace(value))
                        {
                            missingConfigs.Add($"[{section.Key}] {key}");
                        }
                    }
                }

                if (missingConfigs.Count > 0)
                {
                    Form1.WriteLine(1, "缺少必需配置项: " + string.Join(", ", missingConfigs));
                    return false;
                }

                // 验证端口配置
                if (!ValidatePorts())
                {
                    return false;
                }

                // 验证数据库配置
                if (!ValidateDatabase())
                {
                    return false;
                }

                Form1.WriteLine(2, "配置文件验证通过");
                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "配置文件验证", "");
                return false;
            }
        }

        // 检查文件编码
        private static bool CheckFileEncoding(string filePath)
        {
            try
            {
                // 尝试用UTF-8读取
                string content = File.ReadAllText(filePath, Encoding.UTF8);
                
                // 检查是否包含乱码字符
                if (content.Contains("�"))
                {
                    Form1.WriteLine(1, "配置文件可能存在编码问题，建议使用UTF-8编码保存");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "检查文件编码", filePath);
                return false;
            }
        }

        // 验证端口配置
        private static bool ValidatePorts()
        {
            try
            {
                string loginPortStr = Config.IniReadValue("LoginServer", "游戏登陆服务器端口");
                string authPortStr = Config.IniReadValue("LoginServer", "帐号验证服务器端口");

                if (!int.TryParse(loginPortStr, out int loginPort) || loginPort <= 0 || loginPort > 65535)
                {
                    Form1.WriteLine(1, $"游戏登陆服务器端口配置无效: {loginPortStr}");
                    return false;
                }

                if (!int.TryParse(authPortStr, out int authPort) || authPort <= 0 || authPort > 65535)
                {
                    Form1.WriteLine(1, $"帐号验证服务器端口配置无效: {authPortStr}");
                    return false;
                }

                if (loginPort == authPort)
                {
                    Form1.WriteLine(1, "登陆端口和验证端口不能相同");
                    return false;
                }

                Form1.WriteLine(3, $"端口配置验证通过 - 登陆端口: {loginPort}, 验证端口: {authPort}");
                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "验证端口配置", "");
                return false;
            }
        }

        // 验证数据库配置
        private static bool ValidateDatabase()
        {
            try
            {
                string server = Config.IniReadValue("LoginServer", "Server");
                string userName = Config.IniReadValue("LoginServer", "UserName");
                string password = Config.IniReadValue("LoginServer", "PassWord");
                string dataName = Config.IniReadValue("LoginServer", "DataName");

                if (string.IsNullOrWhiteSpace(server))
                {
                    Form1.WriteLine(1, "数据库服务器地址未配置");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(userName))
                {
                    Form1.WriteLine(1, "数据库用户名未配置");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(dataName))
                {
                    Form1.WriteLine(1, "数据库名称未配置");
                    return false;
                }

                Form1.WriteLine(3, $"数据库配置验证通过 - 服务器: {server}, 数据库: {dataName}");
                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "验证数据库配置", "");
                return false;
            }
        }

        // 获取配置摘要信息
        public static string GetConfigSummary()
        {
            try
            {
                var summary = new StringBuilder();
                summary.AppendLine("=== EvoLogin配置摘要 ===");
                
                // 基本信息
                summary.AppendLine($"服务器名: {Config.IniReadValue("LoginServer", "服务器名")}");
                summary.AppendLine($"分区号: {Config.IniReadValue("LoginServer", "分区号")}");
                summary.AppendLine($"登陆端口: {Config.IniReadValue("LoginServer", "游戏登陆服务器端口")}");
                summary.AppendLine($"验证端口: {Config.IniReadValue("LoginServer", "帐号验证服务器端口")}");
                
                // 数据库信息
                summary.AppendLine($"数据库服务器: {Config.IniReadValue("LoginServer", "Server")}");
                summary.AppendLine($"数据库名称: {Config.IniReadValue("LoginServer", "DataName")}");
                
                // 多开设置
                summary.AppendLine($"自动查询多开: {Config.IniReadValue("LoginServer", "自动查询多开")}");
                summary.AppendLine($"允许多开数量: {Config.IniReadValue("LoginServer", "允许多开数量")}");
                
                // 服务器数量
                summary.AppendLine($"服务器数量: {Config.IniReadValue("LoginServer", "serverCount")}");
                
                summary.AppendLine("========================");
                return summary.ToString();
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "获取配置摘要", "");
                return "配置摘要获取失败: " + ex.Message;
            }
        }

        // 修复配置文件编码问题
        public static bool FixConfigEncoding()
        {
            try
            {
                string configPath = Application.StartupPath + "\\config\\config.ini";
                string backupPath = configPath + ".backup." + DateTime.Now.ToString("yyyyMMddHHmmss");
                
                // 备份原文件
                File.Copy(configPath, backupPath);
                Form1.WriteLine(2, $"配置文件已备份至: {backupPath}");
                
                // 尝试用不同编码读取并转换为UTF-8
                string content = null;
                
                // 尝试GB2312编码
                try
                {
                    content = File.ReadAllText(configPath, Encoding.GetEncoding("GB2312"));
                    if (!content.Contains("�"))
                    {
                        File.WriteAllText(configPath, content, Encoding.UTF8);
                        Form1.WriteLine(2, "配置文件编码已修复为UTF-8");
                        return true;
                    }
                }
                catch { }
                
                // 尝试GBK编码
                try
                {
                    content = File.ReadAllText(configPath, Encoding.GetEncoding("GBK"));
                    if (!content.Contains("�"))
                    {
                        File.WriteAllText(configPath, content, Encoding.UTF8);
                        Form1.WriteLine(2, "配置文件编码已修复为UTF-8");
                        return true;
                    }
                }
                catch { }
                
                Form1.WriteLine(1, "无法自动修复配置文件编码，请手动检查");
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.LogSecureException(ex, "修复配置文件编码", "");
                return false;
            }
        }
    }
}

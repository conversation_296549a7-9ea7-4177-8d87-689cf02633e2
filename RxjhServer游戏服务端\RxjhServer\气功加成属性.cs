namespace RxjhServer;

public class 气功加成属性
{
	private int _FLD_PID;

	private int _FLD_INDEX;

	private int _FLD_JOB;

	private double _FLD_每点加成比率值1;

	private double _FLD_每点加成比率值2;

	private string _FLD_NAME;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public int FLD_INDEX
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_JOB
	{
		get
		{
			return _FLD_JOB;
		}
		set
		{
			_FLD_JOB = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public double FLD_每点加成比率值1
	{
		get
		{
			return _FLD_每点加成比率值1;
		}
		set
		{
			_FLD_每点加成比率值1 = value;
		}
	}

	public double FLD_每点加成比率值2
	{
		get
		{
			return _FLD_每点加成比率值2;
		}
		set
		{
			_FLD_每点加成比率值2 = value;
		}
	}

	public static int 取气功位置(int play_job, int 气功ID)
	{
		switch (气功ID)
		{
		case 46:
		case 74:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				6 => 3, 
				4 => 7, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 181:
		case 182:
		case 183:
		case 184:
		case 185:
		case 186:
		case 187:
		case 188:
		case 189:
		case 190:
		case 191:
		case 192:
		case 193:
			switch (play_job)
			{
			case 1:
			case 2:
			case 3:
			case 4:
			case 5:
			case 7:
				return 6;
			case 6:
			case 8:
			case 9:
			case 10:
			case 11:
			case 12:
			case 13:
				return 5;
			default:
				return -1;
			}
		case 11:
		case 251:
			if (play_job != 1 && play_job != 8)
			{
				return -1;
			}
			return 1;
		case 16:
		case 256:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				1 => 5, 
				8 => 7, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 18:
		case 258:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				1 => 9, 
				8 => 11, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 110:
		case 260:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				8 => 10, 
				1 => 11, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 20:
		case 270:
			if (play_job != 2 && play_job != 9)
			{
				return -1;
			}
			return 0;
		case 26:
		case 276:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				9 => 7, 
				2 => 5, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 27:
		case 278:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				9 => 9, 
				2 => 8, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 28:
		case 279:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				9 => 10, 
				2 => 7, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 29:
		case 280:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				9 => 11, 
				2 => 11, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 30:
		case 281:
			if (play_job != 3 && play_job != 12)
			{
				return -1;
			}
			return 0;
		case 12:
		case 22:
		case 32:
		case 72:
		case 283:
			if ((uint)(play_job - 1) > 2u && play_job != 6 && play_job != 12)
			{
				return -1;
			}
			return 2;
		case 35:
		case 284:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				12 => 3, 
				3 => 4, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 36:
		case 288:
			if (play_job != 3 && play_job != 12)
			{
				return -1;
			}
			return 8;
		case 39:
		case 289:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				12 => 9, 
				3 => 5, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 21:
		case 253:
		case 271:
		case 386:
		case 653:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				9 => 1, 
				2 => 1, 
				8 => 3, 
				11 => 2, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 38:
		case 550:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				10 => 0, 
				3 => 7, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 31:
		case 282:
		case 551:
			switch (play_job)
			{
			case 10:
				return 1;
			case 3:
			case 12:
				return 1;
			default:
				return -1;
			}
		case 10:
		case 250:
		case 552:
			switch (play_job)
			{
			default:
				return -1;
			case 10:
				return 2;
			case 1:
			case 8:
				return 0;
			}
		case 14:
		case 24:
		case 34:
		case 44:
		case 64:
		case 254:
		case 274:
		case 285:
		case 553:
			switch (play_job)
			{
			case 1:
			case 4:
			case 10:
				return 3;
			default:
				return -1;
			case 2:
			case 3:
			case 8:
			case 9:
			case 12:
				return 4;
			}
		case 37:
		case 558:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				10 => 4, 
				3 => 11, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 130:
		case 560:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				10 => 11, 
				3 => 9, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 50:
		case 450:
			if (play_job != 5 && play_job != 13)
			{
				return -1;
			}
			return 0;
		case 51:
		case 451:
			if (play_job != 5 && play_job != 13)
			{
				return -1;
			}
			return 1;
		case 53:
		case 454:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				13 => 4, 
				5 => 3, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 54:
		case 456:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				13 => 7, 
				5 => 4, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		case 55:
		case 457:
		{
			if (1 == 0)
			{
			}
			int result = play_job switch
			{
				13 => 8, 
				5 => 5, 
				_ => -1, 
			};
			if (1 == 0)
			{
			}
			return result;
		}
		default:
			foreach (气功加成属性 value in World.气功加成.Values)
			{
				if (value.FLD_JOB == play_job && value.FLD_PID == 气功ID)
				{
					return value.FLD_INDEX;
				}
			}
			return -1;
		case 17:
		case 57:
		case 257:
		case 287:
		case 459:
			switch (play_job)
			{
			case 1:
			case 5:
				return 7;
			default:
				return -1;
			case 13:
				return 10;
			case 12:
				return 6;
			case 8:
				return 8;
			}
		}
	}
}

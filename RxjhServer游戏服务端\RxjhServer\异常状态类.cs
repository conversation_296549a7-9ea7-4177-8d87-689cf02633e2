using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class 异常状态类 : IDisposable
{
	public System.Timers.Timer npcyd;

	public System.Timers.Timer yczt;

	public double ycztsl;

	public DateTime time;

	public Players Play;

	public Players PlayS;

	public NpcClass Npc;

	public int NpcPlayId;

	private int _FLD_PID;

	private double _FLD_NUM;

	public int 下毒人物;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_NUM
	{
		get
		{
			return _FLD_NUM;
		}
		set
		{
			_FLD_NUM = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-Dispose");
		}
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (yczt != null)
		{
			yczt.Enabled = false;
			yczt.Close();
			yczt.Dispose();
			yczt = null;
		}
		Play = null;
		Npc = null;
	}

	public 异常状态类(Players Play_, int 时间, int 异常ID, double 异常数量)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-NEW");
		}
		FLD_PID = 异常ID;
		FLD_NUM = 异常数量;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Play = Play_;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 时间结束事件1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000);
	}

	public 异常状态类(Players Play_, int 时间, int 异常ID, double 异常数量, int 每秒执行, int 下毒者)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-NEW");
		}
		FLD_PID = 异常ID;
		FLD_NUM = 异常数量;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Play = Play_;
		下毒人物 = 下毒者;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 时间结束事件1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000, 每秒执行);
	}

	public void 异常状态类出血人物(double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-异常状态类出血人物");
		}
		ycztsl = ycztsll;
		yczt = new System.Timers.Timer(1000.0);
		yczt.Elapsed += yczt_Elapsed;
		yczt.Enabled = true;
		yczt.AutoReset = true;
	}

	public void 异常状态类出血怪物(Players Play, double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-异常状态类出血怪物");
		}
		PlayS = Play;
		ycztsl = ycztsll;
		yczt = new System.Timers.Timer(1000.0);
		yczt.Elapsed += yczt_Elapsed;
		yczt.Enabled = true;
		yczt.AutoReset = true;
	}

	public void 异常状态类出血红潮散(double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-异常状态类出血红潮散");
		}
		ycztsl = ycztsll;
		yczt = new System.Timers.Timer(3000.0);
		yczt.Elapsed += yczt_Elapsed;
		yczt.Enabled = true;
		yczt.AutoReset = true;
	}

	public void 异常状态类掉蓝(double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-异常状态类出血");
		}
		ycztsl = ycztsll;
		yczt = new System.Timers.Timer(3000.0);
		yczt.Elapsed += yczt_Elapsed蓝;
		yczt.Enabled = true;
		yczt.AutoReset = true;
	}

	public void yczt_Elapsed蓝(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "yczt_Elapsed");
		}
		if (Play != null && !Play.Player死亡 && !Play.退出中 && !Play.交易.交易中 && !Play.打开仓库中 && !Play.进店中)
		{
			Play.人物_MP -= (int)ycztsl;
			if (Play.人物_MP <= 0)
			{
				Play.人物_MP = 0;
			}
			Play.更新HP_MP_SP();
		}
	}

	public void yczt_Elapsed(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "yczt_Elapsed");
		}
		if (Play != null)
		{
			if (Play.Player死亡 || Play.退出中 || Play.交易.交易中 || Play.打开仓库中 || Play.进店中)
			{
				return;
			}
			Play.人物_HP -= (int)ycztsl;
			if (Play.人物_HP <= 0)
			{
				Play.人物死亡(下毒人物);
				if (yczt != null)
				{
					yczt.Enabled = false;
					yczt.Close();
					yczt.Dispose();
					yczt = null;
				}
			}
			Play.更新HP_MP_SP();
		}
		else
		{
			if (Npc == null)
			{
				return;
			}
			Npc.Rxjh_HP -= (int)ycztsl;
			Npc.Play_Add(PlayS, (int)ycztsl);
			if (Npc.Rxjh_HP <= 0)
			{
				Npc.发送死亡数据(NpcPlayId);
				if (yczt != null)
				{
					yczt.Enabled = false;
					yczt.Close();
					yczt.Dispose();
					yczt = null;
				}
			}
		}
	}

	public 异常状态类(NpcClass Play_, int _NpcPlayId, int 时间, int 异常ID, double 异常数量)
	{
		NpcPlayId = _NpcPlayId;
		FLD_PID = 异常ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Npc = Play_;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 时间结束事件1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		FLD_NUM = 异常数量;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000);
	}

	public 异常状态类(Players Players, NpcClass Play_, int _NpcPlayId, int 时间, int 异常ID, double 异常数量)
	{
		PlayS = Players;
		NpcPlayId = _NpcPlayId;
		FLD_PID = 异常ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Npc = Play_;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 时间结束事件1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		FLD_NUM = 异常数量;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000);
	}

	public 异常状态类(NpcClass Play_, int _NpcPlayId, int 时间, int 异常ID, double 异常数量, int 每秒执行)
	{
		NpcPlayId = _NpcPlayId;
		FLD_PID = 异常ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(时间);
		Npc = Play_;
		npcyd = new System.Timers.Timer(时间);
		npcyd.Elapsed += 时间结束事件1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		FLD_NUM = 异常数量;
		状态效果(FLD_PID, 1, (int)异常数量, 时间 / 1000, 每秒执行);
	}

	public void 时间结束事件1(object source, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "时间结束事件1");
		}
		时间结束事件();
	}

	public void 时间结束事件()
	{
		int num = 0;
		try
		{
			if (npcyd != null)
			{
				num = 1;
				npcyd.Enabled = false;
				num = 2;
				npcyd.Close();
				num = 3;
				npcyd.Dispose();
				num = 4;
				npcyd = null;
			}
			num = 5;
			异常状态类 value;
			if (Npc != null)
			{
				num = 6;
				if (Npc.异常状态 != null)
				{
					num = 7;
					Npc.异常状态.TryRemove(FLD_PID, out value);
				}
				num = 8;
				switch (FLD_PID)
				{
				case 9:
				case 22:
					Npc.FLD_JSDF = 0.0;
					break;
				case 25:
					Npc.FLD_JSAT = 0.0;
					break;
				case 44:
				{
					if (World.阎王爆范围爆炸是否开启 == 1)
					{
						Npc.触发阎王爆(PlayS, NpcPlayId);
						break;
					}
					int num2 = (int)((double)Npc.阎王爆累计伤害 * (1.0 + PlayS.神女尸毒爆发)) / World.阎王爆伤害降低百分比;
					if (num2 >= 1)
					{
						if (Npc.Rxjh_HP > num2)
						{
							Npc.Play_Add(PlayS, num2);
							Npc.Rxjh_HP -= num2;
						}
						else
						{
							Npc.Play_Add(PlayS, Npc.Rxjh_HP);
							Npc.Rxjh_HP = 0;
						}
						Npc.发送阎王爆攻击数据(num2);
						if (Npc.Rxjh_HP <= 0 && !Npc.NPC死亡)
						{
							Npc.发送死亡数据(NpcPlayId);
						}
						Npc.阎王爆累计伤害 = 0;
					}
					Npc.怪物阎王爆 = false;
					break;
				}
				}
				num = 9;
				状态效果(FLD_PID, 0, 0, 0);
				num = 10;
				Dispose();
			}
			else if (Play != null)
			{
				num = 11;
				switch (FLD_PID)
				{
				case 1:
					Play.五鼓迷魂香_减少攻击 = 0.0;
					Play.更新武功和状态();
					break;
				case 13:
					Play.十香软筋散_减少攻击 = 0.0;
					Play.更新武功和状态();
					break;
				case 22:
					Play.琴师_阳明春晓_减少攻击 = 0.0;
					Play.更新武功和状态();
					break;
				case 14:
					Play.卸甲散_减少防御 = 0.0;
					Play.更新武功和状态();
					break;
				case 25:
					Play.琴师_潇湘雨夜_减少防御 = 0.0;
					Play.更新武功和状态();
					break;
				case 3:
					Play.中毒 = false;
					break;
				case 4:
					Play.人物锁定 = false;
					break;
				case 12:
					Play.天水_减少生命 = 0.0;
					Play.更新HP_MP_SP();
					break;
				case 15:
					Play.迷魂散_减少内功 = 0.0;
					Play.更新HP_MP_SP();
					break;
				case 8:
					Play.人物锁定 = false;
					break;
				case 9:
					Play.addFLD_追加百分比_防御(0.07);
					Play.更新武功和状态();
					break;
				case 17:
					Play.人物锁定 = false;
					break;
				case 26:
					Play.人物锁定 = false;
					break;
				case 54:
					Play.人物锁定 = false;
					break;
				case 28:
					Play.安全模式 = 1;
					Play.人物锁定 = false;
					break;
				}
				num = 22;
				if (Play.异常状态 != null)
				{
					num = 23;
					Play.异常状态.TryRemove(FLD_PID, out value);
				}
				num = 24;
				状态效果(FLD_PID, 0, 0, 0);
				num = 25;
				Dispose();
			}
		}
		catch (Exception ex)
		{
			PlayS.报错次数阀值++;
			if (World.是否开启票红字2 == 1)
			{
				RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "异常状态类", "时间结束事件", $"玩家ID: {FLD_PID}");
				Form1.WriteLine(100, num + "|异常状态类时间结束事件出错[" + FLD_PID + "]" + ex);
			}
		}
		finally
		{
			Dispose();
		}
	}

	public void 状态效果(int 异常ID, int 开关, int 异常数量, int 时间, int 每秒执行)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-状态效果");
		}
		byte[] array = Converter.hexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(每秒执行), 0, array, 54, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
		if (Play != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send多包(array, array.Length);
			}
			Play.发送当前范围广播数据多包(array, array.Length);
		}
		else if (Npc != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
			Npc.广播数据(array, array.Length);
		}
	}

	public void 状态效果(int 异常ID, int 开关, int 异常数量, int 时间)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "异常状态类-状态效果");
		}
		byte[] array = Converter.hexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
		if (Play != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Play.人物全服ID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send多包(array, array.Length);
			}
			Play.发送当前范围广播数据多包(array, array.Length);
		}
		else if (Npc != null)
		{
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
			Npc.广播数据(array, array.Length);
		}
	}
}

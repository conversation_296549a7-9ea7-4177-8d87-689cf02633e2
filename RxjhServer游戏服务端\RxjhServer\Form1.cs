using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using ClassLibrary;
using NLua;
using RxjhServer.bbg;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using RxjhTool;

namespace RxjhServer;

public class Form1 : Form
{
    private 服务器维护类 serverMaintenance;  //EVIAS 维护

    private static List<TxtClass> txt = new List<TxtClass>();

	private static int kjdx = 300;

	private static bool _needsRefresh = false;
	private static DateTime _lastRefreshTime = DateTime.MinValue;

	private static int _scrollOffset = 0; 
	private static bool _autoScroll = true; 

	private System.Timers.Timer 狮子吼 = new System.Timers.Timer(21000.0);

	private DateTime sj = DateTime.Now;

	private System.Timers.Timer 复查IP用户;

	private int 自动公告ID;

	private static World world;

	private Thread thThreadRead;

	private static Thread timerThread;

	private RxjhServer.Network.Listener l;

	private bool runn;
	private StatusStrip statusStrip1; 
	private MenuStrip menuStrip1;
	private ToolStripMenuItem toolStrip仙魔大战;
	private ToolStripMenuItem toolStrip攻城战;
	private ToolStripMenuItem toolStrip帮战;
	private ToolStripMenuItem toolStrip武林血战;
	private ToolStripMenuItem toolStrip异口同声;
	private ToolStripMenuItem toolStrip全服双倍;
	private ToolStripMenuItem toolStrip幸运玩家;
	private ToolStripMenuItem toolStripBoss攻城;
	private ToolStripMenuItem toolStrip世界BOSS;
	private ToolStripMenuItem toolStrip势力战;
	private ToolStripMenuItem toolStrip野外BOSS;
	private ToolStripMenuItem toolStrip死亡无敌;
	private ToolStripMenuItem toolStrip比武泡点;
	private ToolStripMenuItem toolStrip天降红包;
	private ToolStripMenuItem toolStrip南林钟离;
	private ToolStripMenuItem toolStrip大乱斗;
	private ToolStripMenuItem toolStrip极限比武;
	private ImageList imageList1;
	private FlickerFreePanel GraphPanel;
	private System.Windows.Forms.Timer timer1;
	private ToolStrip toolStrip1;
	private ToolStripTextBox toolStripTextBox1;
	private ToolStripButton toolStripButton1;
	private ToolStripTextBox toolStripTextBox2;
	private ToolStripComboBox toolStripComboBox1;
	private ToolStripButton toolStripButton2;
	private ToolStripSeparator toolStripSeparator1;
	private ToolStripButton toolStripButton3;
	private ToolStripSeparator toolStripSeparator2;
	private ToolStripButton toolStripButton4;
	private ToolStripComboBox toolStripComboBox2;

    // 2025-0618 EVIAS 保留重要功能的MenuItem引用（仅保留仍在使用的）
    private MenuItem menuItem3;   // 停止登陆服务
    private MenuItem menuItem17;  // 存档人物

    private IContainer components;

	public Form1()
	{
		InitializeComponent();

		this.KeyPreview = true; 
		this.KeyDown += Form1_KeyDown;
		this.GraphPanel.MouseWheel += GraphPanel_MouseWheel;
		this.Load += (s, e) => 调整GraphPanel布局();
		this.Resize += (s, e) => 调整GraphPanel布局();
	}

	private void 调整GraphPanel布局()
	{
		try
		{
			int menuStripHeight = menuStrip1?.Height ?? 0;
			int toolStripHeight = toolStrip1.Height;
			int statusStripHeight = statusStrip1.Height;
			int totalTopHeight = menuStripHeight + toolStripHeight;
			int availableHeight = this.ClientSize.Height - totalTopHeight - statusStripHeight;
			// ToolStrip设置了Dock=Top，会自动紧贴MenuStrip，不需要额外间隙
			GraphPanel.Location = new Point(0, totalTopHeight);
			GraphPanel.Size = new Size(this.ClientSize.Width, availableHeight);
		}
		catch (Exception ex)
		{
			RxjhClass.HandleUIException(ex, "GraphPanel布局调整", $"窗体大小: {this.ClientSize}");
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            this.statusStrip1 = new System.Windows.Forms.StatusStrip(); 
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripTextBox1 = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripComboBox2 = new System.Windows.Forms.ToolStripComboBox();
            this.toolStripButton1 = new System.Windows.Forms.ToolStripButton();
            this.toolStripTextBox2 = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripComboBox1 = new System.Windows.Forms.ToolStripComboBox();
            this.toolStripButton2 = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripButton3 = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripButton4 = new System.Windows.Forms.ToolStripButton();
            this.GraphPanel = new RxjhServer.FlickerFreePanel();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            //
            // statusStrip1 
            //
            this.statusStrip1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.statusStrip1.Location = new System.Drawing.Point(0, 433);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(755, 22);
            this.statusStrip1.TabIndex = 6;
            this.statusStrip1.SizingGrip = false; 
            this.statusStrip1.Stretch = true; 
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "");
            this.imageList1.Images.SetKeyName(1, "");
            this.imageList1.Images.SetKeyName(2, "");
            this.imageList1.Images.SetKeyName(3, "");
            this.imageList1.Images.SetKeyName(4, "");
            //
            // timer1
            // 
            this.timer1.Enabled = true;
            this.timer1.Interval = 1000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // toolStrip1
            // 
            this.toolStrip1.Dock = System.Windows.Forms.DockStyle.Top;
            this.toolStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripTextBox1,
            this.toolStripComboBox2,
            this.toolStripButton1,
            this.toolStripTextBox2,
            this.toolStripComboBox1,
            this.toolStripButton2,
            this.toolStripSeparator1,
            this.toolStripButton3,
            this.toolStripSeparator2,
            this.toolStripButton4});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(755, 33);
            this.toolStrip1.TabIndex = 15;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripTextBox1
            // 
            this.toolStripTextBox1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F);
            this.toolStripTextBox1.Name = "toolStripTextBox1";
            this.toolStripTextBox1.Size = new System.Drawing.Size(330, 33);
            this.toolStripTextBox1.Text = resources.GetString("toolStripTextBox1.Text");
            // 
            // toolStripComboBox2
            // 
            this.toolStripComboBox2.DropDownWidth = 50;
            this.toolStripComboBox2.IntegralHeight = false;
            this.toolStripComboBox2.Items.AddRange(new object[] {
            "正常",
            "加密"});
            this.toolStripComboBox2.Name = "toolStripComboBox2";
            this.toolStripComboBox2.Size = new System.Drawing.Size(100, 33);
            this.toolStripComboBox2.Text = "正常";
            // 
            // toolStripButton1
            // 
            this.toolStripButton1.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton1.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButton1.Image")));
            this.toolStripButton1.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton1.Name = "toolStripButton1";
            this.toolStripButton1.Size = new System.Drawing.Size(50, 28);
            this.toolStripButton1.Text = "发送";
            this.toolStripButton1.Click += new System.EventHandler(this.toolStripButton1_Click);
            // 
            // toolStripTextBox2
            // 
            this.toolStripTextBox2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F);
            this.toolStripTextBox2.Name = "toolStripTextBox2";
            this.toolStripTextBox2.Size = new System.Drawing.Size(140, 33);
            this.toolStripTextBox2.Text = "空";
            // 
            // toolStripComboBox1
            // 
            this.toolStripComboBox1.Items.AddRange(new object[] {
            "10",
            "9"});
            this.toolStripComboBox1.Name = "toolStripComboBox1";
            this.toolStripComboBox1.Size = new System.Drawing.Size(110, 33);
            this.toolStripComboBox1.Text = "10";
            // 
            // toolStripButton2
            // 
            this.toolStripButton2.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton2.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButton2.Image")));
            this.toolStripButton2.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton2.Name = "toolStripButton2";
            this.toolStripButton2.Size = new System.Drawing.Size(50, 28);
            this.toolStripButton2.Text = "发送";
            this.toolStripButton2.Click += new System.EventHandler(this.toolStripButton2_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 33);
            // 
            // toolStripButton3
            // 
            this.toolStripButton3.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton3.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButton3.Image")));
            this.toolStripButton3.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton3.Name = "toolStripButton3";
            this.toolStripButton3.Size = new System.Drawing.Size(86, 28);
            this.toolStripButton3.Text = "复查活动";
            this.toolStripButton3.Click += new System.EventHandler(this.toolStripButton3_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 33);
            // 
            // toolStripButton4
            // 
            this.toolStripButton4.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButton4.Image = ((System.Drawing.Image)(resources.GetObject("toolStripButton4.Image")));
            this.toolStripButton4.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton4.Name = "toolStripButton4";
            this.toolStripButton4.Size = new System.Drawing.Size(34, 28);
            this.toolStripButton4.Text = "查";
            this.toolStripButton4.Click += new System.EventHandler(this.toolStripButton4_Click);
            // 
            // GraphPanel
            // 
            this.GraphPanel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.GraphPanel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.GraphPanel.BackColor = System.Drawing.Color.White;
            this.GraphPanel.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.GraphPanel.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.GraphPanel.Location = new System.Drawing.Point(0, 33);
            this.GraphPanel.Name = "GraphPanel";
            this.GraphPanel.Size = new System.Drawing.Size(755, 400); 
            this.GraphPanel.TabIndex = 0;
            this.GraphPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.GraphPanel_Paint);
            //
            // Form1
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(755, 455);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.GraphPanel);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "RxjhServer";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.Layout += new System.Windows.Forms.LayoutEventHandler(this.Form1_Layout);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
	}


    [STAThread] //EVIAS 注册
    private static void Main()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(defaultValue: false);
        RegisterForm form = new RegisterForm();
        World.Key1 = Config.IniReadValue("GameServer", "Key1").Trim();
        World.Key2 = Config.IniReadValue("GameServer", "Key2").Trim();
        try
        {
            if (form.qdyzcod())
            {
                Application.Run(new Form1());
            }
            else
            {
                Application.Run(new RegisterForm());
            }
        }
        catch (Exception ex)
        {
            RxjhClass.HandleUIException(ex, "应用程序启动", "Main方法异常");
            MessageBox.Show("应用程序启动失败: " + ex.Message);
        }
    }


    private void Form1_Load(object sender, EventArgs e)
	{
		try
		{
			timerThread = new Thread(new Timer.TimerThread().TimerMain);
			timerThread.Name = "Timer  Thread";
			timerThread.Start();
			world = new World();
			world.SetConfig();
			world.SetConfig2();
			天魔神宫.读取天魔神宫占领数据();
			if (World.检查数据库配置())
			{
				world.SetMonSter();
				world.SetNpc();
				world.SetDrop();
				world.Set_GSDrop();
				world.SetBossDrop();
				world.SetOpen();
				world.Set套装();
				world.SetBbgItem();
				world.加载百宝阁抽奖();
				world.SetChouJiang();
				world.SetLever();
				world.SetWxLever();
				world.SetKONGFU();
				world.SetItme();
				world.SetShop();
				world.SetMover();
				world.Set公告();
				world.Set等级奖励();
				world.Set假人等级奖励();
				world.Set比武泡点奖励();
				world.Set大乱斗奖励();
				world.Set物品兑换();
				world.Set神器兑换();
				world.Set移动();
				world.Set制药物品();
				world.Set安全区();
				world.SetKill();
				world.SetJianc();
				world.Set升天气功();
				world.假人商店出售物品();
				world.Set制作物品();
				world.SetScript();
				world.Set任务数据新();
				world.SetQG();
				world.Set石头属性();
				world.加载职业系数();
				world.强化概率参数();
				world.充值排行();
				world.荣誉门派排行();
				world.荣誉势力排行();
				world.荣誉武林排行();
				world.荣誉讨伐排行();
				world.boss攻城怪物();
				world.boss伏魔洞怪物();
				world.boss冰宫内城怪物();
				world.boss活动副本怪物();
				world.加载门战数据();
				world.加载门派战绩();
				world.安全地图区域();
				world.加载物品回收();
				world.加载装备洗髓();
				world.加载装备首爆();
				world.加载英雄职业武器();
				world.加载累计充值称号();
				world.SetLjcz();
				world.Set冲关地图();
				world.Set兑换码1();
				World.更新所有排行数据();
				world.Set道具组合();
                world.进阶装备(); //新增1  EVIAS
                world.分解装备(); //EVIAS  
                World.conn = new Connect();
				World.conn.Sestup();
				l = new RxjhServer.Network.Listener((ushort)World.游戏服务器端口);
				new AuthServer(World.老百宝阁服务器IP, World.老百宝阁服务器端口);
				Text = World.服务器名 + " - RxjhServer_" + Assembly.GetExecutingAssembly().GetName().Version.ToString();
				thThreadRead = new Thread(FlushAll);
				thThreadRead.Name = "FlushAll";
				thThreadRead.Priority = ThreadPriority.Lowest;
				thThreadRead.Start();
				Timer.DelayCall(TimeSpan.FromMilliseconds(World.公告刷新时间 * 1000), TimeSpan.FromMilliseconds(World.公告刷新时间 * 1000), 自动公告事件);
				Timer.DelayCall(TimeSpan.FromMilliseconds(World.排名刷新时间 * 1000), TimeSpan.FromMilliseconds(World.排名刷新时间 * 1000), 自动更新荣誉);
				狮子吼 = new System.Timers.Timer(21000.0);
				狮子吼.Elapsed += 狮子吼事件;
				狮子吼.AutoReset = true;
				狮子吼.Enabled = true;
				复查IP用户 = new System.Timers.Timer(60000.0);
				复查IP用户.Elapsed += 复查IP用户事件;
				复查IP用户.AutoReset = true;
				复查IP用户.Enabled = true;
				Connsend.Send(World.sql, "启动成功", "启动成功", "0");
				Thread1();
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, "Form1_Load  错误" + ex.Message);
		}

		初始化StatusStrip(); 
		创建MenuStrip菜单();
		初始化服务器维护系统();
		启动数据库监控定时器();
		启动内存监控系统();
		启动性能优化系统();
	}

	private void 初始化StatusStrip()
	{
		try
		{
			statusStrip1.Items.Clear();

			var lblConnections = new ToolStripStatusLabel();
			lblConnections.Name = "lblConnections";
			lblConnections.Text = " 连:0 在:0 云:0 假:0 商:0 ";
			lblConnections.Spring = true; 
			lblConnections.TextAlign = ContentAlignment.MiddleLeft;
			lblConnections.BorderSides = ToolStripStatusLabelBorderSides.None;
			statusStrip1.Items.Add(lblConnections);

			var lblGameObjects = new ToolStripStatusLabel();
			lblGameObjects.Name = "lblGameObjects";
			lblGameObjects.Text = "| 物品:0 怪物:0 ";
			lblGameObjects.Spring = true; 
			lblGameObjects.TextAlign = ContentAlignment.MiddleLeft;
			lblGameObjects.BorderSides = ToolStripStatusLabelBorderSides.None;
			statusStrip1.Items.Add(lblGameObjects);

			var lblDatabase = new ToolStripStatusLabel();
			lblDatabase.Name = "lblDatabase";
			lblDatabase.Text = "| DB活跃:0 总:0 成功:0 ";
			lblDatabase.Spring = true; 
			lblDatabase.TextAlign = ContentAlignment.MiddleLeft;
			lblDatabase.BorderSides = ToolStripStatusLabelBorderSides.None;
			statusStrip1.Items.Add(lblDatabase);

			var lblNetworkStats = new ToolStripStatusLabel();
			lblNetworkStats.Name = "lblNetworkStats";
			lblNetworkStats.Text = "| 收:0B/s 发:0B/s 包:0 ";
			lblNetworkStats.Spring = true; 
			lblNetworkStats.TextAlign = ContentAlignment.MiddleLeft;
			lblNetworkStats.BorderSides = ToolStripStatusLabelBorderSides.None;
			statusStrip1.Items.Add(lblNetworkStats);

			var lblMemory = new ToolStripStatusLabel();
			lblMemory.Name = "lblMemory";
			lblMemory.Text = "| 内存:0MB GC:0/0/0";
			lblMemory.Spring = true; 
			lblMemory.TextAlign = ContentAlignment.MiddleLeft;
			lblMemory.BorderSides = ToolStripStatusLabelBorderSides.None;
			statusStrip1.Items.Add(lblMemory);
		}
		catch (Exception ex)
		{
			WriteLine(1, "StatusStrip初始化错误: " + ex.Message);
		}
	}

	private void 创建MenuStrip菜单()
	{
		try
		{
			menuStrip1.Dock = DockStyle.Top;
			menuStrip1.Font = new Font("Microsoft YaHei", 9F, FontStyle.Regular);
			menuStrip1.BackColor = SystemColors.Menu;
			menuStrip1.Height = 28; 
			menuStrip1.GripMargin = new Padding(2, 2, 0, 2); 
			menuStrip1.Padding = new Padding(6, 2, 0, 2); 

			// 创建顶级菜单项
			var 服务菜单 = new ToolStripMenuItem("服务");
			var 控制菜单 = new ToolStripMenuItem("控制");
			var 重读菜单 = new ToolStripMenuItem("重读");
			var 在线工具菜单 = new ToolStripMenuItem("在线工具");
			var 其它工具菜单 = new ToolStripMenuItem("其它工具");
			var 清理开区菜单 = new ToolStripMenuItem("清理开区");
			var 服务器维护菜单 = new ToolStripMenuItem("服务器维护"); 
			var 其它菜单 = new ToolStripMenuItem("其它");

			添加服务菜单项(服务菜单);
			添加控制菜单项(控制菜单);
			添加重读菜单项(重读菜单);
			添加在线工具菜单项(在线工具菜单);
			添加其它工具菜单项(其它工具菜单);
			添加清理开区菜单项(清理开区菜单);
			添加服务器维护菜单项(服务器维护菜单); 
			添加其它菜单项(其它菜单);
			设置菜单项样式(服务菜单);
			设置菜单项样式(控制菜单);
			设置菜单项样式(重读菜单);
			设置菜单项样式(在线工具菜单);
			设置菜单项样式(其它工具菜单);
			设置菜单项样式(清理开区菜单);
			设置菜单项样式(服务器维护菜单); 
			设置菜单项样式(其它菜单);

			menuStrip1.Items.AddRange(new ToolStripItem[] {
				服务菜单, 控制菜单, 重读菜单, 在线工具菜单, 其它工具菜单, 清理开区菜单, 服务器维护菜单, 其它菜单
			});

			this.MainMenuStrip = menuStrip1;
			this.Controls.Add(menuStrip1);
			menuStrip1.SendToBack(); 
			this.Menu = null;

		}
		catch (Exception ex)
		{
			WriteLine(1, $"创建MenuStrip菜单失败: {ex.Message}");
		}
	}

	private void 设置菜单项样式(ToolStripMenuItem menuItem)
	{
		try
		{
			menuItem.BackColor = SystemColors.Menu;
			menuItem.ForeColor = Color.Black;

			menuItem.MouseEnter += (s, e) => {
				menuItem.BackColor = Color.FromArgb(179, 215, 243); // #b3d7f3
			};

			menuItem.MouseLeave += (s, e) => {
				menuItem.BackColor = SystemColors.Menu;
			};
		}
		catch (Exception ex)
		{
			WriteLine(1, $"设置菜单项样式失败: {ex.Message}");
		}
	}

	private void 添加服务菜单项(ToolStripMenuItem 服务菜单)
	{
		try
		{
			// 开始登陆服务
			var 开始登陆 = new ToolStripMenuItem("开始登陆服务");
			开始登陆.Click += (s, e) => menuItem32_Click(s, e);
			服务菜单.DropDownItems.Add(开始登陆);

			// 停止登陆服务
			var 停止登陆 = new ToolStripMenuItem("停止登陆服务");
			停止登陆.Click += (s, e) => menuItem3_Click(s, e);
			服务菜单.DropDownItems.Add(停止登陆);
			menuItem3 = new MenuItem("停止登陆服务", (s, e) => menuItem3_Click(s, e));

			// 存档人物
			var 存档人物 = new ToolStripMenuItem("存档人物");
			存档人物.Click += (s, e) => menuItem17_Click(s, e);
			服务菜单.DropDownItems.Add(存档人物);
			menuItem17 = new MenuItem("存档人物", (s, e) => menuItem17_Click(s, e));

			// 存档所有
			var 存档所有 = new ToolStripMenuItem("存档所有");
			存档所有.Click += (s, e) => menuItem31_Click(s, e);
			服务菜单.DropDownItems.Add(存档所有);

			// 添加分隔符
			服务菜单.DropDownItems.Add(new ToolStripSeparator());

			// 禁止线路PK
			var 禁止PK = new ToolStripMenuItem("禁止线路PK");
			禁止PK.Checked = World.禁止PK == 1;
			禁止PK.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.禁止PK = 0;
						WriteLine(55, "关闭禁止线路PK");
					}
					else
					{
						item.Checked = true;
						World.禁止PK = 1;
						WriteLine(55, "开启禁止线路PK");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "禁止PK切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(禁止PK);

			// 禁止线路开店
			var 禁止开店 = new ToolStripMenuItem("禁止线路开店");
			禁止开店.Checked = World.禁止开店 == 1;
			禁止开店.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.禁止开店 = 0;
						WriteLine(55, "关闭禁止线路开店");
					}
					else
					{
						item.Checked = true;
						World.禁止开店 = 1;
						WriteLine(55, "开启禁止线路开店");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "禁止开店切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(禁止开店);

			// 禁止线路交易
			var 禁止交易 = new ToolStripMenuItem("禁止线路交易");
			禁止交易.Checked = World.禁止交易 == 1;
			禁止交易.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.禁止交易 = 0;
						WriteLine(55, "关闭禁止线路交易");
					}
					else
					{
						item.Checked = true;
						World.禁止交易 = 1;
						WriteLine(55, "开启禁止线路交易");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "禁止交易切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(禁止交易);

			// 添加分隔符
			服务菜单.DropDownItems.Add(new ToolStripSeparator());

			// 开启下雪场景
			var 下雪场景 = new ToolStripMenuItem("开启下雪场景");
			下雪场景.Checked = World.开启下雪场景 == 1;
			下雪场景.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.开启下雪场景 = 0;
						WriteLine(55, "关闭下雪场景");
					}
					else
					{
						item.Checked = true;
						World.开启下雪场景 = 1;
						WriteLine(55, "开启下雪场景");
					}
					// 更新所有玩家的场景
					foreach (Players value in World.allConnectedChars.Values)
					{
						value.更新服务器时间和场景();
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "下雪场景切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(下雪场景);

			// 开启账号锁安全码
			var 安全码 = new ToolStripMenuItem("开启账号锁安全码");
			安全码.Checked = World.是否开启安全码 == 1;
			安全码.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.是否开启安全码 = 0;
						WriteLine(2, "关闭安全码完成");
					}
					else
					{
						item.Checked = true;
						World.是否开启安全码 = 1;
						WriteLine(2, "开启安全码完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "安全码切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(安全码);

			// 开启攻击错误提示
			var 攻击错误 = new ToolStripMenuItem("开启攻击错误提示");
			攻击错误.Checked = World.是否开启票红字 == 1;
			攻击错误.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.是否开启票红字 = 0;
						WriteLine(2, "关闭错误红字完成");
					}
					else
					{
						item.Checked = true;
						World.是否开启票红字 = 1;
						WriteLine(2, "开启错误红字完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "攻击错误切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(攻击错误);

			// 开启公告掉落提示
			var 公告掉落 = new ToolStripMenuItem("开启公告掉落提示");
			公告掉落.Checked = World.是否开启公告掉落提示 == 1;
			公告掉落.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.是否开启公告掉落提示 = 0;
						WriteLine(2, "关闭公告掉落完成");
					}
					else
					{
						item.Checked = true;
						World.是否开启公告掉落提示 = 1;
						WriteLine(2, "开启公告掉落完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "公告掉落切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(公告掉落);

			// 开启其他错误提示
			var 其他错误 = new ToolStripMenuItem("开启其他错误提示");
			其他错误.Checked = World.是否开启票红字2 == 1;
			其他错误.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.是否开启票红字2 = 0;
						WriteLine(2, "关闭其他错误完成");
					}
					else
					{
						item.Checked = true;
						World.是否开启票红字2 = 1;
						WriteLine(2, "开启其他错误完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "其他错误切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(其他错误);

			// 是否开启推广返利
			var 推广返利 = new ToolStripMenuItem("是否开启推广返利");
			推广返利.Checked = World.是否开启推广返利 == 1;
			推广返利.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.是否开启推广返利 = 0;
						WriteLine(2, "关闭推广返利完成");
					}
					else
					{
						item.Checked = true;
						World.是否开启推广返利 = 1;
						WriteLine(2, "开启推广返利完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "推广返利切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(推广返利);

			// 是否开启删除人物
			var 删除人物 = new ToolStripMenuItem("是否开启删除人物");
			删除人物.Checked = World.开启人物删除 == 1;
			删除人物.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.开启人物删除 = 0;
						WriteLine(55, "关闭人物删除");
					}
					else
					{
						item.Checked = true;
						World.开启人物删除 = 1;
						WriteLine(55, "开启人物删除");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "删除人物切换失败: " + ex.Message);
				}
			};
			服务菜单.DropDownItems.Add(删除人物);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加服务菜单项失败: {ex.Message}");
		}
	}

	private void 添加控制菜单项(ToolStripMenuItem 控制菜单)
	{
		try
		{
			// 用户
			var 用户 = new ToolStripMenuItem("用户");
			用户.Click += (s, e) => menuItem5_Click(s, e);
			控制菜单.DropDownItems.Add(用户);

			var 组队 = new ToolStripMenuItem("组队");
			组队.Click += (s, e) => menuItem27_Click(s, e);
			控制菜单.DropDownItems.Add(组队);

			var 地面物品 = new ToolStripMenuItem("地面物品");
			地面物品.Click += (s, e) => menuItem37_Click(s, e);
			控制菜单.DropDownItems.Add(地面物品);

			控制菜单.DropDownItems.Add(new ToolStripSeparator());

			var 显示记录 = new ToolStripMenuItem("显示记录");
			显示记录.Checked = World.AlWorldlog; 
			显示记录.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.AlWorldlog = false;
					}
					else
					{
						item.Checked = true;
						World.AlWorldlog = true;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "显示记录切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(显示记录);

			var 显示掉落 = new ToolStripMenuItem("显示掉落");
			显示掉落.Checked = World.Droplog; 
			显示掉落.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.Droplog = false;
					}
					else
					{
						item.Checked = true;
						World.Droplog = true;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "显示掉落切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(显示掉落);

			var 记录日志 = new ToolStripMenuItem("记录日志");
			记录日志.Checked = World.jllog == 1; 
			记录日志.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.jllog = 0;
					}
					else
					{
						item.Checked = true;
						World.jllog = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "记录日志切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(记录日志);

			var 记录封包 = new ToolStripMenuItem("记录封包");
			记录封包.Checked = World.Log == 1; 
			记录封包.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.Log = 0;
					}
					else
					{
						item.Checked = true;
						World.Log = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "记录封包切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(记录封包);

			// 验证服务器
			var 验证服务器 = new ToolStripMenuItem("验证服务器");
			验证服务器.Checked = World.验证服务器log == 1; 
			验证服务器.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.验证服务器log = 0;
					}
					else
					{
						item.Checked = true;
						World.验证服务器log = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "验证服务器切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(验证服务器);

			// 查复制
			var 查复制 = new ToolStripMenuItem("查复制");
			查复制.Checked = World.AllItmelog == 1; // 根据实际状态设置
			查复制.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.AllItmelog = 0;
					}
					else
					{
						item.Checked = true;
						World.AllItmelog = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "查复制切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(查复制);

			// 查SQL
			var 查SQL = new ToolStripMenuItem("查SQL");
			查SQL.Checked = World.jlMsg == 1; // 设置初始状态
			查SQL.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.jlMsg = 0;
					}
					else
					{
						item.Checked = true;
						World.jlMsg = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "查SQL切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(查SQL);

			// 添加分隔符
			控制菜单.DropDownItems.Add(new ToolStripSeparator());

			var Npc控制 = new ToolStripMenuItem("Npc控制");
			Npc控制.Click += (s, e) => menuItem48_Click(s, e);
			控制菜单.DropDownItems.Add(Npc控制);

			var 石头控制 = new ToolStripMenuItem("石头控制");
			石头控制.Click += (s, e) => menuItem55_Click(s, e);
			控制菜单.DropDownItems.Add(石头控制);

			var 气功控制 = new ToolStripMenuItem("气功控制");
			气功控制.Click += (s, e) => menuItem47_Click(s, e);
			控制菜单.DropDownItems.Add(气功控制);

			var 自动清理内存 = new ToolStripMenuItem("自动清理内存");
			自动清理内存.Checked = World.AutGC == 1; // 根据实际状态设置
			自动清理内存.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (item.Checked)
					{
						item.Checked = false;
						World.AutGC = 0;
					}
					else
					{
						item.Checked = true;
						World.AutGC = 1;
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "自动清理内存切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(自动清理内存);

			控制菜单.DropDownItems.Add(new ToolStripSeparator());

			toolStrip仙魔大战 = new ToolStripMenuItem("仙魔大战  未开");
			toolStrip仙魔大战.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (World.仙魔大战 == null)
					{
						DBA.ExeSqlCommand("DELETE FROM 荣誉仙魔排行 where 分区信息='" + World.分区编号 + "'", "GameServer");
						WriteLine(55, "活动删除荣誉仙魔排行");
						World.仙魔大战 = new 仙魔大战Class();
						WriteLine(2, "仙魔大战开始啦再次点击关闭仙魔大战");
						item.Text = "仙魔大战  开启";
					}
					else
					{
						World.仙魔大战.Dispose();
						WriteLine(2, "仙魔大战结束啦再次点击开启仙魔大战");
						item.Text = "仙魔大战  未开";
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "仙魔大战切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip仙魔大战);

			// 攻城战
			toolStrip攻城战 = new ToolStripMenuItem("攻城战  未开");
			toolStrip攻城战.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem61_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "攻城战切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip攻城战);

			// 帮战
			toolStrip帮战 = new ToolStripMenuItem("帮战  未开");
			toolStrip帮战.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem62_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "帮战切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip帮战);

			// 武林血战
			toolStrip武林血战 = new ToolStripMenuItem("武林血战  未开");
			toolStrip武林血战.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem63_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "武林血战切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip武林血战);

			// 异口同声
			toolStrip异口同声 = new ToolStripMenuItem("异口同声  未开");
			toolStrip异口同声.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem65_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "异口同声切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip异口同声);

			// 全服双倍
			toolStrip全服双倍 = new ToolStripMenuItem("全服双倍  未开");
			toolStrip全服双倍.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem66_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "全服双倍切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip全服双倍);

			// 幸运玩家
			toolStrip幸运玩家 = new ToolStripMenuItem("幸运玩家  未开");
			toolStrip幸运玩家.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem67_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "幸运玩家切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip幸运玩家);

			// boss攻城
			toolStripBoss攻城 = new ToolStripMenuItem("boss攻城  未开");
			toolStripBoss攻城.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem70_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "boss攻城切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStripBoss攻城);

			// 世界BOSS
			toolStrip世界BOSS = new ToolStripMenuItem("世界BOSS  未开");
			toolStrip世界BOSS.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem71_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "世界BOSS切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip世界BOSS);

			// 势力战
			toolStrip势力战 = new ToolStripMenuItem("势力战  未开");
			toolStrip势力战.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem79_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "势力战切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip势力战);

			// 野外BOSS
			toolStrip野外BOSS = new ToolStripMenuItem("野外BOSS  未开");
			toolStrip野外BOSS.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem81_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "野外BOSS切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip野外BOSS);

			// 死亡无敌
			toolStrip死亡无敌 = new ToolStripMenuItem("死亡无敌  未开");
			toolStrip死亡无敌.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					if (World.开启死亡无敌 == 1)
					{
						World.开启死亡无敌 = 0;
						WriteLine(2, "关闭死亡无敌完成");
					}
					else
					{
						World.开启死亡无敌 = 1;
						WriteLine(2, "开启死亡无敌完成");
					}
				}
				catch (Exception ex)
				{
					WriteLine(1, "死亡无敌切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip死亡无敌);

			// 比武泡点
			toolStrip比武泡点 = new ToolStripMenuItem("比武泡点  未开");
			toolStrip比武泡点.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem34_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "比武泡点切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip比武泡点);

			// 天降红包
			toolStrip天降红包 = new ToolStripMenuItem("天降红包  未开");
			toolStrip天降红包.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem104_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "天降红包切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip天降红包);

			// 南林钟离
			toolStrip南林钟离 = new ToolStripMenuItem("南林钟离  未开");
			toolStrip南林钟离.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem105_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "南林钟离切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip南林钟离);

			// 大乱斗
			toolStrip大乱斗 = new ToolStripMenuItem("大乱斗  未开");
			toolStrip大乱斗.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem108_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "大乱斗切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip大乱斗);

			// 极限比武
			toolStrip极限比武 = new ToolStripMenuItem("极限比武  未开");
			toolStrip极限比武.Click += (s, e) => {
				try
				{
					var item = s as ToolStripMenuItem;
					menuItem125_Click(item, e);
				}
				catch (Exception ex)
				{
					WriteLine(1, "极限比武切换失败: " + ex.Message);
				}
			};
			控制菜单.DropDownItems.Add(toolStrip极限比武);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加控制菜单项失败: {ex.Message}");
		}
	}

	// 2025-0618 EVIAS 添加重读菜单的子菜单项
	private void 添加重读菜单项(ToolStripMenuItem 重读菜单)
	{
		try
		{
			// 重读配置文件
			var 重读配置文件 = new ToolStripMenuItem("重读配置文件");
			重读配置文件.Click += (s, e) => menuItem16_Click(s, e);
			重读菜单.DropDownItems.Add(重读配置文件);

			// 重读全部配置
			var 重读全部配置 = new ToolStripMenuItem("重读全部配置");
			重读全部配置.Click += (s, e) => menuItem7_Click(s, e);
			重读菜单.DropDownItems.Add(重读全部配置);

			// 重读武功数据
			var 重读武功数据 = new ToolStripMenuItem("重读武功数据");
			重读武功数据.Click += (s, e) => menuItem30_Click(s, e);
			重读菜单.DropDownItems.Add(重读武功数据);

			// 重读过滤
			var 重读过滤 = new ToolStripMenuItem("重读过滤");
			重读过滤.Click += (s, e) => menuItem28_Click(s, e);
			重读菜单.DropDownItems.Add(重读过滤);

			// 重读NPC[危险]
			var 重读NPC = new ToolStripMenuItem("重读NPC[危险]");
			重读NPC.Click += (s, e) => menuItem8_Click(s, e);
			重读菜单.DropDownItems.Add(重读NPC);

			// 添加分隔符
			重读菜单.DropDownItems.Add(new ToolStripSeparator());

			// 重读怪物数据
			var 重读怪物数据 = new ToolStripMenuItem("重读怪物数据");
			重读怪物数据.Click += (s, e) => menuItem22_Click(s, e);
			重读菜单.DropDownItems.Add(重读怪物数据);

			// 重读BOSS物品掉落
			var 重读BOSS掉落 = new ToolStripMenuItem("重读BOSS物品掉落");
			重读BOSS掉落.Click += (s, e) => menuItem23_Click(s, e);
			重读菜单.DropDownItems.Add(重读BOSS掉落);

			// 重读物品掉落
			var 重读物品掉落 = new ToolStripMenuItem("重读物品掉落");
			重读物品掉落.Click += (s, e) => menuItem9_Click(s, e);
			重读菜单.DropDownItems.Add(重读物品掉落);

			// 重读高手怪物品掉落
			var 重读高手怪掉落 = new ToolStripMenuItem("重读高手怪物品掉落");
			重读高手怪掉落.Click += (s, e) => menuItem25_Click(s, e);
			重读菜单.DropDownItems.Add(重读高手怪掉落);

			// 重读开箱
			var 重读开箱 = new ToolStripMenuItem("重读开箱");
			重读开箱.Click += (s, e) => menuItem10_Click(s, e);
			重读菜单.DropDownItems.Add(重读开箱);

			// 重读物品
			var 重读物品 = new ToolStripMenuItem("重读物品");
			重读物品.Click += (s, e) => menuItem11_Click(s, e);
			重读菜单.DropDownItems.Add(重读物品);

			// 重读NPC商店
			var 重读NPC商店 = new ToolStripMenuItem("重读NPC商店");
			重读NPC商店.Click += (s, e) => menuItem12_Click(s, e);
			重读菜单.DropDownItems.Add(重读NPC商店);

			// 重读传送点
			var 重读传送点 = new ToolStripMenuItem("重读传送点");
			重读传送点.Click += (s, e) => menuItem13_Click(s, e);
			重读菜单.DropDownItems.Add(重读传送点);

			// 重读自定义移动数据
			var 重读移动数据 = new ToolStripMenuItem("重读自定义移动数据");
			重读移动数据.Click += (s, e) => menuItem50_Click(s, e);
			重读菜单.DropDownItems.Add(重读移动数据);

			// 重读自动公告
			var 重读自动公告 = new ToolStripMenuItem("重读自动公告");
			重读自动公告.Click += (s, e) => menuItem14_Click(s, e);
			重读菜单.DropDownItems.Add(重读自动公告);

			// 添加分隔符
			重读菜单.DropDownItems.Add(new ToolStripSeparator());

			// 重读脚本
			var 重读脚本 = new ToolStripMenuItem("重读脚本");
			重读脚本.Click += (s, e) => menuItem39_Click(s, e);
			重读菜单.DropDownItems.Add(重读脚本);

			// 重读制作列表
			var 重读制作列表 = new ToolStripMenuItem("重读制作列表");
			重读制作列表.Click += (s, e) => menuItem42_Click(s, e);
			重读菜单.DropDownItems.Add(重读制作列表);

			// 重读百宝阁数据
			var 重读百宝阁数据 = new ToolStripMenuItem("重读百宝阁数据");
			重读百宝阁数据.Click += (s, e) => menuItem43_Click(s, e);
			重读菜单.DropDownItems.Add(重读百宝阁数据);

			// 重读套装数据
			var 重读套装数据 = new ToolStripMenuItem("重读套装数据");
			重读套装数据.Click += (s, e) => menuItem44_Click(s, e);
			重读菜单.DropDownItems.Add(重读套装数据);

			// 重读等级奖励
			var 重读等级奖励 = new ToolStripMenuItem("重读等级奖励");
			重读等级奖励.Click += (s, e) => menuItem45_Click(s, e);
			重读菜单.DropDownItems.Add(重读等级奖励);

			// 重读物品兑换
			var 重读物品兑换 = new ToolStripMenuItem("重读物品兑换");
			重读物品兑换.Click += (s, e) => menuItem24_Click(s, e);
			重读菜单.DropDownItems.Add(重读物品兑换);

			// 重读任务数据
			var 重读任务数据 = new ToolStripMenuItem("重读任务数据");
			重读任务数据.Click += (s, e) => menuItem52_Click(s, e);
			重读菜单.DropDownItems.Add(重读任务数据);

			// 重读气功加成比率
			var 重读气功加成 = new ToolStripMenuItem("重读气功加成比率");
			重读气功加成.Click += (s, e) => menuItem46_Click(s, e);
			重读菜单.DropDownItems.Add(重读气功加成);

			// 重读石头设置
			var 重读石头设置 = new ToolStripMenuItem("重读石头设置");
			重读石头设置.Click += (s, e) => menuItem53_Click(s, e);
			重读菜单.DropDownItems.Add(重读石头设置);

			// 添加分隔符
			重读菜单.DropDownItems.Add(new ToolStripSeparator());

			// 重读装备洗髓首爆
			var 重读装备洗髓 = new ToolStripMenuItem("重读装备洗髓首爆");
			重读装备洗髓.Click += (s, e) => menuItem41_Click(s, e);
			重读菜单.DropDownItems.Add(重读装备洗髓);

			// 重读英雄职业武器
			var 重读英雄武器 = new ToolStripMenuItem("重读英雄职业武器");
			重读英雄武器.Click += (s, e) => menuItem68_Click(s, e);
			重读菜单.DropDownItems.Add(重读英雄武器);

			// 重读boss攻城怪物
			var 重读boss攻城 = new ToolStripMenuItem("重读boss攻城怪物");
			重读boss攻城.Click += (s, e) => menuItem80_Click(s, e);
			重读菜单.DropDownItems.Add(重读boss攻城);

			// 重读boss副本怪物
			var 重读boss副本 = new ToolStripMenuItem("重读boss副本怪物");
			重读boss副本.Click += (s, e) => menuItem82_Click(s, e);
			重读菜单.DropDownItems.Add(重读boss副本);

			// 重读物品回收
			var 重读物品回收 = new ToolStripMenuItem("重读物品回收");
			重读物品回收.Click += (s, e) => menuItem83_Click(s, e);
			重读菜单.DropDownItems.Add(重读物品回收);

			// 重读累计充值称号
			var 重读累计充值 = new ToolStripMenuItem("重读累计充值称号");
			重读累计充值.Click += (s, e) => menuItem89_Click(s, e);
			重读菜单.DropDownItems.Add(重读累计充值);

			// 重读冲关地图
			var 重读冲关地图 = new ToolStripMenuItem("重读冲关地图");
			重读冲关地图.Click += (s, e) => menuItem92_Click(s, e);
			重读菜单.DropDownItems.Add(重读冲关地图);

			// 重读比武泡点奖励
			var 重读比武泡点 = new ToolStripMenuItem("重读比武泡点奖励");
			重读比武泡点.Click += (s, e) => menuItem33_Click(s, e);
			重读菜单.DropDownItems.Add(重读比武泡点);

			// 重读职业系数
			var 重读职业系数 = new ToolStripMenuItem("重读职业系数");
			重读职业系数.Click += (s, e) => menuItem35_Click(s, e);
			重读菜单.DropDownItems.Add(重读职业系数);

			// 重读强化概率参数
			var 重读强化概率 = new ToolStripMenuItem("重读强化概率参数");
			重读强化概率.Click += (s, e) => menuItem36_Click(s, e);
			重读菜单.DropDownItems.Add(重读强化概率);

			// 重读大乱斗奖励
			var 重读大乱斗奖励 = new ToolStripMenuItem("重读大乱斗奖励");
			重读大乱斗奖励.Click += (s, e) => menuItem109_Click(s, e);
			重读菜单.DropDownItems.Add(重读大乱斗奖励);

			// 重读假人等级奖励
			var 重读假人等级奖励 = new ToolStripMenuItem("重读假人等级奖励");
			重读假人等级奖励.Click += (s, e) => menuItem90_Click(s, e);
			重读菜单.DropDownItems.Add(重读假人等级奖励);

			// 重读假人商店物品
			var 重读假人商店 = new ToolStripMenuItem("重读假人商店物品");
			重读假人商店.Click += (s, e) => menuItem112_Click(s, e);
			重读菜单.DropDownItems.Add(重读假人商店);

			// 重读进阶装备
			var 重读进阶装备 = new ToolStripMenuItem("重读进阶装备");
			重读进阶装备.Click += (s, e) => menuItem127_Click(s, e);
			重读菜单.DropDownItems.Add(重读进阶装备);

			// 重读分解装备
			var 重读分解装备 = new ToolStripMenuItem("重读分解装备");
			重读分解装备.Click += (s, e) => menuItem128_Click(s, e);
			重读菜单.DropDownItems.Add(重读分解装备);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加重读菜单项失败: {ex.Message}");
		}
	}

	private void 添加在线工具菜单项(ToolStripMenuItem 在线工具菜单)
	{
		try
		{
			// 人物数据管理
			var 人物数据管理 = new ToolStripMenuItem("人物数据管理");
			人物数据管理.Click += (s, e) => menuItem114_Click(s, e);
			在线工具菜单.DropDownItems.Add(人物数据管理);

			// 在线GM工具
			var 在线GM工具 = new ToolStripMenuItem("在线GM工具");
			在线GM工具.Click += (s, e) => menuItem57_Click(s, e);
			在线工具菜单.DropDownItems.Add(在线GM工具);

			// 假人注册登陆
			var 假人注册登陆 = new ToolStripMenuItem("假人注册登陆");
			假人注册登陆.Click += (s, e) => menuItem111_Click(s, e);
			在线工具菜单.DropDownItems.Add(假人注册登陆);

			// 百宝数据修改
			var 百宝数据修改 = new ToolStripMenuItem("百宝数据修改");
			百宝数据修改.Click += (s, e) => menuItem115_Click(s, e);
			在线工具菜单.DropDownItems.Add(百宝数据修改);

			// 商店数据修改
			var 商店数据修改 = new ToolStripMenuItem("商店数据修改");
			商店数据修改.Click += (s, e) => menuItem116_Click(s, e);
			在线工具菜单.DropDownItems.Add(商店数据修改);

			// 开箱数据修改
			var 开箱数据修改 = new ToolStripMenuItem("开箱数据修改");
			开箱数据修改.Click += (s, e) => menuItem117_Click(s, e);
			在线工具菜单.DropDownItems.Add(开箱数据修改);

			// 物品数据修改
			var 物品数据修改 = new ToolStripMenuItem("物品数据修改");
			物品数据修改.Click += (s, e) => menuItem118_Click(s, e);
			在线工具菜单.DropDownItems.Add(物品数据修改);

			// CDK兑换调整
			var CDK兑换调整 = new ToolStripMenuItem("CDK兑换调整");
			CDK兑换调整.Click += (s, e) => menuItem126_Click(s, e);
			在线工具菜单.DropDownItems.Add(CDK兑换调整);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加在线工具菜单项失败: {ex.Message}");
		}
	}

	// 2025-0618 EVIAS 添加其它工具菜单的子菜单项
	private void 添加其它工具菜单项(ToolStripMenuItem 其它工具菜单)
	{
		try
		{
			// 角色换名修改
			var 角色换名修改 = new ToolStripMenuItem("角色换名修改");
			角色换名修改.Click += (s, e) => menuItem119_Click(s, e);
			其它工具菜单.DropDownItems.Add(角色换名修改);

			// 批量刷怪工具
			var 批量刷怪工具 = new ToolStripMenuItem("批量刷怪工具");
			批量刷怪工具.Click += (s, e) => menuItem120_Click(s, e);
			其它工具菜单.DropDownItems.Add(批量刷怪工具);

			// 全服叠加查询
			var 全服叠加查询 = new ToolStripMenuItem("全服叠加查询");
			全服叠加查询.Click += (s, e) => menuItem124_Click(s, e);
			其它工具菜单.DropDownItems.Add(全服叠加查询);

			// YBI编辑工具
			var YBI编辑工具 = new ToolStripMenuItem("YBI编辑工具");
			YBI编辑工具.Click += (s, e) => menuItem49_Click(s, e);
			其它工具菜单.DropDownItems.Add(YBI编辑工具);

			// 实时移动参数
			var 实时移动参数 = new ToolStripMenuItem("实时移动参数");
			实时移动参数.Click += (s, e) => menuItem74_Click(s, e);
			其它工具菜单.DropDownItems.Add(实时移动参数);

			// 自动修改公告
			var 自动修改公告 = new ToolStripMenuItem("自动修改公告");
			自动修改公告.Click += (s, e) => menuItem73_Click(s, e);
			其它工具菜单.DropDownItems.Add(自动修改公告);

			// 全体换线移动
			var 全体换线移动 = new ToolStripMenuItem("全体换线移动");
			全体换线移动.Click += (s, e) => menuItem76_Click(s, e);
			其它工具菜单.DropDownItems.Add(全体换线移动);

			// 加载充值排名
			var 加载充值排名 = new ToolStripMenuItem("加载充值排名");
			加载充值排名.Click += (s, e) => menuItem103_Click(s, e);
			其它工具菜单.DropDownItems.Add(加载充值排名);

			// 设置经验封印
			var 设置经验封印 = new ToolStripMenuItem("设置经验封印");
			设置经验封印.Click += (s, e) => menuItem110_Click(s, e);
			其它工具菜单.DropDownItems.Add(设置经验封印);

			// 职业伤害调整
			var 职业伤害调整 = new ToolStripMenuItem("职业伤害调整");
			职业伤害调整.Click += (s, e) => menuItem122_Click(s, e);
			其它工具菜单.DropDownItems.Add(职业伤害调整);

			// 强化参数调整
			var 强化参数调整 = new ToolStripMenuItem("强化参数调整");
			强化参数调整.Click += (s, e) => menuItem123_Click(s, e);
			其它工具菜单.DropDownItems.Add(强化参数调整);

		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加其它工具菜单项失败: {ex.Message}");
		}
	}

	private void 添加清理开区菜单项(ToolStripMenuItem 清理开区菜单)
	{
		try
		{
			// 清理分区记录(天数)
			var 清理分区记录 = new ToolStripMenuItem("清理分区记录(天数)");
			清理分区记录.Click += (s, e) => menuItem51_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理分区记录);

			// 清理分区数据(角色)
			var 清理分区角色 = new ToolStripMenuItem("清理分区数据(角色)");
			清理分区角色.Click += (s, e) => menuItem59_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理分区角色);

			// 清理分区账号(所有)
			var 清理分区账号 = new ToolStripMenuItem("清理分区账号(所有)");
			清理分区账号.Click += (s, e) => menuItem64_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理分区账号);

			// 清理分区记录(所有)
			var 清理分区记录所有 = new ToolStripMenuItem("清理分区记录(所有)");
			清理分区记录所有.Click += (s, e) => menuItem75_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理分区记录所有);

			// 添加分隔符
			清理开区菜单.DropDownItems.Add(new ToolStripSeparator());

			// 清理30天未登录账号
			var 清理30天账号 = new ToolStripMenuItem("清理30天未登录账号");
			清理30天账号.Click += (s, e) => menuItem58_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理30天账号);

			// 清理60天未登录账号
			var 清理60天账号 = new ToolStripMenuItem("清理60天未登录账号");
			清理60天账号.Click += (s, e) => menuItem96_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理60天账号);

			// 清理90天未登录账号
			var 清理90天账号 = new ToolStripMenuItem("清理90天未登录账号");
			清理90天账号.Click += (s, e) => menuItem97_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理90天账号);

			// 添加分隔符
			清理开区菜单.DropDownItems.Add(new ToolStripSeparator());

			// 清理无账号人物数据
			var 清理无账号人物 = new ToolStripMenuItem("清理无账号人物数据");
			清理无账号人物.Click += (s, e) => menuItem98_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理无账号人物);

			// 清理无账号综合仓库
			var 清理无账号综合仓库 = new ToolStripMenuItem("清理无账号综合仓库");
			清理无账号综合仓库.Click += (s, e) => menuItem99_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理无账号综合仓库);

			// 清理无账号个人仓库
			var 清理无账号个人仓库 = new ToolStripMenuItem("清理无账号个人仓库");
			清理无账号个人仓库.Click += (s, e) => menuItem100_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理无账号个人仓库);

			// 清理无账号帮派数据
			var 清理无账号帮派 = new ToolStripMenuItem("清理无账号帮派数据");
			清理无账号帮派.Click += (s, e) => menuItem101_Click(s, e);
			清理开区菜单.DropDownItems.Add(清理无账号帮派);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加清理开区菜单项失败: {ex.Message}");
		}
	}

	// 2025-0623 EVIAS 添加服务器维护菜单的子菜单项
	private void 添加服务器维护菜单项(ToolStripMenuItem 服务器维护菜单)
	{
		try
		{
			// 定时维护
			var 定时维护 = new ToolStripMenuItem("定时维护");
			定时维护.Click += (s, e) => menuItem维护_Click(s, e);
			服务器维护菜单.DropDownItems.Add(定时维护);

			// 立即维护
			var 立即维护 = new ToolStripMenuItem("立即维护");
			立即维护.Click += (s, e) => menuItem立即维护_Click(s, e);
			服务器维护菜单.DropDownItems.Add(立即维护);

			// 取消维护
			var 取消维护 = new ToolStripMenuItem("取消维护");
			取消维护.Click += (s, e) => menuItem取消维护_Click(s, e);
			服务器维护菜单.DropDownItems.Add(取消维护);

			// 添加分隔符
			服务器维护菜单.DropDownItems.Add(new ToolStripSeparator());

			// 异常管理工具菜单
			var 异常管理工具菜单 = new ToolStripMenuItem("异常管理工具");

			// 查看异常统计
			var 异常统计 = new ToolStripMenuItem("查看异常统计");
			异常统计.Click += (s, e) => menuItem异常统计_Click(s, e);
			异常管理工具菜单.DropDownItems.Add(异常统计);

			// 清空控制台
			var 清空控制台 = new ToolStripMenuItem("清空控制台");
			清空控制台.Click += (s, e) => menuItem清空控制台_Click(s, e);
			异常管理工具菜单.DropDownItems.Add(清空控制台);

			// 清理日志文件
			var 清理日志文件 = new ToolStripMenuItem("清理日志文件");
			清理日志文件.Click += (s, e) => menuItem清理日志_Click(s, e);
			异常管理工具菜单.DropDownItems.Add(清理日志文件);

			// 异常管理器
			var 异常管理器 = new ToolStripMenuItem("异常管理器");
			异常管理器.Click += (s, e) => menuItem异常管理器_Click(s, e);
			异常管理工具菜单.DropDownItems.Add(异常管理器);

			服务器维护菜单.DropDownItems.Add(异常管理工具菜单);

			// 添加分隔符
			服务器维护菜单.DropDownItems.Add(new ToolStripSeparator());
			var 数据库监控菜单 = new ToolStripMenuItem("数据库监控");

			// 连接统计查看
			var 查看连接统计 = new ToolStripMenuItem("查看连接统计");
			查看连接统计.Click += (s, e) => 显示数据库连接统计();
			数据库监控菜单.DropDownItems.Add(查看连接统计);

			// 重置统计数据
			var 重置统计数据 = new ToolStripMenuItem("重置统计数据");
			重置统计数据.Click += (s, e) => 重置数据库统计();
			数据库监控菜单.DropDownItems.Add(重置统计数据);

			// 添加分隔符
			数据库监控菜单.DropDownItems.Add(new ToolStripSeparator());

			// 测试连接 
			var 测试连接菜单项 = new ToolStripMenuItem("测试数据库连接");
			测试连接菜单项.Click += (s, e) => 测试数据库连接();
			数据库监控菜单.DropDownItems.Add(测试连接菜单项);
			服务器维护菜单.DropDownItems.Add(数据库监控菜单);
			服务器维护菜单.DropDownItems.Add(new ToolStripSeparator());

			// 2025-0623 EVIAS 内存管理优化菜单
			var 内存管理优化菜单 = new ToolStripMenuItem("内存管理优化");

			// 查看内存状态
			var 查看内存状态 = new ToolStripMenuItem("查看内存状态");
			查看内存状态.Click += (s, e) => 显示内存状态();
			内存管理优化菜单.DropDownItems.Add(查看内存状态);

			// 强制垃圾回收
			var 强制垃圾回收 = new ToolStripMenuItem("强制垃圾回收");
			强制垃圾回收.Click += (s, e) => 执行强制垃圾回收();
			内存管理优化菜单.DropDownItems.Add(强制垃圾回收);

			// 对象池统计
			var 对象池统计 = new ToolStripMenuItem("对象池统计");
			对象池统计.Click += (s, e) => 显示对象池统计();
			内存管理优化菜单.DropDownItems.Add(对象池统计);

			// 添加分隔符
			内存管理优化菜单.DropDownItems.Add(new ToolStripSeparator());

			// 性能统计
			var 性能统计 = new ToolStripMenuItem("性能统计");
			性能统计.Click += (s, e) => 显示性能统计();
			内存管理优化菜单.DropDownItems.Add(性能统计);

			// 添加分隔符
			内存管理优化菜单.DropDownItems.Add(new ToolStripSeparator());

			// 集合优化管理
			var 集合优化管理 = new ToolStripMenuItem("集合优化管理");

			// 地面物品管理
			var 地面物品管理 = new ToolStripMenuItem("地面物品管理");
			地面物品管理.Click += (s, e) => 显示地面物品管理();
			集合优化管理.DropDownItems.Add(地面物品管理);

			// 怪物缓存管理
			var 怪物缓存管理 = new ToolStripMenuItem("怪物缓存管理");
			怪物缓存管理.Click += (s, e) => 显示怪物缓存管理();
			集合优化管理.DropDownItems.Add(怪物缓存管理);

			// 网络优化管理
			var 网络优化管理 = new ToolStripMenuItem("网络优化管理");
			网络优化管理.Click += (s, e) => 显示网络优化管理();
			集合优化管理.DropDownItems.Add(网络优化管理);

			内存管理优化菜单.DropDownItems.Add(集合优化管理);

			服务器维护菜单.DropDownItems.Add(内存管理优化菜单);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加服务器维护菜单项失败: {ex.Message}");
		}
	}

	private void 添加其它菜单项(ToolStripMenuItem 其它菜单)
	{
		try
		{
			// 关于
			var 关于 = new ToolStripMenuItem("关于");
			关于.Click += (s, e) => menuItem78_Click(s, e);
			其它菜单.DropDownItems.Add(关于);

			// 网络设置
			var 网络设置 = new ToolStripMenuItem("网络设置");
			网络设置.Click += (s, e) => menuItem107_Click(s, e);
			其它菜单.DropDownItems.Add(网络设置);

			// 防CC设置
			var 防CC设置 = new ToolStripMenuItem("防CC设置");
			防CC设置.Click += (s, e) => menuItem95_Click(s, e);
			其它菜单.DropDownItems.Add(防CC设置);

			// 添加分隔符
			其它菜单.DropDownItems.Add(new ToolStripSeparator());

			// 一键合区(区域合并)
			var 一键合区 = new ToolStripMenuItem("一键合区(区域合并)");
			一键合区.Click += (s, e) => menuItem94_Click(s, e);
			其它菜单.DropDownItems.Add(一键合区);

			// 一键开区(清理分区)
			var 一键开区 = new ToolStripMenuItem("一键开区(清理分区)");
			一键开区.Click += (s, e) => menuItem102_Click(s, e);
			其它菜单.DropDownItems.Add(一键开区);

			// 添加分隔符
			其它菜单.DropDownItems.Add(new ToolStripSeparator());

			// 初始化数据库(勿点)
			var 初始化数据库 = new ToolStripMenuItem("初始化数据库(勿点)");
			初始化数据库.Click += (s, e) => menuItem121_Click(s, e);
			其它菜单.DropDownItems.Add(初始化数据库);
		}
		catch (Exception ex)
		{
			WriteLine(1, $"添加其它菜单项失败: {ex.Message}");
		}
	}

	private void 更新菜单文字(object menuItem, string newText)
	{
		try
		{
			if (menuItem is ToolStripMenuItem toolStripItem)
			{
				toolStripItem.Text = newText;
			}
			else if (menuItem is MenuItem menuItemOld)
			{
				menuItemOld.Text = newText;
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, $"更新菜单文字失败: {ex.Message}");
		}
	}

	public void 自动更新荣誉()
	{
		if (World.jlMsg == 1)
		{
			WriteLine(0, "刷新荣誉事件");
		}
		World.更新所有排行数据();
		world.充值排行();
		world.荣誉门派排行();
		world.荣誉势力排行();
		world.荣誉武林排行();
		world.荣誉讨伐排行();
		world.加载门派战绩();
		WriteLine(2, "刷新荣誉排行数据完成");
	}

	private void 复查IP用户事件(object source, ElapsedEventArgs e)
	{
		try
		{
			World.privateTeams.Clear();
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (!World.privateTeams.ContainsKey(value.Client.ToString()) && !value.Client.挂机)
				{
					World.privateTeams.TryAdd(value.Client.ToString(), value);
				}
			}
		}
		catch (Exception)
		{
		}
	}

	private void menuItem32_Click(object sender, EventArgs e)
	{
		if (World.conn != null)
		{
			World.conn.Dispose();
			World.conn = null;
		}
		World.conn = new Connect();
		World.conn.Sestup();
	}

	private void menuItem3_Click(object sender, EventArgs e)
	{
		World.conn.Dispose();
		WriteLine(2, "停止玩家登陆成功");
	}

	private void 狮子吼事件(object source, ElapsedEventArgs e)
	{
		World.Process狮子吼Queue();
	}

	private static void Thread1()
	{
		try
		{
			LuaFunction function = World.脚本.pLuaVM.GetFunction("OpenItmeTrigGer");
			if (function != null)
			{
				object[] args = new object[4] { 0, 100, 0, 1 };
				object[] array = function.Call(args);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"Lua脚本执行失败: {ex.Message}");
		}
	}

	private void Form1_FormClosing(object sender, FormClosingEventArgs e)
	{
		try
		{
			if (World.SqlPool.Count > 0)
			{
				if (MessageBox.Show("数据列队还没有完成  " + World.SqlPool.Count, "数据列队还没有完成", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk) == DialogResult.OK)
				{
					runn = true;
					timerThread.Abort();
					thThreadRead.Abort();
					if (l != null)
					{
						l.Dispose();
						l = null;
					}
					World.极限比武台服务.EndCompetition();
				}
				else
				{
					e.Cancel = true;
				}
			}
			else if (World.allConnectedChars.Count > 0)
			{
				foreach (Players value in World.allConnectedChars.Values)
				{
					if (value.账号是否在线 > 0)
					{
						value.账号是否在线--;
						value.保存在线账号数据();
					}
					if (value.随机时间在线 > 0)
					{
						value.随机时间在线--;
						value.保存随机检测();
					}
					if (value.角色是否在线 > 0)
					{
						value.角色是否在线--;
						value.更新玩家是否在线();
					}
				}
				Thread.Sleep(500);
				if (MessageBox.Show("有人物在线  " + World.allConnectedChars.Count, "还有人物在线", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk) == DialogResult.OK)
				{
					runn = true;
					timerThread.Abort();
					thThreadRead.Abort();
					if (l != null)
					{
						l.Dispose();
						l = null;
					}
					World.极限比武台服务.EndCompetition();
				}
				else
				{
					e.Cancel = true;
				}
			}
			else
			{
				runn = true;
				timerThread.Abort();
				thThreadRead.Abort();
				if (l != null)
				{
					l.Dispose();
					l = null;
				}
				World.极限比武台服务.EndCompetition();
			}
		}
		catch
		{
		}
	}

	private void FlushAll()
	{
		try
		{
			while (!runn)
			{
				Timer.Slice();
				Thread.Sleep(10);
				World.ProcessSqlQueue();
			}
		}
		catch (Exception)
		{
		}
	}

	public void 自动公告事件()
	{
		World.conn.复查用户登陆();
		if (World.公告.Count > 0)
		{
			公告类 公告类2 = World.公告[自动公告ID];
			World.conn.全线发送公告(公告类2.type, 公告类2.msg);
			自动公告ID++;
			if (自动公告ID >= World.公告.Count)
			{
				自动公告ID = 0;
			}
		}
		if (World.自动存档 == 1)
		{
			menuItem17.PerformClick();
		}
        if (World.自动清理记录) //EVIAS
        {
            DateTime now = DateTime.Now;

            if (now.Hour == 1 && now.Minute >= 30 &&
                (now.Date > World.上次清理时间.Date || World.上次清理时间 == DateTime.MinValue))
            {
                if (now.Date != World.上次清理时间.Date ||
                    (now - World.上次清理时间).TotalMinutes > World.公告刷新时间 * 2)
                {
                    Form2.FlushAll8();
                    World.上次清理时间 = now;
                    WriteLine(55, "自动清理记录完成 - 时间: " + now.ToString());
                }
            }
        }
        try
		{
			if (World.allConnectedChars.Count > World.list.Count)
			{
				Queue queue = Queue.Synchronized(new Queue());
				foreach (Players value3 in World.allConnectedChars.Values)
				{
					queue.Enqueue(value3);
				}
				while (queue.Count > 0)
				{
					Players players = (Players)queue.Dequeue();
					if (!World.list.TryGetValue(players.人物全服ID, out var _))
					{
						World.allConnectedChars.TryRemove(players.人物全服ID, out var _);
					}
				}
				if (World.allConnectedChars.Count > World.list.Count)
				{
					World.conn.Dispose();
					menuItem3.PerformClick();
					World.自动存档 = 0;
				}
			}
			if (World.是否开启王龙 == 1)
			{
				foreach (Players value4 in World.allConnectedChars.Values)
				{
					if (value4.人物坐标_地图 >= 23001 && value4.人物坐标_地图 <= 24000)
					{
						byte[] array = Converter.hexStringToByte("AA5516002C01121708000000000000000000000000000000558D55AA");
						Buffer.BlockCopy(BitConverter.GetBytes(World.王龙的金币), 0, array, 10, 8);
						if (value4.Client != null)
						{
							value4.Client.Send(array, array.Length);
						}
					}
				}
			}
			if (World.传书记录 == 1)
			{
				DBA.ExeSqlCommand("DELETE FROM 传书记录 WHERE DateDiff(dd,传书时间,getdate())>" + World.传书保存天数, "GameLog");
			}
			World.最大在线 = int.Parse(Config.IniReadValue("GameServer", "最大在线").Trim());
			World.conn.发送("更新服务器配置|" + World.服务器ID + "|" + World.最大在线);
			World.week = (int)DateTime.Now.DayOfWeek;
		}
		catch
		{
		}
	} 

    private void timer1_Tick(object source, EventArgs e)
    {
        try
        {
            if (_needsRefresh)
            {
                if (GraphPanel != null && GraphPanel.IsHandleCreated)
                {
                    GraphPanel.Invalidate();
                }
                _needsRefresh = false; // 重置标志
            }

            // 更新连接统计信息
            var lblConnections = statusStrip1.Items["lblConnections"] as ToolStripStatusLabel;
            if (lblConnections != null)
                lblConnections.Text = $" 连:{World.list.Count} 在:{World.allConnectedChars.Count} 云:{World.云挂机数量} 假:{World.假人数量} 商:{World.离线数量} ";

            // 更新游戏对象信息
            var lblGameObjects = statusStrip1.Items["lblGameObjects"] as ToolStripStatusLabel;
            if (lblGameObjects != null)
                lblGameObjects.Text = $"| 物品:{World.ItmeTeM.Count} 怪物:{MapClass.GetNpcConn()} ";

            var lblDatabase = statusStrip1.Items["lblDatabase"] as ToolStripStatusLabel;
            if (lblDatabase != null)
                lblDatabase.Text = FastString.Concat("| DB活跃:", RxjhServer.DbClss.DbConnectionMonitor.ActiveConnections, " 总:", RxjhServer.DbClss.DbConnectionMonitor.TotalConnections, " 成功:", RxjhServer.DbClss.DbConnectionMonitor.SuccessfulConnections, " ");

            // 更新网络流量信息
            var lblNetworkStats = statusStrip1.Items["lblNetworkStats"] as ToolStripStatusLabel;
            if (lblNetworkStats != null)
            {
                double recvSpeed = World.接收速度 / World.发包基数;
                double sendSpeed = World.发送速度 / World.发包基数;
                string recvText = (recvSpeed >= 1024) ? $"{Math.Round(recvSpeed / 1024, 1)}K" : $"{Math.Round(recvSpeed, 1)}B";
                string sendText = (sendSpeed >= 1024) ? $"{Math.Round(sendSpeed / 1024, 1)}K" : $"{Math.Round(sendSpeed, 1)}B";
                double packetCount = (double)Converter.Hexstring.Count / World.发包基数;
                lblNetworkStats.Text = $"| 收:{recvText}/s 发:{sendText}/s 包:{Math.Round(packetCount, 1)} ";
            }

            var lblMemory = statusStrip1.Items["lblMemory"] as ToolStripStatusLabel;
            if (lblMemory != null)
            {
                var memoryInfo = MemoryMonitor.Instance.GetMemoryInfo();
                lblMemory.Text = FastString.Concat("| 内存:", memoryInfo.GCMemory / 1024 / 1024, "MB GC:", memoryInfo.Gen0Collections, "/", memoryInfo.Gen1Collections, "/", memoryInfo.Gen2Collections);
            }

            TimeSpan uptime = DateTime.Now.Subtract(sj);
            string baseTitle = World.服务器名 + " - RxjhServer_" + Assembly.GetExecutingAssembly().GetName().Version.ToString();
            this.Text = FastString.Concat(baseTitle, " - ", FastString.BuildUptime(uptime));

            World.接收速度 = 0.0;
            World.发送速度 = 0.0;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"状态栏更新错误: {ex.Message}");
        }
    }

    private void menuItem5_Click(object sender, EventArgs e)
	{
		new UserList().ShowDialog();
	}

	private void menuItem27_Click(object sender, EventArgs e)
	{
		new FormUser组队().ShowDialog();
	}

	private void menuItem7_Click(object sender, EventArgs e)
	{
		world.SetDrop();
		world.SetOpen();
		world.Set套装();
		world.SetBbgItem();
		world.加载百宝阁抽奖();
		world.SetChouJiang();
		world.SetLever();
		world.SetKONGFU();
		world.SetItme();
		world.SetShop();
		world.SetMover();
		world.Set公告();
		world.Set等级奖励();
		world.Set假人等级奖励();
		world.Set比武泡点奖励();
		world.Set大乱斗奖励();
		world.Set物品兑换();
		world.Set神器兑换();
		world.强化概率参数();
		world.Set石头属性();
		world.Set移动();
		world.Set兑换码1();
		world.加载职业系数();
		world.Set道具组合();
        world.进阶装备(); //新增1 EVIAS
        world.分解装备(); //新增1 EVIAS
    }

	private void menuItem8_Click(object sender, EventArgs e)
	{
		Queue queue = Queue.Synchronized(new Queue());
		foreach (MapClass value in World.Map.Values)
		{
			foreach (NpcClass value2 in value.npcTemplate.Values)
			{
				queue.Enqueue(value2);
			}
		}
		while (queue.Count > 0)
		{
			((NpcClass)queue.Dequeue()).Dispose();
		}
		world.SetNpc();
	}

	private void menuItem9_Click(object sender, EventArgs e)
	{
		world.SetDrop();
	}

	private void menuItem10_Click(object sender, EventArgs e)
	{
		world.SetOpen();
	}

	private void menuItem11_Click(object sender, EventArgs e)
	{
		world.SetItme();
	}

	private void menuItem12_Click(object sender, EventArgs e)
	{
		world.SetShop();
	}

	private void menuItem13_Click(object sender, EventArgs e)
	{
		world.SetMover();
	}

	private void menuItem14_Click(object sender, EventArgs e)
	{
		world.Set公告();
	}

	
	private void menuItem16_Click(object sender, EventArgs e)
	{
		world.SetConfig();
		World.最大在线 = int.Parse(Config.IniReadValue("GameServer", "最大在线").Trim());
		World.conn.发送("更新服务器配置|" + World.服务器ID + "|" + World.最大在线);
		WriteLine(2, "重新加载服务器配置文件完成");
	}

	private void menuItem17_Click(object sender, EventArgs e)
	{
		List<Players> list = new List<Players>();
		foreach (Players value in World.allConnectedChars.Values)
		{
			list.Add(value);
		}
		foreach (Players item in list)
		{
			try
			{
				item.FLD_金币检测 = item.Player_Money;
				item.保存人物的数据();
			}
			catch (Exception ex)
			{
				WriteLine(1, "保存人物的数据  错误" + ex.Message);
			}
		}
		list.Clear();
		WriteLine(8, "保存人物的数据  完成");
	}



    private void menuItem22_Click(object sender, EventArgs e)
	{
		world.SetMonSter();
	}

	private void menuItem23_Click(object sender, EventArgs e)
	{
		world.SetBossDrop();
	}

	private void menuItem25_Click(object sender, EventArgs e)
	{
		world.Set_GSDrop();
	}

	private void menuItem28_Click(object sender, EventArgs e)
	{
		world.SetKill();
		world.SetJianc();
	}

	private void menuItem30_Click(object sender, EventArgs e)
	{
		world.SetKONGFU();
	}

	private void toolStripButton1_Click(object sender, EventArgs e)
	{
		try
		{
			byte[] array = Converter.hexStringToByte2(toolStripTextBox1.Text);
			foreach (NetState value in World.list.Values)
			{
				if (value != null)
				{
					if (toolStripComboBox2.Text == "正常")
					{
						Buffer.BlockCopy(BitConverter.GetBytes(value.WorldId), 0, array, 5, 2);
						value.Send单包(array, array.Length);
					}
					else
					{
						value.Send多包(array, array.Length);
					}
				}
			}
		}
		catch
		{
		}
	}

	private void toolStripButton2_Click(object sender, EventArgs e)
	{
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				value?.系统提示(toolStripTextBox2.Text, int.Parse(toolStripComboBox1.SelectedItem.ToString()), "系统信息");
			}
		}
		catch
		{
		}
	}

	private void toolStripButton3_Click(object sender, EventArgs e)
	{
		活动开启状态();
		World.conn.复查用户登陆();
		WriteLine(6, "手动复查人物清理内存/活动开启状态/完成");
	}

	private void toolStripButton4_Click(object sender, EventArgs e)
	{
		try
		{
			foreach (MapClass value in World.Map.Values)
			{
				foreach (NpcClass value2 in value.npcTemplate.Values)
				{
					value2.getbl();
				}
			}
			foreach (组队Class value3 in World.W组队.Values)
			{
				WriteLine(2, FastString.Concat("组队:", value3.组队id, "人物：", value3.组队列表.Count)); 
				foreach (Players value4 in value3.组队列表.Values)
				{
					WriteLine(2, FastString.Concat("组队员:", value4.Userid, "人物:", value4.UserName)); 
				}
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, ex.ToString());
		}
	}


    private void GraphPanel_Paint(object sender, PaintEventArgs e)
    {
        try
        {
            Graphics graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            graphics.PixelOffsetMode = PixelOffsetMode.None;

            graphics.Clear(GraphPanel.BackColor);

            int startY = 5; 
            int lineHeight = 16; 

            string statusInfo = FastString.Format(
                "连接: {0}/{1} | 在线: {2} | 线路: {3} | 端口: {4} | 真实IP: {5} | 假人: {6} | 离线队列: {7} | 数据库队列: {8} | 狮子吼: {9}",
                World.list?.Count ?? 0,
                World.最大在线,
                World.allConnectedChars?.Count ?? 0,
                World.服务器ID,
                World.游戏服务器端口,
                World.Iplist?.Count ?? 0,
                World.假人数量,
                World.m_Disposed?.Count ?? 0,
                World.SqlPool?.Count ?? 0,
                World.狮子吼List?.Count ?? 0
            );

            using (var statusFont = GetStatusFont())
            {
                var textSize = graphics.MeasureString(statusInfo, statusFont);
                graphics.FillRectangle(Brushes.White, 8, startY - 2, textSize.Width + 4, textSize.Height + 2);

                graphics.DrawString(statusInfo, statusFont, Brushes.DarkSlateBlue, new Point(10, startY));
            }

            int yPos = startY + 20;
            int maxY = GraphPanel.Height - 5; 

            using (var logFont = new Font("Consolas", 8.5f, FontStyle.Regular))
            {
                lock (txt)
                {
                    int maxLines = (maxY - yPos) / lineHeight;

                    int totalLines = txt.Count;
                    int startIndex, endIndex;

                    if (_autoScroll)
                    {
                        endIndex = totalLines;
                        startIndex = Math.Max(0, totalLines - maxLines);
                    }
                    else
                    {
                        endIndex = Math.Max(maxLines, totalLines - _scrollOffset);
                        startIndex = Math.Max(0, endIndex - maxLines);

                        endIndex = Math.Min(endIndex, totalLines);
                        startIndex = Math.Max(0, startIndex);
                    }
                    for (int i = startIndex; i < endIndex; i++)
                    {
                        if (yPos + lineHeight > maxY)
                        {
                            break; 
                        }

                        TxtClass item = txt[i];
                        if (item?.Txt != null)
                        {
                            Brush textBrush = GetTextBrush(item.type);

                            string displayText = WrapText(graphics, item.Txt, logFont, GraphPanel.Width - 20);
                            string[] lines = displayText.Split('\n');

                            for (int j = 0; j < lines.Length; j++)
                            {
                                if (yPos + lineHeight > maxY)
                                {
                                    break; 
                                }

                                graphics.DrawString(lines[j], logFont, textBrush, new Point(10, yPos));
                                yPos += lineHeight;
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"绘图错误: {ex.Message}");
        }
    }

    private Font GetStatusFont()
    {
        try
        {
            return new Font("Microsoft YaHei", 8.5f, FontStyle.Bold);
        }
        catch
        {
            return new Font(SystemFonts.DefaultFont.FontFamily, 8.5f, FontStyle.Bold);
        }
    }

    private string GetClippedTextWithEllipsis(Graphics g, string text, Font font, float maxWidth)
    {
        string ellipsis = "...";
        SizeF ellipsisSize = g.MeasureString(ellipsis, font);

        string[] parts = text.Split(new[] { " | " }, StringSplitOptions.None);
        string result = string.Join(" | ", parts);

        while (parts.Length > 1 && g.MeasureString(result + ellipsis, font).Width > maxWidth)
        {
            Array.Resize(ref parts, parts.Length - 1);
            result = string.Join(" | ", parts) + ellipsis;
        }

        return result;
    }

    // 2025-0618 EVIAS 优化文本颜色显示
    private Brush GetTextBrush(int type)
    {
        switch (type)
        {
            case 1: return Brushes.Crimson;       // 错误信息 - 深红色
            case 2: return Brushes.ForestGreen;   // 成功信息 - 森林绿
            case 3: return Brushes.RoyalBlue;     // 登录信息 - 皇家蓝
            case 4: return Brushes.DarkOrange;    // 掉落信息 - 深橙色
            case 5: return Brushes.DarkViolet;    // 物品信息 - 深紫色
            case 6: return Brushes.Navy;          // 调试信息 - 海军蓝
            case 7: return Brushes.DimGray;       // 数据包信息 - 暗灰色
            case 8: return Brushes.DarkGreen;     // 保存信息 - 深绿色
            default: return Brushes.Black;        // 默认信息 - 黑色
        }
    }

    // 2025-0618 EVIAS 添加文本换行功能
    private string WrapText(Graphics graphics, string text, Font font, float maxWidth)
    {
        if (string.IsNullOrEmpty(text)) return text;

        string[] words = text.Split(' ');
        StringBuilder result = new StringBuilder();
        StringBuilder currentLine = new StringBuilder();

        foreach (string word in words)
        {
            string testLine = currentLine.Length == 0 ? word : currentLine + " " + word;
            SizeF size = graphics.MeasureString(testLine, font);

            if (size.Width <= maxWidth)
            {
                currentLine.Append(currentLine.Length == 0 ? word : " " + word);
            }
            else
            {
                if (currentLine.Length > 0)
                {
                    result.AppendLine(currentLine.ToString());
                    currentLine.Clear();
                }
                currentLine.Append(word);
            }
        }

        if (currentLine.Length > 0)
        {
            result.Append(currentLine.ToString());
        }

        return result.ToString();
    }

    // 2025-0618 EVIAS 滚动事件处理
    private void GraphPanel_MouseWheel(object sender, MouseEventArgs e)
    {
        try
        {
            int scrollLines = 3; 

            if (e.Delta > 0) 
            {
                _scrollOffset += scrollLines;
                _autoScroll = false;
            }
            else 
            {
                _scrollOffset = Math.Max(0, _scrollOffset - scrollLines);

                if (_scrollOffset == 0)
                {
                    _autoScroll = true;
                }
            }

            lock (txt)
            {
                int maxLines = (GraphPanel.Height - 50) / 16;
                int maxOffset = Math.Max(0, txt.Count - maxLines);
                _scrollOffset = Math.Min(_scrollOffset, maxOffset);
            }

            GraphPanel.Invalidate(); // 重绘
        }
        catch (Exception ex)
        {
            WriteLine(1, $"滚动处理错误: {ex.Message}");
        }
    }

    private void Form1_KeyDown(object sender, KeyEventArgs e)
    {
        try
        {
            switch (e.KeyCode)
            {
                case Keys.Home: // Home键 - 跳到最早的日志
                    lock (txt)
                    {
                        int maxLines = (GraphPanel.Height - 50) / 16;
                        _scrollOffset = Math.Max(0, txt.Count - maxLines);
                        _autoScroll = false;
                    }
                    GraphPanel.Invalidate();
                    break;

                case Keys.End: // End键 - 跳到最新的日志
                    _scrollOffset = 0;
                    _autoScroll = true;
                    GraphPanel.Invalidate();
                    break;

                case Keys.PageUp: // PageUp - 向上翻页
                    int pageUpLines = (GraphPanel.Height - 50) / 16 - 2;
                    _scrollOffset += pageUpLines;
                    _autoScroll = false;
                    lock (txt)
                    {
                        int maxLines = (GraphPanel.Height - 50) / 16;
                        int maxOffset = Math.Max(0, txt.Count - maxLines);
                        _scrollOffset = Math.Min(_scrollOffset, maxOffset);
                    }
                    GraphPanel.Invalidate();
                    break;

                case Keys.PageDown: // PageDown - 向下翻页
                    int pageDownLines = (GraphPanel.Height - 50) / 16 - 2;
                    _scrollOffset = Math.Max(0, _scrollOffset - pageDownLines);
                    if (_scrollOffset == 0)
                    {
                        _autoScroll = true;
                    }
                    GraphPanel.Invalidate();
                    break;
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, $"键盘处理错误: {ex.Message}");
        }
    }

    // 2025-0618 EVIAS 修复控制台文字叠加问题
    public static void WriteLine(int type, string txtt)
	{
		int num = kjdx / 18;
		lock (txt)
		{
			if (!World.AlWorldlog)
			{
				return;
			}
			// 记录到文件日志
			if (World.jllog == 1)
			{
				switch (type)
				{
				case 1:
					logo.FileTxtLog(txtt);
					break;
				case 2:
					logo.FileCQTxtLog(txtt);
					break;
				case 3:
					logo.FileLoninTxtLog(txtt);
					break;
				case 4:
					logo.FileDropItmeTxtLog(txtt);
					break;
				case 5:
					logo.FileItmeTxtLog(txtt);
					break;
				case 6:
					logo.FileBugTxtLog(txtt);
					break;
				case 7:
					logo.FilePakTxtLog(txtt);
					break;
				case 8:
					logo.SeveTxtLog(txtt);
					break;
				}
			}

			// 特殊类型的额外日志记录
			switch (type)
			{
			case 9:
				logo.zhtfTxtLog(txtt);
				break;
			case 10:
				logo.zhjsTxtLog(txtt);
				break;
			case 22:
				logo.jshdLog(txtt);
				break;
			case 33:
				logo.ljczLog(txtt);
				break;
			case 44:
				logo.bbcjLog(txtt);
				break;
			case 55:
				logo.deleteLog(txtt);
				break;
			case 66:
				logo.sjhdLog(txtt);
				break;
			case 77:
				logo.cfzTxtLog(txtt);
				break;
			case 88:
				logo.pzTxtLog(txtt);
				break;
			case 99:
				logo.FileTxtLog(txtt);
				break;
			case 100:
				logo.FileTxtLog(txtt);
				break;
			case 101:
				logo.WGTxtLog(txtt);
				break;
			}

			txt.Add(new TxtClass(type, FastString.BuildLogMessage(type, txtt)));

			if (_autoScroll)
			{
				_scrollOffset = 0; // 保持在底部
				try
				{
					var mainForm = Application.OpenForms.OfType<Form1>().FirstOrDefault();
					if (mainForm?.GraphPanel != null && mainForm.GraphPanel.IsHandleCreated)
					{
						mainForm.GraphPanel.BeginInvoke(new Action(() => mainForm.GraphPanel.Invalidate()));
					}
					else
					{
						_needsRefresh = true;
					}
				}
				catch
				{
					_needsRefresh = true;
				}
			}
			else
			{
				// 非自动滚动模式下仍使用延迟刷新
				_needsRefresh = true;
			}

			int maxDisplayLines = Math.Max(50, kjdx / 16); 

			int count = txt.Count;
			if (count > maxDisplayLines)
			{
				int removeCount = count - maxDisplayLines;
				for (int i = 0; i < removeCount; i++)
				{
					txt.RemoveAt(0);
				}
			}
		}
	}

	public void 活动开启状态()
	{
		try
		{
			// 仙魔大战
			string 仙魔状态 = (World.仙魔大战 != null) ? "仙魔大战  开启" : "仙魔大战  未开";
			if (toolStrip仙魔大战 != null) toolStrip仙魔大战.Text = 仙魔状态;

			// 攻城战
			string 攻城状态 = (World.攻城 != null) ? "攻城战  开启" : "攻城战  未开";
			if (toolStrip攻城战 != null) toolStrip攻城战.Text = 攻城状态;

			// 帮战
			string 帮战状态 = (World.帮战 != null) ? "帮战  开启" : "帮战  未开";
			if (toolStrip帮战 != null) toolStrip帮战.Text = 帮战状态;

			// 武林血战
			string 武林血战状态 = (World.武林血战进程 != 0) ? "武林血战  开启" : "武林血战  未开";
			if (toolStrip武林血战 != null) toolStrip武林血战.Text = 武林血战状态;
			// 异口同声
			string 异口同声状态 = (World.开启异口同声 != null) ? "异口同声  开启" : "异口同声  未开";
			if (toolStrip异口同声 != null) toolStrip异口同声.Text = 异口同声状态;

			// 全服双倍
			string 全服双倍状态 = (World.开启全服经验 != null) ? "全服双倍  开启" : "全服双倍  未开";
			if (toolStrip全服双倍 != null) toolStrip全服双倍.Text = 全服双倍状态;

			// 幸运玩家
			string 幸运玩家状态 = (World.开启幸运玩家 != null) ? "幸运玩家  开启" : "幸运玩家  未开";
			if (toolStrip幸运玩家 != null) toolStrip幸运玩家.Text = 幸运玩家状态;

			// boss攻城
			string boss攻城状态 = (World.boss攻城 != null) ? "boss攻城  开启" : "boss攻城  未开";
			if (toolStripBoss攻城 != null) toolStripBoss攻城.Text = boss攻城状态;

			// 世界BOSS
			string 世界BOSS状态 = (World.世界boss != null) ? "世界BOSS  开启" : "世界BOSS  未开";
			if (toolStrip世界BOSS != null) toolStrip世界BOSS.Text = 世界BOSS状态;
			// 势力战
			string 势力战状态 = (World.eve != null) ? "势力战  开启" : "势力战  未开";
			if (toolStrip势力战 != null) toolStrip势力战.Text = 势力战状态;

			// 野外BOSS
			string 野外BOSS状态 = (World.野外boss != null) ? "野外BOSS  开启" : "野外BOSS  未开";
			if (toolStrip野外BOSS != null) toolStrip野外BOSS.Text = 野外BOSS状态;

			// 死亡无敌
			string 死亡无敌状态 = (World.开启死亡无敌 == 0) ? "死亡无敌  未开" : "死亡无敌  开启";
			if (toolStrip死亡无敌 != null) toolStrip死亡无敌.Text = 死亡无敌状态;

			// 比武泡点
			string 比武泡点状态 = (World.比武泡点 != null) ? "比武泡点  开启" : "比武泡点  未开";
			if (toolStrip比武泡点 != null) toolStrip比武泡点.Text = 比武泡点状态;

			// 天降红包
			string 天降红包状态 = (World.红包雨 != null) ? "天降红包  开启" : "天降红包  未开";
			if (toolStrip天降红包 != null) toolStrip天降红包.Text = 天降红包状态;

			// 南林钟离
			string 南林钟离状态 = (World.南林钟离 != null) ? "南林钟离  开启" : "南林钟离  未开";
			if (toolStrip南林钟离 != null) toolStrip南林钟离.Text = 南林钟离状态;

			// 大乱斗
			string 大乱斗状态 = (World.大乱斗 != null) ? "大乱斗  开启" : "大乱斗  未开";
			if (toolStrip大乱斗 != null) toolStrip大乱斗.Text = 大乱斗状态;

			// 极限比武
			string 极限比武状态 = World.极限比武台服务.IsCompetitionStarted() ? "极限比武  开启" : "极限比武  未开";
			if (toolStrip极限比武 != null) toolStrip极限比武.Text = 极限比武状态;
		}
		catch (Exception ex)
		{
			WriteLine(1, $"活动状态更新失败: {ex.Message}");
		}
	}

	private void Form1_Layout(object sender, LayoutEventArgs e)
	{
		if (GraphPanel.Height != 0)
		{
			kjdx = GraphPanel.Height;
		}
	}

	private void menuItem37_Click(object sender, EventArgs e)
	{
		int num = (int)new Form2().ShowDialog();
	}

	private void menuItem39_Click(object sender, EventArgs e)
	{
		world.SetScript();
	}

	private void menuItem42_Click(object sender, EventArgs e)
	{
		world.Set制作物品();
	}

	private void menuItem43_Click(object sender, EventArgs e)
	{
		world.SetBbgItem();
		world.SetChouJiang();
		world.加载百宝阁抽奖();
	}

	private void menuItem44_Click(object sender, EventArgs e)
	{
		world.Set套装();
	}

	private void menuItem45_Click(object sender, EventArgs e)
	{
		world.Set等级奖励();
	}

	private void menuItem24_Click(object sender, EventArgs e)
	{
		world.Set物品兑换();
		world.Set神器兑换();
	}

	private void menuItem49_Click(object sender, EventArgs e)
	{
		new YbiForm().ShowDialog();
	}

	private void menuItem50_Click(object sender, EventArgs e)
	{
		world.Set移动();
	}

	private void menuItem52_Click(object sender, EventArgs e)
	{
		world.Set任务数据新();
	}

	private void menuItem46_Click(object sender, EventArgs e)
	{
		world.SetQG();
		world.Set升天气功();
	}

	private void menuItem48_Click(object sender, EventArgs e)
	{
		new NpcList().Show();
	}

	private void menuItem53_Click(object sender, EventArgs e)
	{
		world.Set石头属性();
	}

	private void menuItem55_Click(object sender, EventArgs e)
	{
		new 石头属性调整().Show();
	}

	private void menuItem47_Click(object sender, EventArgs e)
	{
		new SkillContrl().Show();
	}

	private void menuItem31_Click(object sender, EventArgs e)
	{
		try
		{
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.Client != null)
				{
					value.名字检测 = value.UserName;
					value.FLD_金币检测 = value.Player_Money;
					value.保存人物的数据();
				}
			}
		}
		catch (Exception ex)
		{
			WriteLine(1, "保存人物的数据出错|" + ex.Message);
		}
		WriteLine(6, "保存人物所有数据完成");
	}

	private void menuItem61_Click(object sender, EventArgs e)
	{
		if (World.攻城 == null)
		{
			World.攻城 = new 攻城战();
			WriteLine(2, "单线攻城战开启成功");
			if (sender is ToolStripMenuItem toolStripItem)
				toolStripItem.Text = "攻城战  开启";
			else if (sender is MenuItem menuItem)
				menuItem.Text = "攻城战  开启";
		}
		else
		{
			World.攻城.Dispose();
			WriteLine(2, "单线攻城战结束");
			if (sender is ToolStripMenuItem toolStripItem)
				toolStripItem.Text = "攻城战  未开";
			else if (sender is MenuItem menuItem)
				menuItem.Text = "攻城战  未开";
		}
	}

	private void menuItem62_Click(object sender, EventArgs e)
	{
		if (World.帮战 == null)
		{
			World.是否开启门战系统 = 1;
			World.胜利帮派ID = 0;
			World.帮战 = new 帮派战_门战();
			WriteLine(2, "帮战开始成功");
			if (sender is ToolStripMenuItem toolStripItem)
				toolStripItem.Text = "帮战  开启";
			else if (sender is MenuItem menuItem)
				menuItem.Text = "帮战  开启";
		}
		else
		{
			World.是否开启门战系统 = 0;
			World.帮战.Dispose();
			World.帮战 = null;
			WriteLine(2, "帮战结束成功");
			if (sender is ToolStripMenuItem toolStripItem)
				toolStripItem.Text = "帮战  未开";
			else if (sender is MenuItem menuItem)
				menuItem.Text = "帮战  未开";
		}
	}

	private void menuItem63_Click(object sender, EventArgs e)
	{
		try
		{
			if (World.武林血战进程 == 0)
			{
				if (World.个人血战 != null)
				{
					World.个人血战.Dispose();
					World.个人血战 = null;
				}
				World.个人血战 = new 武林血战();
				WriteLine(1, "武林血战开始！");
				更新菜单文字(sender, "武林血战  开启");
			}
			else if (MessageBox.Show("个人血战正在进行中确定要结束么？", "警告", MessageBoxButtons.YesNo) == DialogResult.Yes)
			{
				if (World.个人血战 != null)
				{
					World.个人血战.Dispose();
					World.个人血战 = null;
				}
				WriteLine(1, "武林血战结束！");
				更新菜单文字(sender, "武林血战  未开");
			}
		}
		catch (Exception)
		{
		}
	}

	private void menuItem51_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "区" + World.记录保存天数 + "天前记录数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll8);
			thread.Name = "Timer  Thread";
			thread.Start();
		}
	}

	private void menuItem59_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "人物吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll7);
			thread.Name = "Timer  Thread";
			thread.Start();
		}
	}

	private void menuItem64_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "账号吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll9);
			thread.Name = "Timer  Thread";
			thread.Start();
		}
	}

	private void menuItem65_Click(object sender, EventArgs e)
	{
		if (World.开启异口同声 == null)
		{
			World.开启异口同声 = new 异口同声();
			WriteLine(2, "异口同声开启完成");
			更新菜单文字(sender, "异口同声  开启");
		}
		else
		{
			World.开启异口同声.Dispose();
			World.开启异口同声 = null;
			WriteLine(2, "异口同声结束完成");
			更新菜单文字(sender, "异口同声  未开");
		}
	}

	private void menuItem66_Click(object sender, EventArgs e)
	{
		if (World.开启全服经验 == null)
		{
			World.开启全服经验 = new 全服经验();
			World.发送特殊公告("全服双倍已经开启,请各位大侠做好准备", 6, "公告");
			WriteLine(2, "双倍经验开始");
			更新菜单文字(sender, "全服双倍  开启");
		}
		else
		{
			World.开启全服经验.Dispose();
			World.开启全服经验 = null;
			WriteLine(2, "双倍经验结束");
			更新菜单文字(sender, "全服双倍  未开");
		}
	}

	private void menuItem67_Click(object sender, EventArgs e)
	{
		if (World.开启幸运玩家 == null)
		{
			World.开启幸运玩家 = new 幸运玩家();
			WriteLine(2, "幸运奖开始");
			更新菜单文字(sender, "幸运玩家  开启");
		}
		else
		{
			World.开启幸运玩家.Dispose();
			World.开启幸运玩家 = null;
			WriteLine(2, "幸运奖开始结束");
			更新菜单文字(sender, "幸运玩家  未开");
		}
	}

	private void menuItem41_Click(object sender, EventArgs e)
	{
		world.加载装备洗髓();
		WriteLine(2, "装备洗髓加载完成");
		world.加载装备首爆();
		WriteLine(2, "装备首爆加载完成");
	}

	private void menuItem68_Click(object sender, EventArgs e)
	{
		world.加载英雄职业武器();
		WriteLine(2, "英雄职业武器加载完成");
	}

	private void menuItem69_Click(object sender, EventArgs e)
	{
		new SendItem().Show();
	}

	private void menuItem70_Click(object sender, EventArgs e)
	{
		if (World.boss攻城 == null)
		{
			World.boss攻城 = new boss攻城();
			WriteLine(2, "boss开始");
			更新菜单文字(sender, "boss攻城  开启");
		}
		else
		{
			World.boss攻城.Dispose();
			World.boss攻城 = null;
			WriteLine(2, "boss结束");
			更新菜单文字(sender, "boss攻城  未开");
		}
	}

	private void menuItem71_Click(object sender, EventArgs e)
	{
		if (World.世界boss == null)
		{
			World.世界boss = new 世界BOSS();
			WriteLine(2, "世界boss开始");
			更新菜单文字(sender, "世界boss  开启");
		}
		else
		{
			World.世界boss.Dispose();
			World.世界boss = null;
			WriteLine(2, "世界boss结束");
			更新菜单文字(sender, "世界boss  未开");
		}
	}
	
	private void menuItem73_Click(object sender, EventArgs e)
	{
		游戏公告 游戏公告2 = new 游戏公告();
		游戏公告2.StartPosition = FormStartPosition.CenterParent;
		游戏公告2.ShowDialog();
	}

	private void menuItem74_Click(object sender, EventArgs e)
	{
		Move move = new Move();
		move.StartPosition = FormStartPosition.CenterParent;
		move.ShowDialog();
	}

	private void menuItem75_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "区记录数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll6);
			thread.Name = "Timer  Thread";
			thread.Start();
		}
	}

	private void menuItem76_Click(object sender, EventArgs e)
	{
		全体跨线 全体跨线2 = new 全体跨线();
		全体跨线2.StartPosition = FormStartPosition.CenterParent;
		全体跨线2.ShowDialog();
	}


    private void menuItem78_Click(object sender, EventArgs e)
    {
        string aboutMessage =
            $"EvrxjhServer 服务器管理工具\n\n" +
            $"版本: {Assembly.GetExecutingAssembly().GetName().Version}\n" +
            $"服务器: {World.服务器名}\n\n" +
            "本程序仅供学习C#网络编程技术之用\n" +
            "禁止用于商业或非法用途\n" +
            "使用即代表您同意上述声明\n\n" +
            "© 2025 EvrxjhServer ";

        MessageBox.Show(
            aboutMessage,
            "关于 EvrxjhServer ",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information
        );
    }

    private void menuItem79_Click(object sender, EventArgs e)
	{
		if (World.eve == null)
		{
			World.势力战参加最低转职 = 2;
			World.势力战参加最高转职 = 11;
			World.势力战类型 = 0;
			World.eve = new EventClass();
			World.势力战开启公告(World.势力战参加最低转职, World.势力战参加最高转职);
			WriteLine(2, "势力战开始啦再次点击关闭势力战");
			更新菜单文字(sender, "势力战  开启");
		}
		else
		{
			World.eve.Dispose();
			World.eve = null;
			WriteLine(2, "势力战结束啦再次点击开启势力战");
			更新菜单文字(sender, "势力战  未开");
		}
	}

	private void menuItem80_Click(object sender, EventArgs e)
	{
		world.boss攻城怪物();
	}

	private void menuItem81_Click(object sender, EventArgs e)
	{
		if (World.野外boss == null)
		{
			World.野外boss = new 野外BOSS();
			WriteLine(2, "野外boss 开始");
			更新菜单文字(sender, "野外boss  开启");
		}
		else
		{
			World.野外boss.Dispose();
			World.野外boss = null;
			WriteLine(2, "野外boss 结束");
			更新菜单文字(sender, "野外boss  未开");
		}
	}

	private void menuItem82_Click(object sender, EventArgs e)
	{
		world.boss伏魔洞怪物();
		world.boss冰宫内城怪物();
		world.boss活动副本怪物();
	}

	private void menuItem83_Click(object sender, EventArgs e)
	{
		world.加载物品回收();
	}

	private void menuItem89_Click(object sender, EventArgs e)
	{
		world.加载累计充值称号();
		world.SetLjcz();
		WriteLine(2, "累计充值称号加载完成");
		WriteLine(2, "加载百宝充值完成");
	}

	private void menuItem90_Click(object sender, EventArgs e)
	{
		world.Set假人等级奖励();
	}

	private void menuItem92_Click(object sender, EventArgs e)
	{
		world.Set冲关地图();
	}



    private void menuItem95_Click(object sender, EventArgs e)
	{
		new BinIP().ShowDialog();
	}

	private void menuItem58_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "分区30天未登陆账号吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			string sqlCommand = string.Format("DELETE FROM TBL_ACCOUNT where FLD_FQ='" + World.分区编号 + "'and 是否假人=0 and DateDiff(dd,FLD_LASTLOGINTIME,getdate())>30");
			DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
			WriteLine(55, "30天未登陆分区账号库清楚完成");
		}
	}

	private void menuItem96_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "分区60天未登陆账号吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			string sqlCommand = string.Format("DELETE FROM TBL_ACCOUNT where FLD_FQ='" + World.分区编号 + "'and 是否假人=0 and DateDiff(dd,FLD_LASTLOGINTIME,getdate())>60");
			DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
			WriteLine(55, "60天未登陆分区账号库清楚完成");
		}
	}

	private void menuItem97_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "分区90天未登陆账号吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			string sqlCommand = string.Format("DELETE FROM TBL_ACCOUNT where FLD_FQ='" + World.分区编号 + "'and 是否假人=0 and DateDiff(dd,FLD_LASTLOGINTIME,getdate())>90");
			DBA.ExeSqlCommand(sqlCommand, "rxjhaccount");
			WriteLine(55, "90天未登陆分区账号库清楚完成");
		}
	}

	private void menuItem98_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除无账号人物数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll1);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "无账号人物数据删除完成");
		}
	}

	private void menuItem99_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除无账号综合仓库数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll3);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "无账号综合仓库删除完成");
		}
	}

	private void menuItem100_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除无账号个人仓库数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll2);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "无账号个人仓库删除完成");
		}
	}

	private void menuItem101_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除无账号帮派数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll4);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "无账号帮派数据删除完成");
		}
	}

	private void menuItem33_Click(object sender, EventArgs e)
	{
		world.Set比武泡点奖励();
	}

	private void menuItem34_Click(object sender, EventArgs e)
	{
		if (World.比武泡点 == null)
		{
			DBA.ExeSqlCommand("DELETE FROM 荣誉比武排行 where 分区信息='" + World.分区编号 + "'", "GameServer");
			World.比武泡点 = new 比武泡点系统();
			WriteLine(2, "比武泡点 开始");
			更新菜单文字(sender, "结束比武泡点 ");
		}
		else
		{
			World.比武泡点.Dispose();
			World.比武泡点 = null;
			WriteLine(2, "比武泡点 结束");
			更新菜单文字(sender, "开启比武泡点 ");
		}
	}

	private void menuItem35_Click(object sender, EventArgs e)
	{
		world.加载职业系数();
	}

	private void menuItem36_Click(object sender, EventArgs e)
	{
		world.强化概率参数();
	}

	private void menuItem57_Click(object sender, EventArgs e)
	{
		GMGJ gMGJ = new GMGJ();
		gMGJ.StartPosition = FormStartPosition.CenterParent;
		gMGJ.ShowDialog();
	}

	private void menuItem94_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要合并" + World.分区编号 + "全部数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, FastString.Concat("一键合", World.分区编号, "区完成.数据合并")); 
		}
	}

	private void menuItem102_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要删除" + World.分区编号 + "全部数据吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAll0);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, FastString.Concat("一键开", World.分区编号, "区完成.数据清理")); 
		}
	}

	private void menuItem103_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要加载充值排名吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.加载充值排名);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "加载充值排名完成");
		}
	}

	private void menuItem104_Click(object sender, EventArgs e)
	{
		if (World.红包雨 == null)
		{
			World.红包雨 = new 红包雨系统();
			WriteLine(2, "天降红包 开始");
			更新菜单文字(sender, "天降红包 开始");
		}
		else
		{
			World.红包雨.Dispose();
			World.红包雨 = null;
			WriteLine(2, "天降红包 结束");
			更新菜单文字(sender, "天降红包 未开");
		}
	}

	private void menuItem105_Click(object sender, EventArgs e)
	{
		if (World.南林钟离 == null)
		{
			World.南林钟离 = new BossClass();
			WriteLine(2, "南林钟离 开始");
			更新菜单文字(sender, "南林钟离 开启");
		}
		else
		{
			World.南林钟离.Dispose();
			World.南林钟离 = null;
			WriteLine(2, "南林钟离 结束");
			更新菜单文字(sender, "南林钟离 未开");
		}
	}

    private void menuItem107_Click(object sender, EventArgs e)
	{
		if (l != null)
		{
			new NetSet(l.Server).Show();
		}
	}

	private void menuItem108_Click(object sender, EventArgs e)
	{
		if (World.大乱斗 == null)
		{
			DBA.ExeSqlCommand("DELETE FROM 荣誉乱斗排行 where 分区信息='" + World.分区编号 + "'", "GameServer");
			World.大乱斗 = new 大乱斗系统();
			WriteLine(2, "大乱斗 开始");
			更新菜单文字(sender, "大乱斗 开始");
		}
		else
		{
			World.大乱斗.Dispose();
			World.大乱斗 = null;
			WriteLine(2, "大乱斗 结束");
			更新菜单文字(sender, "大乱斗 未开");
		}
	}

	private void menuItem109_Click(object sender, EventArgs e)
	{
		world.Set大乱斗奖励();
	}

	private void menuItem110_Click(object sender, EventArgs e)
	{
		等级封印 等级封印2 = new 等级封印();
		等级封印2.Show();
	}

	private void menuItem111_Click(object sender, EventArgs e)
	{
		frmRobot frmRobot2 = new frmRobot(l);
		frmRobot2.Show();
	}

	private void menuItem112_Click(object sender, EventArgs e)
	{
		world.假人商店出售物品();
	}

	private void menuItem114_Click(object sender, EventArgs e)
	{
		Evias rWSXB = new Evias();
		rWSXB.textBoxName.Text = "";
		rWSXB.ShowDialog();
	}

	private void menuItem115_Click(object sender, EventArgs e)
	{
		BBGEdit bBGEdit = new BBGEdit();
		bBGEdit.Show();
	}

	private void menuItem117_Click(object sender, EventArgs e)
	{
		OpenEdit openEdit = new OpenEdit();
		openEdit.StartPosition = FormStartPosition.CenterParent;
		openEdit.ShowDialog();
	}

	private void menuItem116_Click(object sender, EventArgs e)
	{
		SELL sELL = new SELL();
		sELL.StartPosition = FormStartPosition.CenterParent;
		sELL.ShowDialog();
	}

	private void menuItem118_Click(object sender, EventArgs e)
	{
		ItemEdit itemEdit = new ItemEdit();
		itemEdit.StartPosition = FormStartPosition.CenterParent;
		itemEdit.ShowDialog();
	}

	private void menuItem119_Click(object sender, EventArgs e)
	{
		Form4 form = new Form4();
		form.ShowDialog();
	}

	private void menuItem120_Click(object sender, EventArgs e)
	{
		NPCADD nPCADD = new NPCADD();
		nPCADD.StartPosition = FormStartPosition.CenterParent;
		nPCADD.ShowDialog();
	}

	private void menuItem121_Click(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定要初始化删除全部数据吗..无法恢复请在三考虑..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
		{
			Thread thread = new Thread(Form2.FlushAl23);
			thread.Name = "Timer  Thread";
			thread.Start();
			WriteLine(55, "初始化数据完成.数据清理");
		}
	}

	private void menuItem122_Click(object sender, EventArgs e)
	{
		职业参数调整 职业参数调整2 = new 职业参数调整();
		职业参数调整2.StartPosition = FormStartPosition.CenterParent;
		职业参数调整2.ShowDialog();
	}

	private void menuItem123_Click(object sender, EventArgs e)
	{
		强化参数调整 强化参数调整2 = new 强化参数调整();
		强化参数调整2.StartPosition = FormStartPosition.CenterParent;
		强化参数调整2.ShowDialog();
	}

	private void menuItem126_Click(object sender, EventArgs e)
	{
		FormCreateCdk formCreateCdk = new FormCreateCdk();
		formCreateCdk.ShowDialog();
	}

	private void menuItem124_Click(object sender, EventArgs e)
	{
		全服叠加数 全服叠加数2 = new 全服叠加数();
		全服叠加数2.ShowDialog();
	}

	private void menuItem125_Click(object sender, EventArgs e)
	{
		if (!World.极限比武台服务.IsCompetitionStarted())
		{
			World.极限比武台服务.StartCompetition();
			WriteLine(2, "极限比武 开始");
			更新菜单文字(sender, "极限比武 开始");
		}
		else
		{
			World.极限比武台服务.EndCompetition();
			WriteLine(2, "极限比武 结束");
			更新菜单文字(sender, "极限比武 未开");
		}
	}

    private void menuItem127_Click(object sender, EventArgs e)
    {
        world.进阶装备();
    }

    private void menuItem128_Click(object sender, EventArgs e)
    {
        world.分解装备();
    }

    // 2025-0623 EVIAS 初始化服务器维护系统
    private void 初始化服务器维护系统()
    {
        try
        {
            serverMaintenance = new 服务器维护类();
            WriteLine(2, "服务器维护系统初始化完成");
        }
        catch (Exception ex)
        {
            WriteLine(1, $"初始化服务器维护系统失败: {ex.Message}");
        }
    }

    private void menuItem维护_Click(object sender, EventArgs e)
    {
        DialogResult result = MessageBox.Show("是否需要停机维护？", "服务器维护", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            string 倒计时输入 = "5";
            if (InputBox.Show("请输入倒计时时间(分钟)", "倒计时设置", ref 倒计时输入) != DialogResult.OK)
            {
                return; 
            }

            if (!int.TryParse(倒计时输入, out int 倒计时分钟) || 倒计时分钟 <= 0)
            {
                MessageBox.Show("请输入有效的倒计时时间！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string 时长输入 = "15";
            if (InputBox.Show("请输入维护时长(分钟)", "维护时长", ref 时长输入) != DialogResult.OK)
            {
                return; 
            }

            if (!int.TryParse(时长输入, out int 维护时长) || 维护时长 <= 0)
            {
                MessageBox.Show("请输入有效的维护时长！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            serverMaintenance.开始维护(维护时长, 倒计时分钟);
        }
    }

    private void menuItem立即维护_Click(object sender, EventArgs e)
    {
        DialogResult result = MessageBox.Show("确定要立即进行服务器维护吗？",
            "立即维护", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

        if (result == DialogResult.Yes)
        {
            if (serverMaintenance.立即维护())
            {
                MessageBox.Show("服务器已进入维护状态，所有玩家已被踢出。", "维护完成",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("立即维护操作失败，请查看日志。", "操作失败",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void menuItem取消维护_Click(object sender, EventArgs e)
    {
        DialogResult result = MessageBox.Show("确定要取消当前的服务器维护吗？",
            "取消维护", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            if (serverMaintenance.取消维护())
            {
                MessageBox.Show("服务器维护已取消", "操作成功",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("当前没有正在进行的维护任务可取消", "操作提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }


    private void menuItem清空控制台_Click(object sender, EventArgs e)
    {
        try
        {
            lock (txt)
            {
                txt.Clear();
            }
            GraphPanel.Invalidate(); 
            WriteLine(2, "控制台已清空");
        }
        catch (Exception ex)
        {
            WriteLine(1, "清空控制台失败: " + ex.Message);
        }
    }

    // 2025-0617 EVIAS 异常统计菜单点击事件
    private void menuItem异常统计_Click(object sender, EventArgs e)
    {
        try
        {
            string stats = RxjhClass.GetExceptionStatistics();
            MessageBox.Show(stats, "异常统计报告", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"获取异常统计失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // 2025-0618 EVIAS 日志清理菜单点击事件
    private void menuItem清理日志_Click(object sender, EventArgs e)
    {
        try
        {
            DialogResult result = MessageBox.Show(
                $"确定要清理 {World.记录保存天数} 天前的日志文件吗？\n\n" +
                "此操作将删除logs文件夹中的过期日志文件，无法恢复！",
                "确认清理日志",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                WriteLine(2, "开始手动清理日志文件...");
                RxjhClass.CleanupLogFiles();
                MessageBox.Show("日志清理完成！请查看控制台了解详细信息。", "清理完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"清理日志失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // 2025-0618 EVIAS 异常管理器菜单点击事件
    private void menuItem异常管理器_Click(object sender, EventArgs e)
    {
        try
        {
            异常管理器 exceptionManager = new 异常管理器();
            exceptionManager.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开异常管理器失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }


    // 2025-0618 EVIAS 显示数据库连接统计信息
    private void 显示数据库连接统计()
    {
        try
        {
            string stats = RxjhServer.DbClss.DbConnectionMonitor.GetDetailedReport();
            MessageBox.Show(stats, "数据库连接统计", MessageBoxButtons.OK, MessageBoxIcon.Information);
            WriteLine(6, "查看数据库连接统计: " + RxjhServer.DbClss.DbConnectionMonitor.GetStats());
        }
        catch (Exception ex)
        {
            WriteLine(1, "显示数据库连接统计错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 重置数据库统计数据
    private void 重置数据库统计()
    {
        try
        {
            RxjhServer.DbClss.DbConnectionMonitor.ResetStats();
            WriteLine(6, "数据库统计数据已重置");
            MessageBox.Show("数据库统计数据已重置", "重置完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            WriteLine(1, "重置数据库统计错误: " + ex.Message);
        }
    }


    // 2025-0618 EVIAS 启动数据库连接监控定时器
    private void 启动数据库监控定时器()
    {
        try
        {
            var dbMonitorTimer = new System.Windows.Forms.Timer();
            dbMonitorTimer.Interval = 5 * 60 * 1000; // 5分钟
            dbMonitorTimer.Tick += 数据库监控定时器_Tick;
            dbMonitorTimer.Start();

            WriteLine(6, "数据库连接监控定时器已启动，每5分钟检查一次");
        }
        catch (Exception ex)
        {
            WriteLine(1, "启动数据库监控定时器错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 数据库监控定时器事件
    private void 数据库监控定时器_Tick(object sender, EventArgs e)
    {
        try
        {
            // 检查活跃连接数是否异常
            int activeConnections = RxjhServer.DbClss.DbConnectionMonitor.ActiveConnections;
            int totalConnections = RxjhServer.DbClss.DbConnectionMonitor.TotalConnections;
            int failedConnections = RxjhServer.DbClss.DbConnectionMonitor.FailedConnections;

            // 如果活跃连接数过高，记录警告
            if (activeConnections > 10)
            {
                WriteLine(1, $"警告: 数据库活跃连接数过高 ({activeConnections})，可能存在连接泄漏");
            }

            // 如果失败率过高，记录警告
            if (totalConnections > 100)
            {
                double failureRate = (double)failedConnections / totalConnections * 100;
                if (failureRate > 10)
                {
                    WriteLine(1, $"警告: 数据库连接失败率过高 ({failureRate:F2}%)");
                }
            }

            // 记录定期统计信息
            WriteLine(6, $"数据库连接监控: {RxjhServer.DbClss.DbConnectionMonitor.GetStats()}");
        }
        catch (Exception ex)
        {
            WriteLine(1, "数据库监控定时器错误: " + ex.Message);
        }
    }


    // 2025-0618 EVIAS 启动内存监控系统
    private void 启动内存监控系统()
    {
        try
        {
            // 启动内存监控
            var memoryMonitor = MemoryMonitor.Instance;

            // 启动对象池管理器
            var poolManager = ObjectPoolManager.Instance;

            WriteLine(6, "内存监控系统已启动");
        }
        catch (Exception ex)
        {
            WriteLine(1, "启动内存监控系统错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 测试数据库连接
    private void 测试数据库连接()
    {
        try
        {
            WriteLine(6, "开始测试数据库连接...");

            string connectionString = DBA.getstrConnection("GameServer");
            if (string.IsNullOrEmpty(connectionString))
            {
                MessageBox.Show("数据库连接字符串为空！", "连接测试失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            using (var connection = new SqlConnection(connectionString))
            {
                var startTime = DateTime.Now;
                connection.Open();
                var connectTime = DateTime.Now - startTime;

                using (var command = new SqlCommand("SELECT @@VERSION", connection))
                {
                    var queryStart = DateTime.Now;
                    var result = command.ExecuteScalar();
                    var queryTime = DateTime.Now - queryStart;

                    string message = $"数据库连接测试成功！\n\n" +
                                   $"连接耗时: {connectTime.TotalMilliseconds:F2}ms\n" +
                                   $"查询耗时: {queryTime.TotalMilliseconds:F2}ms\n" +
                                   $"数据库版本: {result?.ToString().Split('\n')[0] ?? "未知"}";

                    MessageBox.Show(message, "连接测试成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    WriteLine(6, $"数据库连接测试成功，连接耗时: {connectTime.TotalMilliseconds:F2}ms");
                }
            }
        }
        catch (Exception ex)
        {
            string errorMessage = $"数据库连接测试失败！\n\n错误信息:\n{ex.Message}";
            MessageBox.Show(errorMessage, "连接测试失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, "数据库连接测试失败: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 显示内存状态
    private void 显示内存状态()
    {
        try
        {
            var memoryInfo = MemoryMonitor.Instance.GetMemoryInfo();
            var memoryStats = MemoryMonitor.Instance.GetMemoryStats();
            var collectionStats = MemoryMonitor.Instance.GetCollectionStats();

            string message = $"内存使用状态:\n\n" +
                           $"工作集: {memoryInfo.WorkingSet / 1024 / 1024:F2} MB\n" +
                           $"私有内存: {memoryInfo.PrivateMemory / 1024 / 1024:F2} MB\n" +
                           $"GC内存: {memoryInfo.GCMemory / 1024 / 1024:F2} MB\n\n" +
                           $"垃圾回收统计:\n" +
                           $"第0代: {memoryInfo.Gen0Collections} 次\n" +
                           $"第1代: {memoryInfo.Gen1Collections} 次\n" +
                           $"第2代: {memoryInfo.Gen2Collections} 次\n\n" +
                           $"集合统计:\n{collectionStats}\n\n" +
                           $"建议: {(MemoryMonitor.Instance.ShouldCleanupMemory() ? "建议执行垃圾回收" : "内存使用正常")}";

            MessageBox.Show(message, "内存状态", MessageBoxButtons.OK, MessageBoxIcon.Information);
            WriteLine(6, $"内存状态查看: {memoryStats}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"获取内存状态失败:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, "显示内存状态错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 执行强制垃圾回收
    private void 执行强制垃圾回收()
    {
        try
        {
            var beforeInfo = MemoryMonitor.Instance.GetMemoryInfo();

            MemoryMonitor.Instance.ForceGarbageCollection("手动触发");

            var afterInfo = MemoryMonitor.Instance.GetMemoryInfo();
            long freedMemory = beforeInfo.GCMemory - afterInfo.GCMemory;

            string message = $"垃圾回收完成!\n\n" +
                           $"回收前GC内存: {beforeInfo.GCMemory / 1024 / 1024:F2} MB\n" +
                           $"回收后GC内存: {afterInfo.GCMemory / 1024 / 1024:F2} MB\n" +
                           $"释放内存: {freedMemory / 1024 / 1024:F2} MB\n\n" +
                           $"注意: 频繁的强制垃圾回收可能影响性能";

            MessageBox.Show(message, "垃圾回收完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"执行垃圾回收失败:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, "执行强制垃圾回收错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 显示对象池统计
    private void 显示对象池统计()
    {
        try
        {
            string stats = FastString.Join("\n",
                StringBuilderPool.Instance.GetPoolStats(),
                ByteArrayPool.Instance.GetStats(),
                PacketPool.Instance.GetStats(),
                TempDataPool.Instance.GetStats()
            );

            string message = $"对象池使用统计:\n\n{stats}\n\n" +
                           $"说明:\n" +
                           $"- 当前: 池中可用对象数量\n" +
                           $"- 租用: 总租用次数\n" +
                           $"- 归还: 总归还次数\n" +
                           $"- 创建: 总创建次数\n\n" +
                           $"对象池可以减少内存分配，提升性能";

            MessageBox.Show(message, "对象池统计", MessageBoxButtons.OK, MessageBoxIcon.Information);
            WriteLine(6, $"对象池统计查看: {stats.Replace('\n', ' ')}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"获取对象池统计失败:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, "显示对象池统计错误: " + ex.Message);
        }
    }

    // 2025-0618 EVIAS 启动性能优化系统
    private void 启动性能优化系统()
    {
        try
        {
            ConfigCache.Instance.PreloadCommonConfigs();

            MonsterCacheManager.Instance.PreloadHotMonsters();

            Timer.DelayCall(TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5), () =>
            {
                try
                {
                    // 清理过期配置缓存
                    ConfigCache.Instance.CleanupExpiredItems();

                    // 清理空的玩家索引容器
                    PlayerIndexManager.Instance.CleanupEmptyContainers();

                    // 清理过期地面物品
                    if (ItemCleanupManager.Instance.ShouldCleanup())
                    {
                        ItemCleanupManager.Instance.CleanupExpiredItems();
                        ItemCleanupManager.Instance.CleanupEmptyMapIndexes();
                    }

                    // 清理怪物缓存
                    if (MonsterCacheManager.Instance.ShouldCleanupCache())
                    {
                        MonsterCacheManager.Instance.CleanupExpiredCache();
                    }

                    // 清理网络缓存
                    if (NetworkOptimizer.Instance.ShouldCleanup())
                    {
                        NetworkOptimizer.Instance.CleanupExpiredCache();
                    }

                    // 刷新网络批量队列
                    NetworkOptimizer.Instance.FlushAllBatchQueues();
                }
                catch (Exception ex)
                {
                    WriteLine(1, FastString.Concat("定期清理任务失败: ", ex.Message));
                }
            });

            WriteLine(6, "性能优化系统启动完成");
        }
        catch (Exception ex)
        {
            WriteLine(1, FastString.Concat("启动性能优化系统失败: ", ex.Message));
        }
    }

    // 2025-0618 EVIAS 显示性能统计
    private void 显示性能统计()
    {
        try
        {
            string playerIndexStats = PlayerIndexManager.Instance.GetIndexStats();
            string configCacheStats = ConfigCache.Instance.GetCacheStats();
            string memoryStats = MemoryMonitor.Instance.GetMemoryStats();

            string itemCleanupStats = ItemCleanupManager.Instance.GetCleanupStats();
            string monsterCacheStats = MonsterCacheManager.Instance.GetCacheStats();
            string networkStats = NetworkOptimizer.Instance.GetNetworkStats();

            string message = FastString.Join("\n\n",
                "=== 性能统计报告 ===",
                playerIndexStats,
                configCacheStats,
                memoryStats,
                "",
                "=== 第三阶段集合优化统计 ===",
                itemCleanupStats,
                monsterCacheStats,
                networkStats,
                "",
                "说明:",
                "- 玩家索引: 提供O(1)复杂度的玩家查找",
                "- 配置缓存: 减少配置文件I/O操作",
                "- 内存监控: 实时监控内存使用和GC状态",
                "- 物品清理: 自动清理过期地面物品",
                "- 怪物缓存: 热点怪物数据缓存",
                "- 网络优化: 封包缓存和批量发送"
            );

            MessageBox.Show(message, "性能统计", MessageBoxButtons.OK, MessageBoxIcon.Information);
            WriteLine(6, FastString.Concat("性能统计查看: ", playerIndexStats, " | ", configCacheStats, " | ", itemCleanupStats));
        }
        catch (Exception ex)
        {
            MessageBox.Show(FastString.Concat("获取性能统计失败:\n", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, FastString.Concat("显示性能统计错误: ", ex.Message));
        }
    }

    // 2025-0619 EVIAS 显示地面物品管理
    private void 显示地面物品管理()
    {
        try
        {
            string cleanupStats = ItemCleanupManager.Instance.GetCleanupStats();
            string mapStats = ItemCleanupManager.Instance.GetMapItemStats();

            string message = FastString.Join("\n\n",
                "=== 地面物品管理 ===",
                cleanupStats,
                "",
                mapStats,
                "",
                "操作说明:",
                "- 系统每2分钟自动清理过期物品",
                "- 每个地图最多保留1000个物品",
                "- 物品默认20秒后自动消失",
                "",
                "点击确定执行强制清理所有过期物品"
            );

            var result = MessageBox.Show(message, "地面物品管理", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            if (result == DialogResult.OK)
            {
                ItemCleanupManager.Instance.ForceCleanupAll();
                MessageBox.Show("强制清理完成！", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            WriteLine(6, FastString.Concat("地面物品管理查看: ", cleanupStats));
        }
        catch (Exception ex)
        {
            MessageBox.Show(FastString.Concat("地面物品管理失败:\n", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, FastString.Concat("显示地面物品管理错误: ", ex.Message));
        }
    }

    // 2025-0619 EVIAS 显示怪物缓存管理
    private void 显示怪物缓存管理()
    {
        try
        {
            // 创建怪物缓存管理窗口
            var form = new Form()
            {
                Text = "怪物缓存管理",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            // 创建控件
            var lblStats = new Label()
            {
                Text = "缓存统计:",
                Location = new Point(10, 10),
                Size = new Size(80, 20)
            };

            var txtStats = new TextBox()
            {
                Location = new Point(10, 35),
                Size = new Size(560, 60),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical
            };

            var lblAdd = new Label()
            {
                Text = "添加怪物到缓存 (输入怪物ID，多个用逗号分隔):",
                Location = new Point(10, 110),
                Size = new Size(300, 20)
            };

            var txtMonsterIds = new TextBox()
            {
                Location = new Point(10, 135),
                Size = new Size(400, 25),
                Text = "例如: 1001,1002,1003"
            };

            var btnAdd = new Button()
            {
                Text = "添加到缓存",
                Location = new Point(420, 135),
                Size = new Size(100, 25)
            };

            var lblRemove = new Label()
            {
                Text = "从缓存移除怪物 (输入怪物ID):",
                Location = new Point(10, 175),
                Size = new Size(200, 20)
            };

            var txtRemoveId = new TextBox()
            {
                Location = new Point(10, 200),
                Size = new Size(400, 25),
                Text = "例如: 1001"
            };

            var btnRemove = new Button()
            {
                Text = "从缓存移除",
                Location = new Point(420, 200),
                Size = new Size(100, 25)
            };

            var lblHotMonsters = new Label()
            {
                Text = "当前热点怪物:",
                Location = new Point(10, 240),
                Size = new Size(100, 20)
            };

            var txtHotMonsters = new TextBox()
            {
                Location = new Point(10, 265),
                Size = new Size(560, 100),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical
            };

            var btnRefresh = new Button()
            {
                Text = "刷新信息",
                Location = new Point(10, 380),
                Size = new Size(100, 30)
            };

            var btnClearAll = new Button()
            {
                Text = "清空所有缓存",
                Location = new Point(120, 380),
                Size = new Size(120, 30)
            };

            var btnPreload = new Button()
            {
                Text = "预加载BOSS",
                Location = new Point(250, 380),
                Size = new Size(100, 30)
            };

            var btnClose = new Button()
            {
                Text = "关闭",
                Location = new Point(470, 380),
                Size = new Size(100, 30),
                DialogResult = DialogResult.OK
            };

            // 添加控件到窗体
            form.Controls.AddRange(new Control[] {
                lblStats, txtStats, lblAdd, txtMonsterIds, btnAdd,
                lblRemove, txtRemoveId, btnRemove, lblHotMonsters, txtHotMonsters,
                btnRefresh, btnClearAll, btnPreload, btnClose
            });

            // 刷新信息方法
            Action refreshInfo = () =>
            {
                txtStats.Text = MonsterCacheManager.Instance.GetCacheStats();
                txtHotMonsters.Text = MonsterCacheManager.Instance.GetHotMonstersRanking();
            };

            // 事件处理
            btnAdd.Click += (s, e) =>
            {
                try
                {
                    string input = txtMonsterIds.Text.Trim();
                    if (string.IsNullOrEmpty(input))
                    {
                        MessageBox.Show("请输入怪物ID", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var ids = input.Split(',').Select(id => int.Parse(id.Trim())).ToArray();
                    int successCount = MonsterCacheManager.Instance.AddMonstersToCache(ids, "手动添加");
                    MessageBox.Show($"成功添加 {successCount}/{ids.Length} 个怪物到缓存", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    txtMonsterIds.Clear();
                    refreshInfo();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"添加失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            btnRemove.Click += (s, e) =>
            {
                try
                {
                    string input = txtRemoveId.Text.Trim();
                    if (string.IsNullOrEmpty(input))
                    {
                        MessageBox.Show("请输入怪物ID", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    int monsterId = int.Parse(input);
                    bool success = MonsterCacheManager.Instance.RemoveMonsterFromCache(monsterId);
                    MessageBox.Show(success ? "移除成功" : "怪物不在缓存中", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    txtRemoveId.Clear();
                    refreshInfo();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"移除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            btnRefresh.Click += (s, e) => refreshInfo();

            btnClearAll.Click += (s, e) =>
            {
                var result = MessageBox.Show("确定要清空所有缓存吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    MonsterCacheManager.Instance.ClearAllCache();
                    MessageBox.Show("缓存已清空", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    refreshInfo();
                }
            };

            btnPreload.Click += (s, e) =>
            {
                MonsterCacheManager.Instance.PreloadHotMonsters();
                MessageBox.Show("BOSS怪物预加载完成", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                refreshInfo();
            };

            // 初始化信息
            refreshInfo();

            // 显示窗体
            form.ShowDialog(this);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开怪物缓存管理失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // 2025-0619 EVIAS 显示网络优化管理
    private void 显示网络优化管理()
    {
        try
        {
            string networkStats = NetworkOptimizer.Instance.GetNetworkStats();
            string batchStatus = NetworkOptimizer.Instance.GetBatchQueueStatus();

            string message = FastString.Join("\n\n",
                "=== 网络优化管理 ===",
                networkStats,
                "",
                batchStatus,
                "",
                "优化说明:",
                "- 封包缓存5分钟过期，最多1000个",
                "- 批量发送每10个封包合并一次",
                "- 大于100字节的封包自动压缩",
                "",
                "点击确定刷新所有批量队列并清理缓存"
            );

            var result = MessageBox.Show(message, "网络优化管理", MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            if (result == DialogResult.OK)
            {
                NetworkOptimizer.Instance.FlushAllBatchQueues();
                NetworkOptimizer.Instance.CleanupExpiredCache();
                MessageBox.Show("网络优化刷新完成！", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            WriteLine(6, FastString.Concat("网络优化管理查看: ", networkStats));
        }
        catch (Exception ex)
        {
            MessageBox.Show(FastString.Concat("网络优化管理失败:\n", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            WriteLine(1, FastString.Concat("显示网络优化管理错误: ", ex.Message));
        }
    }
}


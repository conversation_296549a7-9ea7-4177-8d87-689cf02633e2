namespace RxjhVer23;

internal class NewWebShopSaleItem : INewWebShopSaleItem
{
	public string SaleId = "";

	public long ItemId;

	public string ItemName = "";

	public string ItemDesc = "";

	public PurchaseSettlementTypeEnum SalePriceType = PurchaseSettlementTypeEnum.RxPiont;

	public int CalePrice;

	public int DiscountedPrice;

	public int MainMenuId;

	public int SubMenuId;

	public int[] ReservedFieldArray = new int[21];

	public int ItemMagic;

	public int ItemMagic1;

	public int ItemMagic2;

	public int ItemMagic3;

	public int ItemMagic4;

	public int IntermediateSoulAttribute;

	public int SoulAwakeningStage;

	public int EvolutionLevel;

	public int FourGodsPower;

	public int IsBound;

	public int AvailableDays;

	int INewWebShopSaleItem.FLD_SALE_ID { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_ID { get; set; }

	string INewWebShopSaleItem.FLD_ITEM_NAME { get; set; }

	string INewWebShopSaleItem.FLD_ITEM_DESC { get; set; }

	int INewWebShopSaleItem.FLD_SALE_PRICE { get; set; }

	int INewWebShopSaleItem.FLD_SALE_PRICE_TYPE { get; set; }

	int INewWebShopSaleItem.FLD_SALE_TYPE1 { get; set; }

	int INewWebShopSaleItem.FLD_SALE_TYPE2 { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_MAGIC { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_MAGIC1 { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_MAGIC2 { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_MAGIC3 { get; set; }

	int INewWebShopSaleItem.FLD_ITEM_MAGIC4 { get; set; }

	int INewWebShopSaleItem.FLD_INTERMEDIATE_SOUL_ATTRIBUTE { get; set; }

	int INewWebShopSaleItem.FLD_SOUL_AWAKENING_STAGE { get; set; }

	int INewWebShopSaleItem.FLD_EVOLUTION_LEVEL { get; set; }

	int INewWebShopSaleItem.FLD_FOUR_GODS_POWER { get; set; }

	int INewWebShopSaleItem.FLD_IS_BOUND { get; set; }

	int INewWebShopSaleItem.FLD_AVAILABLE_DAYS { get; set; }

	public NewWebShopSaleItem()
	{
	}

	public NewWebShopSaleItem(INewWebShopSaleItem saleItem)
	{
		SaleId = $"SALE{saleItem.FLD_SALE_ID:D5}";
		ItemId = saleItem.FLD_ITEM_ID;
		ItemName = saleItem.FLD_ITEM_NAME;
		ItemDesc = saleItem.FLD_ITEM_DESC;
		SalePriceType = (PurchaseSettlementTypeEnum)saleItem.FLD_SALE_PRICE_TYPE;
		CalePrice = saleItem.FLD_SALE_PRICE * 3;
		DiscountedPrice = saleItem.FLD_SALE_PRICE;
		MainMenuId = saleItem.FLD_SALE_TYPE1;
		SubMenuId = saleItem.FLD_SALE_TYPE2;
		ItemMagic = saleItem.FLD_ITEM_MAGIC;
		ItemMagic1 = saleItem.FLD_ITEM_MAGIC1;
		ItemMagic2 = saleItem.FLD_ITEM_MAGIC2;
		ItemMagic3 = saleItem.FLD_ITEM_MAGIC3;
		ItemMagic4 = saleItem.FLD_ITEM_MAGIC4;
		IntermediateSoulAttribute = saleItem.FLD_INTERMEDIATE_SOUL_ATTRIBUTE;
		SoulAwakeningStage = saleItem.FLD_SOUL_AWAKENING_STAGE;
		EvolutionLevel = saleItem.FLD_EVOLUTION_LEVEL;
		FourGodsPower = saleItem.FLD_FOUR_GODS_POWER;
		IsBound = saleItem.FLD_IS_BOUND;
		AvailableDays = saleItem.FLD_AVAILABLE_DAYS;
		((INewWebShopSaleItem)this).FLD_SALE_ID = saleItem.FLD_SALE_ID;
		((INewWebShopSaleItem)this).FLD_ITEM_ID = saleItem.FLD_ITEM_ID;
		((INewWebShopSaleItem)this).FLD_ITEM_NAME = saleItem.FLD_ITEM_NAME;
		((INewWebShopSaleItem)this).FLD_ITEM_DESC = saleItem.FLD_ITEM_DESC;
		((INewWebShopSaleItem)this).FLD_SALE_PRICE_TYPE = saleItem.FLD_SALE_PRICE_TYPE;
		((INewWebShopSaleItem)this).FLD_SALE_PRICE = saleItem.FLD_SALE_PRICE;
		((INewWebShopSaleItem)this).FLD_SALE_TYPE1 = saleItem.FLD_SALE_TYPE1;
		((INewWebShopSaleItem)this).FLD_SALE_TYPE2 = saleItem.FLD_SALE_TYPE2;
		((INewWebShopSaleItem)this).FLD_ITEM_MAGIC = saleItem.FLD_ITEM_MAGIC;
		((INewWebShopSaleItem)this).FLD_ITEM_MAGIC1 = saleItem.FLD_ITEM_MAGIC1;
		((INewWebShopSaleItem)this).FLD_ITEM_MAGIC2 = saleItem.FLD_ITEM_MAGIC2;
		((INewWebShopSaleItem)this).FLD_ITEM_MAGIC3 = saleItem.FLD_ITEM_MAGIC3;
		((INewWebShopSaleItem)this).FLD_ITEM_MAGIC4 = saleItem.FLD_ITEM_MAGIC4;
		((INewWebShopSaleItem)this).FLD_INTERMEDIATE_SOUL_ATTRIBUTE = saleItem.FLD_INTERMEDIATE_SOUL_ATTRIBUTE;
		((INewWebShopSaleItem)this).FLD_SOUL_AWAKENING_STAGE = saleItem.FLD_SOUL_AWAKENING_STAGE;
		((INewWebShopSaleItem)this).FLD_EVOLUTION_LEVEL = saleItem.FLD_EVOLUTION_LEVEL;
		((INewWebShopSaleItem)this).FLD_FOUR_GODS_POWER = saleItem.FLD_FOUR_GODS_POWER;
		((INewWebShopSaleItem)this).FLD_IS_BOUND = saleItem.FLD_IS_BOUND;
		((INewWebShopSaleItem)this).FLD_AVAILABLE_DAYS = saleItem.FLD_AVAILABLE_DAYS;
	}
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Security.Cryptography;
using System.Windows.Forms;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using System.Linq;

namespace RxjhServer;

public class Evias : Form
{
	public class ListBoxItem
	{
		public string ItemText;

		public int ItemValue;

		public ListBoxItem(string text, int val)
		{
			ItemText = text;
			ItemValue = val;
		}

		public override string ToString()
		{
			return ItemText;
		}
	}

	private IContainer components = new Container();

	public TextBox textBoxName;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private GroupBox groupBox1;

	private Button buttonITEMSelect;

	private TextBox textBoxSTSX;

	private ComboBox comboBoxST;

	private TextBox textBoxSTDM;

	private Label label18;

	private Label label17;

	private TextBox textBoxJH;

	private Label label16;

	private TextBox textBoxTS;

	private CheckBox checkBoxBD;

	private Label label15;

	private TextBox textBoxZHH;

	private Label label13;

	private TextBox textBoxCHH;

	private TextBox textBoxFJ0;

	private ComboBox comboBoxFJSX;

	private ComboBox comboBoxFJQH;

	private TextBox textBoxSX4;

	private TextBox textBoxSX3;

	private TextBox textBoxSX2;

	private TextBox textBoxSX1;

	private ComboBox comboBoxSX3;

	private ComboBox comboBoxSX2;

	private ComboBox comboBoxSX1;

	private TextBox textBoxFJ1;

	private ComboBox comboBoxSX4;

	private TextBox textBoxPID;

	private Label label40;

	private Button buttonSendItem;

	private Label label3;

	private TextBox textBoxNUM;

	private ListBox listBoxALLITEM;

	private ComboBox comboBoxITEMTYPE;

	private Label label14;

	private Label label6;

	private TextBox textBoxSXFJDM;

	private Label label11;

	private TextBox textBoxSXDM1;

	private Label label10;

	private TextBox textBoxSXDM2;

	private Label label9;

	private TextBox textBoxSXDM3;

	private Label label8;

	private TextBox textBoxSXDM4;

	private TabPage tabPage3;

	private ListBox listBoxAllPlayer;

	private GroupBox groupBox4;

	private TabPage tabPage4;

	private GroupBox groupBox5;

	private CheckBox checkBox1;

	private TextBox textBoxPack;

	private Label label29;

	private Button buttonSendPack;

	private Button buttonSeeZBItem;

	private ListView listViewItem1;

	private ColumnHeader columnHeader_0;

	private ColumnHeader columnHeader_1;

	private ColumnHeader columnHeader_2;

	private ColumnHeader columnHeader_3;

	private ColumnHeader columnHeader_8;

	private ColumnHeader columnHeader_4;

	private ColumnHeader columnHeader_5;

	private ColumnHeader columnHeader_6;

	private Button buttonSeePackItem;

	private Button button1;

	private Button buttonShuaXin;

	private Button button2;

	private TextBox textBox1;

	private Label label31;

	private TextBox textBox2;

	private Label label33;

	private Button button3;

	public TextBox textBoxKey;

	private Label label34;

	private Button button4;

	private ColumnHeader columnHeader1;

	private Button button6;

	private Button button7;

	private Button button8;

	private TextBox textBox11;

	private Label label44;

	private Button button11;

	private Button button10;

	private Button button9;

	private Button button12;

	private Button button14;

	private Button button13;

	private Button button5;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 删除数据;

	private ToolStripMenuItem 绑定物品;

	private ToolStripMenuItem 解绑物品;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;
    private Button button21;
    private ComboBox comboBox3;
    private ComboBox comboBox2;
    private ComboBox comboBox1;
    private Label label57;
    private Button button41;
    private TextBox textBox23;
    private Label label56;
    private StatusStrip statusStrip1;
    private ToolStripStatusLabel toolStripStatusLabel1;
    private ToolStripStatusLabel tishi;
    private TabPage tabPage2;
    private GroupBox groupBox2;
    private Button button20;
    private Button button19;
    private Button button18;
    private Button button17;
    private Button button16;
    private Button button15;
    private Button buttonClearPublicItem;
    private Button buttonClearQiGong;
    private Button buttonClearKongFu;
    private Button buttonClearZTData;
    private Button buttonClearQData;
    private Button buttonCleraQItem;
    private Button buttonClearZBItem;
    private Button buttonClearItem;
    private GroupBox groupBox3;
    private TextBox textBox22;
    private Label label55;
    private Label label54;
    private TextBox textBox21;
    private Label label53;
    private Label label52;
    private Label label51;
    private Label label50;
    private TextBox textBox20;
    private TextBox textBox19;
    private TextBox textBox18;
    private TextBox textBox17;
    private TextBox textBox16;
    private Label label49;
    private TextBox textBox15;
    private TextBox textBox14;
    private Label label48;
    private Label label47;
    private TextBox textBox13;
    private TextBox textBox12;
    private Label label46;
    private Label label45;
    private TextBox textBox10;
    private TextBox textBox9;
    private TextBox textBox8;
    private TextBox textBox7;
    private TextBox textBox6;
    private TextBox textBox5;
    private TextBox textBox4;
    private Label label43;
    private Label label42;
    private Label label41;
    private Label label39;
    private Label label38;
    private Label label37;
    private Label label36;
    private TextBox textBox3;
    private Label label1;
    private Label label35;
    private TextBox textBoxShenLi;
    private Button buttonPlayerSub;
    private Button buttonPlayerAdd;
    private Button buttonPlayerEdit;
    private TextBox textBoxPlayerSXValue;
    private Label label32;
    private ComboBox comboBoxPlayerSXSelect;
    private Label label30;
    private Label label12;
    private TextBox textBoxSE;
    private TextBox textBoxPlayerSTWG;
    private Label label2;
    private TextBox textBoxPlayerY;
    private Label label4;
    private TextBox textBoxPlayerX;
    private Label label5;
    private TextBox textBoxPlayerMap;
    private Label label7;
    private TextBox textBoxPlayerLL;
    private TextBox textBoxPlayerWuXun;
    private TextBox textBoxPlayerEXP;
    private TextBox textBoxPlayerMoney;
    private TextBox textBoxPlayerJobLevel;
    private TextBox textBoxPlayerJob;
    private TextBox textBoxPlayerLevel;
    private TextBox textBoxRXPIONT;
    private TextBox textBoxUserID;
    private TextBox textBoxWorldID;
    private Label label19;
    private Label label20;
    private Label label21;
    private Label label22;
    private Label label23;
    private Label label24;
    private Label label25;
    private Label label26;
    private Label label27;
    private Label label28;
    private Label label58;
    private TextBox textBox24;
    public int 序列号 = 0;

	public Evias()
	{
		InitializeComponent();
		序列号 = 0;
		listViewItem1.ContextMenuStrip = contextMenuStrip1;
		contextMenuStrip1.Closed += contextMenuStrip1_Closed;
	}

	private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
	{
		listViewItem1.ContextMenuStrip = contextMenuStrip1;
	}

	private void GMGJ_Load(object sender, EventArgs e)
	{
		textBoxNUM.Text = "1";
		textBoxSXFJDM.Text = "0";
        textBoxSTDM.Text = "0"; // 设置默认值
        textBoxSXDM1.Text = "0";
		textBoxSXDM2.Text = "0";
		textBoxSXDM3.Text = "0";
		textBoxSXDM4.Text = "0";
        comboBoxITEMTYPE.Text = "请选择";
		listBoxAllPlayer.Items.Clear();
		comboBoxFJQH.SelectedIndex = 0;
		comboBoxFJSX.SelectedIndex = 0;
		comboBoxSX1.SelectedIndex = 0;
		comboBoxSX2.SelectedIndex = 0;
		comboBoxSX3.SelectedIndex = 0;
		comboBoxSX4.SelectedIndex = 0;
        comboBoxST.Items.Clear();
        comboBoxST.SelectedIndex = -1; //Evias
        初始化石头类型下拉框();

        foreach (Players value in World.allConnectedChars.Values)
		{
			ListBoxItem item = new ListBoxItem(value.UserName, value.人物全服ID);
			listBoxAllPlayer.Items.Add(item);
		}
		if (textBoxName.Text.Length > 0)
		{
			listBoxAllPlayer.SelectedIndex = listBoxAllPlayer.FindStringExact(textBoxName.Text, 0);
			查看玩家信息();
		}
		ItemDef.AddComBoxItemReside2(comboBoxITEMTYPE);
	}

	private void 查看玩家信息()
	{
		Players players = World.检查玩家name(textBoxName.Text);
		if (players != null)
		{
			textBoxWorldID.Text = players.人物全服ID.ToString();
			textBoxUserID.Text = players.Userid;
			textBoxRXPIONT.Text = players.FLD_RXPIONT.ToString();
			textBoxName.Text = players.UserName;
			textBoxPlayerLevel.Text = players.Player_Level.ToString();
			textBoxPlayerJob.Text = players.Player_Job.ToString();
			textBoxPlayerJobLevel.Text = players.Player_Job_leve.ToString();
			textBoxPlayerMoney.Text = players.Player_Money.ToString();
			textBoxPlayerEXP.Text = players.人物经验.ToString();
			textBoxPlayerWuXun.Text = players.Player_WuXun.ToString();
			textBoxPlayerLL.Text = players.Player_ExpErience.ToString();
			textBoxPlayerMap.Text = players.人物坐标_地图.ToString();
			textBoxPlayerX.Text = players.人物坐标_X.ToString();
			textBoxPlayerY.Text = players.人物坐标_Y.ToString();
			textBoxPlayerSTWG.Text = players.升天武功点数.ToString();
			textBoxSE.Text = players.人物善恶.ToString();
			textBoxShenLi.Text = players.神女武功点数.ToString();
			textBox3.Text = players.转生次数.ToString();
			textBox4.Text = players.转生_追加_攻击.ToString();
			textBox5.Text = players.转生_追加_防御.ToString();
			textBox6.Text = players.转生_追加_生命.ToString();
			textBox7.Text = players.角色暴率.ToString();
			textBox8.Text = players.杀人次数.ToString();
			textBox9.Text = players.被杀次数.ToString();
			textBox10.Text = players.最后在线时间.ToString();
			textBox12.Text = players.个人仓库钱数.ToString();
			textBox13.Text = players.综合仓库钱数.ToString();
			textBox16.Text = players.称号积分.ToString();
			textBox17.Text = players.玫瑰称号积分.ToString();
			textBox18.Text = players.FLD_RXPIONTX.ToString();
			textBox19.Text = players.设置固定伤害.ToString();
			textBox20.Text = players.GM工具强化合成概率.ToString();
			textBox21.Text = players.累计充值.ToString();
			textBox14.Text = RxjhClass.GetUserIpadds(players.zastcoginip).ToString();
			textBox15.Text = RxjhClass.GetUserIpadds(players.lastloginip).ToString();
			textBox22.Text = players.游戏安全码.ToString();
            textBox24.Text = players.FLD_账号累充.ToString(); //EVIAS 抽奖次数
        }
	}

	private void comboBoxITEMTYPE_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = ((ItemDef.MyItem)comboBoxITEMTYPE.SelectedItem).Value.ToString();
		listBoxALLITEM.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(dBToDataTable.Rows[i]["FLD_PID"].ToString(), dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			listBoxALLITEM.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
	}

	private void listBoxALLITEM_SelectedIndexChanged(object sender, EventArgs e)
	{
		KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBoxALLITEM.SelectedItem;
		textBoxPID.Text = keyValuePair.Key;
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_PID='" + textBoxPID.Text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
		{
			textBoxPID.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
			dBToDataTable.Dispose();
		}
	}

	private void buttonSendItem_Click(object sender, EventArgs e) //EVIAS 优化
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus(); 
                return;
            }

            if (string.IsNullOrWhiteSpace(textBoxPID.Text))
            {
                tishi.Text = "请输入有效的物品ID";
                textBoxPID.Focus();
                return;
            }
			Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				string pidText = textBoxPID.Text.Trim();
				string numText = textBoxNUM.Text.Trim();
				string stdmText = textBoxSTDM.Text.Trim();
				string sxfjdmText = textBoxSXFJDM.Text.Trim();
				string sxdm1Text = textBoxSXDM1.Text.Trim();
				string sxdm2Text = textBoxSXDM2.Text.Trim();
				string sxdm3Text = textBoxSXDM3.Text.Trim();
				string sxdm4Text = textBoxSXDM4.Text.Trim();
				string chhText = textBoxCHH.Text.Trim();
				string zhhText = textBoxZHH.Text.Trim();
				string jhText = textBoxJH.Text.Trim();
				string tsText = textBoxTS.Text.Trim();

				if (!int.TryParse(pidText, out int pid))
				{
					tishi.Text = "物品ID格式不正确";
					textBoxPID.Focus();
					return;
				}
				if (!int.TryParse(numText, out int numValue))
				{
					tishi.Text = "数量格式不正确";
					textBoxNUM.Focus();
					return;
				}

				ItmeClass itmeID = ItmeClass.GetItmeID(pid);
				if (itmeID == null)
				{
					tishi.Text = "发送失败，所选物品不存在";
					return;
				}
				int num = 1;
				if (itmeID.FLD_SIDE == 0)
				{
					num = numValue;
				}
				for (int i = 0; i < num; i++)
				{
					int num2 = players.得到包裹空位(players);
					if (num2 == -1)
					{
						MessageBox.Show("没有空位");
						break;
					}
	
					if (itmeID.FLD_RESIDE2 == 0) //EVIAS
					{
						if (!int.TryParse(stdmText, out int stdmValue))
						{
							tishi.Text = "属性代码格式不正确";
							textBoxSTDM.Focus();
							return;
						}
						players.增加物品带属性(pid, num2, numValue, stdmValue, 0, 0, 0, 0, 0, 0, 0, checkBoxBD.Checked ? 1 : 0, 0);
					}
					else
					{
						// 依次校验所有参数
						if (!int.TryParse(sxfjdmText, out int sxfjdmValue) ||
							!int.TryParse(sxdm1Text, out int sxdm1Value) ||
							!int.TryParse(sxdm2Text, out int sxdm2Value) ||
							!int.TryParse(sxdm3Text, out int sxdm3Value) ||
							!int.TryParse(sxdm4Text, out int sxdm4Value) ||
							!int.TryParse(chhText, out int chhValue) ||
							!int.TryParse(zhhText, out int zhhValue) ||
							!int.TryParse(jhText, out int jhValue) ||
							!int.TryParse(tsText, out int tsValue))
						{
							tishi.Text = "请检查所有属性参数是否为数字";
							return;
						}
						players.增加物品带属性(pid, num2, numValue, sxfjdmValue, sxdm1Value, sxdm2Value, sxdm3Value, sxdm4Value, chhValue, zhhValue, jhValue, checkBoxBD.Checked ? 1 : 0, tsValue);
					}
					tishi.Text = "发送物品成功";
				}
			}
			else
			{
                tishi.Text = "该玩家没有在线请重新输入";
            }
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.ToString());
		}
	}

	private void comboBoxSX1_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM1.Text = 属性选择(comboBoxSX1.SelectedItem.ToString(), int.Parse(textBoxSX1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void comboBoxSX2_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM2.Text = 属性选择(comboBoxSX2.SelectedItem.ToString(), int.Parse(textBoxSX2.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void comboBoxSX3_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM3.Text = 属性选择(comboBoxSX3.SelectedItem.ToString(), int.Parse(textBoxSX3.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void comboBoxSX4_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM4.Text = 属性选择(comboBoxSX4.SelectedItem.ToString(), int.Parse(textBoxSX4.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxSX1_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM1.Text = 属性选择(comboBoxSX1.SelectedItem.ToString(), int.Parse(textBoxSX1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxSX2_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM2.Text = 属性选择(comboBoxSX2.SelectedItem.ToString(), int.Parse(textBoxSX2.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxSX3_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM3.Text = 属性选择(comboBoxSX3.SelectedItem.ToString(), int.Parse(textBoxSX3.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxSX4_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXDM4.Text = 属性选择(comboBoxSX4.SelectedItem.ToString(), int.Parse(textBoxSX4.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void comboBoxFJQH_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXFJDM.Text = 附加属性选择((comboBoxFJQH.SelectedItem == null) ? "无" : comboBoxFJQH.SelectedItem.ToString(), int.Parse(textBoxFJ0.Text), (comboBoxFJSX.SelectedItem == null) ? "无" : comboBoxFJSX.SelectedItem.ToString(), int.Parse(textBoxFJ1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void comboBoxFJSX_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXFJDM.Text = 附加属性选择((comboBoxFJQH.SelectedItem == null) ? "无" : comboBoxFJQH.SelectedItem.ToString(), int.Parse(textBoxFJ0.Text), (comboBoxFJSX.SelectedItem == null) ? "无" : comboBoxFJSX.SelectedItem.ToString(), int.Parse(textBoxFJ1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxFJ0_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXFJDM.Text = 附加属性选择((comboBoxFJQH.SelectedItem == null) ? "无" : comboBoxFJQH.SelectedItem.ToString(), int.Parse(textBoxFJ0.Text), (comboBoxFJSX.SelectedItem == null) ? "无" : comboBoxFJSX.SelectedItem.ToString(), int.Parse(textBoxFJ1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private void textBoxFJ1_TextChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSXFJDM.Text = 附加属性选择((comboBoxFJQH.SelectedItem == null) ? "无" : comboBoxFJQH.SelectedItem.ToString(), int.Parse(textBoxFJ0.Text), (comboBoxFJSX.SelectedItem == null) ? "无" : comboBoxFJSX.SelectedItem.ToString(), int.Parse(textBoxFJ1.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

	private int 附加属性选择(string QHTYPE, int QHNUM, string FJSXTYPE, int FJSXNUM)
	{
		int num = 0;
		try
		{
			if (QHTYPE != null)
			{
				switch (QHTYPE)
				{
				case "武器强化":
				case "戒指强化":
					num += 10000000 + QHNUM;
					break;
				case "防具强化":
				case "项链强化":
					num += 20000000 + QHNUM;
					break;
				case "披风强化":
					num += 40000000 + QHNUM;
					break;
				case "耳环强化":
					num += 30000000 + QHNUM;
					break;
				case "药品次数":
					num += 2000000000 + QHNUM;
					break;
				case "灵宠强化":
					num += 190000000 + QHNUM;
					break;
				}
			}
			if (FJSXTYPE != null)
			{
				int num2 = (FJSXNUM - 1) * 100;
				switch (FJSXTYPE)
				{
				case "火":
					num += 1000001000 + num2;
					break;
				case "水":
					num += 1000002000 + num2;
					break;
				case "风":
					num += 1000003000 + num2;
					break;
				case "内":
					num += 1000004000 + num2;
					break;
				case "外":
					num += 1000005000 + num2;
					break;
				case "毒":
					num += 1000006000 + num2;
					break;
				}
			}
		}
		catch
		{
			return num;
		}
		return num;
	}

	private int 属性选择(string txt, int sx)
	{
		try
		{
			if (txt != null)
			{
				switch (txt.Length)
				{
				case 7:
					switch (txt[2])
					{
					case '药':
						if (!(txt == "精九药品恢复量"))
						{
							if (!(txt == "精蓝药品恢复量"))
							{
								break;
							}
							return 1010000000 + sx;
						}
						return 1000000000 + sx;
					case '防':
						if (!(txt == "武功防御力增加"))
						{
							break;
						}
						return 110000000 + sx;
					case '金':
						if (!(txt == "获得金钱%增加"))
						{
							break;
						}
						return 120000000 + sx;
					case '命':
						if (!(txt == "武功命中%增加"))
						{
							break;
						}
						return 190000000 + sx;
					case '回':
						if (!(txt == "武功回避%增加"))
						{
							break;
						}
						return 200000000 + sx;
					}
					break;
				case 6:
					switch (txt[1])
					{
					case '九':
						if (!(txt == "精九追加生命"))
						{
							break;
						}
						return 1020000000 + sx;
					case '蓝':
						if (!(txt == "精蓝追加内力"))
						{
							break;
						}
						return 1030000000 + sx;
					case '验':
						if (!(txt == "经验值增加%"))
						{
							break;
						}
						return 150000000 + sx;
					}
					break;
				case 5:
					switch (txt[0])
					{
					case '无':
						if (!(txt == "无双恢复量"))
						{
							break;
						}
						return 1110000000 + sx;
					case '命':
						if (!(txt == "命中率增加"))
						{
							break;
						}
						return 50000000 + sx;
					case '回':
						if (!(txt == "回避率增加"))
						{
							break;
						}
						return 60000000 + sx;
					case '追':
						if (!(txt == "追加伤害值"))
						{
							break;
						}
						return 100000000 + sx;
					}
					break;
				case 4:
					switch (txt[0])
					{
					case '攻':
						if (!(txt == "攻击增加"))
						{
							break;
						}
						return 10000000 + sx;
					case '防':
						if (!(txt == "防御增加"))
						{
							break;
						}
						return 20000000 + sx;
					case '生':
						if (!(txt == "生命增加"))
						{
							break;
						}
						return 30000000 + sx;
					case '内':
						if (!(txt == "内功增加"))
						{
							break;
						}
						return 40000000 + sx;
					}
					break;
				case 8:
					switch (txt[0])
					{
					case '武':
						if (!(txt == "武功攻击力%增加"))
						{
							break;
						}
						return 70000000 + sx;
					case '全':
						if (!(txt == "全部气功等级增加"))
						{
							break;
						}
						return 80000000 + sx;
					case '升':
						if (!(txt == "升级成功率%增加"))
						{
							break;
						}
						return 90000000 + sx;
					}
					break;
				case 3:
					if (!(txt == "耐久度"))
					{
						break;
					}
					return 2000000000 + sx;
				case 9:
					if (!(txt == "死亡损失经验减少%"))
					{
						break;
					}
					return 130000000 + sx;
				}
			}
			return 0;
		}
		catch
		{
			return 0;
		}
	}

	private int 石头属性选择(string txt, int sx)
	{
		try
		{
			if (txt != null)
			{
				switch (txt)
				{
				case "攻击增加":
					return 100000 + sx;
				case "防御增加":
					return 200000 + sx;
                case "武功防御增加":
					return 110000000 + sx;
				case "生命增加":
					return 300000 + sx;
				case "内功增加":
					return 400000 + sx;
				case "命中率增加":
					return 500000 + sx;
				case "回避率增加":
					return 600000 + sx;
				case "追加伤害值":
					return 1000000 + sx;
				case "武功攻击力%增加":
					return 700000 + sx;
				case "全部气功等级增加":
					return 800000 + sx;
				case "升级成功率%增加":
					return 900000 + sx;
				case "武功防御力增加":
					return 1100000 + sx;
				case "获得金钱%增加":
					return 1200000 + sx;
				case "火":
					return 2001000;
				case "水":
					return 2002000;
				case "风":
					return 2003000;
				case "内":
					return 2004000;
				case "外":
					return 2005000;
				case "毒":
					return 2006000;
				case "死亡损失经验减少%":
					return 1300000 + sx;
				case "经验值增加%":
					return 1500000 + sx;
				}
			}
			return 0;
		}
		catch
		{
			return 0;
		}
	}

	private void comboBoxST_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			textBoxSTDM.Text = 石头属性选择(comboBoxST.SelectedItem.ToString(), int.Parse(textBoxSTSX.Text)).ToString();
		}
		catch (Exception)
		{
		}
	}

    private void textBoxSTSX_TextChanged(object sender, EventArgs e)
    {
        if (comboBox2.SelectedItem != null && comboBox2.SelectedItem.ToString() == "职业通用"
            && comboBox3.SelectedItem != null)
        {
            int stsx;
            if (int.TryParse(textBoxSTSX.Text, out stsx))
            {
                switch (comboBox3.SelectedItem.ToString())
                {
                    case "全部气功":
                        textBoxSTDM.Text = (800000 + stsx).ToString();
                        break;
                    case "升级概率%":
                        textBoxSTDM.Text = (900000 + stsx).ToString();
                        break;
                    case "金钱增加%":
                        textBoxSTDM.Text = (1200000 + stsx).ToString();
                        break;
                    case "经验值增加%":
                        textBoxSTDM.Text = (1500000 + stsx).ToString();
                        break;
                    case "死亡经验减少%":
                        textBoxSTDM.Text = (1300000 + stsx).ToString();
                        break;
                   
                }
            }
            else
            {
                textBoxSTDM.Text = "0";
            }
        }
        else
        {
            try
            {
                if (comboBoxST.SelectedItem != null && !string.IsNullOrWhiteSpace(textBoxSTSX.Text))
                {
                    textBoxSTDM.Text = 石头属性选择(comboBoxST.SelectedItem.ToString(), int.Parse(textBoxSTSX.Text)).ToString();
                }
                else
                {
                    textBoxSTDM.Text = "0";
                }
            }
            catch (Exception)
            {
                textBoxSTDM.Text = "0";
            }
        }
    }

    private void buttonITEMSelect_Click(object sender, EventArgs e)
	{
		ItemSel itemSel = new ItemSel();
		itemSel.PID = textBoxPID;
		itemSel.NAME = null;
		itemSel.Show();
	}

	private void buttonClearItem_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.清理背包();
                tishi.Text = "清理背包完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias2" + ex.StackTrace);
		}
	}

	private void buttonClearZBItem_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				for (int i = 0; i < players.装备栏已穿装备.Length; i++)
				{
					players.装备栏已穿装备[i].物品_byte = new byte[60];
				}
				players.更新人物数据(players);
				players.更新广播人物数据();
				players.更新装备效果();
				players.计算人物装备数据();
				players.更新武功和状态();
				players.更新金钱和负重();
				players.更新HP_MP_SP();
				players.保存人物的数据();
				tishi.Text = "清理已穿装备完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias7" + ex.StackTrace);
		}
	}

	private void buttonCleraQItem_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				for (int i = 0; i < players.任务物品.Length; i++)
				{
					players.任务物品[i].物品_byte = new byte[60];
				}
				players.更新人物数据(players);
				players.保存人物的数据();
				players.发送任务物品列表();
				tishi.Text = "清理人物物品完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias10" + ex.StackTrace);
		}
	}

	private void buttonClearQData_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.任务.Clear();
				players.更新人物数据(players);
				players.保存人物的数据();
				players.更新人物任务();
				tishi.Text = "清理任务完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias8" + ex.StackTrace);
		}
	}

	private void buttonClearZTData_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players == null)
			{
				return;
			}
			if (players.追加状态列表 != null)
			{
				foreach (追加状态类 value in players.追加状态列表.Values)
				{
					if (value.FLD_PID != 1008001043 && value.FLD_PID != 1008001042)
					{
						value.时间结束事件();
					}
				}
			}
			if (players.追加状态New列表 != null)
			{
				players.清空追加状态New列表();
			}
			players.保存人物的数据();
			players.更新人物数据(players);
			players.更新广播人物数据();
			players.更新气功();
			players.更新装备效果();
			players.计算人物装备数据();
			players.更新武功和状态();
			players.更新金钱和负重();
			players.更新HP_MP_SP();
			players.清空辅助状态();
			tishi.Text = "清理状态完成";
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias9" + ex.StackTrace);
		}
	}

	private void buttonClearKongFu_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.武功新 = new 武功类[5, 32];
				players.保存人物的数据();
				players.更新人物数据(players);
				players.更新广播人物数据();
				players.更新装备效果();
				players.计算人物装备数据();
				players.更新武功和状态();
				players.更新金钱和负重();
				players.更新HP_MP_SP();
				players.清空辅助状态();
				tishi.Text = "清理武功完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias11" + ex.StackTrace);
		}
	}

	private void buttonClearQiGong_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.气功Clear();
				players.升天气功.Clear();
                players.反气功Clear(); //24.0 EVIAS  新增反气功 
                players.更新气功();
				players.保存人物的数据();
				players.更新人物数据(players);
				players.更新广播人物数据();
				players.更新装备效果();
				players.计算人物装备数据();
				players.更新武功和状态();
				players.更新金钱和负重();
				players.更新HP_MP_SP();
				players.清空辅助状态();
				tishi.Text = "清理气功完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "button789" + ex.Message);
		}
	}

	private void buttonClearPublicItem_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.公有药品.Clear();
				players.保存人物的数据();
				players.更新人物数据(players);
				players.更新广播人物数据();
				players.更新装备效果();
				players.计算人物装备数据();
				players.更新武功和状态();
				players.更新金钱和负重();
				players.更新HP_MP_SP();
				players.清空辅助状态();
                tishi.Text = "清理药品完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias12" + ex.StackTrace);
		}
	}

	private void listBoxAllPlayer_SelectedIndexChanged(object sender, EventArgs e)
	{
		ListBoxItem listBoxItem = (ListBoxItem)listBoxAllPlayer.SelectedItem;
		textBoxName.Text = listBoxItem.ItemText;
		if (textBoxName.Text.Length > 0)
		{
			listBoxAllPlayer.SelectedIndex = listBoxAllPlayer.FindStringExact(textBoxName.Text, 0);
			查看玩家信息();
		}
	}

	private void buttonSendPack_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null || players.Client == null)
		{
			return;
		}
		byte[] array = Converter.hexStringToByte2(textBoxPack.Text);
		if (array.Length > 10)
		{
			if (checkBox1.Checked)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 4, 2);
			}
			players.Client.Send(array, array.Length);
		}
	}

	private void buttonSeePackItem_Click(object sender, EventArgs e)
	{
		序列号 = 1;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.装备栏包裹.Length; i++)
		{
			物品类 物品类2 = players.装备栏包裹[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias13" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void buttonSeeZBItem_Click(object sender, EventArgs e)
	{
		序列号 = 8;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.装备栏已穿装备.Length; i++)
		{
			物品类 物品类2 = players.装备栏已穿装备[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias14" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
        {
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            World.检查玩家name(textBoxName.Text)?.横幅公告(textBoxPack.Text);
		}
		catch (Exception ex)
		{
			RxjhClass.HandleGameException(ex, null, "横幅公告", $"玩家: {textBoxName.Text}, 内容: {textBoxPack.Text}");
		}
	}

	private void buttonShuaXin_Click(object sender, EventArgs e)
	{
		listBoxAllPlayer.Items.Clear();
		foreach (Players value in World.allConnectedChars.Values)
		{
			ListBoxItem item = new ListBoxItem(value.UserName, value.人物全服ID);
			listBoxAllPlayer.Items.Add(item);
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null || players.Client == null)
		{
			return;
		}
		byte[] array = Converter.hexStringToByte2(textBoxPack.Text);
		if (array.Length > 10)
		{
			if (checkBox1.Checked)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(players.人物全服ID), 0, array, 0, 2);
			}
			players.Client.Send单包封装发送(array, array.Length);
		}
	}

	private void textBoxName_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Return && textBoxName.Text.Length > 0)
		{
			listBoxAllPlayer.SelectedIndex = listBoxAllPlayer.FindStringExact(textBoxName.Text, 0);
			查看玩家信息();
		}
	}

    private void buttonPlayerEdit_Click(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
            if (players != null)
            {
                players.查百宝阁元宝数();
                if (comboBoxPlayerSXSelect.Text != null)
                {
                    switch (comboBoxPlayerSXSelect.Text)
                    {
                        case "人物元宝":
                            players.FLD_RXPIONT = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "人物钻石":
                            players.FLD_RXPIONTX = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "人物等级":
                            players.Player_Level = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "人物转职":
                            players.Player_Job_leve = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "人物经验":
                            players.人物经验 = long.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "人物武勋":
                            players.Player_WuXun = int.Parse(textBoxPlayerSXValue.Text); 
                            break;
                        case "人物历练":
                            players.Player_ExpErience = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "善恶值":
                            players.人物善恶 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "游戏币":
                            players.Player_Money = long.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "升天武功点":
                            players.升天武功点数 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "神女神力点":
                            players.神女武功点数 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "转生次数":
                            players.转生次数 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "转生攻击":
                            players.转生_追加_攻击 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "转生防御":
                            players.转生_追加_防御 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "转生生命":
                            players.转生_追加_生命 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "个人暴率":
                            players.角色暴率 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "杀人次数":
                            players.杀人次数 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "被杀次数":
                            players.被杀次数 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "宝石称号":
                            players.称号积分 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "玫瑰称号":
                            players.玫瑰称号积分 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "累计充值":
                            players.累计充值 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "抽奖次数":
                            players.FLD_账号累充 = int.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "个人仓库金钱":
                            players.个人仓库钱数 = long.Parse(textBoxPlayerSXValue.Text);
                            break;
                        case "综合仓库金钱":
                            players.综合仓库钱数 = long.Parse(textBoxPlayerSXValue.Text);
                            break;
                    }
                }
                players.保存人物的数据();
                players.更新金钱和负重();
                players.更新经验和历练();
                players.更新武功和状态();
                players.更新人物数据(players);
                players.更新HP_MP_SP();
                tishi.Text = "修改人物数据成功";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("修改人物数据修改出错：" + ex.StackTrace);
        }
    }

    private void buttonPlayerAdd_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus(); 
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players == null)
			{
				return;
			}
			if (comboBoxPlayerSXSelect.Text != null)
			{
				switch (comboBoxPlayerSXSelect.Text)
				{
				case "人物元宝":
					players.检察元宝数据(int.Parse(textBoxPlayerSXValue.Text), 1, "系统后台");
					break;
				case "人物钻石":
					players.检察钻石数据(int.Parse(textBoxPlayerSXValue.Text), 1, "系统后台");
					break;
				case "人物等级":
					players.Player_Level += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物转职":
					players.Player_Job_leve += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物经验":
					players.人物经验 += long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物武勋":
					players.武勋加减(int.Parse(textBoxPlayerSXValue.Text), 1, "系统后台");
					break;
				case "人物历练":
					players.Player_ExpErience += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "善恶值":
					players.人物善恶 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "游戏币":
					players.Player_Money += long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "升天武功点":
					players.升天武功点数 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "神女神力点":
					players.神女武功点数 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生次数":
					players.转生次数 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生攻击":
					players.转生_追加_攻击 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生防御":
					players.转生_追加_防御 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生生命":
					players.转生_追加_生命 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "个人暴率":
					players.角色暴率 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "杀人次数":
					players.杀人次数 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "被杀次数":
					players.被杀次数 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "宝石称号":
					players.称号积分 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "玫瑰称号":
					players.玫瑰称号积分 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "累计充值":
					players.累计充值 += int.Parse(textBoxPlayerSXValue.Text);
					break;
                case "抽奖次数":
					players.FLD_账号累充 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "个人仓库金钱":
					players.个人仓库钱数 += long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "综合仓库金钱":
					players.综合仓库钱数 += long.Parse(textBoxPlayerSXValue.Text);
					break;
				}
			}
			players.保存人物的数据();
			players.更新金钱和负重();
			players.更新经验和历练();
			players.更新武功和状态();
			players.更新人物数据(players);
			players.更新HP_MP_SP();
			tishi.Text = "增加人物数据成功";
        }
		catch (Exception ex)
		{
			MessageBox.Show("增加人物数据修改出错：" + ex.StackTrace);
		}
	}

	private void buttonPlayerSub_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus(); 
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players == null)
			{
				return;
			}
			if (comboBoxPlayerSXSelect.Text != null)
			{
				switch (comboBoxPlayerSXSelect.Text)
				{
				case "人物元宝":
					players.检察元宝数据(int.Parse(textBoxPlayerSXValue.Text), 0, "系统后台");
					break;
				case "人物钻石":
					players.检察钻石数据(int.Parse(textBoxPlayerSXValue.Text), 0, "系统后台");
					break;
				case "人物等级":
					players.Player_Level -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物转职":
					players.Player_Job_leve -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物经验":
					players.人物经验 -= long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "人物武勋":
					players.武勋加减(int.Parse(textBoxPlayerSXValue.Text), 0, "系统后台");
					break;
				case "人物历练":
					players.Player_ExpErience -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "善恶值":
					players.人物善恶 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "游戏币":
					players.Player_Money -= long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "升天武功点":
					players.升天武功点数 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "神女神力点":
					players.神女武功点数 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生次数":
					players.转生次数 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生攻击":
					players.转生_追加_攻击 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生防御":
					players.转生_追加_防御 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "转生生命":
					players.转生_追加_生命 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "个人暴率":
					players.角色暴率 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "杀人次数":
					players.杀人次数 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "被杀次数":
					players.被杀次数 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "宝石称号":
					players.称号积分 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "玫瑰称号":
					players.玫瑰称号积分 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "累计充值":
					players.累计充值 -= int.Parse(textBoxPlayerSXValue.Text);
					break;
                case "抽奖次数":
					players.FLD_账号累充 += int.Parse(textBoxPlayerSXValue.Text);
					break;
				case "个人仓库金钱":
					players.个人仓库钱数 -= long.Parse(textBoxPlayerSXValue.Text);
					break;
				case "综合仓库金钱":
					players.综合仓库钱数 -= long.Parse(textBoxPlayerSXValue.Text);
					break;
				}
			}
			players.保存人物的数据();
			players.更新金钱和负重();
			players.更新经验和历练();
			players.更新武功和状态();
			players.更新人物数据(players);
			players.更新HP_MP_SP();
			tishi.Text = "减少人物数据成功";

        }
		catch (Exception ex)
		{
			MessageBox.Show("减少人物数据修改出错：" + ex.StackTrace);
		}
	}

	private void button3_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				int 人物全服ID = int.Parse(textBox2.Text);
				int 异常ID = int.Parse(textBox1.Text);
				World.异常状态效果(players, 人物全服ID, 异常ID, 1, 1, 10000);
				tishi.Text = "增加异常数据成功";
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show("增加异常数据修改出错：" + ex.StackTrace);
		}
	}

    private void button4_Click(object sender, EventArgs e) //EVIAS
    {
        string text = textBoxKey.Text;
        if (text.Length <= 0)
        {
            return;
        }

        listBoxAllPlayer.Items.Clear();
        bool found = false;  

        foreach (Players value in World.allConnectedChars.Values)
        {
            if (value.UserName.IndexOf(text) != -1)
            {
                ListBoxItem item = new ListBoxItem(value.UserName, value.人物全服ID);
                listBoxAllPlayer.Items.Add(item);
                found = true;  
            }
        }
        if (!found)
        {
            tishi.Text = $"没有找到名称包含【{textBoxKey.Text}】的在线玩家";
        }
    }

    private void button5_Click(object sender, EventArgs e) //EVIAS
    {
        string text = textBoxKey.Text;
        if (text.Length <= 0)
        {
            return;
        }

        listBoxAllPlayer.Items.Clear();
        bool found = false;  

        foreach (Players value in World.allConnectedChars.Values)
        {
            if (value.Userid.IndexOf(text) != -1)
            {
                ListBoxItem item = new ListBoxItem(value.UserName, value.人物全服ID);
                listBoxAllPlayer.Items.Add(item);
                found = true;  
            }
        }

        if (!found)
        {
            tishi.Text = $"没有找到包含【{textBoxKey.Text}】账号的在线玩家";
        }
    }

    protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Evias));
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.button20 = new System.Windows.Forms.Button();
            this.button19 = new System.Windows.Forms.Button();
            this.button18 = new System.Windows.Forms.Button();
            this.button17 = new System.Windows.Forms.Button();
            this.button16 = new System.Windows.Forms.Button();
            this.button15 = new System.Windows.Forms.Button();
            this.buttonClearPublicItem = new System.Windows.Forms.Button();
            this.buttonClearQiGong = new System.Windows.Forms.Button();
            this.buttonClearKongFu = new System.Windows.Forms.Button();
            this.buttonClearZTData = new System.Windows.Forms.Button();
            this.buttonClearQData = new System.Windows.Forms.Button();
            this.buttonCleraQItem = new System.Windows.Forms.Button();
            this.buttonClearZBItem = new System.Windows.Forms.Button();
            this.buttonClearItem = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label58 = new System.Windows.Forms.Label();
            this.textBox24 = new System.Windows.Forms.TextBox();
            this.textBox22 = new System.Windows.Forms.TextBox();
            this.label55 = new System.Windows.Forms.Label();
            this.label54 = new System.Windows.Forms.Label();
            this.textBox21 = new System.Windows.Forms.TextBox();
            this.label53 = new System.Windows.Forms.Label();
            this.label52 = new System.Windows.Forms.Label();
            this.label51 = new System.Windows.Forms.Label();
            this.label50 = new System.Windows.Forms.Label();
            this.textBox20 = new System.Windows.Forms.TextBox();
            this.textBox19 = new System.Windows.Forms.TextBox();
            this.textBox18 = new System.Windows.Forms.TextBox();
            this.textBox17 = new System.Windows.Forms.TextBox();
            this.textBox16 = new System.Windows.Forms.TextBox();
            this.label49 = new System.Windows.Forms.Label();
            this.textBox15 = new System.Windows.Forms.TextBox();
            this.textBox14 = new System.Windows.Forms.TextBox();
            this.label48 = new System.Windows.Forms.Label();
            this.label47 = new System.Windows.Forms.Label();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.textBox12 = new System.Windows.Forms.TextBox();
            this.label46 = new System.Windows.Forms.Label();
            this.label45 = new System.Windows.Forms.Label();
            this.textBox10 = new System.Windows.Forms.TextBox();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label43 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.label38 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.textBoxShenLi = new System.Windows.Forms.TextBox();
            this.buttonPlayerSub = new System.Windows.Forms.Button();
            this.buttonPlayerAdd = new System.Windows.Forms.Button();
            this.buttonPlayerEdit = new System.Windows.Forms.Button();
            this.textBoxPlayerSXValue = new System.Windows.Forms.TextBox();
            this.label32 = new System.Windows.Forms.Label();
            this.comboBoxPlayerSXSelect = new System.Windows.Forms.ComboBox();
            this.label30 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.textBoxSE = new System.Windows.Forms.TextBox();
            this.textBoxPlayerSTWG = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBoxPlayerY = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxPlayerX = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBoxPlayerMap = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.textBoxPlayerLL = new System.Windows.Forms.TextBox();
            this.textBoxPlayerWuXun = new System.Windows.Forms.TextBox();
            this.textBoxPlayerEXP = new System.Windows.Forms.TextBox();
            this.textBoxPlayerMoney = new System.Windows.Forms.TextBox();
            this.textBoxPlayerJobLevel = new System.Windows.Forms.TextBox();
            this.textBoxPlayerJob = new System.Windows.Forms.TextBox();
            this.textBoxPlayerLevel = new System.Windows.Forms.TextBox();
            this.textBoxRXPIONT = new System.Windows.Forms.TextBox();
            this.textBoxUserID = new System.Windows.Forms.TextBox();
            this.textBoxWorldID = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label56 = new System.Windows.Forms.Label();
            this.button41 = new System.Windows.Forms.Button();
            this.textBox23 = new System.Windows.Forms.TextBox();
            this.comboBox3 = new System.Windows.Forms.ComboBox();
            this.comboBox2 = new System.Windows.Forms.ComboBox();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label57 = new System.Windows.Forms.Label();
            this.buttonITEMSelect = new System.Windows.Forms.Button();
            this.textBoxSTSX = new System.Windows.Forms.TextBox();
            this.comboBoxST = new System.Windows.Forms.ComboBox();
            this.textBoxSTDM = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.textBoxJH = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.textBoxTS = new System.Windows.Forms.TextBox();
            this.checkBoxBD = new System.Windows.Forms.CheckBox();
            this.label15 = new System.Windows.Forms.Label();
            this.textBoxZHH = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.textBoxCHH = new System.Windows.Forms.TextBox();
            this.textBoxFJ0 = new System.Windows.Forms.TextBox();
            this.comboBoxFJSX = new System.Windows.Forms.ComboBox();
            this.comboBoxFJQH = new System.Windows.Forms.ComboBox();
            this.textBoxSX4 = new System.Windows.Forms.TextBox();
            this.textBoxSX3 = new System.Windows.Forms.TextBox();
            this.textBoxSX2 = new System.Windows.Forms.TextBox();
            this.textBoxSX1 = new System.Windows.Forms.TextBox();
            this.comboBoxSX3 = new System.Windows.Forms.ComboBox();
            this.comboBoxSX2 = new System.Windows.Forms.ComboBox();
            this.comboBoxSX1 = new System.Windows.Forms.ComboBox();
            this.textBoxFJ1 = new System.Windows.Forms.TextBox();
            this.comboBoxSX4 = new System.Windows.Forms.ComboBox();
            this.textBoxPID = new System.Windows.Forms.TextBox();
            this.label40 = new System.Windows.Forms.Label();
            this.buttonSendItem = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.textBoxNUM = new System.Windows.Forms.TextBox();
            this.listBoxALLITEM = new System.Windows.Forms.ListBox();
            this.comboBoxITEMTYPE = new System.Windows.Forms.ComboBox();
            this.label14 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.textBoxSXFJDM = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.textBoxSXDM1 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.textBoxSXDM2 = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.textBoxSXDM3 = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.textBoxSXDM4 = new System.Windows.Forms.TextBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.button21 = new System.Windows.Forms.Button();
            this.button14 = new System.Windows.Forms.Button();
            this.button13 = new System.Windows.Forms.Button();
            this.button12 = new System.Windows.Forms.Button();
            this.button11 = new System.Windows.Forms.Button();
            this.button10 = new System.Windows.Forms.Button();
            this.button9 = new System.Windows.Forms.Button();
            this.button7 = new System.Windows.Forms.Button();
            this.button6 = new System.Windows.Forms.Button();
            this.buttonSeeZBItem = new System.Windows.Forms.Button();
            this.listViewItem1 = new System.Windows.Forms.ListView();
            this.columnHeader_0 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader_6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.buttonSeePackItem = new System.Windows.Forms.Button();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.button8 = new System.Windows.Forms.Button();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.label44 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label33 = new System.Windows.Forms.Label();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.textBoxPack = new System.Windows.Forms.TextBox();
            this.label29 = new System.Windows.Forms.Label();
            this.buttonSendPack = new System.Windows.Forms.Button();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.删除数据 = new System.Windows.Forms.ToolStripMenuItem();
            this.绑定物品 = new System.Windows.Forms.ToolStripMenuItem();
            this.解绑物品 = new System.Windows.Forms.ToolStripMenuItem();
            this.listBoxAllPlayer = new System.Windows.Forms.ListBox();
            this.buttonShuaXin = new System.Windows.Forms.Button();
            this.textBoxKey = new System.Windows.Forms.TextBox();
            this.label34 = new System.Windows.Forms.Label();
            this.button4 = new System.Windows.Forms.Button();
            this.button5 = new System.Windows.Forms.Button();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.tishi = new System.Windows.Forms.ToolStripStatusLabel();
            this.tabControl1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // textBoxName
            // 
            this.textBoxName.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxName.Location = new System.Drawing.Point(15, 92);
            this.textBoxName.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.Size = new System.Drawing.Size(253, 31);
            this.textBoxName.TabIndex = 53;
            this.textBoxName.KeyDown += new System.Windows.Forms.KeyEventHandler(this.textBoxName_KeyDown);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabControl1.Location = new System.Drawing.Point(282, 8);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(4);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1118, 702);
            this.tabControl1.TabIndex = 63;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox2);
            this.tabPage2.Controls.Add(this.groupBox3);
            this.tabPage2.Location = new System.Drawing.Point(4, 33);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage2.Size = new System.Drawing.Size(1110, 665);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "属性修改";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.button20);
            this.groupBox2.Controls.Add(this.button19);
            this.groupBox2.Controls.Add(this.button18);
            this.groupBox2.Controls.Add(this.button17);
            this.groupBox2.Controls.Add(this.button16);
            this.groupBox2.Controls.Add(this.button15);
            this.groupBox2.Controls.Add(this.buttonClearPublicItem);
            this.groupBox2.Controls.Add(this.buttonClearQiGong);
            this.groupBox2.Controls.Add(this.buttonClearKongFu);
            this.groupBox2.Controls.Add(this.buttonClearZTData);
            this.groupBox2.Controls.Add(this.buttonClearQData);
            this.groupBox2.Controls.Add(this.buttonCleraQItem);
            this.groupBox2.Controls.Add(this.buttonClearZBItem);
            this.groupBox2.Controls.Add(this.buttonClearItem);
            this.groupBox2.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBox2.Location = new System.Drawing.Point(914, 7);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox2.Size = new System.Drawing.Size(181, 656);
            this.groupBox2.TabIndex = 28;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "清理操作";
            // 
            // button20
            // 
            this.button20.Location = new System.Drawing.Point(14, 617);
            this.button20.Margin = new System.Windows.Forms.Padding(4);
            this.button20.Name = "button20";
            this.button20.Size = new System.Drawing.Size(156, 34);
            this.button20.TabIndex = 88;
            this.button20.Text = "解冻装备(全部)";
            this.button20.UseVisualStyleBackColor = true;
            this.button20.Click += new System.EventHandler(this.button20_Click);
            // 
            // button19
            // 
            this.button19.Location = new System.Drawing.Point(14, 572);
            this.button19.Margin = new System.Windows.Forms.Padding(4);
            this.button19.Name = "button19";
            this.button19.Size = new System.Drawing.Size(156, 34);
            this.button19.TabIndex = 87;
            this.button19.Text = "解锁装备(自己)";
            this.button19.UseVisualStyleBackColor = true;
            this.button19.Click += new System.EventHandler(this.button19_Click);
            // 
            // button18
            // 
            this.button18.Location = new System.Drawing.Point(14, 527);
            this.button18.Margin = new System.Windows.Forms.Padding(4);
            this.button18.Name = "button18";
            this.button18.Size = new System.Drawing.Size(156, 34);
            this.button18.TabIndex = 86;
            this.button18.Text = "清理英雄武器";
            this.button18.UseVisualStyleBackColor = true;
            this.button18.Click += new System.EventHandler(this.button18_Click);
            // 
            // button17
            // 
            this.button17.Location = new System.Drawing.Point(14, 482);
            this.button17.Margin = new System.Windows.Forms.Padding(4);
            this.button17.Name = "button17";
            this.button17.Size = new System.Drawing.Size(156, 34);
            this.button17.TabIndex = 85;
            this.button17.Text = "清理锁定背包";
            this.button17.UseVisualStyleBackColor = true;
            this.button17.Click += new System.EventHandler(this.button17_Click);
            // 
            // button16
            // 
            this.button16.Location = new System.Drawing.Point(14, 437);
            this.button16.Margin = new System.Windows.Forms.Padding(4);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(156, 34);
            this.button16.TabIndex = 84;
            this.button16.Text = "清理绑定背包";
            this.button16.UseVisualStyleBackColor = true;
            this.button16.Click += new System.EventHandler(this.button16_Click);
            // 
            // button15
            // 
            this.button15.Location = new System.Drawing.Point(14, 392);
            this.button15.Margin = new System.Windows.Forms.Padding(4);
            this.button15.Name = "button15";
            this.button15.Size = new System.Drawing.Size(156, 34);
            this.button15.TabIndex = 83;
            this.button15.Text = "清理行囊";
            this.button15.UseVisualStyleBackColor = true;
            this.button15.Click += new System.EventHandler(this.button15_Click);
            // 
            // buttonClearPublicItem
            // 
            this.buttonClearPublicItem.Location = new System.Drawing.Point(14, 302);
            this.buttonClearPublicItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearPublicItem.Name = "buttonClearPublicItem";
            this.buttonClearPublicItem.Size = new System.Drawing.Size(156, 34);
            this.buttonClearPublicItem.TabIndex = 82;
            this.buttonClearPublicItem.Text = "清空公有物品";
            this.buttonClearPublicItem.UseVisualStyleBackColor = true;
            this.buttonClearPublicItem.Click += new System.EventHandler(this.buttonClearPublicItem_Click);
            // 
            // buttonClearQiGong
            // 
            this.buttonClearQiGong.Location = new System.Drawing.Point(14, 347);
            this.buttonClearQiGong.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearQiGong.Name = "buttonClearQiGong";
            this.buttonClearQiGong.Size = new System.Drawing.Size(156, 34);
            this.buttonClearQiGong.TabIndex = 81;
            this.buttonClearQiGong.Text = "清空气功数据";
            this.buttonClearQiGong.UseVisualStyleBackColor = true;
            this.buttonClearQiGong.Click += new System.EventHandler(this.buttonClearQiGong_Click);
            // 
            // buttonClearKongFu
            // 
            this.buttonClearKongFu.Location = new System.Drawing.Point(14, 257);
            this.buttonClearKongFu.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearKongFu.Name = "buttonClearKongFu";
            this.buttonClearKongFu.Size = new System.Drawing.Size(156, 34);
            this.buttonClearKongFu.TabIndex = 80;
            this.buttonClearKongFu.Text = "清空技能数据";
            this.buttonClearKongFu.UseVisualStyleBackColor = true;
            this.buttonClearKongFu.Click += new System.EventHandler(this.buttonClearKongFu_Click);
            // 
            // buttonClearZTData
            // 
            this.buttonClearZTData.Location = new System.Drawing.Point(14, 212);
            this.buttonClearZTData.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearZTData.Name = "buttonClearZTData";
            this.buttonClearZTData.Size = new System.Drawing.Size(156, 34);
            this.buttonClearZTData.TabIndex = 79;
            this.buttonClearZTData.Text = "清空状态数据";
            this.buttonClearZTData.UseVisualStyleBackColor = true;
            this.buttonClearZTData.Click += new System.EventHandler(this.buttonClearZTData_Click);
            // 
            // buttonClearQData
            // 
            this.buttonClearQData.Location = new System.Drawing.Point(14, 167);
            this.buttonClearQData.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearQData.Name = "buttonClearQData";
            this.buttonClearQData.Size = new System.Drawing.Size(156, 34);
            this.buttonClearQData.TabIndex = 78;
            this.buttonClearQData.Text = "清空任务数据";
            this.buttonClearQData.UseVisualStyleBackColor = true;
            this.buttonClearQData.Click += new System.EventHandler(this.buttonClearQData_Click);
            // 
            // buttonCleraQItem
            // 
            this.buttonCleraQItem.Location = new System.Drawing.Point(14, 122);
            this.buttonCleraQItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonCleraQItem.Name = "buttonCleraQItem";
            this.buttonCleraQItem.Size = new System.Drawing.Size(156, 34);
            this.buttonCleraQItem.TabIndex = 77;
            this.buttonCleraQItem.Text = "清空任务物品";
            this.buttonCleraQItem.UseVisualStyleBackColor = true;
            this.buttonCleraQItem.Click += new System.EventHandler(this.buttonCleraQItem_Click);
            // 
            // buttonClearZBItem
            // 
            this.buttonClearZBItem.Location = new System.Drawing.Point(14, 77);
            this.buttonClearZBItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearZBItem.Name = "buttonClearZBItem";
            this.buttonClearZBItem.Size = new System.Drawing.Size(156, 34);
            this.buttonClearZBItem.TabIndex = 76;
            this.buttonClearZBItem.Text = "清空已装备物品";
            this.buttonClearZBItem.UseVisualStyleBackColor = true;
            this.buttonClearZBItem.Click += new System.EventHandler(this.buttonClearZBItem_Click);
            // 
            // buttonClearItem
            // 
            this.buttonClearItem.Location = new System.Drawing.Point(14, 32);
            this.buttonClearItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonClearItem.Name = "buttonClearItem";
            this.buttonClearItem.Size = new System.Drawing.Size(156, 34);
            this.buttonClearItem.TabIndex = 75;
            this.buttonClearItem.Text = "清理背包";
            this.buttonClearItem.UseVisualStyleBackColor = true;
            this.buttonClearItem.Click += new System.EventHandler(this.buttonClearItem_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(245)))), ((int)(((byte)(245)))), ((int)(((byte)(245)))));
            this.groupBox3.Controls.Add(this.label58);
            this.groupBox3.Controls.Add(this.textBox24);
            this.groupBox3.Controls.Add(this.textBox22);
            this.groupBox3.Controls.Add(this.label55);
            this.groupBox3.Controls.Add(this.label54);
            this.groupBox3.Controls.Add(this.textBox21);
            this.groupBox3.Controls.Add(this.label53);
            this.groupBox3.Controls.Add(this.label52);
            this.groupBox3.Controls.Add(this.label51);
            this.groupBox3.Controls.Add(this.label50);
            this.groupBox3.Controls.Add(this.textBox20);
            this.groupBox3.Controls.Add(this.textBox19);
            this.groupBox3.Controls.Add(this.textBox18);
            this.groupBox3.Controls.Add(this.textBox17);
            this.groupBox3.Controls.Add(this.textBox16);
            this.groupBox3.Controls.Add(this.label49);
            this.groupBox3.Controls.Add(this.textBox15);
            this.groupBox3.Controls.Add(this.textBox14);
            this.groupBox3.Controls.Add(this.label48);
            this.groupBox3.Controls.Add(this.label47);
            this.groupBox3.Controls.Add(this.textBox13);
            this.groupBox3.Controls.Add(this.textBox12);
            this.groupBox3.Controls.Add(this.label46);
            this.groupBox3.Controls.Add(this.label45);
            this.groupBox3.Controls.Add(this.textBox10);
            this.groupBox3.Controls.Add(this.textBox9);
            this.groupBox3.Controls.Add(this.textBox8);
            this.groupBox3.Controls.Add(this.textBox7);
            this.groupBox3.Controls.Add(this.textBox6);
            this.groupBox3.Controls.Add(this.textBox5);
            this.groupBox3.Controls.Add(this.textBox4);
            this.groupBox3.Controls.Add(this.label43);
            this.groupBox3.Controls.Add(this.label42);
            this.groupBox3.Controls.Add(this.label41);
            this.groupBox3.Controls.Add(this.label39);
            this.groupBox3.Controls.Add(this.label38);
            this.groupBox3.Controls.Add(this.label37);
            this.groupBox3.Controls.Add(this.label36);
            this.groupBox3.Controls.Add(this.textBox3);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.label35);
            this.groupBox3.Controls.Add(this.textBoxShenLi);
            this.groupBox3.Controls.Add(this.buttonPlayerSub);
            this.groupBox3.Controls.Add(this.buttonPlayerAdd);
            this.groupBox3.Controls.Add(this.buttonPlayerEdit);
            this.groupBox3.Controls.Add(this.textBoxPlayerSXValue);
            this.groupBox3.Controls.Add(this.label32);
            this.groupBox3.Controls.Add(this.comboBoxPlayerSXSelect);
            this.groupBox3.Controls.Add(this.label30);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.textBoxSE);
            this.groupBox3.Controls.Add(this.textBoxPlayerSTWG);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.textBoxPlayerY);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.textBoxPlayerX);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.textBoxPlayerMap);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.textBoxPlayerLL);
            this.groupBox3.Controls.Add(this.textBoxPlayerWuXun);
            this.groupBox3.Controls.Add(this.textBoxPlayerEXP);
            this.groupBox3.Controls.Add(this.textBoxPlayerMoney);
            this.groupBox3.Controls.Add(this.textBoxPlayerJobLevel);
            this.groupBox3.Controls.Add(this.textBoxPlayerJob);
            this.groupBox3.Controls.Add(this.textBoxPlayerLevel);
            this.groupBox3.Controls.Add(this.textBoxRXPIONT);
            this.groupBox3.Controls.Add(this.textBoxUserID);
            this.groupBox3.Controls.Add(this.textBoxWorldID);
            this.groupBox3.Controls.Add(this.label19);
            this.groupBox3.Controls.Add(this.label20);
            this.groupBox3.Controls.Add(this.label21);
            this.groupBox3.Controls.Add(this.label22);
            this.groupBox3.Controls.Add(this.label23);
            this.groupBox3.Controls.Add(this.label24);
            this.groupBox3.Controls.Add(this.label25);
            this.groupBox3.Controls.Add(this.label26);
            this.groupBox3.Controls.Add(this.label27);
            this.groupBox3.Controls.Add(this.label28);
            this.groupBox3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBox3.Location = new System.Drawing.Point(12, 12);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox3.Size = new System.Drawing.Size(890, 651);
            this.groupBox3.TabIndex = 27;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "人物属性";
            // 
            // label58
            // 
            this.label58.AutoSize = true;
            this.label58.Location = new System.Drawing.Point(619, 492);
            this.label58.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label58.Name = "label58";
            this.label58.Size = new System.Drawing.Size(86, 24);
            this.label58.TabIndex = 124;
            this.label58.Text = "抽奖次数:";
            // 
            // textBox24
            // 
            this.textBox24.Location = new System.Drawing.Point(716, 489);
            this.textBox24.Margin = new System.Windows.Forms.Padding(4);
            this.textBox24.Name = "textBox24";
            this.textBox24.Size = new System.Drawing.Size(150, 31);
            this.textBox24.TabIndex = 123;
            // 
            // textBox22
            // 
            this.textBox22.Location = new System.Drawing.Point(716, 445);
            this.textBox22.Margin = new System.Windows.Forms.Padding(4);
            this.textBox22.Name = "textBox22";
            this.textBox22.Size = new System.Drawing.Size(150, 31);
            this.textBox22.TabIndex = 122;
            // 
            // label55
            // 
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(637, 448);
            this.label55.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(68, 24);
            this.label55.TabIndex = 121;
            this.label55.Text = "验证码:";
            // 
            // label54
            // 
            this.label54.AutoSize = true;
            this.label54.Location = new System.Drawing.Point(619, 326);
            this.label54.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label54.Name = "label54";
            this.label54.Size = new System.Drawing.Size(86, 24);
            this.label54.TabIndex = 120;
            this.label54.Text = "累计充值:";
            // 
            // textBox21
            // 
            this.textBox21.Location = new System.Drawing.Point(716, 323);
            this.textBox21.Margin = new System.Windows.Forms.Padding(4);
            this.textBox21.Name = "textBox21";
            this.textBox21.Size = new System.Drawing.Size(150, 31);
            this.textBox21.TabIndex = 119;
            // 
            // label53
            // 
            this.label53.AutoSize = true;
            this.label53.Location = new System.Drawing.Point(619, 284);
            this.label53.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label53.Name = "label53";
            this.label53.Size = new System.Drawing.Size(86, 24);
            this.label53.TabIndex = 118;
            this.label53.Text = "临时概率:";
            // 
            // label52
            // 
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(619, 242);
            this.label52.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(86, 24);
            this.label52.TabIndex = 117;
            this.label52.Text = "临时伤害:";
            // 
            // label51
            // 
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(619, 120);
            this.label51.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(86, 24);
            this.label51.TabIndex = 116;
            this.label51.Text = "宝石称号:";
            // 
            // label50
            // 
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(619, 201);
            this.label50.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(86, 24);
            this.label50.TabIndex = 115;
            this.label50.Text = "共有钻石:";
            // 
            // textBox20
            // 
            this.textBox20.Location = new System.Drawing.Point(716, 281);
            this.textBox20.Margin = new System.Windows.Forms.Padding(4);
            this.textBox20.Name = "textBox20";
            this.textBox20.Size = new System.Drawing.Size(150, 31);
            this.textBox20.TabIndex = 114;
            // 
            // textBox19
            // 
            this.textBox19.Location = new System.Drawing.Point(716, 239);
            this.textBox19.Margin = new System.Windows.Forms.Padding(4);
            this.textBox19.Name = "textBox19";
            this.textBox19.Size = new System.Drawing.Size(150, 31);
            this.textBox19.TabIndex = 113;
            // 
            // textBox18
            // 
            this.textBox18.Location = new System.Drawing.Point(716, 198);
            this.textBox18.Margin = new System.Windows.Forms.Padding(4);
            this.textBox18.Name = "textBox18";
            this.textBox18.Size = new System.Drawing.Size(150, 31);
            this.textBox18.TabIndex = 112;
            // 
            // textBox17
            // 
            this.textBox17.Location = new System.Drawing.Point(716, 157);
            this.textBox17.Margin = new System.Windows.Forms.Padding(4);
            this.textBox17.Name = "textBox17";
            this.textBox17.Size = new System.Drawing.Size(150, 31);
            this.textBox17.TabIndex = 111;
            // 
            // textBox16
            // 
            this.textBox16.Location = new System.Drawing.Point(716, 117);
            this.textBox16.Margin = new System.Windows.Forms.Padding(4);
            this.textBox16.Name = "textBox16";
            this.textBox16.Size = new System.Drawing.Size(150, 31);
            this.textBox16.TabIndex = 110;
            // 
            // label49
            // 
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(619, 160);
            this.label49.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(86, 24);
            this.label49.TabIndex = 109;
            this.label49.Text = "玫瑰称号:";
            // 
            // textBox15
            // 
            this.textBox15.Location = new System.Drawing.Point(716, 405);
            this.textBox15.Margin = new System.Windows.Forms.Padding(4);
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new System.Drawing.Size(150, 31);
            this.textBox15.TabIndex = 108;
            // 
            // textBox14
            // 
            this.textBox14.Location = new System.Drawing.Point(716, 366);
            this.textBox14.Margin = new System.Windows.Forms.Padding(4);
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new System.Drawing.Size(150, 31);
            this.textBox14.TabIndex = 107;
            // 
            // label48
            // 
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(601, 408);
            this.label48.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(102, 24);
            this.label48.TabIndex = 106;
            this.label48.Text = "本次登陆IP:";
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(601, 369);
            this.label47.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(102, 24);
            this.label47.TabIndex = 105;
            this.label47.Text = "上次登陆IP:";
            // 
            // textBox13
            // 
            this.textBox13.Location = new System.Drawing.Point(716, 75);
            this.textBox13.Margin = new System.Windows.Forms.Padding(4);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(150, 31);
            this.textBox13.TabIndex = 104;
            // 
            // textBox12
            // 
            this.textBox12.Location = new System.Drawing.Point(716, 35);
            this.textBox12.Margin = new System.Windows.Forms.Padding(4);
            this.textBox12.Name = "textBox12";
            this.textBox12.Size = new System.Drawing.Size(150, 31);
            this.textBox12.TabIndex = 103;
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(601, 78);
            this.label46.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(104, 24);
            this.label46.TabIndex = 102;
            this.label46.Text = "综合仓库钱:";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(601, 38);
            this.label45.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(104, 24);
            this.label45.TabIndex = 101;
            this.label45.Text = "个人仓库钱:";
            // 
            // textBox10
            // 
            this.textBox10.Location = new System.Drawing.Point(429, 486);
            this.textBox10.Margin = new System.Windows.Forms.Padding(4);
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new System.Drawing.Size(150, 31);
            this.textBox10.TabIndex = 100;
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(429, 445);
            this.textBox9.Margin = new System.Windows.Forms.Padding(4);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(150, 31);
            this.textBox9.TabIndex = 99;
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(429, 405);
            this.textBox8.Margin = new System.Windows.Forms.Padding(4);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(150, 31);
            this.textBox8.TabIndex = 98;
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(429, 366);
            this.textBox7.Margin = new System.Windows.Forms.Padding(4);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(150, 31);
            this.textBox7.TabIndex = 97;
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(429, 323);
            this.textBox6.Margin = new System.Windows.Forms.Padding(4);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(150, 31);
            this.textBox6.TabIndex = 96;
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(429, 281);
            this.textBox5.Margin = new System.Windows.Forms.Padding(4);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(150, 31);
            this.textBox5.TabIndex = 95;
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(429, 239);
            this.textBox4.Margin = new System.Windows.Forms.Padding(4);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(150, 31);
            this.textBox4.TabIndex = 94;
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(296, 489);
            this.label43.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(122, 24);
            this.label43.TabIndex = 93;
            this.label43.Text = "最后在线时间:";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(350, 448);
            this.label42.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(68, 24);
            this.label42.TabIndex = 92;
            this.label42.Text = "被杀数:";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(350, 408);
            this.label41.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(68, 24);
            this.label41.TabIndex = 91;
            this.label41.Text = "杀人数:";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(332, 369);
            this.label39.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(86, 24);
            this.label39.TabIndex = 90;
            this.label39.Text = "角色暴率:";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(332, 326);
            this.label38.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(96, 24);
            this.label38.TabIndex = 89;
            this.label38.Text = "转生加SM:";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(332, 284);
            this.label37.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(86, 24);
            this.label37.TabIndex = 88;
            this.label37.Text = "转生加防:";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(332, 242);
            this.label36.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(86, 24);
            this.label36.TabIndex = 87;
            this.label36.Text = "转生加攻:";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(429, 198);
            this.textBox3.Margin = new System.Windows.Forms.Padding(4);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(150, 31);
            this.textBox3.TabIndex = 86;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(332, 201);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(86, 24);
            this.label1.TabIndex = 85;
            this.label1.Text = "转生次数:";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(350, 160);
            this.label35.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(68, 24);
            this.label35.TabIndex = 84;
            this.label35.Text = "神力点:";
            // 
            // textBoxShenLi
            // 
            this.textBoxShenLi.Location = new System.Drawing.Point(429, 157);
            this.textBoxShenLi.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxShenLi.Name = "textBoxShenLi";
            this.textBoxShenLi.Size = new System.Drawing.Size(150, 31);
            this.textBoxShenLi.TabIndex = 83;
            // 
            // buttonPlayerSub
            // 
            this.buttonPlayerSub.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.buttonPlayerSub.Cursor = System.Windows.Forms.Cursors.Hand;
            this.buttonPlayerSub.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.buttonPlayerSub.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonPlayerSub.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonPlayerSub.ForeColor = System.Drawing.Color.White;
            this.buttonPlayerSub.Location = new System.Drawing.Point(550, 597);
            this.buttonPlayerSub.Margin = new System.Windows.Forms.Padding(4);
            this.buttonPlayerSub.Name = "buttonPlayerSub";
            this.buttonPlayerSub.Size = new System.Drawing.Size(130, 40);
            this.buttonPlayerSub.TabIndex = 82;
            this.buttonPlayerSub.Text = "减少";
            this.buttonPlayerSub.UseVisualStyleBackColor = false;
            this.buttonPlayerSub.Click += new System.EventHandler(this.buttonPlayerSub_Click);
            // 
            // buttonPlayerAdd
            // 
            this.buttonPlayerAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(92)))), ((int)(((byte)(184)))), ((int)(((byte)(92)))));
            this.buttonPlayerAdd.Cursor = System.Windows.Forms.Cursors.Hand;
            this.buttonPlayerAdd.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(120)))), ((int)(((byte)(0)))));
            this.buttonPlayerAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonPlayerAdd.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonPlayerAdd.ForeColor = System.Drawing.Color.White;
            this.buttonPlayerAdd.Location = new System.Drawing.Point(400, 597);
            this.buttonPlayerAdd.Margin = new System.Windows.Forms.Padding(4);
            this.buttonPlayerAdd.Name = "buttonPlayerAdd";
            this.buttonPlayerAdd.Size = new System.Drawing.Size(130, 40);
            this.buttonPlayerAdd.TabIndex = 81;
            this.buttonPlayerAdd.Text = "增加";
            this.buttonPlayerAdd.UseVisualStyleBackColor = false;
            this.buttonPlayerAdd.Click += new System.EventHandler(this.buttonPlayerAdd_Click);
            // 
            // buttonPlayerEdit
            // 
            this.buttonPlayerEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(66)))), ((int)(((byte)(139)))), ((int)(((byte)(202)))));
            this.buttonPlayerEdit.Cursor = System.Windows.Forms.Cursors.Hand;
            this.buttonPlayerEdit.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(93)))), ((int)(((byte)(168)))));
            this.buttonPlayerEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonPlayerEdit.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonPlayerEdit.ForeColor = System.Drawing.Color.White;
            this.buttonPlayerEdit.Location = new System.Drawing.Point(250, 597);
            this.buttonPlayerEdit.Margin = new System.Windows.Forms.Padding(4);
            this.buttonPlayerEdit.Name = "buttonPlayerEdit";
            this.buttonPlayerEdit.Size = new System.Drawing.Size(130, 40);
            this.buttonPlayerEdit.TabIndex = 80;
            this.buttonPlayerEdit.Text = "修改";
            this.buttonPlayerEdit.UseVisualStyleBackColor = false;
            this.buttonPlayerEdit.Click += new System.EventHandler(this.buttonPlayerEdit_Click);
            // 
            // textBoxPlayerSXValue
            // 
            this.textBoxPlayerSXValue.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxPlayerSXValue.Location = new System.Drawing.Point(564, 541);
            this.textBoxPlayerSXValue.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerSXValue.Name = "textBoxPlayerSXValue";
            this.textBoxPlayerSXValue.Size = new System.Drawing.Size(220, 31);
            this.textBoxPlayerSXValue.TabIndex = 77;
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label32.Location = new System.Drawing.Point(444, 544);
            this.label32.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(89, 25);
            this.label32.TabIndex = 76;
            this.label32.Text = "属性的值:";
            // 
            // comboBoxPlayerSXSelect
            // 
            this.comboBoxPlayerSXSelect.BackColor = System.Drawing.Color.White;
            this.comboBoxPlayerSXSelect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.comboBoxPlayerSXSelect.FormattingEnabled = true;
            this.comboBoxPlayerSXSelect.Items.AddRange(new object[] {
            "人物元宝",
            "人物钻石",
            "人物等级",
            "人物转职",
            "人物经验",
            "人物武勋",
            "人物历练",
            "当前地图",
            "善恶值",
            "游戏币",
            "升天武功点",
            "神女神力点",
            "转生次数",
            "转生攻击",
            "转生防御",
            "转生生命",
            "个人暴率",
            "杀人次数",
            "被杀次数",
            "宝石称号",
            "玫瑰称号",
            "累计充值",
            "抽奖次数",
            "个人仓库金钱",
            "综合仓库金钱"});
            this.comboBoxPlayerSXSelect.Location = new System.Drawing.Point(210, 541);
            this.comboBoxPlayerSXSelect.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxPlayerSXSelect.Name = "comboBoxPlayerSXSelect";
            this.comboBoxPlayerSXSelect.Size = new System.Drawing.Size(210, 32);
            this.comboBoxPlayerSXSelect.TabIndex = 75;
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label30.Location = new System.Drawing.Point(119, 544);
            this.label30.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(89, 25);
            this.label30.TabIndex = 74;
            this.label30.Text = "选择属性:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(332, 120);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(86, 24);
            this.label12.TabIndex = 69;
            this.label12.Text = "玩家善恶:";
            // 
            // textBoxSE
            // 
            this.textBoxSE.Location = new System.Drawing.Point(429, 117);
            this.textBoxSE.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSE.Name = "textBoxSE";
            this.textBoxSE.Size = new System.Drawing.Size(150, 31);
            this.textBoxSE.TabIndex = 68;
            // 
            // textBoxPlayerSTWG
            // 
            this.textBoxPlayerSTWG.Location = new System.Drawing.Point(429, 35);
            this.textBoxPlayerSTWG.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerSTWG.Name = "textBoxPlayerSTWG";
            this.textBoxPlayerSTWG.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerSTWG.TabIndex = 67;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(296, 38);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(122, 24);
            this.label2.TabIndex = 66;
            this.label2.Text = "升天武功点数:";
            // 
            // textBoxPlayerY
            // 
            this.textBoxPlayerY.Location = new System.Drawing.Point(125, 486);
            this.textBoxPlayerY.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerY.Name = "textBoxPlayerY";
            this.textBoxPlayerY.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerY.TabIndex = 65;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(20, 369);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(86, 24);
            this.label4.TabIndex = 64;
            this.label4.Text = "当前历练:";
            // 
            // textBoxPlayerX
            // 
            this.textBoxPlayerX.Location = new System.Drawing.Point(123, 445);
            this.textBoxPlayerX.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerX.Name = "textBoxPlayerX";
            this.textBoxPlayerX.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerX.TabIndex = 63;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(18, 326);
            this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(86, 24);
            this.label5.TabIndex = 62;
            this.label5.Text = "玩家武勋:";
            // 
            // textBoxPlayerMap
            // 
            this.textBoxPlayerMap.Location = new System.Drawing.Point(123, 405);
            this.textBoxPlayerMap.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerMap.Name = "textBoxPlayerMap";
            this.textBoxPlayerMap.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerMap.TabIndex = 61;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(45, 489);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(61, 24);
            this.label7.TabIndex = 60;
            this.label7.Text = "坐标Y:";
            // 
            // textBoxPlayerLL
            // 
            this.textBoxPlayerLL.Location = new System.Drawing.Point(123, 366);
            this.textBoxPlayerLL.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerLL.Name = "textBoxPlayerLL";
            this.textBoxPlayerLL.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerLL.TabIndex = 37;
            // 
            // textBoxPlayerWuXun
            // 
            this.textBoxPlayerWuXun.Location = new System.Drawing.Point(123, 323);
            this.textBoxPlayerWuXun.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerWuXun.Name = "textBoxPlayerWuXun";
            this.textBoxPlayerWuXun.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerWuXun.TabIndex = 36;
            // 
            // textBoxPlayerEXP
            // 
            this.textBoxPlayerEXP.Location = new System.Drawing.Point(123, 281);
            this.textBoxPlayerEXP.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerEXP.Name = "textBoxPlayerEXP";
            this.textBoxPlayerEXP.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerEXP.TabIndex = 35;
            // 
            // textBoxPlayerMoney
            // 
            this.textBoxPlayerMoney.Location = new System.Drawing.Point(429, 75);
            this.textBoxPlayerMoney.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerMoney.Name = "textBoxPlayerMoney";
            this.textBoxPlayerMoney.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerMoney.TabIndex = 34;
            // 
            // textBoxPlayerJobLevel
            // 
            this.textBoxPlayerJobLevel.Location = new System.Drawing.Point(123, 239);
            this.textBoxPlayerJobLevel.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerJobLevel.Name = "textBoxPlayerJobLevel";
            this.textBoxPlayerJobLevel.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerJobLevel.TabIndex = 33;
            // 
            // textBoxPlayerJob
            // 
            this.textBoxPlayerJob.Location = new System.Drawing.Point(123, 198);
            this.textBoxPlayerJob.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerJob.Name = "textBoxPlayerJob";
            this.textBoxPlayerJob.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerJob.TabIndex = 32;
            // 
            // textBoxPlayerLevel
            // 
            this.textBoxPlayerLevel.Location = new System.Drawing.Point(123, 157);
            this.textBoxPlayerLevel.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPlayerLevel.Name = "textBoxPlayerLevel";
            this.textBoxPlayerLevel.Size = new System.Drawing.Size(150, 31);
            this.textBoxPlayerLevel.TabIndex = 31;
            // 
            // textBoxRXPIONT
            // 
            this.textBoxRXPIONT.Location = new System.Drawing.Point(123, 117);
            this.textBoxRXPIONT.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxRXPIONT.Name = "textBoxRXPIONT";
            this.textBoxRXPIONT.Size = new System.Drawing.Size(150, 31);
            this.textBoxRXPIONT.TabIndex = 29;
            // 
            // textBoxUserID
            // 
            this.textBoxUserID.Enabled = false;
            this.textBoxUserID.Location = new System.Drawing.Point(123, 75);
            this.textBoxUserID.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxUserID.Name = "textBoxUserID";
            this.textBoxUserID.Size = new System.Drawing.Size(150, 31);
            this.textBoxUserID.TabIndex = 28;
            // 
            // textBoxWorldID
            // 
            this.textBoxWorldID.Enabled = false;
            this.textBoxWorldID.Location = new System.Drawing.Point(123, 35);
            this.textBoxWorldID.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxWorldID.Name = "textBoxWorldID";
            this.textBoxWorldID.Size = new System.Drawing.Size(150, 31);
            this.textBoxWorldID.TabIndex = 26;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(18, 242);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(86, 24);
            this.label19.TabIndex = 11;
            this.label19.Text = "玩家转职:";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(18, 284);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(86, 24);
            this.label20.TabIndex = 10;
            this.label20.Text = "当前经验:";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(314, 78);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(104, 24);
            this.label21.TabIndex = 9;
            this.label21.Text = "包里游戏币:";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(18, 160);
            this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(86, 24);
            this.label22.TabIndex = 7;
            this.label22.Text = "玩家等级:";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(18, 201);
            this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(86, 24);
            this.label23.TabIndex = 6;
            this.label23.Text = "玩家职业:";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(45, 448);
            this.label24.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(62, 24);
            this.label24.TabIndex = 5;
            this.label24.Text = "坐标X:";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(20, 408);
            this.label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(86, 24);
            this.label25.TabIndex = 4;
            this.label25.Text = "当前地图:";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(20, 120);
            this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(86, 24);
            this.label26.TabIndex = 3;
            this.label26.Text = "共有元宝:";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(18, 78);
            this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(86, 24);
            this.label27.TabIndex = 1;
            this.label27.Text = "玩家账号:";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(2, 38);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(105, 24);
            this.label28.TabIndex = 0;
            this.label28.Text = "玩家全服ID:";
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Location = new System.Drawing.Point(4, 33);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage1.Size = new System.Drawing.Size(1110, 665);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "发送装备";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label56);
            this.groupBox1.Controls.Add(this.button41);
            this.groupBox1.Controls.Add(this.textBox23);
            this.groupBox1.Controls.Add(this.comboBox3);
            this.groupBox1.Controls.Add(this.comboBox2);
            this.groupBox1.Controls.Add(this.comboBox1);
            this.groupBox1.Controls.Add(this.label57);
            this.groupBox1.Controls.Add(this.buttonITEMSelect);
            this.groupBox1.Controls.Add(this.textBoxSTSX);
            this.groupBox1.Controls.Add(this.comboBoxST);
            this.groupBox1.Controls.Add(this.textBoxSTDM);
            this.groupBox1.Controls.Add(this.label18);
            this.groupBox1.Controls.Add(this.label17);
            this.groupBox1.Controls.Add(this.textBoxJH);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.textBoxTS);
            this.groupBox1.Controls.Add(this.checkBoxBD);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Controls.Add(this.textBoxZHH);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.textBoxCHH);
            this.groupBox1.Controls.Add(this.textBoxFJ0);
            this.groupBox1.Controls.Add(this.comboBoxFJSX);
            this.groupBox1.Controls.Add(this.comboBoxFJQH);
            this.groupBox1.Controls.Add(this.textBoxSX4);
            this.groupBox1.Controls.Add(this.textBoxSX3);
            this.groupBox1.Controls.Add(this.textBoxSX2);
            this.groupBox1.Controls.Add(this.textBoxSX1);
            this.groupBox1.Controls.Add(this.comboBoxSX3);
            this.groupBox1.Controls.Add(this.comboBoxSX2);
            this.groupBox1.Controls.Add(this.comboBoxSX1);
            this.groupBox1.Controls.Add(this.textBoxFJ1);
            this.groupBox1.Controls.Add(this.comboBoxSX4);
            this.groupBox1.Controls.Add(this.textBoxPID);
            this.groupBox1.Controls.Add(this.label40);
            this.groupBox1.Controls.Add(this.buttonSendItem);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.textBoxNUM);
            this.groupBox1.Controls.Add(this.listBoxALLITEM);
            this.groupBox1.Controls.Add(this.comboBoxITEMTYPE);
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.textBoxSXFJDM);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.textBoxSXDM1);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.textBoxSXDM2);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.textBoxSXDM3);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.textBoxSXDM4);
            this.groupBox1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(85)))), ((int)(((byte)(104)))));
            this.groupBox1.Location = new System.Drawing.Point(4, 9);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox1.Size = new System.Drawing.Size(1102, 655);
            this.groupBox1.TabIndex = 63;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "发送装备";
            // 
            // label56
            // 
            this.label56.AutoSize = true;
            this.label56.Location = new System.Drawing.Point(440, 103);
            this.label56.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label56.Name = "label56";
            this.label56.Size = new System.Drawing.Size(46, 24);
            this.label56.TabIndex = 175;
            this.label56.Text = "查询";
            // 
            // button41
            // 
            this.button41.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(157)))), ((int)(((byte)(255)))));
            this.button41.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.button41.ForeColor = System.Drawing.Color.Transparent;
            this.button41.Location = new System.Drawing.Point(675, 90);
            this.button41.Margin = new System.Windows.Forms.Padding(4);
            this.button41.Name = "button41";
            this.button41.Size = new System.Drawing.Size(178, 44);
            this.button41.TabIndex = 174;
            this.button41.Text = "模糊查询";
            this.button41.UseVisualStyleBackColor = false;
            this.button41.Click += new System.EventHandler(this.button41_Click);
            // 
            // textBox23
            // 
            this.textBox23.BackColor = System.Drawing.Color.White;
            this.textBox23.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox23.Location = new System.Drawing.Point(505, 97);
            this.textBox23.Margin = new System.Windows.Forms.Padding(4);
            this.textBox23.Name = "textBox23";
            this.textBox23.Size = new System.Drawing.Size(156, 31);
            this.textBox23.TabIndex = 173;
            this.textBox23.Tag = "";
            // 
            // comboBox3
            // 
            this.comboBox3.BackColor = System.Drawing.SystemColors.Window;
            this.comboBox3.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox3.FormattingEnabled = true;
            this.comboBox3.Location = new System.Drawing.Point(840, 491);
            this.comboBox3.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox3.Name = "comboBox3";
            this.comboBox3.Size = new System.Drawing.Size(138, 32);
            this.comboBox3.TabIndex = 150;
            this.comboBox3.SelectedIndexChanged += new System.EventHandler(this.comboBox3_SelectedIndexChanged);
            // 
            // comboBox2
            // 
            this.comboBox2.BackColor = System.Drawing.SystemColors.Window;
            this.comboBox2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox2.FormattingEnabled = true;
            this.comboBox2.Location = new System.Drawing.Point(684, 491);
            this.comboBox2.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox2.Name = "comboBox2";
            this.comboBox2.Size = new System.Drawing.Size(138, 32);
            this.comboBox2.TabIndex = 149;
            this.comboBox2.SelectedIndexChanged += new System.EventHandler(this.comboBox2_SelectedIndexChanged);
            // 
            // comboBox1
            // 
            this.comboBox1.BackColor = System.Drawing.SystemColors.Window;
            this.comboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(505, 491);
            this.comboBox1.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(164, 32);
            this.comboBox1.TabIndex = 148;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label57
            // 
            this.label57.AutoSize = true;
            this.label57.Location = new System.Drawing.Point(405, 494);
            this.label57.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label57.Name = "label57";
            this.label57.Size = new System.Drawing.Size(82, 24);
            this.label57.TabIndex = 147;
            this.label57.Text = "石头类型";
            // 
            // buttonITEMSelect
            // 
            this.buttonITEMSelect.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(40)))), ((int)(((byte)(167)))), ((int)(((byte)(69)))));
            this.buttonITEMSelect.FlatAppearance.BorderSize = 0;
            this.buttonITEMSelect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonITEMSelect.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonITEMSelect.ForeColor = System.Drawing.Color.White;
            this.buttonITEMSelect.Location = new System.Drawing.Point(675, 36);
            this.buttonITEMSelect.Margin = new System.Windows.Forms.Padding(4);
            this.buttonITEMSelect.Name = "buttonITEMSelect";
            this.buttonITEMSelect.Size = new System.Drawing.Size(178, 44);
            this.buttonITEMSelect.TabIndex = 139;
            this.buttonITEMSelect.Text = "选择物品";
            this.buttonITEMSelect.UseVisualStyleBackColor = false;
            this.buttonITEMSelect.Click += new System.EventHandler(this.buttonITEMSelect_Click);
            // 
            // textBoxSTSX
            // 
            this.textBoxSTSX.Location = new System.Drawing.Point(684, 540);
            this.textBoxSTSX.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSTSX.Name = "textBoxSTSX";
            this.textBoxSTSX.Size = new System.Drawing.Size(138, 31);
            this.textBoxSTSX.TabIndex = 138;
            this.textBoxSTSX.Text = "1";
            this.textBoxSTSX.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxSTSX.TextChanged += new System.EventHandler(this.textBoxSTSX_TextChanged);
            // 
            // comboBoxST
            // 
            this.comboBoxST.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxST.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxST.Location = new System.Drawing.Point(505, 541);
            this.comboBoxST.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxST.Name = "comboBoxST";
            this.comboBoxST.Size = new System.Drawing.Size(164, 32);
            this.comboBoxST.TabIndex = 137;
            this.comboBoxST.SelectedIndexChanged += new System.EventHandler(this.comboBoxST_SelectedIndexChanged);
            // 
            // textBoxSTDM
            // 
            this.textBoxSTDM.Location = new System.Drawing.Point(840, 541);
            this.textBoxSTDM.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSTDM.Name = "textBoxSTDM";
            this.textBoxSTDM.Size = new System.Drawing.Size(138, 31);
            this.textBoxSTDM.TabIndex = 135;
            this.textBoxSTDM.Text = "0";
            this.textBoxSTDM.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(405, 545);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(82, 24);
            this.label18.TabIndex = 136;
            this.label18.Text = "石头属性";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(825, 399);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(46, 24);
            this.label17.TabIndex = 133;
            this.label17.Text = "进化";
            // 
            // textBoxJH
            // 
            this.textBoxJH.Location = new System.Drawing.Point(876, 395);
            this.textBoxJH.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxJH.Name = "textBoxJH";
            this.textBoxJH.Size = new System.Drawing.Size(102, 31);
            this.textBoxJH.TabIndex = 134;
            this.textBoxJH.Text = "0";
            this.textBoxJH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(404, 448);
            this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(82, 24);
            this.label16.TabIndex = 131;
            this.label16.Text = "使用天数";
            // 
            // textBoxTS
            // 
            this.textBoxTS.Location = new System.Drawing.Point(505, 443);
            this.textBoxTS.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxTS.Name = "textBoxTS";
            this.textBoxTS.Size = new System.Drawing.Size(162, 31);
            this.textBoxTS.TabIndex = 132;
            this.textBoxTS.Text = "0";
            this.textBoxTS.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // checkBoxBD
            // 
            this.checkBoxBD.AutoSize = true;
            this.checkBoxBD.Location = new System.Drawing.Point(694, 449);
            this.checkBoxBD.Margin = new System.Windows.Forms.Padding(4);
            this.checkBoxBD.Name = "checkBoxBD";
            this.checkBoxBD.Size = new System.Drawing.Size(72, 28);
            this.checkBoxBD.TabIndex = 130;
            this.checkBoxBD.Text = "绑定";
            this.checkBoxBD.UseVisualStyleBackColor = true;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(650, 398);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(46, 24);
            this.label15.TabIndex = 128;
            this.label15.Text = "中魂";
            // 
            // textBoxZHH
            // 
            this.textBoxZHH.Location = new System.Drawing.Point(705, 394);
            this.textBoxZHH.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxZHH.Name = "textBoxZHH";
            this.textBoxZHH.Size = new System.Drawing.Size(117, 31);
            this.textBoxZHH.TabIndex = 129;
            this.textBoxZHH.Text = "0";
            this.textBoxZHH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(440, 399);
            this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(46, 24);
            this.label13.TabIndex = 126;
            this.label13.Text = "初魂";
            // 
            // textBoxCHH
            // 
            this.textBoxCHH.Location = new System.Drawing.Point(505, 394);
            this.textBoxCHH.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxCHH.Name = "textBoxCHH";
            this.textBoxCHH.Size = new System.Drawing.Size(137, 31);
            this.textBoxCHH.TabIndex = 127;
            this.textBoxCHH.Text = "0";
            this.textBoxCHH.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // textBoxFJ0
            // 
            this.textBoxFJ0.Location = new System.Drawing.Point(640, 149);
            this.textBoxFJ0.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxFJ0.Name = "textBoxFJ0";
            this.textBoxFJ0.Size = new System.Drawing.Size(45, 31);
            this.textBoxFJ0.TabIndex = 125;
            this.textBoxFJ0.Text = "1";
            this.textBoxFJ0.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxFJ0.TextChanged += new System.EventHandler(this.textBoxFJ0_TextChanged);
            // 
            // comboBoxFJSX
            // 
            this.comboBoxFJSX.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxFJSX.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxFJSX.FormattingEnabled = true;
            this.comboBoxFJSX.Items.AddRange(new object[] {
            "无",
            "火",
            "水",
            "风",
            "内",
            "外",
            "毒"});
            this.comboBoxFJSX.Location = new System.Drawing.Point(703, 150);
            this.comboBoxFJSX.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxFJSX.Name = "comboBoxFJSX";
            this.comboBoxFJSX.Size = new System.Drawing.Size(61, 32);
            this.comboBoxFJSX.TabIndex = 124;
            this.comboBoxFJSX.SelectedIndexChanged += new System.EventHandler(this.comboBoxFJSX_SelectedIndexChanged);
            // 
            // comboBoxFJQH
            // 
            this.comboBoxFJQH.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxFJQH.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxFJQH.FormattingEnabled = true;
            this.comboBoxFJQH.Items.AddRange(new object[] {
            "无",
            "武器强化",
            "防具强化",
            "耳环强化",
            "戒指强化",
            "项链强化",
            "披风强化",
            "灵宠强化",
            "药品次数"});
            this.comboBoxFJQH.Location = new System.Drawing.Point(505, 150);
            this.comboBoxFJQH.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxFJQH.Name = "comboBoxFJQH";
            this.comboBoxFJQH.Size = new System.Drawing.Size(127, 32);
            this.comboBoxFJQH.TabIndex = 123;
            this.comboBoxFJQH.SelectedIndexChanged += new System.EventHandler(this.comboBoxFJQH_SelectedIndexChanged);
            // 
            // textBoxSX4
            // 
            this.textBoxSX4.Location = new System.Drawing.Point(705, 345);
            this.textBoxSX4.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSX4.Name = "textBoxSX4";
            this.textBoxSX4.Size = new System.Drawing.Size(117, 31);
            this.textBoxSX4.TabIndex = 122;
            this.textBoxSX4.Text = "1";
            this.textBoxSX4.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxSX4.TextChanged += new System.EventHandler(this.textBoxSX4_TextChanged);
            // 
            // textBoxSX3
            // 
            this.textBoxSX3.Location = new System.Drawing.Point(703, 296);
            this.textBoxSX3.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSX3.Name = "textBoxSX3";
            this.textBoxSX3.Size = new System.Drawing.Size(119, 31);
            this.textBoxSX3.TabIndex = 121;
            this.textBoxSX3.Text = "1";
            this.textBoxSX3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxSX3.TextChanged += new System.EventHandler(this.textBoxSX3_TextChanged);
            // 
            // textBoxSX2
            // 
            this.textBoxSX2.Location = new System.Drawing.Point(705, 247);
            this.textBoxSX2.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSX2.Name = "textBoxSX2";
            this.textBoxSX2.Size = new System.Drawing.Size(117, 31);
            this.textBoxSX2.TabIndex = 120;
            this.textBoxSX2.Text = "1";
            this.textBoxSX2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxSX2.TextChanged += new System.EventHandler(this.textBoxSX2_TextChanged);
            // 
            // textBoxSX1
            // 
            this.textBoxSX1.Location = new System.Drawing.Point(705, 198);
            this.textBoxSX1.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSX1.Name = "textBoxSX1";
            this.textBoxSX1.Size = new System.Drawing.Size(117, 31);
            this.textBoxSX1.TabIndex = 119;
            this.textBoxSX1.Text = "1";
            this.textBoxSX1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxSX1.TextChanged += new System.EventHandler(this.textBoxSX1_TextChanged);
            // 
            // comboBoxSX3
            // 
            this.comboBoxSX3.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxSX3.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxSX3.FormattingEnabled = true;
            this.comboBoxSX3.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "武功命中%增加",
            "武功回避%增加",
            "精九药品恢复量",
            "精蓝药品恢复量",
            "精九追加生命",
            "精蓝追加内力",
            "无双恢复量",
            "耐久度"});
            this.comboBoxSX3.Location = new System.Drawing.Point(505, 297);
            this.comboBoxSX3.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxSX3.Name = "comboBoxSX3";
            this.comboBoxSX3.Size = new System.Drawing.Size(180, 32);
            this.comboBoxSX3.TabIndex = 118;
            this.comboBoxSX3.SelectedIndexChanged += new System.EventHandler(this.comboBoxSX3_SelectedIndexChanged);
            // 
            // comboBoxSX2
            // 
            this.comboBoxSX2.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxSX2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxSX2.FormattingEnabled = true;
            this.comboBoxSX2.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "武功命中%增加",
            "武功回避%增加",
            "精九药品恢复量",
            "精蓝药品恢复量",
            "精九追加生命",
            "精蓝追加内力",
            "无双恢复量",
            "耐久度"});
            this.comboBoxSX2.Location = new System.Drawing.Point(505, 248);
            this.comboBoxSX2.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxSX2.Name = "comboBoxSX2";
            this.comboBoxSX2.Size = new System.Drawing.Size(180, 32);
            this.comboBoxSX2.TabIndex = 117;
            this.comboBoxSX2.SelectedIndexChanged += new System.EventHandler(this.comboBoxSX2_SelectedIndexChanged);
            // 
            // comboBoxSX1
            // 
            this.comboBoxSX1.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxSX1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxSX1.FormattingEnabled = true;
            this.comboBoxSX1.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "武功命中%增加",
            "武功回避%增加",
            "精九药品恢复量",
            "精蓝药品恢复量",
            "精九追加生命",
            "精蓝追加内力",
            "无双恢复量",
            "耐久度"});
            this.comboBoxSX1.Location = new System.Drawing.Point(505, 199);
            this.comboBoxSX1.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxSX1.Name = "comboBoxSX1";
            this.comboBoxSX1.Size = new System.Drawing.Size(180, 32);
            this.comboBoxSX1.TabIndex = 116;
            this.comboBoxSX1.SelectedIndexChanged += new System.EventHandler(this.comboBoxSX1_SelectedIndexChanged);
            // 
            // textBoxFJ1
            // 
            this.textBoxFJ1.Location = new System.Drawing.Point(772, 149);
            this.textBoxFJ1.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxFJ1.Name = "textBoxFJ1";
            this.textBoxFJ1.Size = new System.Drawing.Size(50, 31);
            this.textBoxFJ1.TabIndex = 115;
            this.textBoxFJ1.Text = "1";
            this.textBoxFJ1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.textBoxFJ1.TextChanged += new System.EventHandler(this.textBoxFJ1_TextChanged);
            // 
            // comboBoxSX4
            // 
            this.comboBoxSX4.BackColor = System.Drawing.SystemColors.Window;
            this.comboBoxSX4.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxSX4.FormattingEnabled = true;
            this.comboBoxSX4.Items.AddRange(new object[] {
            "无",
            "攻击增加",
            "防御增加",
            "生命增加",
            "内功增加",
            "命中率增加",
            "回避率增加",
            "武功攻击力%增加",
            "全部气功等级增加",
            "升级成功率%增加",
            "追加伤害值",
            "武功防御力增加",
            "获得金钱%增加",
            "死亡损失经验减少%",
            "经验值增加%",
            "武功命中%增加",
            "武功回避%增加",
            "精九药品恢复量",
            "精蓝药品恢复量",
            "精九追加生命",
            "精蓝追加内力",
            "无双恢复量",
            "耐久度"});
            this.comboBoxSX4.Location = new System.Drawing.Point(505, 346);
            this.comboBoxSX4.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxSX4.Name = "comboBoxSX4";
            this.comboBoxSX4.Size = new System.Drawing.Size(180, 32);
            this.comboBoxSX4.TabIndex = 114;
            this.comboBoxSX4.SelectedIndexChanged += new System.EventHandler(this.comboBoxSX4_SelectedIndexChanged);
            // 
            // textBoxPID
            // 
            this.textBoxPID.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxPID.Location = new System.Drawing.Point(505, 45);
            this.textBoxPID.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPID.Name = "textBoxPID";
            this.textBoxPID.Size = new System.Drawing.Size(156, 31);
            this.textBoxPID.TabIndex = 68;
            this.textBoxPID.TextChanged += new System.EventHandler(this.textBoxPID_TextChanged);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(12, 32);
            this.label40.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(82, 24);
            this.label40.TabIndex = 39;
            this.label40.Text = "物品类别";
            // 
            // buttonSendItem
            // 
            this.buttonSendItem.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(157)))), ((int)(((byte)(47)))));
            this.buttonSendItem.FlatAppearance.BorderSize = 0;
            this.buttonSendItem.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.buttonSendItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonSendItem.ForeColor = System.Drawing.Color.White;
            this.buttonSendItem.Location = new System.Drawing.Point(675, 592);
            this.buttonSendItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonSendItem.Name = "buttonSendItem";
            this.buttonSendItem.Size = new System.Drawing.Size(178, 44);
            this.buttonSendItem.TabIndex = 0;
            this.buttonSendItem.Text = "发送";
            this.buttonSendItem.UseVisualStyleBackColor = false;
            this.buttonSendItem.Click += new System.EventHandler(this.buttonSendItem_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(85)))), ((int)(((byte)(104)))));
            this.label3.Location = new System.Drawing.Point(438, 602);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(48, 25);
            this.label3.TabIndex = 5;
            this.label3.Text = "数量";
            // 
            // textBoxNUM
            // 
            this.textBoxNUM.BackColor = System.Drawing.Color.White;
            this.textBoxNUM.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxNUM.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxNUM.Location = new System.Drawing.Point(505, 598);
            this.textBoxNUM.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxNUM.Name = "textBoxNUM";
            this.textBoxNUM.Size = new System.Drawing.Size(162, 31);
            this.textBoxNUM.TabIndex = 6;
            this.textBoxNUM.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // listBoxALLITEM
            // 
            this.listBoxALLITEM.Font = new System.Drawing.Font("微软雅黑", 8.5F);
            this.listBoxALLITEM.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(85)))), ((int)(((byte)(104)))));
            this.listBoxALLITEM.FormattingEnabled = true;
            this.listBoxALLITEM.ItemHeight = 23;
            this.listBoxALLITEM.Location = new System.Drawing.Point(14, 68);
            this.listBoxALLITEM.Margin = new System.Windows.Forms.Padding(4);
            this.listBoxALLITEM.Name = "listBoxALLITEM";
            this.listBoxALLITEM.Size = new System.Drawing.Size(370, 579);
            this.listBoxALLITEM.TabIndex = 38;
            this.listBoxALLITEM.SelectedIndexChanged += new System.EventHandler(this.listBoxALLITEM_SelectedIndexChanged);
            // 
            // comboBoxITEMTYPE
            // 
            this.comboBoxITEMTYPE.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxITEMTYPE.FormattingEnabled = true;
            this.comboBoxITEMTYPE.Items.AddRange(new object[] {
            "衣服",
            "护手",
            "武器",
            "鞋子",
            "内甲",
            "项链",
            "耳环",
            "戒指",
            "披风",
            "弓箭",
            "门甲",
            "宝宝",
            "石头",
            "宝盒",
            "幸运符",
            "气功书",
            "百宝",
            "武功书",
            "其他"});
            this.comboBoxITEMTYPE.Location = new System.Drawing.Point(98, 26);
            this.comboBoxITEMTYPE.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxITEMTYPE.MaxDropDownItems = 20;
            this.comboBoxITEMTYPE.Name = "comboBoxITEMTYPE";
            this.comboBoxITEMTYPE.Size = new System.Drawing.Size(286, 32);
            this.comboBoxITEMTYPE.TabIndex = 40;
            this.comboBoxITEMTYPE.SelectedIndexChanged += new System.EventHandler(this.comboBoxITEMTYPE_SelectedIndexChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(422, 48);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(65, 24);
            this.label14.TabIndex = 42;
            this.label14.Text = "物品ID";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(440, 154);
            this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(46, 24);
            this.label6.TabIndex = 43;
            this.label6.Text = "附加";
            // 
            // textBoxSXFJDM
            // 
            this.textBoxSXFJDM.Location = new System.Drawing.Point(840, 150);
            this.textBoxSXFJDM.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSXFJDM.Name = "textBoxSXFJDM";
            this.textBoxSXFJDM.Size = new System.Drawing.Size(138, 31);
            this.textBoxSXFJDM.TabIndex = 44;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(431, 350);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(57, 24);
            this.label11.TabIndex = 52;
            this.label11.Text = "属性4";
            // 
            // textBoxSXDM1
            // 
            this.textBoxSXDM1.Location = new System.Drawing.Point(840, 199);
            this.textBoxSXDM1.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSXDM1.Name = "textBoxSXDM1";
            this.textBoxSXDM1.Size = new System.Drawing.Size(138, 31);
            this.textBoxSXDM1.TabIndex = 45;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(431, 301);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(57, 24);
            this.label10.TabIndex = 51;
            this.label10.Text = "属性3";
            // 
            // textBoxSXDM2
            // 
            this.textBoxSXDM2.Location = new System.Drawing.Point(840, 248);
            this.textBoxSXDM2.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSXDM2.Name = "textBoxSXDM2";
            this.textBoxSXDM2.Size = new System.Drawing.Size(138, 31);
            this.textBoxSXDM2.TabIndex = 46;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(431, 252);
            this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(57, 24);
            this.label9.TabIndex = 50;
            this.label9.Text = "属性2";
            // 
            // textBoxSXDM3
            // 
            this.textBoxSXDM3.Location = new System.Drawing.Point(840, 297);
            this.textBoxSXDM3.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSXDM3.Name = "textBoxSXDM3";
            this.textBoxSXDM3.Size = new System.Drawing.Size(138, 31);
            this.textBoxSXDM3.TabIndex = 47;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(431, 203);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(57, 24);
            this.label8.TabIndex = 49;
            this.label8.Text = "属性1";
            // 
            // textBoxSXDM4
            // 
            this.textBoxSXDM4.Location = new System.Drawing.Point(840, 346);
            this.textBoxSXDM4.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxSXDM4.Name = "textBoxSXDM4";
            this.textBoxSXDM4.Size = new System.Drawing.Size(138, 31);
            this.textBoxSXDM4.TabIndex = 48;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox4);
            this.tabPage3.Location = new System.Drawing.Point(4, 33);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage3.Size = new System.Drawing.Size(1110, 665);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "物品管理";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.button21);
            this.groupBox4.Controls.Add(this.button14);
            this.groupBox4.Controls.Add(this.button13);
            this.groupBox4.Controls.Add(this.button12);
            this.groupBox4.Controls.Add(this.button11);
            this.groupBox4.Controls.Add(this.button10);
            this.groupBox4.Controls.Add(this.button9);
            this.groupBox4.Controls.Add(this.button7);
            this.groupBox4.Controls.Add(this.button6);
            this.groupBox4.Controls.Add(this.buttonSeeZBItem);
            this.groupBox4.Controls.Add(this.listViewItem1);
            this.groupBox4.Controls.Add(this.buttonSeePackItem);
            this.groupBox4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBox4.Location = new System.Drawing.Point(8, 9);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox4.Size = new System.Drawing.Size(1098, 684);
            this.groupBox4.TabIndex = 0;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "物品管理";
            // 
            // button21
            // 
            this.button21.Location = new System.Drawing.Point(38, 64);
            this.button21.Margin = new System.Windows.Forms.Padding(4);
            this.button21.Name = "button21";
            this.button21.Size = new System.Drawing.Size(112, 34);
            this.button21.TabIndex = 15;
            this.button21.Text = "杂货行囊";
            this.button21.UseVisualStyleBackColor = true;
            this.button21.Click += new System.EventHandler(this.button21_Click);
            // 
            // button14
            // 
            this.button14.Location = new System.Drawing.Point(402, 64);
            this.button14.Margin = new System.Windows.Forms.Padding(4);
            this.button14.Name = "button14";
            this.button14.Size = new System.Drawing.Size(112, 34);
            this.button14.TabIndex = 14;
            this.button14.Text = "宝珠装备";
            this.button14.UseVisualStyleBackColor = true;
            this.button14.Click += new System.EventHandler(this.button14_Click);
            // 
            // button13
            // 
            this.button13.Location = new System.Drawing.Point(766, 64);
            this.button13.Margin = new System.Windows.Forms.Padding(4);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(112, 34);
            this.button13.TabIndex = 13;
            this.button13.Text = "辅助装备";
            this.button13.UseVisualStyleBackColor = true;
            this.button13.Click += new System.EventHandler(this.button13_Click);
            // 
            // button12
            // 
            this.button12.Location = new System.Drawing.Point(584, 64);
            this.button12.Margin = new System.Windows.Forms.Padding(4);
            this.button12.Name = "button12";
            this.button12.Size = new System.Drawing.Size(112, 34);
            this.button12.TabIndex = 12;
            this.button12.Text = "披风行囊";
            this.button12.UseVisualStyleBackColor = true;
            this.button12.Click += new System.EventHandler(this.button12_Click);
            // 
            // button11
            // 
            this.button11.Location = new System.Drawing.Point(948, 22);
            this.button11.Margin = new System.Windows.Forms.Padding(4);
            this.button11.Name = "button11";
            this.button11.Size = new System.Drawing.Size(112, 34);
            this.button11.TabIndex = 11;
            this.button11.Text = "灵兽仓库";
            this.button11.UseVisualStyleBackColor = true;
            this.button11.Click += new System.EventHandler(this.button11_Click);
            // 
            // button10
            // 
            this.button10.Location = new System.Drawing.Point(766, 22);
            this.button10.Margin = new System.Windows.Forms.Padding(4);
            this.button10.Name = "button10";
            this.button10.Size = new System.Drawing.Size(112, 34);
            this.button10.TabIndex = 10;
            this.button10.Text = "灵宠窝";
            this.button10.UseVisualStyleBackColor = true;
            this.button10.Click += new System.EventHandler(this.button10_Click);
            // 
            // button9
            // 
            this.button9.Location = new System.Drawing.Point(584, 22);
            this.button9.Margin = new System.Windows.Forms.Padding(4);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(112, 34);
            this.button9.TabIndex = 9;
            this.button9.Text = "灵兽行囊";
            this.button9.UseVisualStyleBackColor = true;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(402, 22);
            this.button7.Margin = new System.Windows.Forms.Padding(4);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(112, 34);
            this.button7.TabIndex = 8;
            this.button7.Text = "综合仓库";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(220, 22);
            this.button6.Margin = new System.Windows.Forms.Padding(4);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(112, 34);
            this.button6.TabIndex = 7;
            this.button6.Text = "个人仓库";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // buttonSeeZBItem
            // 
            this.buttonSeeZBItem.Location = new System.Drawing.Point(220, 64);
            this.buttonSeeZBItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonSeeZBItem.Name = "buttonSeeZBItem";
            this.buttonSeeZBItem.Size = new System.Drawing.Size(112, 34);
            this.buttonSeeZBItem.TabIndex = 6;
            this.buttonSeeZBItem.Text = "个人装备";
            this.buttonSeeZBItem.UseVisualStyleBackColor = true;
            this.buttonSeeZBItem.Click += new System.EventHandler(this.buttonSeeZBItem_Click);
            // 
            // listViewItem1
            // 
            this.listViewItem1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listViewItem1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader_0,
            this.columnHeader_1,
            this.columnHeader_2,
            this.columnHeader_3,
            this.columnHeader_8,
            this.columnHeader_4,
            this.columnHeader_5,
            this.columnHeader_6,
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4});
            this.listViewItem1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(85)))), ((int)(((byte)(104)))));
            this.listViewItem1.FullRowSelect = true;
            this.listViewItem1.GridLines = true;
            this.listViewItem1.HideSelection = false;
            this.listViewItem1.Location = new System.Drawing.Point(0, 110);
            this.listViewItem1.Margin = new System.Windows.Forms.Padding(4);
            this.listViewItem1.Name = "listViewItem1";
            this.listViewItem1.Size = new System.Drawing.Size(1098, 542);
            this.listViewItem1.TabIndex = 5;
            this.listViewItem1.UseCompatibleStateImageBehavior = false;
            this.listViewItem1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader_0
            // 
            this.columnHeader_0.Text = "位置";
            this.columnHeader_0.Width = 36;
            // 
            // columnHeader_1
            // 
            this.columnHeader_1.Text = "PID";
            this.columnHeader_1.Width = 70;
            // 
            // columnHeader_2
            // 
            this.columnHeader_2.Text = "名字";
            this.columnHeader_2.Width = 100;
            // 
            // columnHeader_3
            // 
            this.columnHeader_3.Text = "属性1";
            this.columnHeader_3.Width = 68;
            // 
            // columnHeader_8
            // 
            this.columnHeader_8.Text = "属性2";
            this.columnHeader_8.Width = 68;
            // 
            // columnHeader_4
            // 
            this.columnHeader_4.Text = "属性3";
            this.columnHeader_4.Width = 68;
            // 
            // columnHeader_5
            // 
            this.columnHeader_5.Text = "属性4";
            this.columnHeader_5.Width = 68;
            // 
            // columnHeader_6
            // 
            this.columnHeader_6.Text = "属性5";
            this.columnHeader_6.Width = 68;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "数量";
            this.columnHeader1.Width = 50;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "全局ID";
            this.columnHeader2.Width = 65;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "来源角色";
            this.columnHeader3.Width = 90;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "是否绑定";
            // 
            // buttonSeePackItem
            // 
            this.buttonSeePackItem.Location = new System.Drawing.Point(38, 22);
            this.buttonSeePackItem.Margin = new System.Windows.Forms.Padding(4);
            this.buttonSeePackItem.Name = "buttonSeePackItem";
            this.buttonSeePackItem.Size = new System.Drawing.Size(112, 34);
            this.buttonSeePackItem.TabIndex = 4;
            this.buttonSeePackItem.Text = "查看背包";
            this.buttonSeePackItem.UseVisualStyleBackColor = true;
            this.buttonSeePackItem.Click += new System.EventHandler(this.buttonSeePackItem_Click);
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.groupBox5);
            this.tabPage4.Location = new System.Drawing.Point(4, 33);
            this.tabPage4.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(1110, 665);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "功能测试";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.button8);
            this.groupBox5.Controls.Add(this.textBox11);
            this.groupBox5.Controls.Add(this.label44);
            this.groupBox5.Controls.Add(this.textBox1);
            this.groupBox5.Controls.Add(this.label31);
            this.groupBox5.Controls.Add(this.textBox2);
            this.groupBox5.Controls.Add(this.label33);
            this.groupBox5.Controls.Add(this.button3);
            this.groupBox5.Controls.Add(this.button2);
            this.groupBox5.Controls.Add(this.button1);
            this.groupBox5.Controls.Add(this.checkBox1);
            this.groupBox5.Controls.Add(this.textBoxPack);
            this.groupBox5.Controls.Add(this.label29);
            this.groupBox5.Controls.Add(this.buttonSendPack);
            this.groupBox5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBox5.Location = new System.Drawing.Point(4, 9);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox5.Size = new System.Drawing.Size(1102, 652);
            this.groupBox5.TabIndex = 1;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "测试功能";
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(237, 381);
            this.button8.Margin = new System.Windows.Forms.Padding(4);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(112, 34);
            this.button8.TabIndex = 74;
            this.button8.Text = "踢出全服ID";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // textBox11
            // 
            this.textBox11.Location = new System.Drawing.Point(132, 384);
            this.textBox11.Margin = new System.Windows.Forms.Padding(4);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(85, 31);
            this.textBox11.TabIndex = 73;
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(55, 389);
            this.label44.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(69, 24);
            this.label44.TabIndex = 72;
            this.label44.Text = "全服ID:";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(307, 331);
            this.textBox1.Margin = new System.Windows.Forms.Padding(4);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(82, 31);
            this.textBox1.TabIndex = 71;
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(234, 336);
            this.label31.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(69, 24);
            this.label31.TabIndex = 70;
            this.label31.Text = "状态ID:";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(132, 331);
            this.textBox2.Margin = new System.Windows.Forms.Padding(4);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(85, 31);
            this.textBox2.TabIndex = 69;
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(19, 336);
            this.label33.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(105, 24);
            this.label33.TabIndex = 68;
            this.label33.Text = "全服或怪ID:";
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(401, 328);
            this.button3.Margin = new System.Windows.Forms.Padding(4);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(112, 34);
            this.button3.TabIndex = 14;
            this.button3.Text = "异常状态";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(146, 285);
            this.button2.Margin = new System.Windows.Forms.Padding(4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(135, 34);
            this.button2.TabIndex = 13;
            this.button2.Text = "发送封包组包";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(298, 285);
            this.button1.Margin = new System.Windows.Forms.Padding(4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(112, 34);
            this.button1.TabIndex = 12;
            this.button1.Text = "发送横幅公告";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Location = new System.Drawing.Point(104, 27);
            this.checkBox1.Margin = new System.Windows.Forms.Padding(4);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(199, 28);
            this.checkBox1.TabIndex = 11;
            this.checkBox1.Text = "替换当前人物全服ID";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // textBoxPack
            // 
            this.textBoxPack.Location = new System.Drawing.Point(15, 60);
            this.textBoxPack.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPack.Multiline = true;
            this.textBoxPack.Name = "textBoxPack";
            this.textBoxPack.Size = new System.Drawing.Size(1079, 210);
            this.textBoxPack.TabIndex = 10;
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(18, 28);
            this.label29.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(64, 24);
            this.label29.TabIndex = 9;
            this.label29.Text = "数据包";
            // 
            // buttonSendPack
            // 
            this.buttonSendPack.Location = new System.Drawing.Point(15, 284);
            this.buttonSendPack.Margin = new System.Windows.Forms.Padding(4);
            this.buttonSendPack.Name = "buttonSendPack";
            this.buttonSendPack.Size = new System.Drawing.Size(112, 34);
            this.buttonSendPack.TabIndex = 8;
            this.buttonSendPack.Text = "发送封包";
            this.buttonSendPack.UseVisualStyleBackColor = true;
            this.buttonSendPack.Click += new System.EventHandler(this.buttonSendPack_Click);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.删除数据,
            this.绑定物品,
            this.解绑物品});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(153, 94);
            // 
            // 删除数据
            // 
            this.删除数据.Name = "删除数据";
            this.删除数据.Size = new System.Drawing.Size(152, 30);
            this.删除数据.Text = "删除物品";
            this.删除数据.Click += new System.EventHandler(this.删除物品);
            // 
            // 绑定物品
            // 
            this.绑定物品.Name = "绑定物品";
            this.绑定物品.Size = new System.Drawing.Size(152, 30);
            this.绑定物品.Text = "绑定物品";
            this.绑定物品.Click += new System.EventHandler(this.绑定人物物品);
            // 
            // 解绑物品
            // 
            this.解绑物品.Name = "解绑物品";
            this.解绑物品.Size = new System.Drawing.Size(152, 30);
            this.解绑物品.Text = "解绑物品";
            this.解绑物品.Click += new System.EventHandler(this.解绑人物物品);
            // 
            // listBoxAllPlayer
            // 
            this.listBoxAllPlayer.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.listBoxAllPlayer.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(85)))), ((int)(((byte)(104)))));
            this.listBoxAllPlayer.FormattingEnabled = true;
            this.listBoxAllPlayer.ItemHeight = 24;
            this.listBoxAllPlayer.Location = new System.Drawing.Point(15, 132);
            this.listBoxAllPlayer.Margin = new System.Windows.Forms.Padding(4);
            this.listBoxAllPlayer.Name = "listBoxAllPlayer";
            this.listBoxAllPlayer.Size = new System.Drawing.Size(253, 580);
            this.listBoxAllPlayer.TabIndex = 64;
            this.listBoxAllPlayer.SelectedIndexChanged += new System.EventHandler(this.listBoxAllPlayer_SelectedIndexChanged);
            // 
            // buttonShuaXin
            // 
            this.buttonShuaXin.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.buttonShuaXin.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonShuaXin.ForeColor = System.Drawing.Color.White;
            this.buttonShuaXin.Location = new System.Drawing.Point(196, 51);
            this.buttonShuaXin.Margin = new System.Windows.Forms.Padding(4);
            this.buttonShuaXin.Name = "buttonShuaXin";
            this.buttonShuaXin.Size = new System.Drawing.Size(74, 34);
            this.buttonShuaXin.TabIndex = 65;
            this.buttonShuaXin.Text = "刷新";
            this.buttonShuaXin.UseVisualStyleBackColor = false;
            this.buttonShuaXin.Click += new System.EventHandler(this.buttonShuaXin_Click);
            // 
            // textBoxKey
            // 
            this.textBoxKey.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxKey.Location = new System.Drawing.Point(82, 14);
            this.textBoxKey.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxKey.Name = "textBoxKey";
            this.textBoxKey.Size = new System.Drawing.Size(186, 31);
            this.textBoxKey.TabIndex = 144;
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label34.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label34.Location = new System.Drawing.Point(15, 18);
            this.label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(66, 25);
            this.label34.TabIndex = 143;
            this.label34.Text = "关键字";
            // 
            // button4
            // 
            this.button4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(92)))), ((int)(((byte)(184)))), ((int)(((byte)(92)))));
            this.button4.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button4.ForeColor = System.Drawing.Color.White;
            this.button4.Location = new System.Drawing.Point(105, 51);
            this.button4.Margin = new System.Windows.Forms.Padding(4);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(86, 34);
            this.button4.TabIndex = 140;
            this.button4.Text = "查名字";
            this.button4.UseVisualStyleBackColor = false;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // button5
            // 
            this.button5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(66)))), ((int)(((byte)(139)))), ((int)(((byte)(202)))));
            this.button5.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button5.ForeColor = System.Drawing.Color.White;
            this.button5.Location = new System.Drawing.Point(14, 51);
            this.button5.Margin = new System.Windows.Forms.Padding(4);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(86, 34);
            this.button5.TabIndex = 145;
            this.button5.Text = "查账号";
            this.button5.UseVisualStyleBackColor = false;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.tishi});
            this.statusStrip1.Location = new System.Drawing.Point(0, 714);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(2, 0, 21, 0);
            this.statusStrip1.Size = new System.Drawing.Size(1400, 31);
            this.statusStrip1.TabIndex = 146;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(100, 24);
            this.toolStripStatusLabel1.Text = "信息提示：";
            // 
            // tishi
            // 
            this.tishi.ForeColor = System.Drawing.Color.Red;
            this.tishi.Name = "tishi";
            this.tishi.Size = new System.Drawing.Size(0, 24);
            // 
            // Evias
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1400, 745);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.button5);
            this.Controls.Add(this.button4);
            this.Controls.Add(this.textBoxKey);
            this.Controls.Add(this.label34);
            this.Controls.Add(this.buttonShuaXin);
            this.Controls.Add(this.listBoxAllPlayer);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.textBoxName);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "Evias";
            this.Text = "人物管理工具";
            this.Load += new System.EventHandler(this.GMGJ_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.tabPage1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void button6_Click(object sender, EventArgs e)
	{
		序列号 = 2;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.个人仓库.Length; i++)
		{
			物品类 物品类2 = players.个人仓库[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias14" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void button7_Click(object sender, EventArgs e)
	{
		序列号 = 3;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.公共仓库.Length; i++)
		{
			物品类 物品类2 = players.公共仓库[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias15" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

    private void button8_Click(object sender, EventArgs e)
    {
        if (!int.TryParse(textBox11.Text.Trim(), out int playerId))
        {
            tishi.Text = "请输入有效的数字ID";
            textBox11.Focus();
            return;
        }

        try
        {
            bool isRemoved = World.list.TryRemove(playerId, out _);
            bool isDisconnected = World.allConnectedChars.TryRemove(playerId, out _);

            if (isRemoved || isDisconnected)
            {
                tishi.Text = "玩家已被踢出！";
            }
            else
            {
                tishi.Text = "未找到该玩家，可能已离线";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"操作失败: {ex.Message}");
        }
    }

    private void button9_Click(object sender, EventArgs e)
	{
		序列号 = 4;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		if (players.人物灵兽 != null)
		{
			for (int i = 0; i < players.人物灵兽.宠物装备栏.Length; i++)
			{
				物品类 物品类2 = players.人物灵兽.宠物装备栏[i];
				if (物品类2 != null)
				{
					string[] array = new string[12];
					try
					{
						array[0] = i.ToString();
						array[1] = 物品类2.Get物品ID.ToString();
						array[2] = 物品类2.得到物品名称();
						array[3] = 物品类2.FLD_MAGIC0.ToString();
						array[4] = 物品类2.FLD_MAGIC1.ToString();
						array[5] = 物品类2.FLD_MAGIC2.ToString();
						array[6] = 物品类2.FLD_MAGIC3.ToString();
						array[7] = 物品类2.FLD_MAGIC4.ToString();
						array[8] = 物品类2.Get物品数量.ToString();
						array[9] = 物品类2.Get物品全局ID.ToString();
						array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
						array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, "Evias16" + ex.StackTrace);
					}
					listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
				}
			}
		}
		else
		{
			MessageBox.Show("玩家没有召唤灵兽无法查询灵兽行囊");
		}
	}

	private void button10_Click(object sender, EventArgs e)
	{
		序列号 = 5;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.灵宠仓库.Length; i++)
		{
			物品类 物品类2 = players.灵宠仓库[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias17" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void button11_Click(object sender, EventArgs e)
	{
		序列号 = 6;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.灵兽仓库.Length; i++)
		{
			物品类 物品类2 = players.灵兽仓库[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias18" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void button12_Click(object sender, EventArgs e)
	{
		序列号 = 7;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		if (players.是否携带披风行囊)
		{
			for (int i = 0; i < players.披风行囊.Length; i++)
			{
				物品类 物品类2 = players.披风行囊[i];
				if (物品类2 != null)
				{
					string[] array = new string[12];
					try
					{
						array[0] = i.ToString();
						array[1] = 物品类2.Get物品ID.ToString();
						array[2] = 物品类2.得到物品名称();
						array[3] = 物品类2.FLD_MAGIC0.ToString();
						array[4] = 物品类2.FLD_MAGIC1.ToString();
						array[5] = 物品类2.FLD_MAGIC2.ToString();
						array[6] = 物品类2.FLD_MAGIC3.ToString();
						array[7] = 物品类2.FLD_MAGIC4.ToString();
						array[8] = 物品类2.Get物品数量.ToString();
						array[9] = 物品类2.Get物品全局ID.ToString();
						array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
						array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
					}
					catch (Exception ex)
					{
						Form1.WriteLine(1, "Evias19" + ex.StackTrace);
					}
					listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
				}
			}
		}
		else
		{
			MessageBox.Show("玩家没有携带披风行囊");
		}
	}

	private void button13_Click(object sender, EventArgs e)
	{
		序列号 = 9;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.辅助装备栏装备.Length; i++)
		{
			物品类 物品类2 = players.辅助装备栏装备[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias20" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

    private void button21_Click(object sender, EventArgs e)
    {
        序列号 = 10;
        listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
        if (players == null)
        {
            return;
        }
        for (int i = 0; i < players.杂货行囊包裹.Length; i++)
        {
            物品类 物品类2 = players.杂货行囊包裹[i];
            if (物品类2 != null)
            {
                string[] array = new string[12];
                try
                {
                    array[0] = i.ToString();
                    array[1] = 物品类2.Get物品ID.ToString();
                    array[2] = 物品类2.得到物品名称();
                    array[3] = 物品类2.FLD_MAGIC0.ToString();
                    array[4] = 物品类2.FLD_MAGIC1.ToString();
                    array[5] = 物品类2.FLD_MAGIC2.ToString();
                    array[6] = 物品类2.FLD_MAGIC3.ToString();
                    array[7] = 物品类2.FLD_MAGIC4.ToString();
                    array[8] = 物品类2.Get物品数量.ToString();
                    array[9] = 物品类2.Get物品全局ID.ToString();
                    array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
                    array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, "Evias21" + ex.StackTrace);
                }
                listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
            }
        }
    }

    private void button14_Click(object sender, EventArgs e)
	{
		序列号 = 10;
		listViewItem1.Items.Clear();
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players == null)
		{
			return;
		}
		for (int i = 0; i < players.宝珠装备栏装备.Length; i++)
		{
			物品类 物品类2 = players.宝珠装备栏装备[i];
			if (物品类2 != null)
			{
				string[] array = new string[12];
				try
				{
					array[0] = i.ToString();
					array[1] = 物品类2.Get物品ID.ToString();
					array[2] = 物品类2.得到物品名称();
					array[3] = 物品类2.FLD_MAGIC0.ToString();
					array[4] = 物品类2.FLD_MAGIC1.ToString();
					array[5] = 物品类2.FLD_MAGIC2.ToString();
					array[6] = 物品类2.FLD_MAGIC3.ToString();
					array[7] = 物品类2.FLD_MAGIC4.ToString();
					array[8] = 物品类2.Get物品数量.ToString();
					array[9] = 物品类2.Get物品全局ID.ToString();
					array[10] = ((物品类2.FLD_道具锁时间 == 0) ? "无" : RxjhClass.取得到人物名字(物品类2.FLD_道具锁时间));
					array[11] = ((!物品类2.物品绑定) ? "无" : "绑定中");
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "Evias21" + ex.StackTrace);
				}
				listViewItem1.Items.Insert(listViewItem1.Items.Count, new ListViewItem(array));
			}
		}
	}

	private void 删除物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定删除玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化装备篮包裹();
				break;
			case 2:
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化个人仓库();
				break;
			case 3:
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化综合仓库();
				break;
			case 4:
				players.人物灵兽.宠物装备栏[num].物品_byte = new byte[World.数据库单个物品大小];
				players.更新灵兽初始话装备篮包裹();
				break;
			case 5:
				players.灵宠仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化灵宠仓库();
				break;
			case 6:
				players.灵兽仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化灵兽仓库();
				break;
			case 7:
				players.披风行囊[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化披风行囊();
				break;
			case 8:
				players.装备栏已穿装备[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化已装备物品();
				break;
			case 9:
				players.辅助装备栏装备[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化已装备物品();
				break;
			case 10:
				players.宝珠装备栏装备[num].物品_byte = new byte[World.数据库单个物品大小];
				players.初始化已装备物品();
				break;
			}
			players.保存人物的数据();
			tishi.Text = "删除玩家物品完成";

		}
	}

	private void 绑定人物物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定绑定玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
			{
				byte[] array3 = new byte[60];
				players.装备栏包裹[num].物品绑定 = true;
				players.装备栏包裹[num].锁定 = true;
				byte[] 物品ID3 = players.装备栏包裹[num].物品ID;
				byte[] 物品全局ID3 = players.装备栏包裹[num].物品全局ID;
				Buffer.BlockCopy(players.装备栏包裹[num].物品_byte, 16, array3, 0, 60);
				int get物品数量3 = players.装备栏包裹[num].Get物品数量;
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定(物品全局ID3, 物品ID3, num, BitConverter.GetBytes(get物品数量3), array3, 绑定: true);
				players.初始化装备篮包裹();
				break;
			}
			case 2:
			{
				byte[] array2 = new byte[60];
				players.个人仓库[num].物品绑定 = true;
				players.个人仓库[num].锁定 = true;
				byte[] 物品ID2 = players.个人仓库[num].物品ID;
				byte[] 物品全局ID2 = players.个人仓库[num].物品全局ID;
				Buffer.BlockCopy(players.个人仓库[num].物品_byte, 16, array2, 0, 60);
				int get物品数量2 = players.个人仓库[num].Get物品数量;
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定个人仓库(物品全局ID2, 物品ID2, num, BitConverter.GetBytes(get物品数量2), array2, 绑定: true);
				players.初始化个人仓库();
				break;
			}
			case 3:
			{
				byte[] array = new byte[60];
				players.公共仓库[num].物品绑定 = true;
				players.公共仓库[num].锁定 = true;
				byte[] 物品ID = players.公共仓库[num].物品ID;
				byte[] 物品全局ID = players.公共仓库[num].物品全局ID;
				Buffer.BlockCopy(players.公共仓库[num].物品_byte, 16, array, 0, 60);
				int get物品数量 = players.公共仓库[num].Get物品数量;
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定综合仓库(物品全局ID, 物品ID, num, BitConverter.GetBytes(get物品数量), array, 绑定: true);
				players.初始化综合仓库();
				break;
			}
			}
			players.保存人物的数据();
			tishi.Text = "绑定玩家物品完成";
        }
	}

	private void 解绑人物物品(object sender, EventArgs e)
	{
		if (MessageBox.Show("确定解绑玩家物品吗..", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) != DialogResult.OK)
		{
			return;
		}
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = World.检查玩家name(textBoxName.Text);
		if (players != null && listViewItem1.SelectedItems.Count > 0)
		{
			string s = listViewItem1.SelectedItems[0].SubItems[0].Text;
			int num = int.Parse(s);
			switch (序列号)
			{
			case 1:
			{
				byte[] array3 = new byte[60];
				players.装备栏包裹[num].物品绑定 = false;
				players.装备栏包裹[num].锁定 = false;
				byte[] 物品ID3 = players.装备栏包裹[num].物品ID;
				byte[] 物品全局ID3 = players.装备栏包裹[num].物品全局ID;
				Buffer.BlockCopy(players.装备栏包裹[num].物品_byte, 16, array3, 0, 60);
				int get物品数量3 = players.装备栏包裹[num].Get物品数量;
				players.装备栏包裹[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定(物品全局ID3, 物品ID3, num, BitConverter.GetBytes(get物品数量3), array3, 绑定: false);
				players.初始化装备篮包裹();
				break;
			}
			case 2:
			{
				byte[] array2 = new byte[60];
				players.个人仓库[num].物品绑定 = false;
				players.个人仓库[num].锁定 = false;
				byte[] 物品ID2 = players.个人仓库[num].物品ID;
				byte[] 物品全局ID2 = players.个人仓库[num].物品全局ID;
				Buffer.BlockCopy(players.个人仓库[num].物品_byte, 16, array2, 0, 60);
				int get物品数量2 = players.个人仓库[num].Get物品数量;
				players.个人仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定个人仓库(物品全局ID2, 物品ID2, num, BitConverter.GetBytes(get物品数量2), array2, 绑定: false);
				players.初始化个人仓库();
				break;
			}
			case 3:
			{
				byte[] array = new byte[60];
				players.公共仓库[num].物品绑定 = false;
				players.公共仓库[num].锁定 = false;
				byte[] 物品ID = players.公共仓库[num].物品ID;
				byte[] 物品全局ID = players.公共仓库[num].物品全局ID;
				Buffer.BlockCopy(players.公共仓库[num].物品_byte, 16, array, 0, 60);
				int get物品数量 = players.公共仓库[num].Get物品数量;
				players.公共仓库[num].物品_byte = new byte[World.数据库单个物品大小];
				players.增加物品带绑定综合仓库(物品全局ID, 物品ID, num, BitConverter.GetBytes(get物品数量), array, 绑定: false);
				players.初始化综合仓库();
				break;
			}
			}
			players.保存人物的数据();
            tishi.Text = "解绑玩家物品完成";

        }
    }

	private void button15_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.清理行囊();
				tishi.Text = "清理行囊完成";
            }
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias22" + ex.StackTrace);
		}
	}

	private void button16_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.清理绑定背包();
				tishi.Text = "清理绑定背包完成";
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias23" + ex.StackTrace);
		}
	}

	private void button17_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.清理锁定背包();
				tishi.Text = "清理锁定背包完成";
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias24" + ex.StackTrace);
		}
	}

	private void button18_Click(object sender, EventArgs e)
	{
		try
		{
            if (string.IsNullOrWhiteSpace(textBoxName.Text))
            {
                tishi.Text = "请输入有效的玩家名称";
                textBoxName.Focus();
                return;
            }
            Players players = World.检查玩家name(textBoxName.Text);
			if (players != null)
			{
				players.清理英雄职业武器();
				tishi.Text = "清理英雄职业武器完成";
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Evias25" + ex.StackTrace);
		}
	}

	private void button19_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = null;
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.UserName == textBoxName.Text)
			{
				players = value;
				break;
			}
		}
		if (players == null)
		{
			tishi.Text = "对方不在同一线路，或者不在线";
			return;
		}
		bool flag = false;
		for (int i = 0; i < 96; i++)
		{
			if (players.装备栏包裹[i].FLD_道具锁时间 != 0 && players.装备栏包裹[i].FLD_道具锁时间 == players.FLD_道具锁时间)
			{
				players.装备栏包裹[i].FLD_道具锁时间 = 0;
				players.系统提示("管理员给你背包解锁成功。", 3, "解锁提示");
				players.初始化装备篮包裹();
				flag = true;
			}
		}
		for (int j = 0; j < 17; j++)
		{
			if (players.装备栏已穿装备[j].FLD_道具锁时间 != 0 && players.装备栏包裹[j].FLD_道具锁时间 == players.FLD_道具锁时间)
			{
				players.装备栏已穿装备[j].FLD_道具锁时间 = 0;
				players.系统提示("管理员给你穿戴解锁成功。", 3, "解锁提示");
				players.初始化已装备物品();
				flag = true;
			}
		}
		if (flag)
		{
			tishi.Text = "[" + players.UserName + "]装备解锁成功";

        }
    }

	private void button20_Click(object sender, EventArgs e)
	{
        if (string.IsNullOrWhiteSpace(textBoxName.Text))
        {
            tishi.Text = "请输入有效的玩家名称";
            textBoxName.Focus();
            return;
        }
        Players players = null;
		foreach (Players value in World.allConnectedChars.Values)
		{
			if (value.UserName == textBoxName.Text)
			{
				players = value;
				break;
			}
		}
		if (players == null)
		{
            tishi.Text = "对方不在同一线路，或者不在线";
            return;
		}
		bool flag = false;
		for (int i = 0; i < 96; i++)
		{
			if (players.装备栏包裹[i].FLD_道具锁时间 != 0)
			{
				players.装备栏包裹[i].FLD_道具锁时间 = 0;
				players.系统提示("您的背包解冻成功，可再次交易", 3, "解冻提示");
				players.初始化装备篮包裹();
				flag = true;
			}
		}
		for (int j = 0; j < 17; j++)
		{
			if (players.装备栏已穿装备[j].FLD_道具锁时间 != 0)
			{
				players.装备栏已穿装备[j].FLD_道具锁时间 = 0;
				players.系统提示("您的穿戴解冻成功，可再次交易", 3, "解冻提示");
				players.初始化已装备物品();
				flag = true;
			}
		}
		if (flag)
		{
            tishi.Text = "[" + players.UserName + "]装备解冻成功";
        }
	}

    private readonly List<石头信息> 所有石头 = new List<石头信息>
    {
        new 石头信息 { 名称 = "热血石", 物品ID = "800000013", 属性列表 = new string[] { }, 职业列表 = new string[] { "职业通用", "刀客", "剑客", "枪客", "弓箭", "医生", "刺客", "琴师", "韩飞官", "谭花灵", "拳师", "梅柳真", "卢风郎", "神女" } },
        new 石头信息 { 名称 = "属性原石", 物品ID = "800000028", 属性列表 = new[] { "火", "水", "风", "内", "外", "毒" } },
        new 石头信息 { 名称 = "金刚石", 物品ID = "800000001", 属性列表 = new[] { "攻击增加", "武功攻击力%增加", "全部气功等级增加", "追加伤害值", "命中率增加" } },
        new 石头信息 { 名称 = "混元金刚石", 物品ID = "800000023", 属性列表 = new[] { "攻击增加", "武功攻击力%增加", "全部气功等级增加", "追加伤害值", "命中率增加" } },
        new 石头信息 { 名称 = "乾坤金刚石", 物品ID = "800000061", 属性列表 = new[] { "攻击增加", "武功攻击力%增加", "全部气功等级增加", "追加伤害值", "命中率增加" } },
        new 石头信息 { 名称 = "天魔方石", 物品ID = "1000001650", 属性列表 = new[] { "攻击增加", "武功攻击力%增加", "全部气功等级增加", "追加伤害值", "命中率增加" } },
        new 石头信息 { 名称 = "寒玉石", 物品ID = "800000002", 属性列表 = new[] { "防御增加", "生命增加", "内功增加", "回避率增加" } },
        new 石头信息 { 名称 = "冰魄寒玉石", 物品ID = "800000024", 属性列表 = new[] { "防御增加", "武功防御增加", "生命增加", "内功增加", "回避率增加" } },
        new 石头信息 { 名称 = "凌霜寒玉石", 物品ID = "800000062", 属性列表 = new[] { "防御增加", "武功防御增加", "生命增加", "内功增加", "回避率增加" } },
        new 石头信息 { 名称 = "地魔方石", 物品ID = "1000001651", 属性列表 = new[] { "防御增加", "武功防御增加", "生命增加", "内功增加", "回避率增加" } },
    };

    private void 初始化石头类型下拉框()
    {
        comboBox1.Items.Clear();
        comboBox1.Items.Add("无"); 
        foreach (var 石头 in 所有石头)
            comboBox1.Items.Add(石头.名称);
        comboBox1.SelectedIndex = 0;
       
        // 清空相关
        comboBox2.Items.Clear();
        comboBox2.SelectedIndex = -1;
        comboBox3.Items.Clear();
        comboBox3.SelectedIndex = -1;
        comboBoxST.Items.Clear();
        comboBoxST.SelectedIndex = -1;
        textBoxSTDM.Text = "0";
    }


    private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (comboBox1.SelectedIndex == 0 || comboBox1.SelectedItem == null ||
            所有石头.FirstOrDefault(s => s.名称 == comboBox1.SelectedItem.ToString()) == null)
        {
            comboBox2.Items.Clear();
            comboBox2.SelectedIndex = -1;
            comboBox3.Items.Clear();
            comboBox3.SelectedIndex = -1;
            comboBoxST.Items.Clear();
            comboBoxST.SelectedIndex = -1;
            textBoxSTDM.Text = "0";
            return;
        }

        var 当前石头 = 所有石头.FirstOrDefault(s => s.名称 == comboBox1.SelectedItem.ToString());
        if (当前石头 == null) return;

        textBoxPID.Text = 当前石头.物品ID;

        comboBoxST.Items.Clear();
        if (当前石头.属性列表 != null && 当前石头.属性列表.Length > 0)
            comboBoxST.Items.AddRange(当前石头.属性列表);
        else
            comboBoxST.Items.Add("无");
        comboBoxST.SelectedIndex = 0;

        //（热血石/职业）
        comboBox2.Items.Clear();
        if (当前石头.职业列表 != null && 当前石头.职业列表.Length > 0)
        {
            comboBox2.Items.AddRange(当前石头.职业列表);
            comboBox2.SelectedIndex = 0;
        }
        else
        {
            comboBox2.Items.Add("无");
            comboBox2.SelectedIndex = 0;
        }

        comboBox3.Items.Clear();

        if (当前石头.名称 == "热血石")
        {
            comboBox2.SelectedItem = "职业通用";
            comboBox3.Items.Clear();
            comboBox3.Items.Add("全部气功");
            comboBox3.Items.Add("升级概率%");
            comboBox3.Items.Add("金钱增加%");
            comboBox3.Items.Add("经验值增加%");
            comboBox3.Items.Add("死亡经验减少%");
            comboBox3.SelectedIndex = 0;
        }
    }

    private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (comboBox2.SelectedIndex >= 0)
        {
            if (comboBox2.SelectedItem.ToString() == "职业通用")
            {
                comboBox3.Items.Clear();
                comboBox3.Items.Add("全部气功");
                comboBox3.Items.Add("升级概率%");
                comboBox3.Items.Add("金钱增加%");
                comboBox3.Items.Add("经验值增加%");
                comboBox3.Items.Add("死亡经验减少%");
                comboBox3.SelectedIndex = 0; 
            }
            else
            {
                int jobId = comboBox2.SelectedIndex + 1;
                LoadSkillsByJob(jobId);
            }
        }
    }

    private void comboBox3_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (comboBox2.SelectedItem != null && comboBox2.SelectedItem.ToString() == "职业通用")
        {
            int stsx = 1;
            int.TryParse(textBoxSTSX.Text, out stsx);
            switch (comboBox3.SelectedItem.ToString())
            {
                case "全部气功":
                    textBoxSTDM.Text = (800000 + stsx).ToString();
                    break;
                case "升级概率%":
                    textBoxSTDM.Text = (900000 + stsx).ToString();
                    break;
                case "金钱增加%":
                    textBoxSTDM.Text = (1200000 + stsx).ToString();
                    break;
                case "经验值增加%":
                    textBoxSTDM.Text = (1500000 + stsx).ToString();
                    break;
                case "死亡经验减少%":
                    textBoxSTDM.Text = (1300000 + stsx).ToString();
                    break;
            }
        }
        else if (comboBox3.SelectedItem != null)
        {
            if (comboBox3.SelectedItem is KeyValuePair<int, string> kvp)
            {
                if (kvp.Key >= 10 && kvp.Key <= 99) // 两位补全
                {
                    textBoxSTDM.Text = $"80{kvp.Key}01";
                }
                else if (kvp.Key >= 100 && kvp.Key <= 999) // 三位补全
                {
                    textBoxSTDM.Text = $"8{kvp.Key}01";
                }
            }
        }
    }

    private void LoadSkillsByJob(int jobId)
    {
        comboBox3.Items.Clear();

        // 查询获取该职业气功
        string sqlCommand = $"SELECT FLD_PID, FLD_NAME FROM TBL_XWWL_SKILL WHERE FLD_JOB = {jobId}";
        DataTable skillsTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");

        if (skillsTable != null && skillsTable.Rows.Count > 0)
        {
            for (int i = 0; i < skillsTable.Rows.Count; i++)
            {
                int pid = (int)skillsTable.Rows[i]["FLD_PID"];
                string name = skillsTable.Rows[i]["FLD_NAME"].ToString();

                KeyValuePair<int, string> item = new KeyValuePair<int, string>(pid, name);
                comboBox3.Items.Add(item);
            }
        }
        skillsTable?.Dispose();
    }

    private void textBoxPID_TextChanged(object sender, EventArgs e) //EVIAS
    {
        try
        {
            textBoxNUM.Text = "1";
        }
        catch (Exception)
        {
        }
    }

    
    private void button41_Click(object sender, EventArgs e) //Evias 模糊查询
    {
        if (string.IsNullOrWhiteSpace(textBox23.Text))
        {
            tishi.Text = "请输入查询关键字";
            return;
        }

        listBoxALLITEM.Items.Clear();
        textBoxPID.Clear();
        tishi.Text = "查询中...";

        try
        {
            string sql = "SELECT TOP 100 FLD_PID, FLD_NAME FROM TBL_XWWL_ITEM WHERE FLD_NAME LIKE @keyword ORDER BY FLD_NAME";
            var parameters = new SqlParameter[] { new SqlParameter("@keyword", "%" + textBox23.Text.Trim() + "%") };

            DataTable result = DbClss.DBA.GetDBToDataTable(sql, parameters, "PublicDb");

            if (result?.Rows.Count > 0)
            {
                int actualCount = Math.Min(result.Rows.Count, 100);

                foreach (DataRow row in result.Rows)
                {
                    listBoxALLITEM.Items.Add(new KeyValuePair<string, string>(
                        row["FLD_PID"].ToString(),
                        row["FLD_NAME"].ToString()
                    ));
                }

                textBoxPID.Text = result.Rows[0]["FLD_PID"].ToString();
                tishi.Text = $"查询完毕，共显示 {actualCount} 条记录";
            }
            else
            {
                tishi.Text = "未找到匹配记录";
            }
        }
        catch (Exception)
        {
            tishi.Text = "查询失败";
        }
    }



}

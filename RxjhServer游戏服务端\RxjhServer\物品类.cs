using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 物品类 : IDisposable
{
	public bool 锁定;

	private bool _物品绑定;

	private int _物品位置;

	private byte[] _物品_byte;

	private Itimesx _属性1;

	private Itimesx _属性2;

	private Itimesx _属性3;

	private Itimesx _属性4;

    public ConcurrentDictionary<int, 热血石加成> 物品属性_热血石加成;

    private int _物品防御力;

	private int _物品武功回避;

	private int _物品武功命中;

	private int _物品对人战斗力;

	private int _物品隐藏生命;

	private int _物品隐藏攻击;

	private int _物品隐藏防御;

	private int _物品隐藏经验;

	private int _物品增加负重;

	private int _物品对怪防御力;

	private int _物品增加罡气;

	private int _物品对怪攻击力;

	private int _强化锁定气功;

	private int _物品_中级附魂_追加_觉醒;

	private int _前置物品攻击力;

	private int _物品攻击力后置;

	private int _物品属性_障力增加;

	private int _物品属性强类型;

	private int _物品属性强;

	private int _物品属性阶段类型;

	private int _物品属性阶段数;

	private int _物品属性_攻击力增加;

	private int _物品属性_防御力增加;

	private int _物品属性_生命力增加;

	private int _物品属性_障力恢复量增加;

	private int _物品属性_内功力增加;

	private int _物品属性_命中率增加;

	private int _物品属性_回避率增加;

	private int _物品属性_武功攻击力;

	private double _物品属性_对怪伤害;

	private double _隐藏属性_增加百分比攻击;

	private double _隐藏属性_增加百分比防御;

	private double _物品对人战斗力百分比;

	private double _物品属性_降低百分比攻击;

	private double _物品属性_降低百分比防御;

	private double _物品属性_增加百分比命中;

	private double _物品属性_增加百分比回避;

	private double _物品属性_初始化愤怒概率百分比;

	private int _物品属性_愤怒值增加;

	private int _物品属性_全部气功等级增加;

	private int _隐藏属性_全部气功等级增加;

	private int _物品属性_升级成功率;

	private int _物品属性_追加伤害值;

	private int _物品属性_降低伤害值;

	private double _物品属性_追加中毒几率百分比;

	private double _物品属性_追加强化;

	private int _物品属性_武功防御力增加;

	private int _物品属性_获得金钱增加;

	private int _物品属性_死亡损失经验减少;

	private int _物品属性_经验获得增加;

	private int _FLD_RESIDE2;

	private int _FLD_DEAL_LOCK;

	public int _FLD_LEVEL;

	public bool 物品绑定
	{
		get
		{
			try
			{
				byte[] array = new byte[2];
				Buffer.BlockCopy(物品_byte, 76, array, 0, 1);
				return BitConverter.ToInt16(array, 0) != 0;
			}
			catch (Exception)
			{
				return false;
			}
		}
		set
		{
			_物品绑定 = value;
		}
	}

	public long Get物品全局ID => BitConverter.ToInt64(物品_byte, 0);

	public long Get物品ID => BitConverter.ToInt32(物品_byte, 8);

	public int FLD_Intrgration
	{
		get
		{
			try
			{
				if (Get物品ID != 0)
				{
					// 2025-06-14: 修复KeyNotFoundException - 使用安全的字典访问方式
					if (World.Itme.TryGetValue((int)Get物品ID, out ItmeClass itmeClass))
					{
						return itmeClass.FLD_INTEGRATION;
					}
				}
			}
			catch
			{
			}
			return 0;
		}
	}

	public int Get物品数量 => BitConverter.ToInt32(物品_byte, 12);

	public int 物品位置
	{
		get
		{
			return _物品位置;
		}
		set
		{
			_物品位置 = value;
		}
	}

	public int 物品类型 => 得到物品类型();

	public int 物品单个重量 => 得到物品单个重量();

	public int 物品总重量 => 得到物品重量();

	public byte[] 物品_byte
	{
		get
		{
			return _物品_byte;
		}
		set
		{
			锁定 = false;
			_物品_byte = value;
		}
	}

	public byte[] 物品数量
	{
		get
		{
			return 得到物品数量();
		}
		set
		{
			设置物品数量(value);
		}
	}

	public byte[] 物品ID => 得到物品ID();

	public byte[] 物品全局ID => 得到全局ID();

	public byte[] 物品属性 => 得到物品属性();

	public Itimesx 属性1
	{
		get
		{
			byte[] dst = new byte[4];
			Buffer.BlockCopy(物品_byte, 20, dst, 0, 4);
			属性1 = new Itimesx(dst);
			return _属性1;
		}
		set
		{
			_属性1 = value;
		}
	}

	public Itimesx 属性2
	{
		get
		{
			byte[] dst = new byte[4];
			Buffer.BlockCopy(物品_byte, 24, dst, 0, 4);
			属性2 = new Itimesx(dst);
			return _属性2;
		}
		set
		{
			_属性2 = value;
		}
	}

	public Itimesx 属性3
	{
		get
		{
			byte[] dst = new byte[4];
			Buffer.BlockCopy(物品_byte, 28, dst, 0, 4);
			属性3 = new Itimesx(dst);
			return _属性3;
		}
		set
		{
			_属性3 = value;
		}
	}

	public Itimesx 属性4
	{
		get
		{
			byte[] dst = new byte[4];
			Buffer.BlockCopy(物品_byte, 32, dst, 0, 4);
			属性4 = new Itimesx(dst);
			return _属性4;
		}
		set
		{
			_属性4 = value;
		}
	}

	public int 物品防御力
	{
		get
		{
			return _物品防御力;
		}
		set
		{
			_物品防御力 = value;
		}
	}

	public int 物品武功回避
	{
		get
		{
			return _物品武功回避;
		}
		set
		{
			_物品武功回避 = value;
		}
	}

	public int 物品武功命中
	{
		get
		{
			return _物品武功命中;
		}
		set
		{
			_物品武功命中 = value;
		}
	}

	public int 物品隐藏生命
	{
		get
		{
			return _物品隐藏生命;
		}
		set
		{
			_物品隐藏生命 = value;
		}
	}

	public int 物品隐藏攻击
	{
		get
		{
			return _物品隐藏攻击;
		}
		set
		{
			_物品隐藏攻击 = value;
		}
	}

	public int 物品对人战斗力
	{
		get
		{
			return _物品对人战斗力;
		}
		set
		{
			_物品对人战斗力 = value;
		}
	}

	public int 物品隐藏防御
	{
		get
		{
			return _物品隐藏防御;
		}
		set
		{
			_物品隐藏防御 = value;
		}
	}

	public int 物品隐藏经验
	{
		get
		{
			return _物品隐藏经验;
		}
		set
		{
			_物品隐藏经验 = value;
		}
	}

	public int 物品增加负重
	{
		get
		{
			return _物品增加负重;
		}
		set
		{
			_物品增加负重 = value;
		}
	}

	public int 物品对怪防御力
	{
		get
		{
			return _物品对怪防御力;
		}
		set
		{
			_物品对怪防御力 = value;
		}
	}

	public int 物品增加罡气
	{
		get
		{
			return _物品增加罡气;
		}
		set
		{
			_物品增加罡气 = value;
		}
	}

	public int 物品对怪攻击力
	{
		get
		{
			return _物品对怪攻击力;
		}
		set
		{
			_物品对怪攻击力 = value;
		}
	}

	public int 强化锁定气功
	{
		get
		{
			return _强化锁定气功;
		}
		set
		{
			_强化锁定气功 = value;
		}
	}

	public int 物品_中级附魂_追加_觉醒
	{
		get
		{
			return _物品_中级附魂_追加_觉醒;
		}
		set
		{
			_物品_中级附魂_追加_觉醒 = value;
		}
	}

	public int 前置物品攻击力
	{
		get
		{
			return _前置物品攻击力;
		}
		set
		{
			_前置物品攻击力 = value;
		}
	}

	public int 物品攻击力后置
	{
		get
		{
			return _物品攻击力后置;
		}
		set
		{
			_物品攻击力后置 = value;
		}
	}

	public int 物品属性_障力增加
	{
		get
		{
			return _物品属性_障力增加;
		}
		set
		{
			_物品属性_障力增加 = value;
		}
	}

	public int 物品属性强类型
	{
		get
		{
			return _物品属性强类型;
		}
		set
		{
			_物品属性强类型 = value;
		}
	}

	public int 物品属性强
	{
		get
		{
			return _物品属性强;
		}
		set
		{
			_物品属性强 = value;
		}
	}

	public int 物品属性阶段类型
	{
		get
		{
			return _物品属性阶段类型;
		}
		set
		{
			_物品属性阶段类型 = value;
		}
	}

	public int 物品属性阶段数
	{
		get
		{
			return _物品属性阶段数;
		}
		set
		{
			_物品属性阶段数 = value;
		}
	}

	public int 物品属性_攻击力增加
	{
		get
		{
			return _物品属性_攻击力增加;
		}
		set
		{
			_物品属性_攻击力增加 = value;
		}
	}

	public int 物品属性_防御力增加
	{
		get
		{
			return _物品属性_防御力增加;
		}
		set
		{
			_物品属性_防御力增加 = value;
		}
	}

	public int 物品属性_生命力增加
	{
		get
		{
			return _物品属性_生命力增加;
		}
		set
		{
			_物品属性_生命力增加 = value;
		}
	}

	public int 物品属性_障力恢复量增加
	{
		get
		{
			return _物品属性_障力恢复量增加;
		}
		set
		{
			_物品属性_障力恢复量增加 = value;
		}
	}

	public int 物品属性_内功力增加
	{
		get
		{
			return _物品属性_内功力增加;
		}
		set
		{
			_物品属性_内功力增加 = value;
		}
	}

	public int 物品属性_命中率增加
	{
		get
		{
			return _物品属性_命中率增加;
		}
		set
		{
			_物品属性_命中率增加 = value;
		}
	}

	public int 物品属性_回避率增加
	{
		get
		{
			return _物品属性_回避率增加;
		}
		set
		{
			_物品属性_回避率增加 = value;
		}
	}

	public int 物品属性_武功攻击力
	{
		get
		{
			return _物品属性_武功攻击力;
		}
		set
		{
			_物品属性_武功攻击力 = value;
		}
	}

	public double 物品属性_降低百分比攻击
	{
		get
		{
			return _物品属性_降低百分比攻击;
		}
		set
		{
			_物品属性_降低百分比攻击 = value;
		}
	}

	public double 物品属性_降低百分比防御
	{
		get
		{
			return _物品属性_降低百分比防御;
		}
		set
		{
			_物品属性_降低百分比防御 = value;
		}
	}

	public double 隐藏属性_增加百分比攻击
	{
		get
		{
			return _隐藏属性_增加百分比攻击;
		}
		set
		{
			_隐藏属性_增加百分比攻击 = value;
		}
	}

	public double 隐藏属性_增加百分比防御
	{
		get
		{
			return _隐藏属性_增加百分比防御;
		}
		set
		{
			_隐藏属性_增加百分比防御 = value;
		}
	}

	public double 物品对人战斗力百分比
	{
		get
		{
			return _物品对人战斗力百分比;
		}
		set
		{
			_物品对人战斗力百分比 = value;
		}
	}

	public double 物品属性_对怪伤害
	{
		get
		{
			return _物品属性_对怪伤害;
		}
		set
		{
			_物品属性_对怪伤害 = value;
		}
	}

	public double 物品属性_增加百分比命中
	{
		get
		{
			return _物品属性_增加百分比命中;
		}
		set
		{
			_物品属性_增加百分比命中 = value;
		}
	}

	public double 物品属性_增加百分比回避
	{
		get
		{
			return _物品属性_增加百分比回避;
		}
		set
		{
			_物品属性_增加百分比回避 = value;
		}
	}

	public double 物品属性_初始化愤怒概率百分比
	{
		get
		{
			return _物品属性_初始化愤怒概率百分比;
		}
		set
		{
			_物品属性_初始化愤怒概率百分比 = value;
		}
	}

	public int 物品属性_愤怒值增加
	{
		get
		{
			return _物品属性_愤怒值增加;
		}
		set
		{
			_物品属性_愤怒值增加 = value;
		}
	}

	public int 物品属性_全部气功等级增加
	{
		get
		{
			return _物品属性_全部气功等级增加;
		}
		set
		{
			_物品属性_全部气功等级增加 = value;
		}
	}

	public int 隐藏属性_全部气功等级增加
	{
		get
		{
			return _隐藏属性_全部气功等级增加;
		}
		set
		{
			_隐藏属性_全部气功等级增加 = value;
		}
	}

	public int 物品属性_升级成功率
	{
		get
		{
			return _物品属性_升级成功率;
		}
		set
		{
			_物品属性_升级成功率 = value;
		}
	}

	public int 物品属性_追加伤害值
	{
		get
		{
			return _物品属性_追加伤害值;
		}
		set
		{
			_物品属性_追加伤害值 = value;
		}
	}

	public int 物品属性_降低伤害值
	{
		get
		{
			return _物品属性_降低伤害值;
		}
		set
		{
			_物品属性_降低伤害值 = value;
		}
	}

	public double 物品属性_追加中毒几率百分比
	{
		get
		{
			return _物品属性_追加中毒几率百分比;
		}
		set
		{
			_物品属性_追加中毒几率百分比 = value;
		}
	}

	public double 物品属性_追加强化
	{
		get
		{
			return _物品属性_追加强化;
		}
		set
		{
			_物品属性_追加强化 = value;
		}
	}

	public int 物品属性_武功防御力增加
	{
		get
		{
			return _物品属性_武功防御力增加;
		}
		set
		{
			_物品属性_武功防御力增加 = value;
		}
	}

	public int 物品属性_获得金钱增加
	{
		get
		{
			return _物品属性_获得金钱增加;
		}
		set
		{
			_物品属性_获得金钱增加 = value;
		}
	}

	public int 物品属性_死亡损失经验减少
	{
		get
		{
			return _物品属性_死亡损失经验减少;
		}
		set
		{
			_物品属性_死亡损失经验减少 = value;
		}
	}

	public int 物品属性_经验获得增加
	{
		get
		{
			return _物品属性_经验获得增加;
		}
		set
		{
			_物品属性_经验获得增加 = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_RESIDE2
	{
		get
		{
			return _FLD_RESIDE2;
		}
		set
		{
			_FLD_RESIDE2 = value;
		}
	}

	public int FLD_DEAL_LOCK
	{
		get
		{
			return _FLD_DEAL_LOCK;
		}
		set
		{
			_FLD_DEAL_LOCK = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 16, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 16, 4);
		}
	}

    /*public int FLD_强化类型
	{
		get
		{
			int fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC <= 0)
			{
				return 0;
			}
			string text = fLD_MAGIC.ToString();
			return int.Parse(text.Substring(text.Length - 8, 1));
		}
	}

    public int FLD_强化数量
    {
        get
        {
            int fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0)
            {
                return 0;
            }
            if (Get物品ID >= 800000046 && Get物品ID <= 800000049) //EVIAS
            {
                return fLD_MAGIC;
            }
            string text = fLD_MAGIC.ToString();
            return int.Parse(text.Substring(text.Length - 2, 2));
        }
    }

    public int FLD_属性类型
	{
		get
		{
			int fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC <= 0 || fLD_MAGIC <= 1000000000)
			{
				return 0;
			}
			string text = fLD_MAGIC.ToString();
			return int.Parse(text.Substring(text.Length - 4, 1));
		}
	}

	public int FLD_属性数量
	{
		get
		{
			int fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC <= 0 || fLD_MAGIC <= 1000000000)
			{
				return 0;
			}
			string text = fLD_MAGIC.ToString();
			return int.Parse(text.Substring(text.Length - 3, 1));
		}
	}*/

    public int FLD_强化类型
    {
        get
        {
            int fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0)
            {
                return 0;
            }

            if (Get物品ID >= 800000046 && Get物品ID <= 800000049)
            {
                return 0; 
            }

            string text = fLD_MAGIC.ToString();

            if (text.Length < 8)
            {
                return 0; // 非强化装备返回0
            }

            return int.Parse(text.Substring(text.Length - 8, 1));
        }
    }

    public int FLD_强化数量
    {
        get
        {
            int fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0)
            {
                return 0;
            }

            if (Get物品ID >= 800000046 && Get物品ID <= 800000049)
            {
                return fLD_MAGIC;
            }

            string text = fLD_MAGIC.ToString();

            if (text.Length < 2)
            {
                return 0;
            }

            return int.Parse(text.Substring(text.Length - 2, 2));
        }
    }

    public int FLD_属性类型
    {
        get
        {
            int fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0 || fLD_MAGIC <= 1000000000)
            {
                return 0;
            }

            string text = fLD_MAGIC.ToString();

            if (text.Length < 4)
            {
                return 0;
            }

            return int.Parse(text.Substring(text.Length - 4, 1));
        }
    }

    public int FLD_属性数量
    {
        get
        {
            int fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0 || fLD_MAGIC <= 1000000000)
            {
                return 0;
            }

            string text = fLD_MAGIC.ToString();

            if (text.Length < 3)
            {
                return 0;
            }

            return int.Parse(text.Substring(text.Length - 3, 1));
        }
    }

    public int FLD_MAGIC1
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 20, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 20, 4);
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 24, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 24, 4);
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 28, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 28, 4);
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 32, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 32, 4);
		}
	}

	public int FLD_FJ_MAGIC0
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 36, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 36, 2);
		}
	}

	public int FLD_FJ_MAGIC1
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 38, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 38, 2);
		}
	}

	public int FLD_FJ_中级附魂
	{
		get
		{
			try
			{
				byte[] array = new byte[2];
				Buffer.BlockCopy(物品_byte, 40, array, 0, 2);
				return BitConverter.ToInt16(array, 0);
			}
			catch (Exception ex)
			{
				RxjhServer.DbClss.RxjhClass.HandleGameException(ex, null, "FLD_FJ_中级附魂_Get", $"物品: {得到物品string()}");
				Form1.WriteLine(1, "FLD_FJ_中级附魂   Get出错：   [" + 得到物品string() + "]" + ex);
				return 0;
			}
		}
		set
		{
			try
			{
				if (value > 0)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(1), 0, 物品_byte, 38, 2);
				}
				else if (value == 0)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(0), 0, 物品_byte, 38, 2);
				}
				Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 40, 2);
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "FLD_FJ_中级附魂   Set出错：   [" + 得到物品string() + "]" + ex);
			}
		}
	}

	public int FLD_FJ_MAGIC2
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 42, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 42, 2);
		}
	}

	public int FLD_FJ_MAGIC3
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 44, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 44, 2);
		}
	}

	public int FLD_FJ_MAGIC4
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 46, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 46, 2);
		}
	}

	public int FLD_FJ_MAGIC5
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 48, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 48, 2);
		}
	}

	public int FLD_JSSJ
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 56, array, 0, 4);
			DateTime dateTime = new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(BitConverter.ToInt32(array, 0));
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 56, 4);
		}
	}

	public int FLD_KSSJ
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 52, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 52, 4);
		}
	}

	public int FLD_道具锁时间
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 72, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 72, 4);
		}
	}

	public int FLD_FJ_觉醒
	{
		get
		{
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 62, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 62, 4);
		}
	}

	public int FLD_FJ_NJ
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 60, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 60, 2);
		}
	}

	public int FLD_FJ_进化
	{
		get
		{
			try
			{
				byte[] array = new byte[2];  // 2025-05-19: 修复数组大小，与实际复制字节数一致
				Buffer.BlockCopy(物品_byte, 68, array, 0, 2);
				return BitConverter.ToInt16(array, 0);  // 2025-05-19: 使用ToInt16读取2字节数据
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "FLD_FJ_进化 Get出错：[" + 得到物品string() + "] " + ex.Message);
				return 0;
			}
		}
		set
		{
			try
			{
				Buffer.BlockCopy(BitConverter.GetBytes((short)value), 0, 物品_byte, 68, 2);  // 2025-05-19: 确保写入2字节
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "FLD_FJ_进化 Set出错：[" + 得到物品string() + "] " + ex.Message);
			}
		}
	}

	public int FLD_FJ_四神之力
	{
		get
		{
			byte[] array = new byte[2];
			Buffer.BlockCopy(物品_byte, 71, array, 0, 1);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 物品_byte, 71, 1);
		}
	}

	public int 得到装备热血石单气功加成值(int 气功ID)
	{
		foreach (热血石加成 value in 物品属性_热血石加成.Values)
		{
			if (value.气功ID == 气功ID)
			{
				return value.加成值;
			}
		}
		return 0;
	}

	public byte[] GetByte()
	{
		using 发包类 发包类 = new 发包类();
		发包类.Write(Get物品全局ID);
		if (物品绑定)
		{
			发包类.Write(Get物品ID + 20000);
		}
		else
		{
			发包类.Write(Get物品ID);
		}
		发包类.Write4(Get物品数量);
		发包类.Write4(FLD_MAGIC0);
		发包类.Write4(FLD_MAGIC1);
		发包类.Write4(FLD_MAGIC2);
		发包类.Write4(FLD_MAGIC3);
		发包类.Write4(FLD_MAGIC4);
		发包类.Write2(FLD_FJ_MAGIC0);
		发包类.Write2(FLD_FJ_MAGIC1);
		发包类.Write2(FLD_FJ_中级附魂);
		发包类.Write2(FLD_FJ_MAGIC2);
		发包类.Write2(FLD_FJ_MAGIC3);
		发包类.Write2(FLD_FJ_MAGIC4);
		发包类.Write2(FLD_FJ_MAGIC5);
		发包类.Write2(0);
		发包类.Write4(FLD_KSSJ);
		发包类.Write4(FLD_JSSJ);
		发包类.Write2(FLD_FJ_NJ);
		发包类.Write2(FLD_FJ_觉醒 + 物品_中级附魂_追加_觉醒);
		发包类.Write4((FLD_FJ_进化 == 2) ? 1 : 0);
		发包类.Write2(FLD_FJ_进化);
		发包类.Write2(0);
		发包类.Write4(FLD_FJ_四神之力);
		发包类.Write4(0);
		发包类.Write4(FLD_道具锁时间);
		发包类.Write4(0);
		发包类.Write4(0);
		return 发包类.ToArray3();
	}

	public byte[] GetBytePF(long pf)
	{
		using 发包类 发包类 = new 发包类();
		发包类.Write(Get物品全局ID);
		if (物品绑定)
		{
			发包类.Write(pf + 20000);
		}
		else
		{
			发包类.Write(pf);
		}
		发包类.Write4(Get物品数量);
		发包类.Write4(FLD_MAGIC0);
		发包类.Write4(FLD_MAGIC1);
		发包类.Write4(FLD_MAGIC2);
		发包类.Write4(FLD_MAGIC3);
		发包类.Write4(FLD_MAGIC4);
		发包类.Write2(FLD_FJ_MAGIC0);
		发包类.Write2(FLD_FJ_MAGIC1);
		发包类.Write2(FLD_FJ_中级附魂);
		发包类.Write2(FLD_FJ_MAGIC2);
		发包类.Write2(FLD_FJ_MAGIC3);
		发包类.Write2(FLD_FJ_MAGIC4);
		发包类.Write2(FLD_FJ_MAGIC5);
		发包类.Write2(0);
		发包类.Write4(FLD_KSSJ);
		发包类.Write4(FLD_JSSJ);
		发包类.Write2(FLD_FJ_NJ);
		发包类.Write2(FLD_FJ_觉醒 + 物品_中级附魂_追加_觉醒);
		发包类.Write4((FLD_FJ_进化 == 2) ? 1 : 0);
		发包类.Write2(FLD_FJ_进化);
		发包类.Write2(0);
		发包类.Write4(FLD_FJ_四神之力);
		发包类.Write4(0);
		发包类.Write4(FLD_道具锁时间);
		发包类.Write4(0);
		发包类.Write4(0);
		return 发包类.ToArray3();
	}

	public void Dispose()
	{
	}

	public 物品类(byte[] 物品_byte_)
	{
		物品_byte = 物品_byte_;
        物品属性_热血石加成 = new ConcurrentDictionary<int, 热血石加成>();
    }

	public 物品类(byte[] 物品_byte_, int 位置)
	{
		物品_byte = 物品_byte_;
		物品位置 = 位置;
        物品属性_热血石加成 = new ConcurrentDictionary<int, 热血石加成>();
      
    }

	public string 得到物品string()
	{
		try
		{
			return Converter.ToString(物品_byte);
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "得到物品string", $"物品位置: {物品位置}");
			return string.Empty;
		}
	}

	public int 得到物品单个重量()
	{
		try
		{
			ItmeClass value;
			return World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out value) ? value.FLD_WEIGHT : 0;
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "得到物品单个重量", $"物品ID: {BitConverter.ToInt32(物品ID, 0)}");
			return 0;
		}
	}

	public int 得到物品重量()
	{
		try
		{
			ItmeClass value;
			return World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out value) ? (value.FLD_WEIGHT * BitConverter.ToInt32(物品数量, 0)) : 0;
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "得到物品重量", $"物品ID: {BitConverter.ToInt32(物品ID, 0)}, 数量: {BitConverter.ToInt32(物品数量, 0)}");
			return 0;
		}
	}

	public byte[] 得到物品属性()
	{
		byte[] array = new byte[60];
		Buffer.BlockCopy(物品_byte, 16, array, 0, 60);
		return array;
	}

	public byte[] 得到全局ID()
	{
		byte[] array = new byte[8];
		Buffer.BlockCopy(物品_byte, 0, array, 0, 8);
		return array;
	}

	public byte[] 得到物品ID()
	{
		byte[] array = new byte[4];
		Buffer.BlockCopy(物品_byte, 8, array, 0, 4);
		return array;
	}

	public byte[] 得到物品数量()
	{
		byte[] array = new byte[4];
		Buffer.BlockCopy(物品_byte, 12, array, 0, 4);
		return array;
	}

	public void 设置物品数量(byte[] 数量)
	{
		Buffer.BlockCopy(数量, 0, 物品_byte, 12, 4);
	}

	public int 得到物品类型()
	{
		int key = BitConverter.ToInt32(得到物品ID(), 0);
		// 2025-06-14: 修复KeyNotFoundException - 使用安全的字典访问方式
		if (World.Itme.TryGetValue(key, out ItmeClass itmeClass))
		{
			return itmeClass.FLD_SIDE;
		}
		return 0;
	}

	public int 得到物品位置类型()
	{
		byte[] value = 得到物品ID();
		ItmeClass value2;
		return World.Itme.TryGetValue(BitConverter.ToInt32(value, 0), out value2) ? value2.FLD_RESIDE2 : 0;
	}

	public string 得到物品名称()
	{
		ItmeClass value;
		return World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out value) ? value.ItmeNAME : string.Empty;
	}

	private void 清空物品属性方法()
	{
		前置物品攻击力 = 0;
		物品攻击力后置 = 0;
		物品属性_攻击力增加 = 0;
		物品属性_障力增加 = 0;
		物品防御力 = 0;
		物品武功回避 = 0;
		物品武功命中 = 0;
		物品增加负重 = 0;
		物品属性_防御力增加 = 0;
		物品属性_生命力增加 = 0;
		物品属性_障力恢复量增加 = 0;
		物品属性_内功力增加 = 0;
		物品属性_命中率增加 = 0;
		物品属性_回避率增加 = 0;
		物品属性_武功攻击力 = 0;
		物品属性_降低百分比防御 = 0.0;
		物品属性_降低百分比攻击 = 0.0;
		隐藏属性_增加百分比防御 = 0.0;
		物品对人战斗力百分比 = 0.0;
		隐藏属性_增加百分比攻击 = 0.0;
		物品属性_对怪伤害 = 0.0;
		物品属性_增加百分比命中 = 0.0;
		物品属性_增加百分比回避 = 0.0;
		物品属性_初始化愤怒概率百分比 = 0.0;
		物品属性_愤怒值增加 = 0;
		物品属性_追加中毒几率百分比 = 0.0;
		物品属性_降低伤害值 = 0;
		物品属性_全部气功等级增加 = 0;
		隐藏属性_全部气功等级增加 = 0;
		物品属性_升级成功率 = 0;
		物品属性_追加伤害值 = 0;
		物品属性_武功防御力增加 = 0;
		物品属性_获得金钱增加 = 0;
		物品属性_死亡损失经验减少 = 0;
		物品属性_经验获得增加 = 0;
		物品_中级附魂_追加_觉醒 = 0;
		FLD_RESIDE2 = 0;
		FLD_LEVEL = 0;
		物品属性_追加强化 = 0.0;
		物品对怪防御力 = 0;
		物品对怪攻击力 = 0;
		强化锁定气功 = 0;
		物品对人战斗力 = 0;
		物品增加罡气 = 0;
		if (物品属性_热血石加成 == null)
		{
        	物品属性_热血石加成 = new ConcurrentDictionary<int, 热血石加成>();
        }
		else
		{
			物品属性_热血石加成.Clear();
		}
	}

	public void 得到灵兽物品属性方法()
	{
		try
		{
			清空物品属性方法();
			if (BitConverter.ToInt32(物品ID, 0) != 0)
			{
				得到物品基本攻击力();
				byte[] array = new byte[4];
				Buffer.BlockCopy(物品_byte, 16, array, 0, 4);
				得到灵兽强化(BitConverter.ToInt32(array, 0).ToString());
				for (int i = 0; i < 4; i++)
				{
					byte[] array2 = new byte[4];
					Buffer.BlockCopy(物品_byte, 20 + i * 4, array2, 0, 4);
					得到基本属性(BitConverter.ToInt32(array2, 0).ToString());
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "得到物品属性方法 出错：" + ex);
		}
	}

	private void 得到灵兽强化(string 强化数)
	{
		try
		{
			if (!World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out var value))
			{
				return;
			}
			int length = 强化数.Length;
			int num = length;
			if (num != 9)
			{
				return;
			}
			int num2 = 0;
			byte[] array = new byte[4];
			Buffer.BlockCopy(物品_byte, 20, array, 0, 4);
			string text = BitConverter.ToInt32(array, 0).ToString();
			if (text != "0")
			{
				num2 = int.Parse(text.Substring(text.Length - 2, 2));
			}
			物品属性强类型 = int.Parse(强化数.Substring(强化数.Length - 9, 2));
			物品属性强 = int.Parse(强化数) - int.Parse(强化数.Substring(0, 2)) * 10000000;
			if (物品属性强类型 != 19 || FLD_RESIDE2 != 16 || !World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out value))
			{
				return;
			}
			int num3 = 0;
			int num4 = 0;
			int num5 = 0;
			if (物品属性强 >= 0 && 物品属性强 <= World.灵宠强化最大数量)
			{
				switch (num2)
				{
				case 0:
					if (物品属性强 % 2 == 0)
					{
						num4 = (int)((double)物品属性强 * 1.5) + 4;
						num3 = (int)((double)物品属性强 * 0.5) + 4;
					}
					else
					{
						num3 = (int)((double)物品属性强 * 0.5) + 5;
						num4 = (int)((double)物品属性强 * 1.5) + 5;
					}
					break;
				case 1:
					if (物品属性强 % 2 == 0)
					{
						num3 = (int)((double)物品属性强 * 0.6) + 5;
						num4 = 物品属性强 * 2 + 16;
					}
					else
					{
						num3 = (int)((double)物品属性强 * 0.6) + 6;
						num4 = 物品属性强 * 2 + 18;
					}
					break;
				case 2:
					if (物品属性强 % 2 == 0)
					{
						num3 = (int)((double)物品属性强 * 0.7) + 8;
						num4 = 物品属性强 * 4 + 32;
					}
					else
					{
						num3 = (int)((double)物品属性强 * 0.7) + 9;
						num4 = 物品属性强 * 4 + 32 + 4;
					}
					break;
				case 3:
					if (物品属性强 % 2 == 0)
					{
						num3 = 物品属性强 + 8;
						num4 = 物品属性强 * 8 + 64;
					}
					else
					{
						num3 = 物品属性强 + 9;
						num4 = 物品属性强 * 8 + 64 + 8;
					}
					break;
				case 4:
					if (物品属性强 % 2 == 0)
					{
						num3 = 物品属性强 + 8;
						num4 = 物品属性强 * 8 + 64;
						num5 = 物品属性强 * 5 + 40;
					}
					else
					{
						num3 = 物品属性强 + 9;
						num4 = 物品属性强 * 8 + 64 + 8;
						num5 = 物品属性强 * 5 + 40 + 5;
					}
					break;
				case 5:
					if (物品属性强 % 2 == 0)
					{
						num3 = 物品属性强 + 8;
						num4 = 物品属性强 * 8 + 64;
						num5 = 物品属性强 * 6 + 88;
					}
					else
					{
						num3 = 物品属性强 + 9;
						num4 = 物品属性强 * 8 + 64 + 8;
						num5 = 物品属性强 * 6 + 88 + 6;
					}
					break;
				case 6:
					if (物品属性强 % 2 == 0)
					{
						num3 = 物品属性强 + 8;
						num4 = 物品属性强 * 8 + 64;
						num5 = 物品属性强 * 7 + 136;
					}
					else
					{
						num3 = 物品属性强 + 9;
						num4 = 物品属性强 * 8 + 64 + 8;
						num5 = 物品属性强 * 7 + 136 + 7;
					}
					break;
				case 7:
					if (物品属性强 % 2 == 0)
					{
						num3 = 物品属性强 + 8;
						num4 = 物品属性强 * 8 + 64;
						num5 = 物品属性强 * 9 + 182;
					}
					else
					{
						num3 = 物品属性强 + 9;
						num4 = 物品属性强 * 8 + 64 + 8;
						num5 = 物品属性强 * 9 + 182 + 9;
					}
					break;
				}
			}
			前置物品攻击力 += num3;
			物品攻击力后置 += num3;
			物品防御力 += num3;
			物品对怪攻击力 += num4;
			物品对怪防御力 += num4;
			物品增加罡气 += num5;
			if (物品属性强 >= 2)
			{
				if (物品属性强 % 2 == 0)
				{
					物品属性_生命力增加 += 物品属性强 * 10 - ((物品属性强 - 1) * 5 - 5);
				}
				else
				{
					物品属性_生命力增加 += 物品属性强 * 10 - (物品属性强 - 1) * 5;
				}
				if (物品属性强 >= 100)
				{
					物品属性_生命力增加 += 550;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "得到强化 出错：" + ex);
		}
	}

	public static byte[] CreatNewItem(int PID, long ItemWordID, int number)
	{
		byte[] array = new byte[77];
		Buffer.BlockCopy(BitConverter.GetBytes(ItemWordID), 0, array, 0, 8);
		Buffer.BlockCopy(BitConverter.GetBytes(PID), 0, array, 8, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(number), 0, array, 12, 4);
		return array;
	}

	public void 得到物品属性方法(int 追加强化, int 触发属性提升)
	{
		try
		{
			清空物品属性方法();
			if (BitConverter.ToInt32(物品ID, 0) != 0)
			{
				得到物品基本攻击力();
				物品属性_追加强化 = 追加强化;
				byte[] array = new byte[4];
				Buffer.BlockCopy(物品_byte, 16, array, 0, 4);
				得到强化(BitConverter.ToInt32(array, 0).ToString(), 触发属性提升);
				for (int i = 0; i < 4; i++)
				{
					byte[] array2 = new byte[4];
					Buffer.BlockCopy(物品_byte, 20 + i * 4, array2, 0, 4);
					得到基本属性(BitConverter.ToInt32(array2, 0).ToString());
				}
			}
		}
		catch (Exception ex)
		{
			// 2025-0617 EVIAS 改进异常处理
			RxjhClass.HandleGameException(ex, null, "得到物品属性方法", $"物品ID: {BitConverter.ToInt32(物品ID, 0)}, 追加强化: {追加强化}");
			Form1.WriteLine(1, "得到物品属性方法   出错：" + ex);
		}
	}

	public void 得到物品基本攻击力()
	{
		try
		{
			if (BitConverter.ToInt32(物品ID, 0) == 0)
			{
				return;
			}
			// 2025-06-14: 使用安全的字典访问方式
			if (!World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out ItmeClass itmeClass))
			{
				return;
			}
			FLD_RESIDE2 = itmeClass.FLD_RESIDE2;
			FLD_DEAL_LOCK = itmeClass.FLD_DEAL_LOCK;
			FLD_LEVEL = itmeClass.FLD_LEVEL;
			switch (FLD_RESIDE2)
			{
			case 1:
			case 2:
			case 5:
			case 6:
				物品属性_障力增加 = itmeClass.FLD_LEVEL;
				if (FLD_FJ_进化 == 0)
				{
					物品防御力 = itmeClass.FLD_DF;
				}
				else if (FLD_FJ_进化 == 1)
				{
					物品防御力 = (int)((double)itmeClass.FLD_DF + (double)itmeClass.FLD_DF * 0.1);
				}
				else if (FLD_FJ_进化 == 2)
				{
					物品防御力 = (int)((double)itmeClass.FLD_DF + (double)itmeClass.FLD_DF * 0.153);
				}
				物品隐藏生命 = itmeClass.FLD_YCHP;
				物品隐藏攻击 = itmeClass.FLD_YCAT;
				物品隐藏防御 = itmeClass.FLD_YCDF;
				物品隐藏经验 = itmeClass.FLD_YCJY;
				隐藏属性_全部气功等级增加 = itmeClass.FLD_YCQG;
				隐藏属性_增加百分比攻击 = (double)itmeClass.FLD_ATBFB * 0.01;
				隐藏属性_增加百分比防御 = (double)itmeClass.FLD_DFBFB * 0.01;
				break;
			case 4:
				物品属性_障力增加 = itmeClass.FLD_LEVEL;
				if (FLD_FJ_进化 == 0)
				{
					前置物品攻击力 = itmeClass.FLD_AT;
					物品攻击力后置 = itmeClass.FLD_AT_Max;
				}
				else if (FLD_FJ_进化 == 1)
				{
					前置物品攻击力 = (int)((double)itmeClass.FLD_AT + (double)itmeClass.FLD_AT * 0.05);
					物品攻击力后置 = (int)((double)itmeClass.FLD_AT_Max + (double)itmeClass.FLD_AT_Max * 0.05);
				}
				else if (FLD_FJ_进化 == 2)
				{
					前置物品攻击力 = (int)((double)itmeClass.FLD_AT + (double)itmeClass.FLD_AT * 0.08);
					物品攻击力后置 = (int)((double)itmeClass.FLD_AT_Max + (double)itmeClass.FLD_AT_Max * 0.08);
				}
				物品隐藏生命 = itmeClass.FLD_YCHP;
				物品隐藏攻击 = itmeClass.FLD_YCAT;
				物品隐藏防御 = itmeClass.FLD_YCDF;
				物品隐藏经验 = itmeClass.FLD_YCJY;
				隐藏属性_全部气功等级增加 = itmeClass.FLD_YCQG;
				隐藏属性_增加百分比攻击 = (double)itmeClass.FLD_ATBFB * 0.01;
				隐藏属性_增加百分比防御 = (double)itmeClass.FLD_DFBFB * 0.01;
				break;
			case 13:
				前置物品攻击力 = itmeClass.FLD_AT;
				物品攻击力后置 = itmeClass.FLD_AT_Max;
				物品隐藏生命 = itmeClass.FLD_YCHP;
				物品隐藏攻击 = itmeClass.FLD_YCAT;
				物品隐藏防御 = itmeClass.FLD_YCDF;
				物品隐藏经验 = itmeClass.FLD_YCJY;
				隐藏属性_全部气功等级增加 = itmeClass.FLD_YCQG;
				隐藏属性_增加百分比攻击 = (double)itmeClass.FLD_ATBFB * 0.01;
				隐藏属性_增加百分比防御 = (double)itmeClass.FLD_DFBFB * 0.01;
				break;
			case 7:
			case 8:
			case 10:
				物品属性_障力增加 = itmeClass.FLD_LEVEL;
				物品隐藏生命 = itmeClass.FLD_YCHP;
				物品隐藏攻击 = itmeClass.FLD_YCAT;
				物品隐藏防御 = itmeClass.FLD_YCDF;
				物品隐藏经验 = itmeClass.FLD_YCJY;
				隐藏属性_全部气功等级增加 = itmeClass.FLD_YCQG;
				隐藏属性_增加百分比攻击 = (double)itmeClass.FLD_ATBFB * 0.01;
				隐藏属性_增加百分比防御 = (double)itmeClass.FLD_DFBFB * 0.01;
				break;
			case 12:
			case 14:
			case 15:
			case 16:
			case 32:
			case 33:
			case 34:
			case 35:
			case 36:
				物品隐藏生命 = itmeClass.FLD_YCHP;
				物品隐藏攻击 = itmeClass.FLD_YCAT;
				物品隐藏防御 = itmeClass.FLD_YCDF;
				物品隐藏经验 = itmeClass.FLD_YCJY;
				隐藏属性_全部气功等级增加 = itmeClass.FLD_YCQG;
				隐藏属性_增加百分比攻击 = (double)itmeClass.FLD_ATBFB * 0.01;
				隐藏属性_增加百分比防御 = (double)itmeClass.FLD_DFBFB * 0.01;
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "得到物品基本攻击力出错：" + ex);
		}
	}

	private int 得到基本属性控制(int 类型, int 数量)
	{
		foreach (石头属性调整类 value in World.石头属性调整.Values)
		{
			if (value.类型 == 类型 && value.数量 == 数量)
			{
				return value.加减;
			}
		}
		return 0;
	}

    private void 得到基本属性(string ysqh)
    {
        try
        {
            string text;
            switch (ysqh.Length)
            {
                default:
                    return;
                case 9:
                    text = ysqh.Substring(0, 2);
                    break;
                case 8:
                    text = ysqh.Substring(0, 1);
                    break;
            }
            int num = ((World.是否支持扩展物品属性位数 == 0) ? ((!string.Equals(text, "8")) ? int.Parse(ysqh.Substring(ysqh.Length - 3, 3)) : int.Parse(ysqh.Substring(ysqh.Length - 2, 2))) : ((!string.Equals(text, "8")) ? (int.Parse(ysqh) - int.Parse(text) * 10000000) : int.Parse(ysqh.Substring(ysqh.Length - 2, 2))));
            num += 得到基本属性控制(int.Parse(text), num);
            switch (int.Parse(text))
            {
                case 1:
                    物品属性_攻击力增加 += num;
                    前置物品攻击力 += num;
                    物品攻击力后置 += num;
                    break;
                case 2:
                    物品属性_防御力增加 += num;
                    物品防御力 += num;
                    break;
                case 3:
                    物品属性_生命力增加 += num;
                    break;
                case 4:
                    物品属性_内功力增加 += num;
                    break;
                case 5:
                    物品属性_命中率增加 += num;
                    break;
                case 6:
                    物品属性_回避率增加 += num;
                    break;
                case 7:
                    物品属性_武功攻击力 += num;
                    break;
                case 8:
                    {
                        string text2 = ysqh.Substring(3, 3);
                        if (text2 == null)
                        {
                            break;
                        }
                        string text3 = text2;
                        string text4 = text3;
                        if (text4 == "000")
                        {
                            物品属性_全部气功等级增加 += num;
                            break;
                        }
                        int num2 = int.Parse(text2);
                        热血石加成 value;
                        if (物品属性_热血石加成.TryGetValue(num2, out value))
                        {
                            value.加成值 += num;
                            break;
                        }
                        value = new 热血石加成();
                        value.气功ID = num2;
                        value.加成值 = num;
                        物品属性_热血石加成.TryAdd(num2, value);
                        break;
                    }
                case 9:
                    物品属性_升级成功率 += num;
                    break;
                case 10:
                    物品属性_追加伤害值 += num;
                    break;
                case 11:
                    物品属性_武功防御力增加 += num;
                    break;
                case 12:
                    物品属性_获得金钱增加 += num;
                    break;
                case 13:
                    物品属性_死亡损失经验减少 += num;
                    break;
                case 15:
                    物品属性_经验获得增加 += num;
                    break;
                case 18:
					物品武功命中 += num;
                	break;
                case 20:
					物品武功回避 += num;
                	break;
                case 14:
                case 16:
                case 17:
                case 19:
                    break;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "得到基本属性   出错：" + ((ex != null) ? ex.ToString() : null));
        }
    }

    private int 得到首饰强化增加障力恢复量(int level, int 强化阶段)
	{
		switch (level)
		{
		case 60:
			switch (强化阶段)
			{
			case 1:
				return 14;
			case 2:
				return 19;
			case 3:
				return 25;
			case 4:
				return 30;
			case 5:
				return 36;
			case 6:
				return 43;
			case 7:
				return 49;
			case 8:
				return 61;
			case 9:
				return 64;
			case 10:
				return 76;
			}
			break;
		case 80:
			switch (强化阶段)
			{
			case 1:
				return 16;
			case 2:
				return 21;
			case 3:
				return 27;
			case 4:
				return 33;
			case 5:
				return 40;
			case 6:
				return 47;
			case 7:
				return 55;
			case 8:
				return 67;
			case 9:
				return 71;
			case 10:
				return 84;
			}
			break;
		case 100:
			switch (强化阶段)
			{
			case 1:
				return 18;
			case 2:
				return 24;
			case 3:
				return 31;
			case 4:
				return 38;
			case 5:
				return 45;
			case 6:
				return 53;
			case 7:
				return 61;
			case 8:
				return 76;
			case 9:
				return 80;
			case 10:
				return 95;
			}
			break;
		case 115:
			switch (强化阶段)
			{
			case 1:
				return 19;
			case 2:
				return 26;
			case 3:
				return 33;
			case 4:
				return 40;
			case 5:
				return 48;
			case 6:
				return 57;
			case 7:
				return 66;
			case 8:
				return 81;
			case 9:
				return 85;
			case 10:
				return 101;
			}
			break;
		case 120:
			switch (强化阶段)
			{
			case 1:
				return 8;
			case 2:
				return 17;
			case 3:
				return 27;
			case 4:
				return 38;
			case 5:
				return 0;
			case 6:
				return 63;
			case 7:
				return 77;
			case 8:
				return 92;
			case 9:
				return 108;
			case 10:
				return 125;
			}
			break;
		case 130:
			switch (强化阶段)
			{
			case 1:
				return 10;
			case 2:
				return 21;
			case 3:
				return 33;
			case 4:
				return 46;
			case 5:
				return 60;
			case 6:
				return 75;
			case 7:
				return 91;
			case 8:
				return 108;
			case 9:
				return 126;
			case 10:
				return 145;
			}
			break;
		case 140:
			switch (强化阶段)
			{
			case 1:
				return 13;
			case 2:
				return 27;
			case 3:
				return 42;
			case 4:
				return 58;
			case 5:
				return 75;
			case 6:
				return 93;
			case 7:
				return 112;
			case 8:
				return 132;
			case 9:
				return 153;
			case 10:
				return 175;
			}
			break;
		case 150:
			switch (强化阶段)
			{
			case 1:
				return 16;
			case 2:
				return 33;
			case 3:
				return 51;
			case 4:
				return 70;
			case 5:
				return 90;
			case 6:
				return 111;
			case 7:
				return 133;
			case 8:
				return 156;
			case 9:
				return 180;
			case 10:
				return 205;
			}
			break;
		case 160:
			switch (强化阶段)
			{
			case 1:
				return 19;
			case 2:
				return 39;
			case 3:
				return 60;
			case 4:
				return 82;
			case 5:
				return 105;
			case 6:
				return 129;
			case 7:
				return 154;
			case 8:
				return 180;
			case 9:
				return 207;
			case 10:
				return 235;
			}
			break;
		case 170: //24.0 EVIAS +170级首饰强化加成
			switch (强化阶段)
			{
			case 1:
				return 23; 
			case 2:
				return 47; 
			case 3:
				return 72; 
			case 4:
				return 98; 
			case 5:
				return 126; 
			case 6:
				return 155; 
			case 7:
				return 185; 
			case 8:
				return 216; 
			case 9:
				return 248; 
			case 10:
				return 282; 
			}
			break;
		}
		return 0;
	}

	private int 得到物品障力(ItmeClass Itme, int 强化阶段)
	{
		switch (FLD_RESIDE2)
		{
		case 1:
		{
			int num2 = 0;
			int num3 = 物品属性强 + (int)物品属性_追加强化;
			if (Itme.FLD_LEVEL <= 80)
			{
				num2 = 5;
			}
			else if (Itme.FLD_LEVEL == 90)
			{
				num2 = 10;
			}
			else if (Itme.FLD_LEVEL == 100)
			{
				num2 = 20;
			}
			else if (Itme.FLD_LEVEL == 110)
			{
				num2 = 30;
			}
			else if (Itme.FLD_LEVEL == 120)
			{
				num2 = 40;
			}
			else if (Itme.FLD_LEVEL == 130)
			{
				num2 = 50;
			}
			else if (Itme.FLD_LEVEL == 140)
			{
				num2 = 60;
			}
			else if (Itme.FLD_LEVEL == 150)
			{
				num2 = 70;
			}
			else if (Itme.FLD_LEVEL == 160)
			{
				num2 = 80;
			}
			else if (Itme.FLD_LEVEL >= 170) //24.0 EVIAS +170级装备障力加成
			{
				num2 = 100; 
			}
			
			物品属性_障力增加 += num3 * num2;
			switch (num3)
			{
			case 6:
				物品属性_障力增加 += 155;
				break;
			case 7:
				物品属性_障力增加 += 195;
				break;
			case 8:
				物品属性_障力增加 += 260;
				break;
			case 9:
				物品属性_障力增加 += 375;
				break;
			case 10:
				物品属性_障力增加 += 615;
				break;
			case 11:
				物品属性_障力增加 += 905;
				break;
			case 12:
				物品属性_障力增加 += 1295;
				break;
			case 13:
				物品属性_障力增加 += 1685;
				break;
			case 14:
				物品属性_障力增加 += 2125;
				break;
			case 15:
				物品属性_障力增加 += 2565;
				break;
			}
			break;
		}
		case 2:
		case 5:
		{
			int num4 = 0;
			int num5 = 物品属性强 + (int)物品属性_追加强化;
			if (Itme.FLD_LEVEL <= 80)
			{
				num4 = 5;
			}
			else if (Itme.FLD_LEVEL == 90)
			{
				num4 = 7;
			}
			else if (Itme.FLD_LEVEL == 100)
			{
				num4 = 9;
			}
			else if (Itme.FLD_LEVEL == 110)
			{
				num4 = 11;
			}
			else if (Itme.FLD_LEVEL == 120)
			{
				num4 = 13;
			}
			else if (Itme.FLD_LEVEL == 130)
			{
				num4 = 15;
			}
			else if (Itme.FLD_LEVEL == 140)
			{
				num4 = 17;
			}
			else if (Itme.FLD_LEVEL == 150)
			{
				num4 = 22;
			}
			else if (Itme.FLD_LEVEL == 160)
			{
				num4 = 27;
			}
			else if (Itme.FLD_LEVEL >= 170) //24.0 EVIAS +170级装备障力加成
			{
				num4 = 32; 
			}
			
            物品属性_障力增加 += num5 * num4;
            if (num5 > 5)
            {
                switch (num5)
                {
                    case 6:
                        物品属性_障力增加 += 5;
                        break;
                    case 7:
                        物品属性_障力增加 += 15;
                        break;
                    case 8:
                        物品属性_障力增加 += 33;
                        break;
                    case 9:
                        物品属性_障力增加 += 55;
                        break;
                    case 10:
                        物品属性_障力增加 += 124;
                        break;
                    case 11:
                        物品属性_障力增加 += 207;
                        break;
                    case 12:
                        物品属性_障力增加 += 305;
                        break;
                    case 13:
                        物品属性_障力增加 += 443;
                        break;
                    case 14:
                        物品属性_障力增加 += 611;
                        break;
                    case 15:
                        物品属性_障力增加 += 814;
                        break;
                }
            }
			break;
		}
		case 6:
		{
			int num = 0;
			if (Itme.FLD_LEVEL <= 55)
			{
				num = 1;
			}
			else if (Itme.FLD_LEVEL <= 65)
			{
				num = 2;
			}
			else if (Itme.FLD_LEVEL <= 75)
			{
				num = 3;
			}
			else if (Itme.FLD_LEVEL <= 85)
			{
				num = 5;
			}
			else if (Itme.FLD_LEVEL == 95)
			{
				num = 7;
			}
			else if (Itme.FLD_LEVEL == 105)
			{
				num = 9;
			}
			else if (Itme.FLD_LEVEL == 115)
			{
				num = 11;
			}
			else if (Itme.FLD_LEVEL == 125)
			{
				num = 13;
			}
			else if (Itme.FLD_LEVEL == 130)
			{
				num = 14;
			}
			else if (Itme.FLD_LEVEL == 135)
			{
				num = 15;
			}
			else if (Itme.FLD_LEVEL == 140)
			{
				num = 16;
			}
			else if (Itme.FLD_LEVEL == 145)
			{
				num = 17;
			}
			else if (Itme.FLD_LEVEL == 150)
			{
				num = 18;
			}
			else if (Itme.FLD_LEVEL == 155)
			{
				num = 22;
			}
			else if (Itme.FLD_LEVEL == 160)
			{
				num = 24;
			}
			else if (Itme.FLD_LEVEL >= 170) //24.0 EVIAS +170级装备障力加成
			{
				num = 28; 
			}
			
			物品属性_障力增加 += 物品属性强 * num;
			if (物品属性强 > 5)
			{
				switch (物品属性强)
				{
				case 6:
					物品属性_障力增加 += 5;
					break;
				case 7:
					物品属性_障力增加 += 15;
					break;
				case 8:
					物品属性_障力增加 += 33;
					break;
				case 9:
					物品属性_障力增加 += 55;
					break;
				case 10:
					物品属性_障力增加 += 124;
					break;
				case 11:
					物品属性_障力增加 += 207;
					break;
				case 12:
					物品属性_障力增加 += 305;
					break;
				case 13:
					物品属性_障力增加 += 443;
					break;
				case 14:
					物品属性_障力增加 += 611;
					break;
				case 15:
					物品属性_障力增加 += 814;
					break;
				}
			}
			break;
		}
		}
		return 0;
	}

	private void 计算武器属性(ItmeClass Itme)
	{
		try
		{
			switch (物品属性强)
			{
			case 1:
				前置物品攻击力 += 6;
				物品攻击力后置 += 6;
				物品武功命中++;
				break;
			case 2:
				前置物品攻击力 += 12;
				物品攻击力后置 += 12;
				物品武功命中 += 2;
				break;
			case 3:
				前置物品攻击力 += 18;
				物品攻击力后置 += 18;
				物品武功命中 += 3;
				break;
			case 4:
				前置物品攻击力 += 24;
				物品攻击力后置 += 24;
				物品武功命中 += 4;
				break;
			case 5:
				if (Itme.FLD_UP_LEVEL == 1)
				{
					前置物品攻击力 += 32;
					物品攻击力后置 += 32;
					物品武功命中 += 5;
				}
				else
				{
					前置物品攻击力 += 30;
					物品攻击力后置 += 30;
					物品武功命中 += 5;
				}
				break;
			case 6:
				if (Itme.FLD_UP_LEVEL == 1)
				{
					前置物品攻击力 += 46;
					物品攻击力后置 += 46;
					物品武功命中 += 7;
				}
				else
				{
					前置物品攻击力 += 38;
					物品攻击力后置 += 38;
					物品武功命中 += 7;
				}
				break;
			case 7:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 64;
						物品攻击力后置 += 64;
						物品武功命中 += 9;
					}
					else
					{
						前置物品攻击力 += 52;
						物品攻击力后置 += 52;
						物品武功命中 += 9;
					}
				}
				else
				{
					前置物品攻击力 += 46;
					物品攻击力后置 += 46;
					物品武功命中 += 9;
				}
				break;
			case 8:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 89;
						物品攻击力后置 += 89;
						物品武功命中 += 11;
					}
					else
					{
						前置物品攻击力 += 72;
						物品攻击力后置 += 72;
						物品武功命中 += 11;
					}
				}
				else
				{
					前置物品攻击力 += 54;
					物品攻击力后置 += 54;
					物品武功命中 += 11;
				}
				break;
			case 9:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 124;
						物品攻击力后置 += 124;
						物品武功命中 += 13;
					}
					else
					{
						前置物品攻击力 += 102;
						物品攻击力后置 += 102;
						物品武功命中 += 13;
					}
				}
				else
				{
					前置物品攻击力 += 62;
					物品攻击力后置 += 62;
					物品武功命中 += 13;
				}
				break;
			case 10:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 194;
						物品攻击力后置 += 194;
						物品武功命中 += 15;
						物品属性_全部气功等级增加++;
					}
					else
					{
						前置物品攻击力 += 162;
						物品攻击力后置 += 162;
						物品武功命中 += 15;
						物品属性_全部气功等级增加++;
					}
				}
				else
				{
					前置物品攻击力 += 70;
					物品攻击力后置 += 70;
					物品武功命中 += 15;
					物品属性_全部气功等级增加++;
				}
				break;
			case 11:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 212;
						物品攻击力后置 += 212;
						物品武功命中 += 17;
						物品属性_全部气功等级增加 += 2;
					}
					else
					{
						前置物品攻击力 += 177;
						物品攻击力后置 += 177;
						物品武功命中 += 17;
						物品属性_全部气功等级增加 += 2;
					}
				}
				else
				{
					前置物品攻击力 += 85;
					物品攻击力后置 += 85;
					物品武功命中 += 17;
					物品属性_全部气功等级增加 += 2;
				}
				break;
			case 12:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 242;
						物品攻击力后置 += 242;
						物品武功命中 += 19;
						物品属性_全部气功等级增加 += 3;
					}
					else
					{
						前置物品攻击力 += 197;
						物品攻击力后置 += 197;
						物品武功命中 += 19;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 105;
					物品攻击力后置 += 105;
					物品武功命中 += 19;
					物品属性_全部气功等级增加 += 3;
				}
				break;
			case 13:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 277;
						物品攻击力后置 += 277;
						物品武功命中 += 20;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 222;
						物品攻击力后置 += 222;
						物品武功命中 += 20;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 130;
					物品攻击力后置 += 130;
					物品武功命中 += 20;
					物品属性_全部气功等级增加 += 3;
				}
				break;
			case 14:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 347;
						物品攻击力后置 += 347;
						物品武功命中 += 21;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 282;
						物品攻击力后置 += 282;
						物品武功命中 += 21;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 190;
					物品攻击力后置 += 190;
					物品武功命中 += 21;
					物品属性_全部气功等级增加 += 3;
				}
				break;
			case 15:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 417;
						物品攻击力后置 += 417;
						物品武功命中 += 22;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 352;
						物品攻击力后置 += 352;
						物品武功命中 += 22;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 260;
					物品攻击力后置 += 260;
					物品武功命中 += 22;
					物品属性_全部气功等级增加 += 3;
				}
				break;
			case 16:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 452;
						物品攻击力后置 += 452;
						物品武功命中 += 23;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 387;
						物品攻击力后置 += 387;
						物品武功命中 += 23;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 295;
					物品攻击力后置 += 295;
					物品武功命中 += 23;
					物品属性_全部气功等级增加 += 3;
				}
				强化锁定气功 += 2;
				break;
			case 17:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 487;
						物品攻击力后置 += 487;
						物品武功命中 += 24;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 422;
						物品攻击力后置 += 422;
						物品武功命中 += 24;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 330;
					物品攻击力后置 += 330;
					物品武功命中 += 24;
					物品属性_全部气功等级增加 += 3;
				}
				强化锁定气功 += 2;
				break;
			case 18:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 522;
						物品攻击力后置 += 522;
						物品武功命中 += 25;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 457;
						物品攻击力后置 += 457;
						物品武功命中 += 25;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 365;
					物品攻击力后置 += 365;
					物品武功命中 += 25;
					物品属性_全部气功等级增加 += 3;
				}
				强化锁定气功 += 2;
				break;
			case 19:
			case 20:
			case 21:
			case 22:
			case 23:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						前置物品攻击力 += 557;
						物品攻击力后置 += 557;
						物品武功命中 += 26;
						物品属性_全部气功等级增加 += 4;
					}
					else
					{
						前置物品攻击力 += 492;
						物品攻击力后置 += 492;
						物品武功命中 += 26;
						物品属性_全部气功等级增加 += 3;
					}
				}
				else
				{
					前置物品攻击力 += 400;
					物品攻击力后置 += 400;
					物品武功命中 += 26;
					物品属性_全部气功等级增加 += 3;
				}
				强化锁定气功 += 2;
				break;
			}
			if (物品属性强 >= 7)
			{
				ConcurrentDictionary<int, Itimesx> concurrentDictionary = new ConcurrentDictionary<int, Itimesx>();
				concurrentDictionary.TryAdd(0, 属性1);
				concurrentDictionary.TryAdd(1, 属性2);
				concurrentDictionary.TryAdd(2, 属性3);
				concurrentDictionary.TryAdd(3, 属性4);
				for (int i = 0; i < 4; i++)
				{
					if (concurrentDictionary[i].属性类型 == 0)
					{
						continue;
					}
					int 属性数量 = concurrentDictionary[i].属性数量;
					switch (concurrentDictionary[i].属性类型)
					{
					case 7:
						if (物品属性强 == 7)
						{
							if (i < 2)
							{
								物品属性_武功攻击力++;
							}
						}
						else if (物品属性强 <= 12)
						{
							物品属性_武功攻击力 += 物品属性强 - 7;
						}
						else if (物品属性强 <= 14)
						{
							double num3 = Math.Round((double)属性数量 / 4.0);
							物品属性_武功攻击力 += 物品属性强 - 7 + (int)num3;
						}
						else
						{
							double num4 = Math.Round((double)属性数量 / 4.0);
							物品属性_武功攻击力 += 物品属性强 - 8 + (int)num4;
						}
						break;
					case 8:
						if (物品属性强 >= 13)
						{
							物品属性_全部气功等级增加++;
						}
						break;
					case 10:
						if (物品属性强 == 7)
						{
							if (i < 2)
							{
								物品属性_追加伤害值++;
							}
						}
						else if (物品属性强 <= 12)
						{
							物品属性_追加伤害值 += 物品属性强 - 7;
						}
						else if (物品属性强 <= 14)
						{
							物品属性_追加伤害值 += 物品属性强 - 7 + (int)Math.Round((double)属性数量 / 4.0);
						}
						else
						{
							物品属性_追加伤害值 += 物品属性强 - 8 + (int)Math.Round((double)属性数量 / 4.0);
						}
						break;
					case 3:
						if (物品属性强 == 7)
						{
							if (i < 2)
							{
								物品属性_生命力增加++;
							}
						}
						else if (物品属性强 <= 12)
						{
							物品属性_生命力增加 += 物品属性强 - 7;
						}
						else if (物品属性强 <= 14)
						{
							物品属性_生命力增加 += 物品属性强 - 7 + (int)Math.Round((double)属性数量 / 4.0);
						}
						else
						{
							物品属性_生命力增加 += 物品属性强 - 8 + (int)Math.Round((double)属性数量 / 4.0);
						}
						break;
					case 1:
						if (物品属性强 == 7)
						{
							if (i < 2)
							{
								前置物品攻击力++;
								物品攻击力后置++;
							}
						}
						else if (物品属性强 <= 12)
						{
							前置物品攻击力 += 物品属性强 - 7;
							物品攻击力后置 += 物品属性强 - 7;
						}
						else if (物品属性强 <= 14)
						{
							double num = Math.Round((double)属性数量 / 4.0);
							前置物品攻击力 += 物品属性强 - 7 + (int)num;
							物品攻击力后置 += 物品属性强 - 7 + (int)num;
						}
						else
						{
							double num2 = Math.Round((double)属性数量 / 4.0);
							前置物品攻击力 += 物品属性强 - 8 + (int)num2;
							物品攻击力后置 += 物品属性强 - 8 + (int)num2;
						}
						break;
					}
				}
				concurrentDictionary.Clear();
			}
			int 附魂加攻击 = World.附魂加攻击;
			int num5 = 1;
			if (FLD_FJ_觉醒 > 0)
			{
				int num6 = FLD_FJ_觉醒 * 附魂加攻击;
				int num7 = FLD_FJ_觉醒 * num5 / World.附魂加战斗力;
				前置物品攻击力 += num6;
				物品攻击力后置 += num6;
				物品对人战斗力 += num7;
			}
		}
		catch
		{
		}
	}

	private void 计算衣服属性(ItmeClass Itme)
	{
		try
		{
			switch (物品属性强)
			{
			case 1:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 6;
					}
					else
					{
						物品防御力 += 4;
					}
				}
				else
				{
					物品防御力 += 3;
				}
				break;
			case 2:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 16;
					}
					else
					{
						物品防御力 += 12;
					}
				}
				else
				{
					物品防御力 += 9;
				}
				break;
			case 3:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 30;
					}
					else
					{
						物品防御力 += 24;
					}
				}
				else
				{
					物品防御力 += 18;
				}
				break;
			case 4:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 48;
					}
					else
					{
						物品防御力 += 40;
					}
				}
				else
				{
					物品防御力 += 30;
				}
				break;
			case 5:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 70;
					}
					else
					{
						物品防御力 += 60;
					}
				}
				else
				{
					物品防御力 += 45;
				}
				break;
			case 6:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 98;
						物品属性_降低伤害值 += 5;
					}
					else
					{
						物品防御力 += 84;
						物品属性_降低伤害值 += 5;
					}
				}
				else
				{
					物品防御力 += 63;
					物品属性_生命力增加 += 5;
				}
				break;
			case 7:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 130;
						物品属性_降低伤害值 += 10;
					}
					else
					{
						物品防御力 += 112;
						物品属性_降低伤害值 += 10;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 70;
						}
						else
						{
							物品对怪防御力 += 70;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 84;
						}
						else
						{
							物品对怪防御力 += 70;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 98;
						}
						else
						{
							物品对怪防御力 += 84;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 84;
					物品属性_生命力增加 += 10;
				}
				break;
			case 8:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 166;
						物品属性_降低伤害值 += 15;
					}
					else
					{
						物品防御力 += 144;
						物品属性_降低伤害值 += 15;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 84;
						}
						else
						{
							物品对怪防御力 += 70;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 98;
						}
						else
						{
							物品对怪防御力 += 84;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 112;
						}
						else
						{
							物品对怪防御力 += 98;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 108;
					物品属性_生命力增加 += 15;
				}
				break;
			case 9:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 206;
						物品属性_降低伤害值 += 25;
						物品属性_生命力增加 += 100;
					}
					else
					{
						物品防御力 += 180;
						物品属性_降低伤害值 += 20;
						物品属性_生命力增加 += 80;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 84;
						}
						else
						{
							物品对怪防御力 += 70;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 98;
						}
						else
						{
							物品对怪防御力 += 84;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 112;
						}
						else
						{
							物品对怪防御力 += 98;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 135;
					物品属性_生命力增加 += 20;
				}
				break;
			case 10:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 261;
						物品属性_降低伤害值 += 40;
						物品属性_生命力增加 += 200;
					}
					else
					{
						物品防御力 += 230;
						物品属性_降低伤害值 += 30;
						物品属性_生命力增加 += 160;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 163;
						}
						else
						{
							物品对怪防御力 += 143;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 183;
						}
						else
						{
							物品对怪防御力 += 163;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 203;
						}
						else
						{
							物品对怪防御力 += 183;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 165;
					物品属性_生命力增加 += 30;
				}
				break;
			case 11:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 299;
						物品属性_降低伤害值 += 50;
						物品属性_生命力增加 += 200;
					}
					else
					{
						物品防御力 += 265;
						物品属性_降低伤害值 += 40;
						物品属性_生命力增加 += 160;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 198;
						}
						else
						{
							物品对怪防御力 += 176;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 220;
						}
						else
						{
							物品对怪防御力 += 198;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 245;
						}
						else
						{
							物品对怪防御力 += 220;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 200;
					物品属性_生命力增加 += 40;
				}
				break;
			case 12:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 351;
						物品属性_降低伤害值 += 60;
						物品属性_生命力增加 += 200;
					}
					else
					{
						物品防御力 += 315;
						物品属性_降低伤害值 += 50;
						物品属性_生命力增加 += 160;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 233;
						}
						else
						{
							物品对怪防御力 += 209;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 257;
						}
						else
						{
							物品对怪防御力 += 233;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 281;
						}
						else
						{
							物品对怪防御力 += 257;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 250;
					物品属性_生命力增加 += 50;
				}
				break;
			case 13:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 409;
						物品属性_降低伤害值 += 70;
						物品属性_生命力增加 += 460;
					}
					else
					{
						物品防御力 += 365;
						物品属性_降低伤害值 += 60;
						物品属性_生命力增加 += 420;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 268;
						}
						else
						{
							物品对怪防御力 += 242;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 294;
						}
						else
						{
							物品对怪防御力 += 268;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 324;
						}
						else
						{
							物品对怪防御力 += 294;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 300;
					物品属性_生命力增加 += 260;
				}
				break;
			case 14:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 467;
						物品属性_降低伤害值 += 80;
						物品属性_生命力增加 += 560;
					}
					else
					{
						物品防御力 += 415;
						物品属性_降低伤害值 += 70;
						物品属性_生命力增加 += 520;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 303;
						}
						else
						{
							物品对怪防御力 += 275;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 330;
						}
						else
						{
							物品对怪防御力 += 303;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 360;
						}
						else
						{
							物品对怪防御力 += 330;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 350;
					物品属性_生命力增加 += 260;
				}
				break;
			case 15:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 525;
						物品属性_降低伤害值 += 90;
						物品属性_生命力增加 += 710;
					}
					else
					{
						物品防御力 += 465;
						物品属性_降低伤害值 += 80;
						物品属性_生命力增加 += 670;
					}
					if (Itme.FLD_NJ != 0)
					{
						break;
					}
					switch (Itme.FLD_LEVEL)
					{
					case 140:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 343;
						}
						else
						{
							物品对怪防御力 += 308;
						}
						break;
					case 150:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 371;
						}
						else
						{
							物品对怪防御力 += 343;
						}
						break;
					case 160:
						if (Itme.FLD_UP_LEVEL == 1)
						{
							物品对怪防御力 += 403;
						}
						else
						{
							物品对怪防御力 += 371;
						}
						break;
					}
				}
				else
				{
					物品防御力 += 400;
					物品属性_生命力增加 += 260;
				}
				break;
			case 16:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 583;
						物品属性_降低伤害值 += 90;
						物品属性_生命力增加 += 710;
					}
					else
					{
						物品防御力 += 515;
						物品属性_降低伤害值 += 80;
						物品属性_生命力增加 += 670;
					}
					if (Itme.FLD_NJ == 0)
					{
						switch (Itme.FLD_LEVEL)
						{
						case 140:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 343;
							}
							else
							{
								物品对怪防御力 += 315;
							}
							break;
						case 150:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 371;
							}
							else
							{
								物品对怪防御力 += 343;
							}
							break;
						case 160:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 403;
							}
							else
							{
								物品对怪防御力 += 371;
							}
							break;
						}
					}
				}
				else
				{
					物品防御力 += 450;
					物品属性_生命力增加 += 260;
				}
				强化锁定气功 += 2;
				break;
			case 17:
			case 18:
			case 19:
			case 20:
				if (Itme.FLD_LEVEL >= 60)
				{
					if (Itme.FLD_UP_LEVEL == 1)
					{
						物品防御力 += 641;
						物品属性_降低伤害值 += 90;
						物品属性_生命力增加 += 710;
					}
					else
					{
						物品防御力 += 565;
						物品属性_降低伤害值 += 80;
						物品属性_生命力增加 += 670;
					}
					if (Itme.FLD_NJ == 0)
					{
						switch (Itme.FLD_LEVEL)
						{
						case 140:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 343;
							}
							else
							{
								物品对怪防御力 += 315;
							}
							break;
						case 150:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 371;
							}
							else
							{
								物品对怪防御力 += 343;
							}
							break;
						case 160:
							if (Itme.FLD_UP_LEVEL == 1)
							{
								物品对怪防御力 += 403;
							}
							else
							{
								物品对怪防御力 += 371;
							}
							break;
						}
					}
				}
				else
				{
					物品防御力 += 500;
					物品属性_生命力增加 += 260;
				}
				强化锁定气功 += 2;
				break;
			}
			int 附魂加防御 = World.附魂加防御;
			int num = 1;
			if (FLD_FJ_觉醒 > 0)
			{
				int num2 = FLD_FJ_觉醒 * 附魂加防御;
				int num3 = FLD_FJ_觉醒 * num / World.附魂加战斗力;
				物品防御力 += num2;
				物品对人战斗力 += num3;
			}
		}
		catch
		{
		}
	}

	private void 得到强化(string ysqh, int 触发属性提升)
	{
		try
		{
			if (!World.Itme.TryGetValue(BitConverter.ToInt32(物品ID, 0), out var value))
			{
				return;
			}
			switch (ysqh.Length)
			{
			case 8:
				物品属性阶段类型 = 0;
				物品属性阶段数 = 0;
				物品属性强类型 = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
				物品属性强 = int.Parse(ysqh.Substring(ysqh.Length - 2, 2)) + (int)物品属性_追加强化;
				switch (物品属性强类型)
				{
				case 1:
					if (FLD_RESIDE2 == 10)
					{
						物品武功命中 += 物品属性强;
						if (value.FLD_LEVEL >= 170) //24.0 EVIAS +170级装备强化加成
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 16; 
								物品攻击力后置 += 物品属性强 * 16;
							}
							else
							{
								前置物品攻击力 += 48 + (物品属性强 - 3) * 35; 
								物品攻击力后置 += 48 + (物品属性强 - 3) * 35;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(170, 物品属性强); //24.0 EVIAS +170级首饰加成
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(170, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 160)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 13;
								物品攻击力后置 += 物品属性强 * 13;
							}
							else
							{
								前置物品攻击力 += 39 + (物品属性强 - 3) * 28;
								物品攻击力后置 += 39 + (物品属性强 - 3) * 28;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(160, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(160, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 150)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 11;
								物品攻击力后置 += 物品属性强 * 11;
							}
							else
							{
								前置物品攻击力 += 30 + (物品属性强 - 3) * 25;
								物品攻击力后置 += 30 + (物品属性强 - 3) * 25;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(150, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(150, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 140)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 9;
								物品攻击力后置 += 物品属性强 * 9;
							}
							else
							{
								前置物品攻击力 += 28 + (物品属性强 - 3) * 21;
								物品攻击力后置 += 28 + (物品属性强 - 3) * 21;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(140, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(140, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 130)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 7;
								物品攻击力后置 += 物品属性强 * 7;
							}
							else
							{
								前置物品攻击力 += 26 + (物品属性强 - 3) * 17;
								物品攻击力后置 += 26 + (物品属性强 - 3) * 17;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(130, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(130, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 120)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 6;
								物品攻击力后置 += 物品属性强 * 6;
							}
							else
							{
								前置物品攻击力 += 20 + (物品属性强 - 3) * 15;
								物品攻击力后置 += 20 + (物品属性强 - 3) * 15;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(120, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(120, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 115)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 5;
								物品攻击力后置 += 物品属性强 * 5;
							}
							else
							{
								前置物品攻击力 += 15 + (物品属性强 - 3) * 9;
								物品攻击力后置 += 15 + (物品属性强 - 3) * 9;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(115, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(115, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 100)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 4;
								物品攻击力后置 += 物品属性强 * 4;
							}
							else
							{
								前置物品攻击力 += 11 + (物品属性强 - 3) * 7;
								物品攻击力后置 += 11 + (物品属性强 - 3) * 7;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(100, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(100, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 80)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 3;
								物品攻击力后置 += 物品属性强 * 3;
							}
							else
							{
								前置物品攻击力 += 9 + (物品属性强 - 3) * 5;
								物品攻击力后置 += 9 + (物品属性强 - 3) * 5;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(80, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(80, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 60)
						{
							if (物品属性强 < 4)
							{
								前置物品攻击力 += 物品属性强 * 2;
								物品攻击力后置 += 物品属性强 * 2;
							}
							else
							{
								前置物品攻击力 += 6 + (物品属性强 - 3) * 3;
								物品攻击力后置 += 6 + (物品属性强 - 3) * 3;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(60, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(60, 物品属性强);
							}
						}
					}
					else
					{
						计算武器属性(value);
					}
					break;
				case 2:
					switch (FLD_RESIDE2)
					{
					case 1:
						计算衣服属性(value);
						得到物品障力(value, 物品属性强);
						break;
					case 2:
					case 5:
						switch (物品属性强)
						{
						case 1:
							物品防御力 += 3;
							break;
						case 2:
							物品防御力 += 6;
							break;
						case 3:
							物品防御力 += 9;
							break;
						case 4:
							物品防御力 += 12;
							break;
						case 5:
							物品防御力 += 15;
							break;
						case 6:
							物品防御力 += 19;
							if (value.FLD_LEVEL >= 60)
							{
								物品属性_降低伤害值 += 5;
							}
							else
							{
								物品属性_生命力增加 += 5;
							}
							break;
						case 7:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 25;
									物品属性_降低伤害值 += 10;
								}
								else
								{
									物品防御力 += 23;
									物品属性_降低伤害值 += 10;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 49;
									}
									else
									{
										物品对怪防御力 += 35;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 63;
									}
									else
									{
										物品对怪防御力 += 49;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 78;
									}
									else
									{
										物品对怪防御力 += 63;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 23;
								物品属性_生命力增加 += 10;
							}
							break;
						case 8:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 34;
									物品属性_降低伤害值 += 15;
								}
								else
								{
									物品防御力 += 29;
									物品属性_降低伤害值 += 15;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 49;
									}
									else
									{
										物品对怪防御力 += 35;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 63;
									}
									else
									{
										物品对怪防御力 += 49;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 78;
									}
									else
									{
										物品对怪防御力 += 63;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 28;
								物品属性_生命力增加 += 15;
							}
							break;
						case 9:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 46;
									物品属性_生命力增加 += 100;
									物品属性_降低伤害值 += 25;
								}
								else
								{
									物品防御力 += 38;
									物品属性_生命力增加 += 80;
									物品属性_降低伤害值 += 20;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 49;
									}
									else
									{
										物品对怪防御力 += 35;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 63;
									}
									else
									{
										物品对怪防御力 += 49;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 78;
									}
									else
									{
										物品对怪防御力 += 63;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 35;
								物品属性_生命力增加 += 20;
							}
							break;
						case 10:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 64;
									物品属性_生命力增加 += 200;
									物品属性_降低伤害值 += 40;
								}
								else
								{
									物品防御力 += 53;
									物品属性_生命力增加 += 160;
									物品属性_降低伤害值 += 30;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 113;
									}
									else
									{
										物品对怪防御力 += 93;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 133;
									}
									else
									{
										物品对怪防御力 += 113;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 154;
									}
									else
									{
										物品对怪防御力 += 133;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 48;
								物品属性_生命力增加 += 30;
							}
							break;
						case 11:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 79;
									物品属性_生命力增加 += 200;
									物品属性_降低伤害值 += 50;
								}
								else
								{
									物品防御力 += 68;
									物品属性_生命力增加 += 160;
									物品属性_降低伤害值 += 40;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 143;
									}
									else
									{
										物品对怪防御力 += 121;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 165;
									}
									else
									{
										物品对怪防御力 += 143;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 188;
									}
									else
									{
										物品对怪防御力 += 165;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 62;
								物品属性_生命力增加 += 40;
							}
							break;
						case 12:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 94;
									物品属性_生命力增加 += 200;
									物品属性_降低伤害值 += 60;
								}
								else
								{
									物品防御力 += 83;
									物品属性_生命力增加 += 160;
									物品属性_降低伤害值 += 50;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 173;
									}
									else
									{
										物品对怪防御力 += 149;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 197;
									}
									else
									{
										物品对怪防御力 += 173;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 222;
									}
									else
									{
										物品对怪防御力 += 197;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 77;
								物品属性_生命力增加 += 50;
							}
							break;
						case 13:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 109;
									物品属性_生命力增加 += 460;
									物品属性_降低伤害值 += 70;
								}
								else
								{
									物品防御力 += 98;
									物品属性_生命力增加 += 420;
									物品属性_降低伤害值 += 60;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 203;
									}
									else
									{
										物品对怪防御力 += 177;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 229;
									}
									else
									{
										物品对怪防御力 += 203;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 257;
									}
									else
									{
										物品对怪防御力 += 229;
									}
									break;
								}
							}
							else
							{
								物品防御力 += 92;
								物品属性_生命力增加 += 260;
							}
							break;
						case 14:
							if (value.FLD_LEVEL >= 130)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 129;
									物品属性_生命力增加 += 560;
									物品属性_降低伤害值 += 80;
								}
								else
								{
									物品防御力 += 118;
									物品属性_生命力增加 += 520;
									物品属性_降低伤害值 += 70;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 233;
									}
									else
									{
										物品对怪防御力 += 205;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 261;
									}
									else
									{
										物品对怪防御力 += 233;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 288;
									}
									else
									{
										物品对怪防御力 += 261;
									}
									break;
								}
							}
							else if (value.FLD_LEVEL >= 60)
							{
								物品防御力 += 113;
								物品属性_生命力增加 += 520;
								物品属性_降低伤害值 += 70;
							}
							else
							{
								物品防御力 += 107;
								物品属性_生命力增加 += 260;
							}
							break;
						case 15:
							if (value.FLD_LEVEL >= 130)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 149;
									物品属性_生命力增加 += 710;
									物品属性_降低伤害值 += 90;
								}
								else
								{
									物品防御力 += 138;
									物品属性_生命力增加 += 670;
									物品属性_降低伤害值 += 80;
								}
								if (value.FLD_NJ != 0)
								{
									break;
								}
								switch (value.FLD_LEVEL)
								{
								case 140:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 263;
									}
									else
									{
										物品对怪防御力 += 233;
									}
									break;
								case 150:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 293;
									}
									else
									{
										物品对怪防御力 += 263;
									}
									break;
								case 160:
									if (value.FLD_UP_LEVEL == 1)
									{
										物品对怪防御力 += 323;
									}
									else
									{
										物品对怪防御力 += 293;
									}
									break;
								}
							}
							else if (value.FLD_LEVEL >= 60)
							{
								物品防御力 += 128;
								物品属性_生命力增加 += 670;
								物品属性_降低伤害值 += 80;
							}
							else
							{
								物品防御力 += 122;
								物品属性_生命力增加 += 260;
							}
							break;
						case 16:
							if (value.FLD_LEVEL >= 130)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 169;
									物品属性_生命力增加 += 710;
									物品属性_降低伤害值 += 90;
								}
								else
								{
									物品防御力 += 158;
									物品属性_生命力增加 += 670;
									物品属性_降低伤害值 += 80;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 140:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 263;
										}
										else
										{
											物品对怪防御力 += 233;
										}
										break;
									case 150:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 293;
										}
										else
										{
											物品对怪防御力 += 263;
										}
										break;
									case 160:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 323;
										}
										else
										{
											物品对怪防御力 += 293;
										}
										break;
									}
								}
							}
							else if (value.FLD_LEVEL >= 60)
							{
								物品防御力 += 143;
								物品属性_生命力增加 += 670;
								物品属性_降低伤害值 += 80;
							}
							else
							{
								物品防御力 += 137;
								物品属性_生命力增加 += 260;
							}
							强化锁定气功 += 2;
							break;
						case 17:
						case 18:
						case 19:
						case 20:
							if (value.FLD_LEVEL >= 130)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 189;
									物品属性_生命力增加 += 710;
									物品属性_降低伤害值 += 90;
								}
								else
								{
									物品防御力 += 178;
									物品属性_生命力增加 += 670;
									物品属性_降低伤害值 += 80;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 140:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 263;
										}
										else
										{
											物品对怪防御力 += 233;
										}
										break;
									case 150:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 293;
										}
										else
										{
											物品对怪防御力 += 263;
										}
										break;
									case 160:
										if (value.FLD_UP_LEVEL == 1)
										{
											物品对怪防御力 += 323;
										}
										else
										{
											物品对怪防御力 += 293;
										}
										break;
									}
								}
							}
							else if (value.FLD_LEVEL >= 60)
							{
								物品防御力 += 158;
								物品属性_生命力增加 += 670;
								物品属性_降低伤害值 += 80;
							}
							else
							{
								物品防御力 += 152;
								物品属性_生命力增加 += 260;
							}
							强化锁定气功 += 2;
							break;
						}
						得到物品障力(value, 物品属性强);
						break;
					case 6:
						switch (物品属性强)
						{
						case 1:
							物品防御力 += 3;
							break;
						case 2:
							物品防御力 += 6;
							break;
						case 3:
							物品防御力 += 9;
							break;
						case 4:
							物品防御力 += 12;
							break;
						case 5:
							物品防御力 += 15;
							break;
						case 6:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 19;
									物品属性_生命力增加 += 45;
								}
								else
								{
									物品防御力 += 19;
									物品属性_生命力增加 += 40;
								}
							}
							else
							{
								物品防御力 += 18;
								物品属性_生命力增加 += 30;
							}
							break;
						case 7:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 24;
									物品属性_生命力增加 += 90;
								}
								else
								{
									物品防御力 += 23;
									物品属性_生命力增加 += 80;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 35;
										break;
									case 160:
										物品对怪防御力 += 49;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 21;
								物品属性_生命力增加 += 60;
							}
							break;
						case 8:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 32;
									物品属性_生命力增加 += 155;
								}
								else
								{
									物品防御力 += 29;
									物品属性_生命力增加 += 140;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 35;
										break;
									case 160:
										物品对怪防御力 += 49;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 25;
								物品属性_生命力增加 += 110;
							}
							break;
						case 9:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 44;
									物品属性_生命力增加 += 220;
								}
								else
								{
									物品防御力 += 38;
									物品属性_生命力增加 += 200;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 35;
										break;
									case 160:
										物品对怪防御力 += 49;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 31;
								物品属性_生命力增加 += 160;
							}
							break;
						case 10:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 62;
									物品属性_生命力增加 += 330;
								}
								else
								{
									物品防御力 += 53;
									物品属性_生命力增加 += 300;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 93;
										break;
									case 160:
										物品对怪防御力 += 113;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 43;
								物品属性_生命力增加 += 250;
							}
							break;
						case 11:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 77;
									物品属性_生命力增加 += 330;
								}
								else
								{
									物品防御力 += 68;
									物品属性_生命力增加 += 300;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 121;
										break;
									case 160:
										物品对怪防御力 += 143;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 58;
								物品属性_生命力增加 += 250;
							}
							break;
						case 12:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 92;
									物品属性_生命力增加 += 330;
								}
								else
								{
									物品防御力 += 83;
									物品属性_生命力增加 += 300;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 149;
										break;
									case 160:
										物品对怪防御力 += 173;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 73;
								物品属性_生命力增加 += 250;
							}
							break;
						case 13:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 107;
									物品属性_生命力增加 += 590;
								}
								else
								{
									物品防御力 += 98;
									物品属性_生命力增加 += 560;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 177;
										break;
									case 160:
										物品对怪防御力 += 203;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 88;
								物品属性_生命力增加 += 510;
							}
							break;
						case 14:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 127;
									物品属性_生命力增加 += 710;
								}
								else
								{
									物品防御力 += 118;
									物品属性_生命力增加 += 610;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 205;
										break;
									case 160:
										物品对怪防御力 += 233;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 108;
								物品属性_生命力增加 += 510;
							}
							break;
						case 15:
						case 16:
						case 17:
						case 18:
						case 19:
						case 20:
							if (value.FLD_LEVEL >= 60)
							{
								if (value.FLD_UP_LEVEL == 1)
								{
									物品防御力 += 147;
									物品属性_生命力增加 += 950;
								}
								else
								{
									物品防御力 += 138;
									物品属性_生命力增加 += 810;
								}
								if (value.FLD_NJ == 0)
								{
									switch (value.FLD_LEVEL)
									{
									case 135:
									case 145:
										物品对怪防御力 += 233;
										break;
									case 160:
										物品对怪防御力 += 263;
										break;
									}
								}
							}
							else
							{
								物品防御力 += 128;
								物品属性_生命力增加 += 510;
							}
							break;
						}
						得到物品障力(value, 物品属性强);
						break;
					case 7:
						if (value.FLD_LEVEL >= 150)
						{
							if (物品属性强 >= 10)
							{
								物品武功回避 += 物品属性强 + 10;
							}
							else if (物品属性强 >= 9)
							{
								物品武功回避 += 物品属性强 + 6;
							}
							else if (物品属性强 >= 8)
							{
								物品武功回避 += 物品属性强 + 4;
							}
							else if (物品属性强 >= 7)
							{
								物品武功回避 += 物品属性强 + 2;
							}
							else if (物品属性强 >= 6)
							{
								物品武功回避 += 物品属性强 + 1;
							}
							else
							{
								物品武功回避 += 物品属性强;
							}
						}
						if (value.FLD_LEVEL >= 170) //24.0 EVIAS +170级防具强化加成
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 16; //24.0 EVIAS +170级前3强化每级+16防御(比160级+3)
							}
							else
							{
								物品防御力 += 48 + (物品属性强 - 3) * 35; //24.0 EVIAS +170级强化4以上更高加成
                                        }
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(170, 物品属性强); //24.0 EVIAS +170级首饰加成
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(170, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 160)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 13;
							}
							else
							{
								物品防御力 += 39 + (物品属性强 - 3) * 28;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(160, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(160, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 150)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 11;
							}
							else
							{
								物品防御力 += 30 + (物品属性强 - 3) * 25;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(150, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(150, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 140)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 9;
							}
							else
							{
								物品防御力 += 28 + (物品属性强 - 3) * 21;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(140, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(140, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 130)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 7;
							}
							else
							{
								物品防御力 += 26 + (物品属性强 - 3) * 17;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(130, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(130, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 120)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 6;
							}
							else
							{
								物品防御力 += 20 + (物品属性强 - 3) * 15;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(120, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(120, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 115)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 5;
							}
							else
							{
								物品防御力 += 15 + (物品属性强 - 3) * 9;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(115, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(115, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 100)
						{
							if (物品属性强 < 5)
							{
								物品防御力 += 物品属性强 * 4;
							}
							else
							{
								物品防御力 += 11 + (物品属性强 - 4) * 7;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(100, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(100, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 80)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 3;
							}
							else
							{
								物品防御力 += 9 + (物品属性强 - 3) * 5;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(80, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(80, 物品属性强);
							}
						}
						else if (value.FLD_LEVEL >= 60)
						{
							if (物品属性强 < 4)
							{
								物品防御力 += 物品属性强 * 2;
							}
							else
							{
								物品防御力 += 6 + (物品属性强 - 3) * 3;
							}
							if (value.FLD_RESIDE1 == 11)
							{
								物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(60, 物品属性强);
							}
							else
							{
								物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(60, 物品属性强);
							}
						}
						break;
					case 8:
					case 9:
					case 10:
					case 11:
					case 12:
					case 13:
						break;
					case 14:
						switch (物品属性强)
						{
						case 1:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 4;
							}
							else
							{
								物品防御力 += 3;
							}
							break;
						case 2:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 8;
							}
							else
							{
								物品防御力 += 6;
							}
							break;
						case 3:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 12;
							}
							else
							{
								物品防御力 += 9;
							}
							break;
						case 4:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 16;
							}
							else
							{
								物品防御力 += 12;
							}
							break;
						case 5:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 20;
							}
							else
							{
								物品防御力 += 15;
							}
							break;
						case 6:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 25;
								物品属性_生命力增加 += 5;
								物品对人战斗力百分比 = 0.03;
							}
							else
							{
								物品防御力 += 19;
								物品属性_生命力增加 += 5;
							}
							break;
						case 7:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 30;
								物品属性_生命力增加 += 10;
								物品对人战斗力百分比 = 0.06;
							}
							else
							{
								物品防御力 += 23;
								物品属性_生命力增加 += 10;
							}
							break;
						case 8:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 37;
								物品属性_生命力增加 += 15;
								物品对人战斗力百分比 = 0.09;
							}
							else
							{
								物品防御力 += 29;
								物品属性_生命力增加 += 15;
							}
							break;
						case 9:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 47;
								物品属性_生命力增加 += 20;
								物品对人战斗力百分比 = 0.12;
							}
							else
							{
								物品防御力 += 38;
								物品属性_生命力增加 += 20;
							}
							break;
						case 10:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 63;
								物品属性_生命力增加 += 30;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 53;
								物品属性_生命力增加 += 30;
							}
							break;
						case 11:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 70;
								物品属性_生命力增加 += 40;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 59;
								物品属性_生命力增加 += 40;
							}
							break;
						case 12:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 77;
								物品属性_生命力增加 += 50;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 65;
								物品属性_生命力增加 += 50;
							}
							break;
						case 13:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 84;
								物品属性_生命力增加 += 260;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 71;
								物品属性_生命力增加 += 260;
							}
							break;
						case 14:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 91;
								物品属性_生命力增加 += 260;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 77;
								物品属性_生命力增加 += 260;
							}
							break;
						case 15:
						case 16:
						case 17:
						case 18:
						case 19:
						case 20:
							if (value.FLD_XW >= 35)
							{
								物品防御力 += 98;
								物品属性_生命力增加 += 260;
								物品对人战斗力百分比 = 0.15;
							}
							else
							{
								物品防御力 += 83;
								物品属性_生命力增加 += 260;
							}
							break;
						}
						break;
					case 3:
					case 4:
						break;
					}
					break;
				case 3:
					if (FLD_RESIDE2 != 8)
					{
						break;
					}
					if (value.FLD_LEVEL >= 170) //24.0 EVIAS +170级生命力强化加成
					{
						物品属性_生命力增加 += 物品属性强 * 200; 
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(170, 物品属性强); 
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(170, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 160)
					{
						物品属性_生命力增加 += 物品属性强 * 160;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(160, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(160, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 150)
					{
						物品属性_生命力增加 += 物品属性强 * 130;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(150, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(150, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 140)
					{
						物品属性_生命力增加 += 物品属性强 * 100;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(140, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(140, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 130)
					{
						物品属性_生命力增加 += 物品属性强 * 80;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(130, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(130, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 120)
					{
						物品属性_生命力增加 += 物品属性强 * 70;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(120, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(120, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 115)
					{
						物品属性_生命力增加 += 物品属性强 * 50;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(115, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(115, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 100)
					{
						物品属性_生命力增加 += 物品属性强 * 40;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(100, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(100, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 80)
					{
						物品属性_生命力增加 += 物品属性强 * 15;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(80, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(80, 物品属性强);
						}
					}
					else if (value.FLD_LEVEL >= 60)
					{
						物品属性_生命力增加 += 物品属性强 * 5;
						if (value.FLD_RESIDE1 == 11)
						{
							物品属性_障力恢复量增加 += 得到首饰强化增加障力恢复量(60, 物品属性强);
						}
						else
						{
							物品属性_追加伤害值 += 得到首饰强化增加障力恢复量(60, 物品属性强);
						}
					}
					break;
				case 4:
					物品属性强 = int.Parse(ysqh.Substring(ysqh.Length - 3, 3));
					if (物品属性强 >= World.披风强化最大数量)
					{
						物品属性强 = World.披风强化最大数量;
					}
					if (物品属性强 > 1)
					{
						if ((物品属性强 & 1) == 1)
						{
							物品防御力 += (物品属性强 - 1) / 2 + 5;
							前置物品攻击力 += (物品属性强 - 1) / 2 + 5;
							物品攻击力后置 += (物品属性强 - 1) / 2 + 5;
							物品属性_生命力增加 += (物品属性强 - 1) / 2 * 10 + 550;
							物品属性_获得金钱增加 += (物品属性强 - 1) * 2;
						}
						else
						{
							物品防御力 += 物品属性强 / 2 + 5;
							前置物品攻击力 += 物品属性强 / 2 + 5;
							物品攻击力后置 += 物品属性强 / 2 + 5;
							物品属性_生命力增加 += 物品属性强 / 2 * 10 + 550;
							物品属性_获得金钱增加 += 物品属性强 * 2;
						}
						if (物品属性强 > 4)
						{
							物品武功命中 += 物品属性强 / 2;
							物品武功回避 += 物品属性强 / 2;
						}
						if (物品属性强 >= 99)
						{
							物品防御力 += (物品属性强 - 98) * 3;
							前置物品攻击力 += (物品属性强 - 98) * 3;
							物品属性_生命力增加 += (物品属性强 - 98) * 20;
						}
						if (物品属性强 >= 500)
						{
							物品属性_武功攻击力 += 物品属性强 - 499;
						}
					}
					else
					{
						物品防御力 += 5;
					}
					物品增加负重 += 物品属性强;
					物品对人战斗力 += 物品属性强 / World.强化附加战斗力;
					break;
				}
				break;
			case 9:
				物品属性阶段类型 = 0;
				物品属性阶段数 = 0;
				物品属性强类型 = int.Parse(ysqh.Substring(ysqh.Length - 9, 1));
				物品属性强 = int.Parse(ysqh.Substring(ysqh.Length - 3, 3)) + (int)物品属性_追加强化;
				if (物品属性强类型 != 4)
				{
					break;
				}
				if (物品属性强 >= World.披风强化最大数量)
				{
					物品属性强 = World.披风强化最大数量;
				}
				if (物品属性强 > 1)
				{
					if ((物品属性强 & 1) == 1)
					{
						物品防御力 += (物品属性强 - 1) / 2 + 5;
						前置物品攻击力 += (物品属性强 - 1) / 2 + 5;
						物品攻击力后置 += (物品属性强 - 1) / 2 + 5;
						物品属性_生命力增加 += (物品属性强 - 1) / 2 * 10 + 550;
						物品属性_获得金钱增加 += (物品属性强 - 1) * 2;
					}
					else
					{
						物品防御力 += 物品属性强 / 2 + 5;
						前置物品攻击力 += 物品属性强 / 2 + 5;
						物品攻击力后置 += 物品属性强 / 2 + 5;
						物品属性_生命力增加 += 物品属性强 / 2 * 10 + 550;
						物品属性_获得金钱增加 += 物品属性强 * 2;
					}
					if (物品属性强 > 4)
					{
						物品武功命中 += 物品属性强 / 2;
						物品武功回避 += 物品属性强 / 2;
					}
					if (物品属性强 >= 99)
					{
						物品防御力 += (物品属性强 - 98) * 3;
						前置物品攻击力 += (物品属性强 - 98) * 3;
						物品属性_生命力增加 += (物品属性强 - 98) * 20;
					}
					if (物品属性强 >= 500)
					{
						物品属性_武功攻击力 += 物品属性强 - 499;
					}
				}
				else
				{
					物品防御力 += 5;
				}
				物品增加负重 += 物品属性强;
				物品对人战斗力 += 物品属性强 / World.强化附加战斗力;
				break;
			case 10:
				物品属性阶段类型 = int.Parse(ysqh.Substring(ysqh.Length - 4, 1));
				物品属性阶段数 = int.Parse(ysqh.Substring(ysqh.Length - 3, 1)) + 1;
				物品属性强类型 = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
				物品属性强 = int.Parse(ysqh.Substring(ysqh.Length - 2, 2)) + (int)物品属性_追加强化;
				if (物品属性强类型 == 1)
				{
					if (value.FLD_RESIDE2 != 4)
					{
						break;
					}
					计算武器属性(value);
					if (物品属性强 > 5 && 物品属性阶段类型 != 0)
					{
						物品属性阶段数 += 物品属性强 - 5;
					}
					int num = 1;
					if (触发属性提升 == 2)
					{
						num = 2;
					}
					if (物品属性阶段类型 != 0 && 物品属性阶段数 > 0)
					{
						switch (物品属性阶段类型)
						{
						case 1:
							物品属性_降低百分比防御 = (double)物品属性阶段数 * 0.01 * (double)num;
							物品属性_对怪伤害 = (double)物品属性阶段数 * 0.005 * (double)num;
							break;
						case 2:
							物品属性_初始化愤怒概率百分比 = 物品属性阶段数 * num;
							break;
						case 3:
							物品属性_增加百分比命中 += (double)物品属性阶段数 * 0.01 * (double)num;
							break;
						case 4:
							物品属性_武功攻击力 += (int)((double)物品属性阶段数 * 0.5) * num;
							break;
						case 5:
							物品属性_追加伤害值 += 物品属性阶段数 * 3 * num;
							break;
						case 6:
							物品属性_追加中毒几率百分比 += (double)物品属性阶段数 * 0.01 * (double)num;
							前置物品攻击力 += 物品属性阶段数 * 3 * num;
							物品攻击力后置 += 物品属性阶段数 * 3 * num;
							break;
						}
					}
				}
				else
				{
					if (物品属性强类型 != 2 || value.FLD_RESIDE2 != 1)
					{
						break;
					}
					计算衣服属性(value);
					得到物品障力(value, 物品属性强);
					int num2 = 1;
					if (触发属性提升 == 2)
					{
						num2 = 2;
					}
					if (物品属性阶段类型 != 0 && 物品属性阶段数 > 0)
					{
						switch (物品属性阶段类型)
						{
						case 1:
							物品属性_降低百分比攻击 = (double)物品属性阶段数 * 0.01 * (double)num2;
							物品属性_对怪伤害 = (double)物品属性阶段数 * 0.005 * (double)num2;
							break;
						case 2:
							物品属性_愤怒值增加 = 物品属性阶段数 * num2;
							break;
						case 3:
							物品属性_增加百分比回避 += (double)物品属性阶段数 * 0.01 * (double)num2;
							break;
						case 4:
							物品属性_武功防御力增加 += 物品属性阶段数 * 8 * num2;
							break;
						case 5:
							物品防御力 += 物品属性阶段数 * 3 * num2;
							break;
						case 6:
							物品属性_追加中毒几率百分比 += (double)物品属性阶段数 * 0.01 * (double)num2;
							break;
						}
					}
				}
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "得到强化出错：" + ex);
		}
	}
}

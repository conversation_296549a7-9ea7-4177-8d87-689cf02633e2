using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 石头属性调整 : Form
{
	private IContainer components = null;

	private Button button1;

	private ListView listView2;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader14;

	private TextBox textBox1;

	private Label label1;

	private Label label2;

	private TextBox textBox2;

	private Label label3;

	private TextBox textBox3;

	private Button button2;

	private ToolTip toolTip1;

	private Label label4;

	public 石头属性调整()
	{
		InitializeComponent();
	}

	private void 石头属性调整_Load(object sender, EventArgs e)
	{
		刷新();
	}

	private int 类型还原(string 类型)
	{
		int result = 0;
		switch (类型)
		{
		case "升级成功率":
			result = 9;
			break;
		case "武功防御力":
			result = 11;
			break;
		case "武功攻击力":
			result = 7;
			break;
		case "追加伤害值":
			result = 10;
			break;
		case "物品防御力":
			result = 2;
			break;
		case "命中率":
			result = 5;
			break;
		case "回避率":
			result = 6;
			break;
		case "物品攻击力":
			result = 1;
			break;
		case "经验获得":
			result = 15;
			break;
		case "生命力":
			result = 3;
			break;
		case "获得金钱":
			result = 12;
			break;
		case "死亡损失经验减少":
			result = 13;
			break;
		case "内功力":
			result = 4;
			break;
		}
		return result;
	}

	private string 类型命名(int 类型)
	{
		string result = "未知属性";
		switch (类型)
		{
		case 1:
			result = "物品攻击力";
			break;
		case 2:
			result = "物品防御力";
			break;
		case 3:
			result = "生命力";
			break;
		case 4:
			result = "内功力";
			break;
		case 5:
			result = "命中率";
			break;
		case 6:
			result = "回避率";
			break;
		case 7:
			result = "武功攻击力";
			break;
		case 9:
			result = "升级成功率";
			break;
		case 10:
			result = "追加伤害值";
			break;
		case 11:
			result = "武功防御力";
			break;
		case 12:
			result = "获得金钱";
			break;
		case 13:
			result = "死亡损失经验减少";
			break;
		case 15:
			result = "经验获得";
			break;
		}
		return result;
	}

	public void 刷新()
	{
		listView2.Items.Clear();
		string sqlCommand = "SELECT * FROM TBL_XWWL_STONE";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			ListViewItem listViewItem = new ListViewItem();
			listViewItem.SubItems.Clear();
			listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["ID"].ToString();
			string text = 类型命名(int.Parse(dBToDataTable.Rows[i]["FLD_TYPE"].ToString()));
			listViewItem.SubItems.Add(text);
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_VALUE"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_增减"].ToString());
			listView2.Items.Add(listViewItem);
		}
		dBToDataTable.Dispose();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		try
		{
			写数据(int.Parse(textBox1.Text), int.Parse(textBox2.Text), int.Parse(textBox3.Text));
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox1.Text;
			listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox2.Text;
			listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox3.Text;
			new World().Set石头属性();
		}
		catch
		{
			MessageBox.Show("输入错误,请检查!");
		}
	}

	private void listView2_MouseClick(object sender, MouseEventArgs e)
	{
		ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
		string text = 类型还原(listView2.Items[selectedIndices[0]].SubItems[1].Text).ToString();
		textBox1.Text = text;
		textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
		textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
	}

	private int 写数据(int type, int value, int 增减)
	{
		string sqlCommand = $"UPDATE TBL_XWWL_STONE SET FLD_增减={增减} WHERE FLD_TYPE={type} and FLD_VALUE={value}";
		try
		{
			int num = DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			if (num == -1)
			{
				MessageBox.Show("写入表错误1,请检查|" + type + "|" + value + "|" + 增减);
				return -1;
			}
		}
		catch
		{
			MessageBox.Show("写入表错误2,请检查|" + type + "|" + value + "|" + 增减);
		}
		return -1;
	}

	private void button2_Click(object sender, EventArgs e)
	{
		string sqlCommand = $"SELECT FLD_VALUE FROM TBL_XWWL_STONE where FLD_VALUE={int.Parse(textBox2.Text)}";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		if (dBToDataTable.Rows.Count > 0)
		{
			MessageBox.Show("数量不能重复增加失败");
		}
		else
		{
			string sqlCommand2 = $"INSERT INTO TBL_XWWL_STONE  (FLD_TYPE,FLD_VALUE, FLD_增减)VALUES({int.Parse(textBox1.Text)},{int.Parse(textBox2.Text)},{int.Parse(textBox3.Text)})";
			int num = DBA.ExeSqlCommand(sqlCommand2, "PublicDb");
			if (num == -1)
			{
				MessageBox.Show(string.Concat(new object[1] { "写入表错误1,请检查" }));
				return;
			}
			MessageBox.Show(string.Concat(new object[1] { "增加成功" }));
			new World().Set石头属性();
			刷新();
		}
		dBToDataTable.Dispose();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(石头属性调整));
            this.button1 = new System.Windows.Forms.Button();
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.button2 = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.label4 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(377, 199);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(111, 29);
            this.button1.TabIndex = 54;
            this.button1.Text = "修改立即生效";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(12, 12);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(338, 314);
            this.listView2.TabIndex = 55;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listView2_MouseClick);
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "id";
            this.columnHeader11.Width = 50;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "石头类型";
            this.columnHeader12.Width = 104;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "对应数";
            this.columnHeader13.Width = 68;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "增减";
            this.columnHeader14.Width = 86;
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(422, 29);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(66, 21);
            this.textBox1.TabIndex = 56;
            this.toolTip1.SetToolTip(this.textBox1, "对应石头属性类型");
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(385, 34);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(29, 12);
            this.label1.TabIndex = 57;
            this.label1.Text = "类型";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(385, 70);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 59;
            this.label2.Text = "数量";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(422, 65);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(66, 21);
            this.textBox2.TabIndex = 58;
            this.toolTip1.SetToolTip(this.textBox2, "对应石头属性数量");
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(384, 111);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 61;
            this.label3.Text = "加减";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(422, 106);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(66, 21);
            this.textBox3.TabIndex = 60;
            this.toolTip1.SetToolTip(this.textBox3, "负数减反之加");
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(377, 246);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(111, 29);
            this.button2.TabIndex = 62;
            this.button2.Text = "新增立即生效";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.ForeColor = System.Drawing.Color.Red;
            this.label4.Location = new System.Drawing.Point(363, 303);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(125, 12);
            this.label4.TabIndex = 63;
            this.label4.Text = "鼠标点放这里查看类型";
            this.toolTip1.SetToolTip(this.label4, "\"物品攻击力\":1\r\n\"物品防御力\":2\r\n\"生命力\":3\r\n\"内功力\":4                \r\n\"命中率\":5  \r\n\"回避率\":6\r\n\"武功攻击" +
        "力\":7\r\n\"升级成功率\":9   \r\n\"追加伤害值\":10  \r\n\"武功防御力\":11           \r\n                  ");
            // 
            // 石头属性调整
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(500, 338);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.button1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "石头属性调整";
            this.Text = "石头属性调整";
            this.Load += new System.EventHandler(this.石头属性调整_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

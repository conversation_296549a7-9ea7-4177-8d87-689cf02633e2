using System;
using System.Net;
using System.Text;

namespace loginServer;

public class NetState
{
	public DateTime Ljtime = DateTime.Now;

	public byte[] g_cur_key = new byte[32]
	{
		201, 55, 147, 8, 162, 107, 81, 151, 165, 41,
		136, 17, 184, 111, 78, 144, 233, 247, 211, 225,
		191, 92, 209, 199, 217, 63, 156, 129, 226, 111,
		89, 152
	};

	private IPAddress m_Address;

	public int WorldId;

	public int packconn;

	public Player Player;

	private ClientInfo CI;

	private bool m_Running;

	private string m_ToString;

	public bool Running => m_Running;

	public override string ToString()
	{
		return m_ToString;
	}

	public void addWorldIdd(int worldid)
	{
		try
		{
			if (!World.ConnectLst.TryGetValue(worldid, out var _))
			{
				World.ConnectLst.TryAdd(worldid, this);
			}
		}
		catch
		{
		}
	}

	public void delWorldIdd()
	{
		try
		{
			if (World.ConnectLst.TryRemove(WorldId, out var _))
			{
				// 2025-0618 EVIAS 添加断开连接统计日志
				Form1.WriteLine(3, $"连接断开 WorldId:{WorldId} IP:{ToString()} 剩余连接数:{World.ConnectLst.Count}");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, $"删除连接失败 WorldId:{WorldId} 错误:{ex.Message}");
		}
	}

	private static long IpToInt(string ip)
	{
		char[] separator = new char[1] { '.' };
		string[] array = ip.Split(separator);
		return (long.Parse(array[0]) << 24) | (long.Parse(array[1]) << 16) | (long.Parse(array[2]) << 8) | long.Parse(array[3]);
	}

	public NetState(ClientInfo ClientInfo)
	{
		ClientInfo.Client = this;
		CI = ClientInfo;
		Ljtime = DateTime.Now;
		m_Running = false;
		try
		{
			m_Address = new IPAddress((uint)IPAddress.HostToNetworkOrder((int)IpToInt(ClientInfo.IpAddress)));
			m_ToString = ClientInfo.IpAddress;
		}
		catch
		{
			m_Address = IPAddress.None;
			m_ToString = "(error)";
		}
		WorldId = ClientInfo.WorldId;
		Player = new Player(this);
		using (new Lock(World.ConnectLst, "listLock"))
		{
			if (!InList(ClientInfo.WorldId))
			{
				World.ConnectLst.TryAdd(ClientInfo.WorldId, this);
				// 2025-0618 EVIAS 添加连接统计日志
				Form1.WriteLine(3, $"新连接添加 WorldId:{ClientInfo.WorldId} IP:{ClientInfo.IpAddress} 总连接数:{World.ConnectLst.Count}");
			}
			else
			{
				Form1.WriteLine(1, $"重复连接 WorldId:{ClientInfo.WorldId} IP:{ClientInfo.IpAddress}");
				Dispose();
			}
		}
	}

	private bool InList(int key)
	{
		NetState value;
		return World.ConnectLst.TryGetValue(key, out value);
	}

	public void Start()
	{
		m_Running = true;
	}

	public void 发送(string msg)
	{
		try
		{
			byte[] bytes = Encoding.Default.GetBytes(msg);
			Send(bytes, bytes.Length);
		}
		catch (Exception)
		{
		}
	}

	public unsafe void Send(byte[] toSendBuff, int len)
	{
		try
		{
			fixed (byte* ptr = toSendBuff)
			{
				CI.Server.Send(CI.ConnId, (IntPtr)ptr, toSendBuff.Length);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Send()_Exception出错" + WorldId + "|" + ToString() + "  " + ex.Message);
			Dispose();
		}
	}

	public void ProcessDataReceived(byte[] data, int length)
	{
		try
		{
			packconn++;
			if (packconn > 1000000)
			{
				packconn = 0;
			}
			byte[] dst = new byte[4];
			Buffer.BlockCopy(data, 0, dst, 0, 2);
			switch (BitConverter.ToInt32(dst, 0))
			{
			case 32768:
			case 32778:
			case 32780:
			case 32790:
				Player.ManagePacket(data, length);
				break;
			default:
				Player.ManagePacket(data, length);
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "ProcessDataReceived()出错" + WorldId + "|" + ToString() + "  " + ex.Message);
			Dispose();
		}
	}

	public void Dispose()
	{
		try
		{
			if (!World.m_Disposed.Contains(this))
			{
				CI.Server.Disconnect(CI.ConnId);
				World.m_Disposed.Enqueue(this);
				m_Address = null;
				m_Running = false;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "  Dispose(bool  flush)出错" + WorldId + "|" + ToString() + "  " + ex.Message);
		}
	}
}

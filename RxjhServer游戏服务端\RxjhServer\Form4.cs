using System;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form4 : Form
{
	private IContainer components;

	private GroupBox groupBox1;

	private Label label1;

	private TextBox textBox1;

	private Button button1;

	private Label label2;

	private TextBox textBox2;

	private StatusStrip statusStrip1;

	private ToolStripStatusLabel toolStripStatusLabel1;

	private ToolStripStatusLabel tishi;

	private Label label3;

	private TextBox textBox3;

	private Label label4;

	private ListBox listBox2;

	private Button button7;

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form4));
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.button7 = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.listBox2 = new System.Windows.Forms.ListBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.tishi = new System.Windows.Forms.ToolStripStatusLabel();
            this.groupBox1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.button7);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.listBox2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.textBox3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.textBox2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.textBox1);
            this.groupBox1.Controls.Add(this.button1);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(460, 221);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "角色换名";
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(219, 22);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(75, 23);
            this.button7.TabIndex = 9;
            this.button7.Text = "查找";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(302, 17);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "角色列表";
            // 
            // listBox2
            // 
            this.listBox2.FormattingEnabled = true;
            this.listBox2.ItemHeight = 12;
            this.listBox2.Location = new System.Drawing.Point(304, 38);
            this.listBox2.Name = "listBox2";
            this.listBox2.Size = new System.Drawing.Size(127, 100);
            this.listBox2.TabIndex = 7;
            this.listBox2.Click += new System.EventHandler(this.listBox2_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(6, 27);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 6;
            this.label3.Text = "请输入玩家帐号";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(113, 24);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(100, 21);
            this.textBox3.TabIndex = 5;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(6, 85);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "更改后的角色名字";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(113, 81);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(100, 21);
            this.textBox2.TabIndex = 3;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 57);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "请输入角色名字";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(113, 54);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(100, 21);
            this.textBox1.TabIndex = 1;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(113, 142);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(100, 29);
            this.button1.TabIndex = 0;
            this.button1.Text = "确认更改";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.tishi});
            this.statusStrip1.Location = new System.Drawing.Point(0, 250);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new System.Drawing.Size(488, 22);
            this.statusStrip1.TabIndex = 65;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(68, 17);
            this.toolStripStatusLabel1.Text = "信息提示：";
            // 
            // tishi
            // 
            this.tishi.ForeColor = System.Drawing.Color.Red;
            this.tishi.Name = "tishi";
            this.tishi.Size = new System.Drawing.Size(0, 17);
            // 
            // Form4
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(488, 272);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "Form4";
            this.Text = "角色换名工具";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
	}

	public Form4()
	{
		InitializeComponent();
        components = new Container(); //Evias
    }

	private void button1_Click(object sender, EventArgs e)
	{
		if (!(textBox1.Text.Trim() == string.Empty) && !(textBox2.Text.Trim() == string.Empty) && !(textBox3.Text.Trim() == string.Empty))
		{
			Players players = World.检查玩家name(textBox1.Text);
			if (players != null)
			{
				tishi.Text = "当前玩家在线无法更改,请离线后在更改";
				return;
			}
			string sqlCommand = "SELECT * FROM TBL_XWWL_GuildMember WHERE FLD_NAME ='" + textBox1.Text + "'";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
			string sqlCommand2 = "SELECT * FROM TBL_XWWL_Guild WHERE G_Master ='" + textBox1.Text + "'";
			DataTable dBToDataTable2 = DBA.GetDBToDataTable(sqlCommand2);
			if (dBToDataTable.Rows.Count <= 0 && dBToDataTable2.Rows.Count <= 0)
			{
				string sqlCommand3 = "UPDATE TBL_XWWL_Char SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
				SqlParameter[] prams = new SqlParameter[3]
				{
					SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
					SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
					SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox2.Text)
				};
				DBA.ExeSqlCommand(sqlCommand3, prams);
				sqlCommand3 = "UPDATE TBL_XWWL_Warehouse SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
				SqlParameter[] prams2 = new SqlParameter[3]
				{
					SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
					SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
					SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox2.Text)
				};
				DBA.ExeSqlCommand(sqlCommand3, prams2);
				tishi.Text = "换名成功请进入游戏查看";
				listBox2.Items.Clear();
				string sqlCommand4 = "select * from tbl_xwwl_char  where fld_id='" + textBox3.Text + "'";
				DataTable dBToDataTable3 = DBA.GetDBToDataTable(sqlCommand4);
				for (int i = 0; i < dBToDataTable3.Rows.Count; i++)
				{
					listBox2.Items.Add(dBToDataTable3.Rows[i]["FLD_NAME"].ToString());
				}
			}
			else
			{
				tishi.Text = "请通知玩家退出门派后在更名";
			}
		}
		else
		{
			tishi.Text = "请认真填写每项，每项不能为空";
		}
	}

	private void button7_Click(object sender, EventArgs e)
	{
		try
		{
			listBox2.Items.Clear();
			string sqlCommand = "select * from tbl_xwwl_char  where fld_id='" + textBox3.Text + "'";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				listBox2.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			}
			tishi.Text = "查询完毕";
		}
		catch
		{
			tishi.Text = "出现未知错误";
		}
	}

	private void listBox2_Click(object sender, EventArgs e)
	{
		textBox1.Text = listBox2.Text;
	}
}

using System;

namespace RxjhServer;

public class 离线挂机系统
{
        public static void 离线挂机(Players player) //EVIAS  修复NULL错误
    {
        try
        {
            if (player == null) return;

            if ((int)DateTime.Now.Subtract(player.自动存仓库时间).TotalMilliseconds >= World.自动存取时间)
            {
                if (player.是否假人 != 0)
                {
                    player.内挂出售(player);
                }
                player.自动存仓库时间 = DateTime.Now;
                return;
            }

            if ((int)DateTime.Now.Subtract(player.内挂挂机打怪时间).TotalMilliseconds >= World.内挂打怪说话时间)
            {
                if (!string.IsNullOrEmpty(World.内挂打怪说话内容))
                {
                    player.内挂喊话(player, World.内挂打怪说话内容);
                }
                player.内挂挂机打怪时间 = DateTime.Now;
            }

            if (World.假人自动结婚 == 1 && player.假人结婚是否成败 == 0 && string.IsNullOrEmpty(player.FLD_情侣))
            {
                player.假人自动结婚();
            }

            if (World.假人加入门派 == 1)
            {
                player.假人自动加门派();
            }

            if (player.人物_HP <= (int)((double)player.人物最大_HP * 0.95))
            {
                int sl = player.人物最大_HP - player.人物_HP;
                player.加血(sl);
                player.吃药效果(1000000102);
                player.更新HP_MP_SP();
            }

            if (player.人物_MP <= (int)((double)player.人物最大_MP * 0.95))
            {
                player.加魔(20000);
                player.吃药效果(1000000104);
                player.更新HP_MP_SP();
            }

            long num = player.内挂获取自动拾取物品(player);
            if (num != 0L && World.ItmeTeM != null && World.ItmeTeM.ContainsKey(num))
            {
                int num2 = player.得到包裹空位(player);
                if (player.离线挂机打怪模式 != 0 || num2 == -1)
                {
                    player.内挂拾取物品包(player, num);
                }
                else
                {
                    player.假人清理背包();
                }
            }

            float num3 = player.人物坐标_X - (float)player.自动挂机坐标X;
            float num4 = player.人物坐标_Y - (float)player.自动挂机坐标Y;
            double num5 = (int)Math.Sqrt((double)num3 * (double)num3 + (double)num4 * (double)num4);

            if ((int)num5 > World.离线挂机打怪范围)
            {
                player.离线挂机当前攻击怪物 = 0;
                player.内挂移动包(player, player.自动挂机坐标X, player.自动挂机坐标Y, (float)num5);
                return;
            }

            if ((long)player.人物_HP <= 0L || player.Player死亡)
            {
                player.移动(player.自动挂机坐标X, player.自动挂机坐标Y, player.人物坐标_Z, player.自动挂机地图);
                player.人物_HP = player.人物最大_HP;
                player.更新HP_MP_SP();
                player.Player死亡 = false;
                return;
            }

            if (player.离线挂机打怪模式 == 1)
            {
                player.内挂吃药(player);
                player.内挂移动(player);
                player.内挂加气动点(player);
            }

            if (player.组队id != 0 && player.Player_Job == 5)
            {
                int 武功ID = 501203;
                if (player.Player_Job_leve >= 6)
                {
                    if (player.追加状态列表 != null && !player.追加状态列表.ContainsKey(501501))
                    {
                        武功ID = 501501;
                    }
                    else if (!player.追加状态列表.ContainsKey(501502))
                    {
                        武功ID = 501502;
                    }
                    else if (!player.追加状态列表.ContainsKey(501601))
                    {
                        武功ID = 501601;
                    }
                    else if (!player.追加状态列表.ContainsKey(501602))
                    {
                        武功ID = 501602;
                    }
                    else if (!player.追加状态列表.ContainsKey(501603))
                    {
                        武功ID = 501603;
                    }
                }
                else if (player.追加状态列表 != null)
                {
                    if (!player.追加状态列表.ContainsKey(501301))
                    {
                        武功ID = 501301;
                    }
                    else if (!player.追加状态列表.ContainsKey(501303))
                    {
                        武功ID = 501303;
                    }
                    else if (!player.追加状态列表.ContainsKey(501401))
                    {
                        武功ID = 501401;
                    }
                    else if (!player.追加状态列表.ContainsKey(501402))
                    {
                        武功ID = 501402;
                    }
                    else if (!player.追加状态列表.ContainsKey(501403))
                    {
                        武功ID = 501403;
                    }
                }
                攻击系统.魔法攻击(player, 武功ID, player.人物全服ID);
                return;
            }

            if (player.组队id != 0 && player.Player_Job == 4 &&
                player.追加状态列表 != null && !player.追加状态列表.ContainsKey(401303))
            {
                int 武功ID2 = 401303;
                if (!player.追加状态列表.ContainsKey(401202))
                {
                    武功ID2 = 401202;
                }
                else if (!player.追加状态列表.ContainsKey(401203))
                {
                    武功ID2 = 401203;
                }
                else if (!player.追加状态列表.ContainsKey(401301))
                {
                    武功ID2 = 401301;
                }
                else if (!player.追加状态列表.ContainsKey(401302))
                {
                    武功ID2 = 401302;
                }
                攻击系统.魔法攻击(player, 武功ID2, player.人物全服ID);
                return;
            }

            player.离线挂机当前攻击怪物 = player.内挂自动攻击NPC目标(player, player.自动挂机坐标X, player.自动挂机坐标Y);
            if (player.离线挂机当前攻击怪物 > 10000)
            {
                NpcClass npc = MapClass.GetNpc(player.人物坐标_地图, player.离线挂机当前攻击怪物);
                if (npc != null && !npc.NPC死亡 && npc.Rxjh_HP > 0)
                {
                    float num6 = player.人物坐标_X - npc.Rxjh_X;
                    float num7 = player.人物坐标_Y - npc.Rxjh_Y;
                    double num8 = (int)Math.Sqrt((double)num6 * (double)num6 + (double)num7 * (double)num7);
                    if ((int)num8 <= 30)
                    {
                        player.内挂攻击包(player);
                    }
                    else
                    {
                        player.内挂移动包(player, npc.Rxjh_X, npc.Rxjh_Y, (float)num8);
                    }
                }
            }
            else
            {
                player.内挂攻击包(player);
            }
        }
        catch (Exception ex)
        {
            RxjhServer.DbClss.RxjhClass.HandleSystemException(ex, "离线挂机系统", "自动打怪", "离线挂机处理");
            Form1.WriteLine(1, "离线自动打怪出错:" + ex.Message);
        }
    }
}

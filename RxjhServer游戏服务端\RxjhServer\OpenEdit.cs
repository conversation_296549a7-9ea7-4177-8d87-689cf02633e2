using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class OpenEdit : Form
{
	private IContainer components = null;

	private ListView listView2;

	private ColumnHeader columnHeader11;

	private ColumnHeader columnHeader12;

	private ColumnHeader columnHeader13;

	private ColumnHeader columnHeader14;

	private ColumnHeader columnHeader15;

	private ColumnHeader columnHeader16;

	private ColumnHeader columnHeader17;

	private Button button43;

	private Panel panel1;

	private Button button1;

	private TextBox textBoxPP;

	private Label label6;

	private TextBox textBoxNUM;

	private Label label2;

	private TextBox textBoxNAMEX;

	private Label label5;

	private TextBox textBoxPIDX;

	private Label label4;

	private TextBox textBoxNAME;

	private Label label3;

	private TextBox textBoxPID;

	private Label label1;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 删除ToolStripMenuItem;

	private ComboBox comboBox11;

	private Label label40;

	private ListBox listBox3;

	private Button button3;

	private ColumnHeader columnHeader1;

	private CheckBox checkBox1;

	private Button button4;

	private TextBox textBox7;

	private Button button5;

	private Button button6;

	private Button button7;

	private ComboBox comboBox1;

	private Button button9;

	private Button button8;

	private ColumnHeader columnHeader2;

	private ColumnHeader columnHeader3;

	private ColumnHeader columnHeader4;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader6;

	private TextBox textBoxM5;

	private Label label7;

	private TextBox textBoxM4;

	private Label label8;

	private TextBox textBoxM3;

	private Label label9;

	private TextBox textBoxM2;

	private Label label10;

	private TextBox textBoxM1;

	private Label label11;

	private ColumnHeader columnHeader8;

	private ColumnHeader columnHeader9;

	private ColumnHeader columnHeader10;

	private ColumnHeader columnHeader18;

	private TextBox textBox2;

	private Label label13;

	private TextBox textBox1;

	private Label label12;

	private TextBox textBox3;

	private Label label14;

	private CheckBox checkBox2;

	private Button button2;

	public string s_id { get; set; }

	public OpenEdit()
	{
		InitializeComponent();
		listView2.ContextMenuStrip = contextMenuStrip1;
		contextMenuStrip1.Closed += contextMenuStrip1_Closed;
		button1.Enabled = false;
		button2.Enabled = false;
	}

	private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
	{
		listView2.ContextMenuStrip = contextMenuStrip1;
	}

	private void button43_Click(object sender, EventArgs e)
	{
		Thread.Sleep(1000);
		刷新();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		bool flag = true;
		if (!string.IsNullOrEmpty(s_id))
		{
			flag = false;
		}
		string text = textBoxPID.Text;
		string text2 = textBoxNAME.Text;
		string text3 = textBoxPIDX.Text;
		string text4 = textBoxNAMEX.Text;
		string text5 = textBoxNUM.Text;
		string text6 = textBoxPP.Text;
		string text7 = (checkBox1.Checked ? "1" : "0");
		string text8 = (checkBox2.Checked ? "1" : "0");
		string sqlCommand = ((!flag) ? string.Format("UPDATE TBL_XWWL_OPEN SET FLD_pid={1},FLD_NAME='{2}', FLD_pidx={3},FLD_namex='{4}',FLD_number={5},FLD_pp={6},是否开启公告={7},FLD_MAGIC1={8},FLD_MAGIC2={9},FLD_MAGIC3={10},FLD_MAGIC4={11},FLD_MAGIC5={12},FLD_SUNX={13},FLD_SUND={14},装备等级={15},是否随机属性={16} WHERE FLD_index={0}", s_id, text, text2, text3, text4, text5, text6, text7, textBoxM1.Text, textBoxM2.Text, textBoxM3.Text, textBoxM4.Text, textBoxM5.Text, textBox1.Text, textBox2.Text, textBox3.Text, text7) : ("INSERT INTO tbl_xwwl_open  (FLD_pid,FLD_NAME, FLD_pidx,FLD_namex,FLD_number,FLD_pp,是否开启公告,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAGIC5,FLD_SUNX,FLD_SUND,装备等级,是否随机属性)VALUES(" + text + ",'" + text2 + "'," + text3 + ",'" + text4 + "'," + text5 + "," + text6 + "," + text7 + "," + textBoxM1.Text + "," + textBoxM2.Text + "," + textBoxM3.Text + "," + textBoxM4.Text + "," + textBoxM5.Text + "," + textBox1.Text + "," + textBox2.Text + "," + textBox3.Text + "," + text8 + ")"));
		if (DBA.ExeSqlCommand(sqlCommand, "PublicDb") == -1)
		{
			MessageBox.Show("添加或修改失败");
			return;
		}
		int index = listView2.SelectedItems[0].Index;
		string text10 = (s_id = listView2.Items[index].SubItems[0].Text);
		string text11 = text10;
		string text12 = text11;
		listView2.Items[index].SubItems[1].Text = textBoxPID.Text;
		listView2.Items[index].SubItems[2].Text = textBoxNAME.Text;
		listView2.Items[index].SubItems[3].Text = textBoxPIDX.Text;
		listView2.Items[index].SubItems[4].Text = textBoxNAMEX.Text;
		listView2.Items[index].SubItems[5].Text = textBoxNUM.Text;
		listView2.Items[index].SubItems[6].Text = textBoxPP.Text;
		listView2.Items[index].SubItems[7].Text = textBoxM1.Text;
		listView2.Items[index].SubItems[8].Text = textBoxM2.Text;
		listView2.Items[index].SubItems[9].Text = textBoxM3.Text;
		listView2.Items[index].SubItems[10].Text = textBoxM4.Text;
		listView2.Items[index].SubItems[11].Text = textBoxM5.Text;
		listView2.Items[index].SubItems[12].Text = text7;
		listView2.Items[index].SubItems[13].Text = textBox1.Text;
		listView2.Items[index].SubItems[14].Text = textBox2.Text;
		listView2.Items[index].SubItems[15].Text = textBox3.Text;
		listView2.Items[index].SubItems[16].Text = text8;
		MessageBox.Show("修改完成");
	}

	private void listView2_MouseClick(object sender, MouseEventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text2 = (s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text);
			string text3 = text2;
			string text4 = text3;
			string text5 = text4;
			textBoxPID.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
			textBoxNAME.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
			textBoxPIDX.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
			textBoxNAMEX.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
			textBoxNUM.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
			textBoxPP.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
			textBoxM1.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
			textBoxM2.Text = listView2.Items[selectedIndices[0]].SubItems[8].Text;
			textBoxM3.Text = listView2.Items[selectedIndices[0]].SubItems[9].Text;
			textBoxM4.Text = listView2.Items[selectedIndices[0]].SubItems[10].Text;
			textBoxM5.Text = listView2.Items[selectedIndices[0]].SubItems[11].Text;
			checkBox1.Checked = listView2.Items[selectedIndices[0]].SubItems[12].Text == "1";
			textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[13].Text;
			textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[14].Text;
			textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[15].Text;
			checkBox2.Checked = listView2.Items[selectedIndices[0]].SubItems[16].Text == "1";
			if (!button1.Enabled)
			{
				button1.Enabled = true;
			}
			if (!button2.Enabled)
			{
				button2.Enabled = true;
			}
		}
	}

	private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
		{
			ListView.SelectedIndexCollection selectedIndices = listView2.SelectedIndices;
			string text = listView2.Items[selectedIndices[0]].Text;
			string sqlCommand = "delete from tbl_xwwl_open where FLD_index =" + text;
			DBA.ExeSqlCommand(sqlCommand, "PublicDb");
			MessageBox.Show("删除成功。");
			Thread.Sleep(1000);
			刷新();
		}
	}

	public void 刷新()
	{
		listView2.Items.Clear();
		string sqlCommand = "select * from tbl_xwwl_open";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			ListViewItem listViewItem = new ListViewItem();
			listViewItem.SubItems.Clear();
			listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["FLD_index"].ToString();
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pid"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pidx"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_namex"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_number"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pp"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC1"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC2"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC3"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC4"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC5"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["是否开启公告"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_SUNX"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_SUND"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["装备等级"].ToString());
			listViewItem.SubItems.Add(dBToDataTable.Rows[i]["是否随机属性"].ToString());
			listView2.Items.Add(listViewItem);
		}
	}

	private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
	{
		string text = ((ItemDef.MyItem)comboBox11.SelectedItem).Value.ToString();
		listBox3.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + text + "'";
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			string key = dBToDataTable.Rows[i]["FLD_PID"].ToString();
			string value = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
			KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(key, value);
			listBox3.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
	}

	private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
	{
		KeyValuePair<string, string> keyValuePair = (KeyValuePair<string, string>)listBox3.SelectedItem;
		textBoxPIDX.Text = keyValuePair.Key;
		textBoxNAMEX.Text = keyValuePair.Value;
	}

	private void button2_Click(object sender, EventArgs e)
	{
		string text = textBoxPID.Text;
		string text2 = textBoxNAME.Text;
		string text3 = textBoxPIDX.Text;
		string text4 = textBoxNAMEX.Text;
		string text5 = textBoxNUM.Text;
		string text6 = textBoxPP.Text;
		string text7 = (checkBox1.Checked ? "1" : "0");
		string text8 = (checkBox2.Checked ? "1" : "0");
		string sqlCommand = "INSERT INTO TBL_XWWL_OPEN  (FLD_pid,FLD_NAME, FLD_pidx,FLD_namex,FLD_number,FLD_pp,是否开启公告,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAGIC5,FLD_SUNX,FLD_SUND,装备等级,是否随机属性)VALUES(" + text + ",'" + text2 + "'," + text3 + ",'" + text4 + "'," + text5 + "," + text6 + "," + text7 + "," + textBoxM1.Text + "," + textBoxM2.Text + "," + textBoxM3.Text + "," + textBoxM4.Text + "," + textBoxM5.Text + "," + textBox1.Text + "," + textBox2.Text + "," + textBox3.Text + "," + text8 + ")";
		DBA.ExeSqlCommand(sqlCommand, "PublicDb");
		MessageBox.Show("增加完成");
		Thread.Sleep(1000);
		刷新();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(OpenEdit));
            this.listView2 = new System.Windows.Forms.ListView();
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader15 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader16 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader17 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader18 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.button43 = new System.Windows.Forms.Button();
            this.panel1 = new System.Windows.Forms.Panel();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.textBoxM5 = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.textBoxM4 = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.textBoxM3 = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.textBoxM2 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.textBoxM1 = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.button9 = new System.Windows.Forms.Button();
            this.button8 = new System.Windows.Forms.Button();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.textBoxPP = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.textBoxNUM = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBoxNAMEX = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBoxPIDX = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxNAME = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBoxPID = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.删除ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.comboBox11 = new System.Windows.Forms.ComboBox();
            this.label40 = new System.Windows.Forms.Label();
            this.listBox3 = new System.Windows.Forms.ListBox();
            this.button4 = new System.Windows.Forms.Button();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.button5 = new System.Windows.Forms.Button();
            this.button6 = new System.Windows.Forms.Button();
            this.button7 = new System.Windows.Forms.Button();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.panel1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView2
            // 
            this.listView2.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14,
            this.columnHeader15,
            this.columnHeader16,
            this.columnHeader17,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6,
            this.columnHeader1,
            this.columnHeader8,
            this.columnHeader9,
            this.columnHeader10,
            this.columnHeader18});
            this.listView2.FullRowSelect = true;
            this.listView2.GridLines = true;
            this.listView2.HideSelection = false;
            this.listView2.Location = new System.Drawing.Point(12, 35);
            this.listView2.Name = "listView2";
            this.listView2.Size = new System.Drawing.Size(1118, 231);
            this.listView2.TabIndex = 1;
            this.listView2.UseCompatibleStateImageBehavior = false;
            this.listView2.View = System.Windows.Forms.View.Details;
            this.listView2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.listView2_MouseClick);
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "id";
            this.columnHeader11.Width = 40;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "盒子PID";
            this.columnHeader12.Width = 84;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "盒子名称";
            this.columnHeader13.Width = 120;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "开出物品PID";
            this.columnHeader14.Width = 84;
            // 
            // columnHeader15
            // 
            this.columnHeader15.Text = "开出物品名字";
            this.columnHeader15.Width = 120;
            // 
            // columnHeader16
            // 
            this.columnHeader16.Text = "数量";
            this.columnHeader16.Width = 40;
            // 
            // columnHeader17
            // 
            this.columnHeader17.Text = "几率";
            this.columnHeader17.Width = 40;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "属性一";
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "属性二";
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "属性三";
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "属性四";
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "属性五";
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "公告";
            this.columnHeader1.Width = 43;
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "随机一";
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "随机二";
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "等级";
            // 
            // columnHeader18
            // 
            this.columnHeader18.Text = "是否随机";
            // 
            // button43
            // 
            this.button43.Location = new System.Drawing.Point(12, 6);
            this.button43.Name = "button43";
            this.button43.Size = new System.Drawing.Size(75, 23);
            this.button43.TabIndex = 3;
            this.button43.Text = "查看全部";
            this.button43.UseVisualStyleBackColor = true;
            this.button43.Click += new System.EventHandler(this.button43_Click);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.textBox3);
            this.panel1.Controls.Add(this.label14);
            this.panel1.Controls.Add(this.checkBox2);
            this.panel1.Controls.Add(this.textBox2);
            this.panel1.Controls.Add(this.label13);
            this.panel1.Controls.Add(this.textBox1);
            this.panel1.Controls.Add(this.label12);
            this.panel1.Controls.Add(this.textBoxM5);
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.textBoxM4);
            this.panel1.Controls.Add(this.label8);
            this.panel1.Controls.Add(this.textBoxM3);
            this.panel1.Controls.Add(this.label9);
            this.panel1.Controls.Add(this.textBoxM2);
            this.panel1.Controls.Add(this.label10);
            this.panel1.Controls.Add(this.textBoxM1);
            this.panel1.Controls.Add(this.label11);
            this.panel1.Controls.Add(this.button9);
            this.panel1.Controls.Add(this.button8);
            this.panel1.Controls.Add(this.checkBox1);
            this.panel1.Controls.Add(this.button3);
            this.panel1.Controls.Add(this.button2);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Controls.Add(this.textBoxPP);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.textBoxNUM);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.textBoxNAMEX);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.textBoxPIDX);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.textBoxNAME);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.textBoxPID);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(343, 276);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(539, 294);
            this.panel1.TabIndex = 4;
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(101, 176);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(129, 21);
            this.textBox3.TabIndex = 71;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(18, 183);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(53, 12);
            this.label14.TabIndex = 70;
            this.label14.Text = "装备等级";
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(116, 223);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(96, 16);
            this.checkBox2.TabIndex = 69;
            this.checkBox2.Text = "开启随机属性";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(401, 176);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(129, 21);
            this.textBox2.TabIndex = 68;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(351, 182);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(41, 12);
            this.label13.TabIndex = 67;
            this.label13.Text = "随机二";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(401, 146);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(129, 21);
            this.textBox1.TabIndex = 66;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(351, 151);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(41, 12);
            this.label12.TabIndex = 65;
            this.label12.Text = "随机一";
            // 
            // textBoxM5
            // 
            this.textBoxM5.Location = new System.Drawing.Point(401, 116);
            this.textBoxM5.Name = "textBoxM5";
            this.textBoxM5.Size = new System.Drawing.Size(129, 21);
            this.textBoxM5.TabIndex = 64;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(351, 119);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 63;
            this.label7.Text = "属性五";
            // 
            // textBoxM4
            // 
            this.textBoxM4.Location = new System.Drawing.Point(401, 89);
            this.textBoxM4.Name = "textBoxM4";
            this.textBoxM4.Size = new System.Drawing.Size(129, 21);
            this.textBoxM4.TabIndex = 62;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(351, 92);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(41, 12);
            this.label8.TabIndex = 61;
            this.label8.Text = "属性四";
            // 
            // textBoxM3
            // 
            this.textBoxM3.Location = new System.Drawing.Point(401, 62);
            this.textBoxM3.Name = "textBoxM3";
            this.textBoxM3.Size = new System.Drawing.Size(129, 21);
            this.textBoxM3.TabIndex = 60;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(351, 65);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(41, 12);
            this.label9.TabIndex = 59;
            this.label9.Text = "属性三";
            // 
            // textBoxM2
            // 
            this.textBoxM2.Location = new System.Drawing.Point(401, 35);
            this.textBoxM2.Name = "textBoxM2";
            this.textBoxM2.Size = new System.Drawing.Size(129, 21);
            this.textBoxM2.TabIndex = 58;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(351, 38);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41, 12);
            this.label10.TabIndex = 57;
            this.label10.Text = "属性二";
            // 
            // textBoxM1
            // 
            this.textBoxM1.Location = new System.Drawing.Point(401, 8);
            this.textBoxM1.Name = "textBoxM1";
            this.textBoxM1.Size = new System.Drawing.Size(129, 21);
            this.textBoxM1.TabIndex = 56;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(351, 11);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(41, 12);
            this.label11.TabIndex = 55;
            this.label11.Text = "属性一";
            // 
            // button9
            // 
            this.button9.Location = new System.Drawing.Point(240, 61);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(62, 23);
            this.button9.TabIndex = 54;
            this.button9.Text = "选择物品";
            this.button9.UseVisualStyleBackColor = true;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(240, 8);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(62, 23);
            this.button8.TabIndex = 53;
            this.button8.Text = "选择物品";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Location = new System.Drawing.Point(14, 223);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(72, 16);
            this.checkBox1.TabIndex = 52;
            this.checkBox1.Text = "开启公告";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(193, 257);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(70, 24);
            this.button3.TabIndex = 15;
            this.button3.Text = "删除";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(101, 257);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(70, 24);
            this.button2.TabIndex = 14;
            this.button2.Text = "增加";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(14, 257);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(70, 24);
            this.button1.TabIndex = 13;
            this.button1.Text = "保存修改";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBoxPP
            // 
            this.textBoxPP.Location = new System.Drawing.Point(101, 143);
            this.textBoxPP.Name = "textBoxPP";
            this.textBoxPP.Size = new System.Drawing.Size(129, 21);
            this.textBoxPP.TabIndex = 12;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(14, 146);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 11;
            this.label6.Text = "开出几率";
            // 
            // textBoxNUM
            // 
            this.textBoxNUM.Location = new System.Drawing.Point(101, 116);
            this.textBoxNUM.Name = "textBoxNUM";
            this.textBoxNUM.Size = new System.Drawing.Size(129, 21);
            this.textBoxNUM.TabIndex = 10;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(14, 120);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 9;
            this.label2.Text = "数量";
            // 
            // textBoxNAMEX
            // 
            this.textBoxNAMEX.Location = new System.Drawing.Point(101, 89);
            this.textBoxNAMEX.Name = "textBoxNAMEX";
            this.textBoxNAMEX.Size = new System.Drawing.Size(129, 21);
            this.textBoxNAMEX.TabIndex = 8;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(14, 92);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 7;
            this.label5.Text = "开出物品名称";
            // 
            // textBoxPIDX
            // 
            this.textBoxPIDX.Location = new System.Drawing.Point(101, 62);
            this.textBoxPIDX.Name = "textBoxPIDX";
            this.textBoxPIDX.Size = new System.Drawing.Size(129, 21);
            this.textBoxPIDX.TabIndex = 6;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(14, 66);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(71, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "开出物品PID";
            // 
            // textBoxNAME
            // 
            this.textBoxNAME.Location = new System.Drawing.Point(101, 35);
            this.textBoxNAME.Name = "textBoxNAME";
            this.textBoxNAME.Size = new System.Drawing.Size(129, 21);
            this.textBoxNAME.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(14, 38);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "盒子名称";
            // 
            // textBoxPID
            // 
            this.textBoxPID.Location = new System.Drawing.Point(101, 8);
            this.textBoxPID.Name = "textBoxPID";
            this.textBoxPID.Size = new System.Drawing.Size(129, 21);
            this.textBoxPID.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(14, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(47, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "盒子PID";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.删除ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(101, 26);
            // 
            // 删除ToolStripMenuItem
            // 
            this.删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
            this.删除ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.删除ToolStripMenuItem.Text = "删除";
            this.删除ToolStripMenuItem.Click += new System.EventHandler(this.删除ToolStripMenuItem_Click);
            // 
            // comboBox11
            // 
            this.comboBox11.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox11.FormattingEnabled = true;
            this.comboBox11.Items.AddRange(new object[] {
            "衣服",
            "护手",
            "武器",
            "鞋子",
            "内甲",
            "项链",
            "耳环",
            "戒指",
            "披风",
            "弓箭",
            "门甲",
            "宝宝",
            "石头",
            "宝盒",
            "幸运符",
            "气功书",
            "百宝",
            "武功书",
            "其他"});
            this.comboBox11.Location = new System.Drawing.Point(78, 275);
            this.comboBox11.MaxDropDownItems = 20;
            this.comboBox11.Name = "comboBox11";
            this.comboBox11.Size = new System.Drawing.Size(129, 20);
            this.comboBox11.TabIndex = 40;
            this.comboBox11.SelectedIndexChanged += new System.EventHandler(this.comboBox11_SelectedIndexChanged);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(17, 278);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(53, 12);
            this.label40.TabIndex = 39;
            this.label40.Text = "物品种类";
            // 
            // listBox3
            // 
            this.listBox3.FormattingEnabled = true;
            this.listBox3.ItemHeight = 12;
            this.listBox3.Location = new System.Drawing.Point(12, 302);
            this.listBox3.Name = "listBox3";
            this.listBox3.Size = new System.Drawing.Size(320, 268);
            this.listBox3.TabIndex = 38;
            this.listBox3.SelectedIndexChanged += new System.EventHandler(this.listBox3_SelectedIndexChanged);
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(382, 6);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(75, 23);
            this.button4.TabIndex = 41;
            this.button4.Text = "搜索盒子ID";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(281, 7);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(96, 21);
            this.textBox7.TabIndex = 42;
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(459, 6);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(75, 23);
            this.button5.TabIndex = 43;
            this.button5.Text = "搜索盒子名";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(536, 6);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(75, 23);
            this.button6.TabIndex = 44;
            this.button6.Text = "搜索物品ID";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(614, 5);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(75, 23);
            this.button7.TabIndex = 45;
            this.button7.Text = "搜索物品名";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(93, 8);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(182, 20);
            this.comboBox1.TabIndex = 46;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // OpenEdit
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1139, 589);
            this.Controls.Add(this.comboBox1);
            this.Controls.Add(this.button7);
            this.Controls.Add(this.button6);
            this.Controls.Add(this.button5);
            this.Controls.Add(this.textBox7);
            this.Controls.Add(this.button4);
            this.Controls.Add(this.comboBox11);
            this.Controls.Add(this.label40);
            this.Controls.Add(this.listBox3);
            this.Controls.Add(this.listView2);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.button43);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MinimizeBox = false;
            this.Name = "OpenEdit";
            this.Text = "盒子数据修改";
            this.Load += new System.EventHandler(this.Form3_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void Form3_Load(object sender, EventArgs e)
	{
		comboBox1.Items.Clear();
		string sqlCommand = "select * from TBL_XWWL_ITEM where FLD_RESIDE2=" + 17;
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, "PublicDb");
		for (int i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			string key = dBToDataTable.Rows[i]["FLD_PID"].ToString();
			string value = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
			KeyValuePair<string, string> keyValuePair = new KeyValuePair<string, string>(key, value);
			comboBox1.Items.Add(keyValuePair);
		}
		dBToDataTable.Dispose();
		ItemDef.AddComBoxItemReside2(comboBox11);
	}

	private void button3_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(s_id) && int.Parse(s_id) > 0)
		{
			string sqlCommand = "delete  from tbl_xwwl_open where fld_index='" + s_id + "'";
			if (DBA.ExeSqlCommand(sqlCommand, "PublicDb") == -1)
			{
				MessageBox.Show("删除错误!");
				return;
			}
			MessageBox.Show("删除成功!");
			Thread.Sleep(1000);
			刷新();
		}
	}

	private void button4_Click(object sender, EventArgs e)
	{
		string sQL = "select * from TBL_XWWL_OPEN where FLD_PID like '%" + textBox7.Text + "%'";
		执行SQL语句并加载(sQL);
	}

	private void button5_Click(object sender, EventArgs e)
	{
		string sQL = "select * from TBL_XWWL_OPEN where FLD_NAME like '%" + textBox7.Text + "%'";
		执行SQL语句并加载(sQL);
	}

	private void button6_Click(object sender, EventArgs e)
	{
		string sQL = "select * from TBL_XWWL_OPEN where FLD_PIDX like '%" + textBox7.Text + "%'";
		执行SQL语句并加载(sQL);
	}

	private void button7_Click(object sender, EventArgs e)
	{
		string sQL = "select * from TBL_XWWL_OPEN where FLD_NAMEx like '%" + textBox7.Text + "%'";
		执行SQL语句并加载(sQL);
	}

	private void 执行SQL语句并加载(string SQL)
	{
		listView2.Items.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable(SQL, "PublicDb");
		if (dBToDataTable != null)
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				ListViewItem listViewItem = new ListViewItem();
				listViewItem.SubItems.Clear();
				listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["FLD_index"].ToString();
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pid"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pidx"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_namex"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_number"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pp"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC1"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC2"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC3"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC4"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC5"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["是否开启公告"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_SUNX"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_SUND"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["装备等级"].ToString());
				listViewItem.SubItems.Add(dBToDataTable.Rows[i]["是否随机属性"].ToString());
				listView2.Items.Add(listViewItem);
			}
		}
	}

	private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
	{
		string sQL = "select * from tbl_xwwl_open where fld_pid=" + ((KeyValuePair<string, string>)comboBox1.SelectedItem).Key;
		执行SQL语句并加载(sQL);
	}

	private void button8_Click(object sender, EventArgs e)
	{
		ItemSel itemSel = new ItemSel();
		itemSel.PID = textBoxPID;
		itemSel.NAME = textBoxNAME;
		itemSel.Show();
	}

	private void button9_Click(object sender, EventArgs e)
	{
		ItemSel itemSel = new ItemSel();
		itemSel.PID = textBoxPIDX;
		itemSel.NAME = textBoxNAMEX;
		itemSel.Show();
	}
}

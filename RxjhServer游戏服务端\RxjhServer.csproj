<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <AssemblyName>RxjhServer</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>True</UseWindowsForms>
    <TargetFramework>net48</TargetFramework>
    <Prefer32Bit>True</Prefer32Bit>
  </PropertyGroup>

	<Target Name="CleanOldBuildTimeLogs" BeforeTargets="Build">
		<Exec Command="del /q &quot;更新时间_*.log&quot;" IgnoreExitCode="true" />
	</Target>
	
	<Target Name="WriteBuildTimeLog" AfterTargets="Build">
		<PropertyGroup>
			<BuildTime>$([System.DateTime]::Now.ToString("yyyy-MM-dd HH:mm:ss"))</BuildTime>
			<BuildDate>$([System.DateTime]::Now.ToString("yyyyMMdd"))</BuildDate>
			<BuildTimeHMS>$([System.DateTime]::Now.ToString("HHmmss"))</BuildTimeHMS>
			<LogFileName>更新时间_$(BuildDate)_$(BuildTimeHMS).log</LogFileName>
		</PropertyGroup>
		<WriteLinesToFile File="$(LogFileName)" Lines="Build Time: $(BuildTime)&#xD;&#xA;User: $([System.Environment]::UserName)&#xD;&#xA;Machine: $([System.Environment]::MachineName)&#xD;&#xA;Project: $(MSBuildProjectName)" Overwrite="true" />
	</Target>
	
  <PropertyGroup>
    <LangVersion>11.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <RootNamespace />
    <StartupObject>RxjhServer.Form1</StartupObject>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="app.ico" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FreeSql.Provider.SqlServerForSystem" Version="3.5.202" />
    <PackageReference Include="HPSocket.Net" Version="6.0.3.1" />
    <PackageReference Include="KeraLua" Version="1.4.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLua" Version="1.7.5" />
    <PackageReference Include="System.Resources.Extensions" Version="9.0.6" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
  </ItemGroup>
</Project>
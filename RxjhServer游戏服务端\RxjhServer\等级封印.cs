using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RxjhServer;

public class 等级封印 : Form
{
	private IContainer components = null;

	private TextBox textBox1;

	private Label label1;

	private Button button1;

	private Label label2;

	private TextBox textBox2;

	private Button button2;

	private Label label3;

	private TextBox textBox3;

	private Button button3;

	private Label label4;

	private TextBox textBox4;

	private Button button4;

	private Label label5;

	private TextBox textBox5;

	private Button button5;

	private Label label6;

	private TextBox textBox6;

	private Button button6;

	private Label label7;

	private TextBox textBox7;

	private Button button7;

	private Label label8;

	private TextBox textBox8;

	private Button button8;

	private Label label9;

	private TextBox textBox9;

	private Button button9;

	private Label label10;

	private TextBox textBox10;

	private Button button10;

	private Label label11;

	private Label label12;

	private Label label13;

	private Label label14;

	private Label label15;

	private Label label16;

	private Label label17;

	private TextBox textBox11;

	private TextBox textBox12;

	private TextBox textBox13;

	private TextBox textBox14;

	private TextBox textBox15;

	private TextBox textBox16;

	private TextBox textBox17;

	private Button button11;

	private Button button12;

	private Button button13;

	private Button button14;

	private Button button15;

	private Button button16;

	private Button button17;

	private Label label18;

	private Label label19;

	private Label label20;

	private Label label21;

	private Label label22;

	private TextBox textBox18;

	private TextBox textBox19;

	private TextBox textBox20;

	private TextBox textBox21;

	private TextBox textBox22;

	private Button button18;

	private Button button19;

	private Button button20;

	private Button button21;

	private Button button22;

	private Label label23;

	private TextBox textBox23;

	private Button button23;

	private readonly Ini ini = new Ini(Application.StartupPath + "\\config\\config.ini");

	public 等级封印()
	{
		InitializeComponent();
		textBox1.Text = World.限制最高级别.ToString() ?? "";
		textBox2.Text = World.经验倍数.ToString() ?? "";
		textBox3.Text = World.历练倍数.ToString() ?? "";
		textBox4.Text = World.钱倍数.ToString() ?? "";
		textBox5.Text = World.暴率.ToString() ?? "";
		textBox6.Text = World.自定义经验等级.ToString() ?? "";
		textBox7.Text = World.自定义等级经验倍数.ToString() ?? "";
		textBox8.Text = World.三十五级以下经验倍数.ToString() ?? "";
		textBox9.Text = World.六十级以下经验倍数.ToString() ?? "";
		textBox10.Text = World.八十级以下经验倍数.ToString() ?? "";
		textBox11.Text = World.一百级以下经验倍数.ToString() ?? "";
		textBox12.Text = World.一百一以下经验倍数.ToString() ?? "";
		textBox13.Text = World.一百二以下经验倍数.ToString() ?? "";
		textBox14.Text = World.一百三以下经验倍数.ToString() ?? "";
		textBox15.Text = World.一百四以下经验倍数.ToString() ?? "";
		textBox16.Text = World.一百五以下经验倍数.ToString() ?? "";
		textBox17.Text = World.一百六以下经验倍数.ToString() ?? "";
		textBox18.Text = World.组队等级限制.ToString() ?? "";
		textBox19.Text = World.人物越级怪物经验差.ToString() ?? "";
		textBox20.Text = World.怪物越级人物经验差.ToString() ?? "";
		textBox21.Text = World.人物越级怪物掉落差.ToString() ?? "";
		textBox22.Text = World.怪物越级人物掉落差.ToString() ?? "";
		textBox23.Text = World.击杀BOSS等级差.ToString() ?? "";
	}

	private void button1_Click(object sender, EventArgs e)
	{
		ini.SetString("限制最高级别", textBox1.Text);
		World.限制最高级别 = int.Parse(textBox1.Text);
		Close();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(等级封印));
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.button2 = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.button3 = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.button4 = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.button5 = new System.Windows.Forms.Button();
            this.label6 = new System.Windows.Forms.Label();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.button6 = new System.Windows.Forms.Button();
            this.label7 = new System.Windows.Forms.Label();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.button7 = new System.Windows.Forms.Button();
            this.label8 = new System.Windows.Forms.Label();
            this.textBox8 = new System.Windows.Forms.TextBox();
            this.button8 = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.textBox9 = new System.Windows.Forms.TextBox();
            this.button9 = new System.Windows.Forms.Button();
            this.label10 = new System.Windows.Forms.Label();
            this.textBox10 = new System.Windows.Forms.TextBox();
            this.button10 = new System.Windows.Forms.Button();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.textBox11 = new System.Windows.Forms.TextBox();
            this.textBox12 = new System.Windows.Forms.TextBox();
            this.textBox13 = new System.Windows.Forms.TextBox();
            this.textBox14 = new System.Windows.Forms.TextBox();
            this.textBox15 = new System.Windows.Forms.TextBox();
            this.textBox16 = new System.Windows.Forms.TextBox();
            this.textBox17 = new System.Windows.Forms.TextBox();
            this.button11 = new System.Windows.Forms.Button();
            this.button12 = new System.Windows.Forms.Button();
            this.button13 = new System.Windows.Forms.Button();
            this.button14 = new System.Windows.Forms.Button();
            this.button15 = new System.Windows.Forms.Button();
            this.button16 = new System.Windows.Forms.Button();
            this.button17 = new System.Windows.Forms.Button();
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.textBox18 = new System.Windows.Forms.TextBox();
            this.textBox19 = new System.Windows.Forms.TextBox();
            this.textBox20 = new System.Windows.Forms.TextBox();
            this.textBox21 = new System.Windows.Forms.TextBox();
            this.textBox22 = new System.Windows.Forms.TextBox();
            this.button18 = new System.Windows.Forms.Button();
            this.button19 = new System.Windows.Forms.Button();
            this.button20 = new System.Windows.Forms.Button();
            this.button21 = new System.Windows.Forms.Button();
            this.button22 = new System.Windows.Forms.Button();
            this.label23 = new System.Windows.Forms.Label();
            this.textBox23 = new System.Windows.Forms.TextBox();
            this.button23 = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(75, 106);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(100, 21);
            this.textBox1.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(9, 112);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "封印等级";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(186, 106);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 2;
            this.button1.Text = "确定";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(9, 81);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "经验倍数";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(75, 76);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(100, 21);
            this.textBox2.TabIndex = 4;
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(186, 75);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(75, 23);
            this.button2.TabIndex = 5;
            this.button2.Text = "确定";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(9, 52);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 6;
            this.label3.Text = "历练倍数";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(75, 45);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(100, 21);
            this.textBox3.TabIndex = 7;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(186, 43);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(75, 23);
            this.button3.TabIndex = 8;
            this.button3.Text = "确定";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(9, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "金钱倍数";
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(75, 16);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(100, 21);
            this.textBox4.TabIndex = 10;
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(186, 14);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(75, 23);
            this.button4.TabIndex = 11;
            this.button4.Text = "确定";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(10, 143);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(29, 12);
            this.label5.TabIndex = 12;
            this.label5.Text = "暴率";
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(75, 139);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(100, 21);
            this.textBox5.TabIndex = 13;
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(186, 138);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(75, 23);
            this.button5.TabIndex = 14;
            this.button5.Text = "确定";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(9, 178);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 15;
            this.label6.Text = "自定义经验等级";
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(127, 173);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(100, 21);
            this.textBox6.TabIndex = 16;
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(250, 172);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(75, 23);
            this.button6.TabIndex = 17;
            this.button6.Text = "确定";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(9, 209);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(89, 12);
            this.label7.TabIndex = 18;
            this.label7.Text = "自定义经验倍数";
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(127, 204);
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(100, 21);
            this.textBox7.TabIndex = 19;
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(250, 203);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(75, 23);
            this.button7.TabIndex = 20;
            this.button7.Text = "确定";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(9, 242);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(101, 12);
            this.label8.TabIndex = 21;
            this.label8.Text = "35级以下经验倍数";
            // 
            // textBox8
            // 
            this.textBox8.Location = new System.Drawing.Point(127, 236);
            this.textBox8.Name = "textBox8";
            this.textBox8.Size = new System.Drawing.Size(100, 21);
            this.textBox8.TabIndex = 22;
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(250, 236);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(75, 23);
            this.button8.TabIndex = 23;
            this.button8.Text = "确定";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(9, 275);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(101, 12);
            this.label9.TabIndex = 24;
            this.label9.Text = "60级以下经验倍数";
            // 
            // textBox9
            // 
            this.textBox9.Location = new System.Drawing.Point(127, 270);
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new System.Drawing.Size(100, 21);
            this.textBox9.TabIndex = 25;
            // 
            // button9
            // 
            this.button9.Location = new System.Drawing.Point(250, 269);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(75, 23);
            this.button9.TabIndex = 26;
            this.button9.Text = "确定";
            this.button9.UseVisualStyleBackColor = true;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(9, 307);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(101, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "80级以下经验倍数";
            // 
            // textBox10
            // 
            this.textBox10.Location = new System.Drawing.Point(127, 302);
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new System.Drawing.Size(100, 21);
            this.textBox10.TabIndex = 28;
            // 
            // button10
            // 
            this.button10.Location = new System.Drawing.Point(250, 300);
            this.button10.Name = "button10";
            this.button10.Size = new System.Drawing.Size(75, 23);
            this.button10.TabIndex = 29;
            this.button10.Text = "确定";
            this.button10.UseVisualStyleBackColor = true;
            this.button10.Click += new System.EventHandler(this.button10_Click);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(10, 337);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(107, 12);
            this.label11.TabIndex = 30;
            this.label11.Text = "100级以下经验倍数";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(10, 368);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(107, 12);
            this.label12.TabIndex = 31;
            this.label12.Text = "115级以下经验倍数";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(9, 397);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(107, 12);
            this.label13.TabIndex = 32;
            this.label13.Text = "120级以下经验倍数";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(9, 424);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(107, 12);
            this.label14.TabIndex = 33;
            this.label14.Text = "130级以下经验倍数";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(9, 452);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(107, 12);
            this.label15.TabIndex = 34;
            this.label15.Text = "140级以下经验倍数";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(9, 478);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(107, 12);
            this.label16.TabIndex = 35;
            this.label16.Text = "150级以下经验倍数";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(9, 503);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(107, 12);
            this.label17.TabIndex = 36;
            this.label17.Text = "160级以下经验倍数";
            // 
            // textBox11
            // 
            this.textBox11.Location = new System.Drawing.Point(127, 332);
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new System.Drawing.Size(100, 21);
            this.textBox11.TabIndex = 37;
            // 
            // textBox12
            // 
            this.textBox12.Location = new System.Drawing.Point(127, 362);
            this.textBox12.Name = "textBox12";
            this.textBox12.Size = new System.Drawing.Size(100, 21);
            this.textBox12.TabIndex = 38;
            // 
            // textBox13
            // 
            this.textBox13.Location = new System.Drawing.Point(127, 390);
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new System.Drawing.Size(100, 21);
            this.textBox13.TabIndex = 39;
            // 
            // textBox14
            // 
            this.textBox14.Location = new System.Drawing.Point(127, 418);
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new System.Drawing.Size(100, 21);
            this.textBox14.TabIndex = 40;
            // 
            // textBox15
            // 
            this.textBox15.Location = new System.Drawing.Point(127, 446);
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new System.Drawing.Size(100, 21);
            this.textBox15.TabIndex = 41;
            // 
            // textBox16
            // 
            this.textBox16.Location = new System.Drawing.Point(127, 472);
            this.textBox16.Name = "textBox16";
            this.textBox16.Size = new System.Drawing.Size(100, 21);
            this.textBox16.TabIndex = 42;
            // 
            // textBox17
            // 
            this.textBox17.Location = new System.Drawing.Point(127, 498);
            this.textBox17.Name = "textBox17";
            this.textBox17.Size = new System.Drawing.Size(100, 21);
            this.textBox17.TabIndex = 43;
            // 
            // button11
            // 
            this.button11.Location = new System.Drawing.Point(250, 331);
            this.button11.Name = "button11";
            this.button11.Size = new System.Drawing.Size(75, 23);
            this.button11.TabIndex = 44;
            this.button11.Text = "确定";
            this.button11.UseVisualStyleBackColor = true;
            this.button11.Click += new System.EventHandler(this.button11_Click);
            // 
            // button12
            // 
            this.button12.Location = new System.Drawing.Point(250, 362);
            this.button12.Name = "button12";
            this.button12.Size = new System.Drawing.Size(75, 23);
            this.button12.TabIndex = 45;
            this.button12.Text = "确定";
            this.button12.UseVisualStyleBackColor = true;
            this.button12.Click += new System.EventHandler(this.button12_Click);
            // 
            // button13
            // 
            this.button13.Location = new System.Drawing.Point(250, 390);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(75, 23);
            this.button13.TabIndex = 46;
            this.button13.Text = "确定";
            this.button13.UseVisualStyleBackColor = true;
            this.button13.Click += new System.EventHandler(this.button13_Click);
            // 
            // button14
            // 
            this.button14.Location = new System.Drawing.Point(250, 419);
            this.button14.Name = "button14";
            this.button14.Size = new System.Drawing.Size(75, 23);
            this.button14.TabIndex = 47;
            this.button14.Text = "确定";
            this.button14.UseVisualStyleBackColor = true;
            this.button14.Click += new System.EventHandler(this.button14_Click);
            // 
            // button15
            // 
            this.button15.Location = new System.Drawing.Point(250, 445);
            this.button15.Name = "button15";
            this.button15.Size = new System.Drawing.Size(75, 23);
            this.button15.TabIndex = 48;
            this.button15.Text = "确定";
            this.button15.UseVisualStyleBackColor = true;
            this.button15.Click += new System.EventHandler(this.button15_Click);
            // 
            // button16
            // 
            this.button16.Location = new System.Drawing.Point(250, 470);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(75, 23);
            this.button16.TabIndex = 49;
            this.button16.Text = "确定";
            this.button16.UseVisualStyleBackColor = true;
            this.button16.Click += new System.EventHandler(this.button16_Click);
            // 
            // button17
            // 
            this.button17.Location = new System.Drawing.Point(250, 497);
            this.button17.Name = "button17";
            this.button17.Size = new System.Drawing.Size(75, 23);
            this.button17.TabIndex = 50;
            this.button17.Text = "确定";
            this.button17.UseVisualStyleBackColor = true;
            this.button17.Click += new System.EventHandler(this.button17_Click);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(304, 35);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(89, 12);
            this.label18.TabIndex = 51;
            this.label18.Text = "组队限制等级差";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(280, 62);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(113, 12);
            this.label19.TabIndex = 52;
            this.label19.Text = "人物越级怪物经验差";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(280, 89);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(113, 12);
            this.label20.TabIndex = 53;
            this.label20.Text = "怪物越级人物经验差";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(280, 118);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(113, 12);
            this.label21.TabIndex = 54;
            this.label21.Text = "人物越级怪物掉落差";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(280, 143);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(113, 12);
            this.label22.TabIndex = 55;
            this.label22.Text = "怪物越级人物掉落差";
            // 
            // textBox18
            // 
            this.textBox18.Location = new System.Drawing.Point(399, 32);
            this.textBox18.Name = "textBox18";
            this.textBox18.Size = new System.Drawing.Size(100, 21);
            this.textBox18.TabIndex = 56;
            // 
            // textBox19
            // 
            this.textBox19.Location = new System.Drawing.Point(399, 59);
            this.textBox19.Name = "textBox19";
            this.textBox19.Size = new System.Drawing.Size(100, 21);
            this.textBox19.TabIndex = 57;
            // 
            // textBox20
            // 
            this.textBox20.Location = new System.Drawing.Point(399, 86);
            this.textBox20.Name = "textBox20";
            this.textBox20.Size = new System.Drawing.Size(100, 21);
            this.textBox20.TabIndex = 58;
            // 
            // textBox21
            // 
            this.textBox21.Location = new System.Drawing.Point(399, 112);
            this.textBox21.Name = "textBox21";
            this.textBox21.Size = new System.Drawing.Size(100, 21);
            this.textBox21.TabIndex = 59;
            // 
            // textBox22
            // 
            this.textBox22.Location = new System.Drawing.Point(399, 138);
            this.textBox22.Name = "textBox22";
            this.textBox22.Size = new System.Drawing.Size(100, 21);
            this.textBox22.TabIndex = 60;
            // 
            // button18
            // 
            this.button18.Location = new System.Drawing.Point(505, 31);
            this.button18.Name = "button18";
            this.button18.Size = new System.Drawing.Size(75, 23);
            this.button18.TabIndex = 61;
            this.button18.Text = "确定";
            this.button18.UseVisualStyleBackColor = true;
            this.button18.Click += new System.EventHandler(this.button18_Click);
            // 
            // button19
            // 
            this.button19.Location = new System.Drawing.Point(505, 58);
            this.button19.Name = "button19";
            this.button19.Size = new System.Drawing.Size(75, 23);
            this.button19.TabIndex = 62;
            this.button19.Text = "确定";
            this.button19.UseVisualStyleBackColor = true;
            this.button19.Click += new System.EventHandler(this.button19_Click);
            // 
            // button20
            // 
            this.button20.Location = new System.Drawing.Point(505, 85);
            this.button20.Name = "button20";
            this.button20.Size = new System.Drawing.Size(75, 23);
            this.button20.TabIndex = 63;
            this.button20.Text = "确定";
            this.button20.UseVisualStyleBackColor = true;
            this.button20.Click += new System.EventHandler(this.button20_Click);
            // 
            // button21
            // 
            this.button21.Location = new System.Drawing.Point(505, 112);
            this.button21.Name = "button21";
            this.button21.Size = new System.Drawing.Size(75, 23);
            this.button21.TabIndex = 64;
            this.button21.Text = "确定";
            this.button21.UseVisualStyleBackColor = true;
            this.button21.Click += new System.EventHandler(this.button21_Click);
            // 
            // button22
            // 
            this.button22.Location = new System.Drawing.Point(505, 137);
            this.button22.Name = "button22";
            this.button22.Size = new System.Drawing.Size(75, 23);
            this.button22.TabIndex = 65;
            this.button22.Text = "确定";
            this.button22.UseVisualStyleBackColor = true;
            this.button22.Click += new System.EventHandler(this.button22_Click);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(304, 11);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(89, 12);
            this.label23.TabIndex = 66;
            this.label23.Text = "击杀BOSS等级差";
            // 
            // textBox23
            // 
            this.textBox23.Location = new System.Drawing.Point(399, 6);
            this.textBox23.Name = "textBox23";
            this.textBox23.Size = new System.Drawing.Size(100, 21);
            this.textBox23.TabIndex = 67;
            // 
            // button23
            // 
            this.button23.Location = new System.Drawing.Point(505, 4);
            this.button23.Name = "button23";
            this.button23.Size = new System.Drawing.Size(75, 23);
            this.button23.TabIndex = 68;
            this.button23.Text = "确定";
            this.button23.UseVisualStyleBackColor = true;
            this.button23.Click += new System.EventHandler(this.button23_Click);
            // 
            // 等级封印
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(590, 534);
            this.Controls.Add(this.button23);
            this.Controls.Add(this.textBox23);
            this.Controls.Add(this.label23);
            this.Controls.Add(this.button22);
            this.Controls.Add(this.button21);
            this.Controls.Add(this.button20);
            this.Controls.Add(this.button19);
            this.Controls.Add(this.button18);
            this.Controls.Add(this.textBox22);
            this.Controls.Add(this.textBox21);
            this.Controls.Add(this.textBox20);
            this.Controls.Add(this.textBox19);
            this.Controls.Add(this.textBox18);
            this.Controls.Add(this.label22);
            this.Controls.Add(this.label21);
            this.Controls.Add(this.label20);
            this.Controls.Add(this.label19);
            this.Controls.Add(this.label18);
            this.Controls.Add(this.button17);
            this.Controls.Add(this.button16);
            this.Controls.Add(this.button15);
            this.Controls.Add(this.button14);
            this.Controls.Add(this.button13);
            this.Controls.Add(this.button12);
            this.Controls.Add(this.button11);
            this.Controls.Add(this.textBox17);
            this.Controls.Add(this.textBox16);
            this.Controls.Add(this.textBox15);
            this.Controls.Add(this.textBox14);
            this.Controls.Add(this.textBox13);
            this.Controls.Add(this.textBox12);
            this.Controls.Add(this.textBox11);
            this.Controls.Add(this.label17);
            this.Controls.Add(this.label16);
            this.Controls.Add(this.label15);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.button10);
            this.Controls.Add(this.textBox10);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.button9);
            this.Controls.Add(this.textBox9);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.button8);
            this.Controls.Add(this.textBox8);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.button7);
            this.Controls.Add(this.textBox7);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.button6);
            this.Controls.Add(this.textBox6);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.button5);
            this.Controls.Add(this.textBox5);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.button4);
            this.Controls.Add(this.textBox4);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.button3);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "等级封印";
            this.Text = "等级设置";
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	private void button2_Click(object sender, EventArgs e)
	{
		ini.SetString("经验倍数", textBox2.Text);
		World.经验倍数 = double.Parse(textBox2.Text);
		MessageBox.Show("修改完成");
	}

	private void button3_Click(object sender, EventArgs e)
	{
		ini.SetString("历练倍数", textBox3.Text);
		World.历练倍数 = int.Parse(textBox3.Text);
		MessageBox.Show("修改完成");
	}

	private void button4_Click(object sender, EventArgs e)
	{
		ini.SetString("钱倍数", textBox4.Text);
		World.钱倍数 = double.Parse(textBox4.Text);
		MessageBox.Show("修改完成");
	}

	private void button5_Click(object sender, EventArgs e)
	{
		ini.SetString("暴率", textBox5.Text);
		World.暴率 = int.Parse(textBox5.Text);
		MessageBox.Show("修改完成");
	}

	private void button6_Click(object sender, EventArgs e)
	{
		ini.SetString("自定义经验等级", textBox6.Text);
		World.自定义经验等级 = int.Parse(textBox6.Text);
		MessageBox.Show("修改完成");
	}

	private void button7_Click(object sender, EventArgs e)
	{
		ini.SetString("自定义等级经验倍数", textBox7.Text);
		World.自定义等级经验倍数 = double.Parse(textBox7.Text);
		MessageBox.Show("修改完成");
	}

	private void button8_Click(object sender, EventArgs e)
	{
		ini.SetString("三十五级以下经验倍数", textBox8.Text);
		World.三十五级以下经验倍数 = double.Parse(textBox8.Text);
		MessageBox.Show("修改完成");
	}

	private void button9_Click(object sender, EventArgs e)
	{
		ini.SetString("六十级以下经验倍数", textBox9.Text);
		World.六十级以下经验倍数 = double.Parse(textBox9.Text);
		MessageBox.Show("修改完成");
	}

	private void button10_Click(object sender, EventArgs e)
	{
		ini.SetString("八十级以下经验倍数", textBox10.Text);
		World.八十级以下经验倍数 = double.Parse(textBox10.Text);
		MessageBox.Show("修改完成");
	}

	private void button11_Click(object sender, EventArgs e)
	{
		ini.SetString("一百级以下经验倍数", textBox11.Text);
		World.一百级以下经验倍数 = double.Parse(textBox11.Text);
		MessageBox.Show("修改完成");
	}

	private void button12_Click(object sender, EventArgs e)
	{
		ini.SetString("一百一以下经验倍数", textBox12.Text);
		World.一百一以下经验倍数 = double.Parse(textBox12.Text);
		MessageBox.Show("修改完成");
	}

	private void button13_Click(object sender, EventArgs e)
	{
		ini.SetString("一百二以下经验倍数", textBox13.Text);
		World.一百二以下经验倍数 = double.Parse(textBox13.Text);
		MessageBox.Show("修改完成");
	}

	private void button14_Click(object sender, EventArgs e)
	{
		ini.SetString("一百三以下经验倍数", textBox14.Text);
		World.一百三以下经验倍数 = double.Parse(textBox14.Text);
		MessageBox.Show("修改完成");
	}

	private void button15_Click(object sender, EventArgs e)
	{
		ini.SetString("一百四以下经验倍数", textBox15.Text);
		World.一百四以下经验倍数 = double.Parse(textBox15.Text);
		MessageBox.Show("修改完成");
	}

	private void button16_Click(object sender, EventArgs e)
	{
		ini.SetString("一百五以下经验倍数", textBox16.Text);
		World.一百五以下经验倍数 = double.Parse(textBox16.Text);
		MessageBox.Show("修改完成");
	}

	private void button17_Click(object sender, EventArgs e)
	{
		ini.SetString("一百六以下经验倍数", textBox17.Text);
		World.一百六以下经验倍数 = double.Parse(textBox17.Text);
		MessageBox.Show("修改完成");
	}

	private void button21_Click(object sender, EventArgs e)
	{
		ini.SetString("人物越级怪物掉落差", textBox21.Text);
		World.人物越级怪物掉落差 = int.Parse(textBox21.Text);
		MessageBox.Show("修改完成");
	}

	private void button18_Click(object sender, EventArgs e)
	{
		ini.SetString("组队等级限制", textBox18.Text);
		World.组队等级限制 = int.Parse(textBox18.Text);
		MessageBox.Show("修改完成");
	}

	private void button19_Click(object sender, EventArgs e)
	{
		ini.SetString("人物越级怪物经验差", textBox19.Text);
		World.人物越级怪物经验差 = int.Parse(textBox19.Text);
		MessageBox.Show("修改完成");
	}

	private void button20_Click(object sender, EventArgs e)
	{
		ini.SetString("怪物越级人物经验差", textBox20.Text);
		World.怪物越级人物经验差 = int.Parse(textBox20.Text);
		MessageBox.Show("修改完成");
	}

	private void button22_Click(object sender, EventArgs e)
	{
		ini.SetString("怪物越级人物掉落差", textBox22.Text);
		World.怪物越级人物掉落差 = int.Parse(textBox22.Text);
		MessageBox.Show("修改完成");
	}

	private void button23_Click(object sender, EventArgs e)
	{
		ini.SetString("击杀BOSS等级差", textBox23.Text);
		World.击杀BOSS等级差 = int.Parse(textBox23.Text);
		MessageBox.Show("修改完成");
	}
}

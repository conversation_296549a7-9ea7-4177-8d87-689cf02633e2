using System;
using System.Timers;

namespace loginServer;

public class KillClass
{
	public Timer npcyd = new Timer(300000.0);

	public DateTime time;

	private string userid;

	private int _conn;

	private int _move;

	public string UserId
	{
		get
		{
			return userid;
		}
		set
		{
			userid = value;
		}
	}

	public int conn
	{
		get
		{
			return _conn;
		}
		set
		{
			_conn = value;
		}
	}

	public int move
	{
		get
		{
			return _move;
		}
		set
		{
			_move = value;
		}
	}

	public KillClass()
	{
		time = DateTime.Now.AddMinutes(5.0);
		npcyd.Elapsed += 时间结束事件;
		npcyd.Enabled = true;
	}

	public void 时间结束事件(object source, ElapsedEventArgs e)
	{
		lock (World.KillList)
		{
			World.KillList.TryRemove(UserId, out var _);
		}
		npcyd.Close();
		npcyd.Dispose();
	}
}

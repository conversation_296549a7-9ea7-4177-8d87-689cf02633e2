using System.Collections.Generic;

namespace RxjhVer23;

public interface INewWebShopUser
{
	int 人物全服ID { get; }

	string Userid { get; }

	int FLD_RXPIONT { get; }

	int FLD_RXPIONTX { get; }

	void 查百宝阁元宝数();

	bool CheckSingleItemBackpackSpace(INewWebShopSaleItem saleItem, int count);

	bool CheckMultipleItemsBackpackSpace(List<INewWebShopSaleItem> saleItems, List<int> counts);

	void DistributeSingleItemToBackpack(INewWebShopSaleItem saleItem, int count);

	void DistributeMultipleItemsToBackpack(List<INewWebShopSaleItem> saleItems, List<int> counts);

	void SendFullPacket(byte[] fullPacket);
}

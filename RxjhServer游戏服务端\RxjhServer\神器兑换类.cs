namespace RxjhServer;

public class 神器兑换类
{
	private int _是否开启;

	private int _颜色;

	private int _需要武勋;

	private int _需要元宝;

	private int _需要钻石;

	private int _ID;

	private int _需要武皇币;

	private string _获得物品;

	private string _需要物品;

	private int _需要充值点;

	private string _系统公告;

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public int 是否开启
	{
		get
		{
			return _是否开启;
		}
		set
		{
			_是否开启 = value;
		}
	}

	public int 颜色
	{
		get
		{
			return _颜色;
		}
		set
		{
			_颜色 = value;
		}
	}

	public int 需要武勋
	{
		get
		{
			return _需要武勋;
		}
		set
		{
			_需要武勋 = value;
		}
	}

	public int 需要元宝
	{
		get
		{
			return _需要元宝;
		}
		set
		{
			_需要元宝 = value;
		}
	}

	public int 需要钻石
	{
		get
		{
			return _需要钻石;
		}
		set
		{
			_需要钻石 = value;
		}
	}

	public int 需要武皇币
	{
		get
		{
			return _需要武皇币;
		}
		set
		{
			_需要武皇币 = value;
		}
	}

	public string 获得物品
	{
		get
		{
			return _获得物品;
		}
		set
		{
			_获得物品 = value;
		}
	}

	public string 需要物品
	{
		get
		{
			return _需要物品;
		}
		set
		{
			_需要物品 = value;
		}
	}

	public int 需要充值点
	{
		get
		{
			return _需要充值点;
		}
		set
		{
			_需要充值点 = value;
		}
	}

	public string 系统公告
	{
		get
		{
			return _系统公告;
		}
		set
		{
			_系统公告 = value;
		}
	}
}

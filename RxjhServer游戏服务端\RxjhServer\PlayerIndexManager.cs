using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace RxjhServer
{
    // 2025-0618 EVIAS 高性能玩家索引管理器
    public class PlayerIndexManager
    {
        private static readonly Lazy<PlayerIndexManager> _instance = new(() => new PlayerIndexManager());
        public static PlayerIndexManager Instance => _instance.Value;

        // 主索引 - O(1)查找
        private readonly ConcurrentDictionary<int, Players> _playersByWorldId = new();
        private readonly ConcurrentDictionary<string, Players> _playersByUserId = new();
        private readonly ConcurrentDictionary<string, Players> _playersByUserName = new();

        // 地图索引 - 快速获取地图内玩家
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<int, Players>> _playersByMap = new();

        // 帮派索引 - 快速获取帮派成员
        private readonly ConcurrentDictionary<string, ConcurrentDictionary<int, Players>> _playersByGuild = new();

        // 状态索引 - 快速筛选特定状态玩家
        private readonly ConcurrentDictionary<int, Players> _onlinePlayers = new();
        private readonly ConcurrentDictionary<int, Players> _cloudPlayers = new();
        private readonly ConcurrentDictionary<int, Players> _fakePlayers = new();

        // 统计信息
        private long _indexOperations = 0;
        private long _searchOperations = 0;

        private PlayerIndexManager() { }

        // 添加玩家到索引
        public void AddPlayer(Players player)
        {
            if (player?.Client == null) return;

            try
            {
                Interlocked.Increment(ref _indexOperations);

                // 主索引
                _playersByWorldId.TryAdd(player.Client.WorldId, player);
                _playersByUserId.TryAdd(player.Userid, player);
                _playersByUserName.TryAdd(player.UserName.ToLower(), player);

                // 地图索引
                AddToMapIndex(player);

                // 帮派索引
                AddToGuildIndex(player);

                // 状态索引
                AddToStatusIndex(player);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("添加玩家索引失败: ", player.UserName, " - ", ex.Message));
            }
        }

        // 从索引中移除玩家
        public void RemovePlayer(Players player)
        {
            if (player?.Client == null) return;

            try
            {
                Interlocked.Increment(ref _indexOperations);

                // 主索引
                _playersByWorldId.TryRemove(player.Client.WorldId, out _);
                _playersByUserId.TryRemove(player.Userid, out _);
                _playersByUserName.TryRemove(player.UserName.ToLower(), out _);

                // 地图索引
                RemoveFromMapIndex(player);

                // 帮派索引
                RemoveFromGuildIndex(player);

                // 状态索引
                RemoveFromStatusIndex(player);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("移除玩家索引失败: ", player.UserName, " - ", ex.Message));
            }
        }

        // 按用户名查找玩家 - O(1)复杂度
        public Players FindByUserName(string userName)
        {
            if (string.IsNullOrEmpty(userName)) return null;

            Interlocked.Increment(ref _searchOperations);
            _playersByUserName.TryGetValue(userName.ToLower(), out var player);
            return player;
        }

        // 按WorldId查找玩家 - O(1)复杂度
        public Players FindByWorldId(int worldId)
        {
            Interlocked.Increment(ref _searchOperations);
            _playersByWorldId.TryGetValue(worldId, out var player);
            return player;
        }

        // 按UserId查找玩家 - O(1)复杂度
        public Players FindByUserId(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return null;

            Interlocked.Increment(ref _searchOperations);
            _playersByUserId.TryGetValue(userId, out var player);
            return player;
        }

        // 获取地图内所有玩家 - O(1)复杂度
        public IEnumerable<Players> GetPlayersInMap(int mapId)
        {
            Interlocked.Increment(ref _searchOperations);
            if (_playersByMap.TryGetValue(mapId, out var mapPlayers))
            {
                return mapPlayers.Values;
            }
            return Enumerable.Empty<Players>();
        }

        // 获取帮派所有成员 - O(1)复杂度
        public IEnumerable<Players> GetGuildMembers(string guildName)
        {
            if (string.IsNullOrEmpty(guildName)) return Enumerable.Empty<Players>();

            Interlocked.Increment(ref _searchOperations);
            if (_playersByGuild.TryGetValue(guildName.ToLower(), out var guildPlayers))
            {
                return guildPlayers.Values;
            }
            return Enumerable.Empty<Players>();
        }

        // 获取在线玩家数量
        public int GetOnlineCount() => _onlinePlayers.Count;

        // 获取云挂玩家数量
        public int GetCloudCount() => _cloudPlayers.Count;

        // 获取假人玩家数量
        public int GetFakeCount() => _fakePlayers.Count;

        // 获取所有在线玩家
        public IEnumerable<Players> GetOnlinePlayers() => _onlinePlayers.Values;

        // 更新玩家地图位置
        public void UpdatePlayerMap(Players player, int oldMapId, int newMapId)
        {
            if (player?.Client == null) return;

            try
            {
                // 从旧地图移除
                if (oldMapId > 0 && _playersByMap.TryGetValue(oldMapId, out var oldMapPlayers))
                {
                    oldMapPlayers.TryRemove(player.Client.WorldId, out _);
                }

                // 添加到新地图
                if (newMapId > 0)
                {
                    var newMapPlayers = _playersByMap.GetOrAdd(newMapId, _ => new ConcurrentDictionary<int, Players>());
                    newMapPlayers.TryAdd(player.Client.WorldId, player);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("更新玩家地图索引失败: ", player.UserName, " - ", ex.Message));
            }
        }

        // 私有方法：添加到地图索引
        private void AddToMapIndex(Players player)
        {
            if (player.人物坐标_地图 > 0)
            {
                var mapPlayers = _playersByMap.GetOrAdd(player.人物坐标_地图, _ => new ConcurrentDictionary<int, Players>());
                mapPlayers.TryAdd(player.Client.WorldId, player);
            }
        }

        // 私有方法：从地图索引移除
        private void RemoveFromMapIndex(Players player)
        {
            if (player.人物坐标_地图 > 0 && _playersByMap.TryGetValue(player.人物坐标_地图, out var mapPlayers))
            {
                mapPlayers.TryRemove(player.Client.WorldId, out _);
            }
        }

        // 私有方法：添加到帮派索引
        private void AddToGuildIndex(Players player)
        {
            if (!string.IsNullOrEmpty(player.帮派名字))
            {
                var guildPlayers = _playersByGuild.GetOrAdd(player.帮派名字.ToLower(), _ => new ConcurrentDictionary<int, Players>());
                guildPlayers.TryAdd(player.Client.WorldId, player);
            }
        }

        // 私有方法：从帮派索引移除
        private void RemoveFromGuildIndex(Players player)
        {
            if (!string.IsNullOrEmpty(player.帮派名字) && _playersByGuild.TryGetValue(player.帮派名字.ToLower(), out var guildPlayers))
            {
                guildPlayers.TryRemove(player.Client.WorldId, out _);
            }
        }

        // 私有方法：添加到状态索引
        private void AddToStatusIndex(Players player)
        {
            if (!player.Client.挂机)
            {
                _onlinePlayers.TryAdd(player.Client.WorldId, player);
            }
            
        }

        // 私有方法：从状态索引移除
        private void RemoveFromStatusIndex(Players player)
        {
            _onlinePlayers.TryRemove(player.Client.WorldId, out _);
            _cloudPlayers.TryRemove(player.Client.WorldId, out _);
            _fakePlayers.TryRemove(player.Client.WorldId, out _);
        }

        // 获取索引统计信息
        public string GetIndexStats()
        {
            return FastString.Format(
                "玩家索引统计: 总数:{0} 地图:{1} 帮派:{2} 在线:{3} 操作:{4} 查询:{5}",
                _playersByWorldId.Count,
                _playersByMap.Count,
                _playersByGuild.Count,
                _onlinePlayers.Count,
                _indexOperations,
                _searchOperations
            );
        }

        // 清理空的索引容器
        public void CleanupEmptyContainers()
        {
            try
            {
                // 清理空的地图索引
                var emptyMaps = _playersByMap.Where(kvp => kvp.Value.IsEmpty).Select(kvp => kvp.Key).ToList();
                foreach (var mapId in emptyMaps)
                {
                    _playersByMap.TryRemove(mapId, out _);
                }

                // 清理空的帮派索引
                var emptyGuilds = _playersByGuild.Where(kvp => kvp.Value.IsEmpty).Select(kvp => kvp.Key).ToList();
                foreach (var guildName in emptyGuilds)
                {
                    _playersByGuild.TryRemove(guildName, out _);
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, FastString.Concat("清理空索引容器失败: ", ex.Message));
            }
        }
    }
}

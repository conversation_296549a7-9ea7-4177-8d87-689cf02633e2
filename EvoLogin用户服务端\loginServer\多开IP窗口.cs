using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

public class 多开IP窗口 : Form
{
	private IContainer components = null;

	private ListView listView1;

	private ColumnHeader columnHeader5;

	private ColumnHeader columnHeader7;

	private ColumnHeader columnHeader6;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 解封ToolStripMenuItem;

	private StatusStrip statusStrip1;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private ToolStripStatusLabel toolStripStatusLabel1;

	public 多开IP窗口()
	{
		InitializeComponent();
	}

	private void 封停账号窗口_Load(object sender, EventArgs e)
	{
		try
		{
			string sqlCommand = "SELECT * FROM 限制IP多开表";
			DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand);
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count > 0)
			{
				for (int i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					string text = dBToDataTable.Rows[i]["ID"].ToString();
					string text2 = dBToDataTable.Rows[i]["FLD_PID"].ToString();
					string text3 = dBToDataTable.Rows[i]["FLD_NUMBER"].ToString();
					string text4 = dBToDataTable.Rows[i]["FLD_TIME"].ToString();
					string text5 = RxjhClass.GetUserIpadds(dBToDataTable.Rows[i]["FLD_PID"].ToString());
					string[] items = new string[5] { text, text2, text3, text4, text5 };
					ListViewItem item = new ListViewItem(items);
					listView1.Items.Insert(listView1.Items.Count, item);
				}
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "加载封停账号出错:" + ex.ToString());
		}
	}

	private void 解封ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listView1.SelectedItems.Count <= 0)
			{
				return;
			}
			string text = listView1.SelectedItems[0].SubItems[0].Text;
			if (text.Length > 0)
			{
				if (DBA.ExeSqlCommand("DELETE 限制IP多开表 WHERE ID='" + text + "'", "rxjhaccount") == -1)
				{
					toolStripStatusLabel1.Text = "删除失败";
					return;
				}
				toolStripStatusLabel1.Text = "删除成功";
				MessageBox.Show("删除成功");
			}
		}
		catch (Exception)
		{
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(多开IP窗口));
            this.listView1 = new System.Windows.Forms.ListView();
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.解封ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.contextMenuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listView1
            // 
            this.listView1.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader5,
            this.columnHeader7,
            this.columnHeader6,
            this.columnHeader1,
            this.columnHeader2});
            this.listView1.ContextMenuStrip = this.contextMenuStrip1;
            this.listView1.FullRowSelect = true;
            this.listView1.GridLines = true;
            this.listView1.HideSelection = false;
            this.listView1.Location = new System.Drawing.Point(0, 18);
            this.listView1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.listView1.Name = "listView1";
            this.listView1.Size = new System.Drawing.Size(782, 506);
            this.listView1.TabIndex = 5;
            this.listView1.UseCompatibleStateImageBehavior = false;
            this.listView1.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "账号";
            // 
            // columnHeader7
            // 
            this.columnHeader7.Text = "多开IP";
            this.columnHeader7.Width = 114;
            // 
            // columnHeader6
            // 
            this.columnHeader6.Text = "允许数量";
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "允许时间";
            this.columnHeader1.Width = 134;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "允许地区";
            this.columnHeader2.Width = 144;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.解封ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(153, 34);
            // 
            // 解封ToolStripMenuItem
            // 
            this.解封ToolStripMenuItem.Name = "解封ToolStripMenuItem";
            this.解封ToolStripMenuItem.Size = new System.Drawing.Size(152, 30);
            this.解封ToolStripMenuItem.Text = "删除多开";
            this.解封ToolStripMenuItem.Click += new System.EventHandler(this.解封ToolStripMenuItem_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1});
            this.statusStrip1.Location = new System.Drawing.Point(0, 542);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(2, 0, 21, 0);
            this.statusStrip1.Size = new System.Drawing.Size(798, 22);
            this.statusStrip1.TabIndex = 6;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.AutoSize = false;
            this.toolStripStatusLabel1.BackColor = System.Drawing.Color.Transparent;
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(200, 15);
            this.toolStripStatusLabel1.Text = "等待操作";
            this.toolStripStatusLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // 多开IP窗口
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(798, 564);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.listView1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "多开IP窗口";
            this.Text = "多开IP窗口";
            this.Load += new System.EventHandler(this.封停账号窗口_Load);
            this.contextMenuStrip1.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}

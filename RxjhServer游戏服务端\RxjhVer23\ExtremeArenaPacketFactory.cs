using System;
using System.Collections.Generic;

namespace RxjhVer23;

internal class ExtremeArenaPacketFactory
{
	private class MessageParametes
	{
		public short Paramete1 = 1;

		public short Paramete2 = 1;

		public short Paramete3 = 1;

		public int Paramete4 = 0;

		public int Paramete5 = 0;

		public int Paramete6 = 0;

		public int Paramete7 = 0;

		public int Paramete8 = 0;
	}

	private const int _systemGuid = 9999;

	private const int _opcodeMessage = 1328;

	private const int _opcodeReviveCountStatus = 323;

	private const int _opcodeTopThreeRankingPodium = 790;

	private const int _opcodeRankingListOrParticipationRecords = 346;

	private static byte[] GenerateFullPacket(PacketDataWriteStream stream, MessageParametes msg, int guid)
	{
		stream.Write2(msg.Paramete1);
		stream.Write2(msg.Paramete2);
		stream.Write2(msg.Paramete3);
		stream.Write4(msg.Paramete4);
		stream.Write4(msg.Paramete5);
		stream.Write4(msg.Paramete6);
		stream.Write4(msg.Paramete7);
		stream.Write4(msg.Paramete8);
		return GenerateFullPacket(stream.GetPacketData(1328, guid));
	}

	public static byte[] SpMessageCompetitionStartsIn3Minutes()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 1,
			Paramete3 = 1,
			Paramete4 = 1,
			Paramete5 = 6679,
			Paramete6 = 50,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageCompetitionStartsIn2Minutes()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 2,
			Paramete3 = 1,
			Paramete4 = 1,
			Paramete5 = 6680,
			Paramete6 = 50,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageRegistrationSuccessful()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 11,
			Paramete3 = 1,
			Paramete4 = 654,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageRegistrationTimeout()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 11,
			Paramete3 = 3,
			Paramete4 = 0,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageRegistrationCriteriaNotMet()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 11,
			Paramete3 = 5,
			Paramete4 = 0,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageRegistrationAlreadySuccessful()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 11,
			Paramete3 = 4,
			Paramete4 = 0,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageCompetitionStartsIn1Minute()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 3,
			Paramete3 = 1,
			Paramete4 = 2,
			Paramete5 = 6681,
			Paramete6 = 50,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageMatchFailed()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 17,
			Paramete3 = 1,
			Paramete4 = 5912,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageUpdateReviveCountStatus(int guid, int reviveCount, bool isShow)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(1000001610);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write2(118);
		packetDataWriteStream.Write2(2);
		packetDataWriteStream.Write4(reviveCount);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(isShow ? 1 : 0);
		packetDataWriteStream.Write4(2406162017u);
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write4(0);
		return GenerateFullPacket(packetDataWriteStream.GetPacketData(323, guid));
	}

	public static byte[] SpMessageCompetitionCountdown5Seconds()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 14,
			Paramete3 = 1,
			Paramete4 = 45001,
			Paramete5 = 120,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageCompetitionStart()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 5,
			Paramete3 = 2,
			Paramete4 = 0,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageCompetitionCountdownSync(int elapsedSeconds)
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 10,
			Paramete3 = 1,
			Paramete4 = elapsedSeconds,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessagePlayerDeath(int guid)
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 7,
			Paramete3 = 2,
			Paramete4 = 3,
			Paramete5 = 6677,
			Paramete6 = 5,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageCompetitionEndAnimation(bool isVictory, int points)
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = (short)(isVictory ? 12 : 13),
			Paramete3 = 1,
			Paramete4 = (isVictory ? 6675 : 6676),
			Paramete5 = points,
			Paramete6 = 0,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageVictoryAdditional()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 16,
			Paramete3 = 1,
			Paramete4 = 20,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 900000842,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageFailureAdditional()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 16,
			Paramete3 = 1,
			Paramete4 = 5,
			Paramete5 = 0,
			Paramete6 = 0,
			Paramete7 = 900000842,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpMessageRemindPlayerToExit()
	{
		using PacketDataWriteStream stream = new PacketDataWriteStream();
		int guid = 9999;
		MessageParametes msg = new MessageParametes
		{
			Paramete1 = 1,
			Paramete2 = 6,
			Paramete3 = 1,
			Paramete4 = 4,
			Paramete5 = 6678,
			Paramete6 = 10,
			Paramete7 = 0,
			Paramete8 = 0
		};
		return GenerateFullPacket(stream, msg, guid);
	}

	public static byte[] SpTopThreeRankingPodium(int guid, List<IExtremeArenaRanking> rankings)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		for (int i = 0; i < rankings.Count; i++)
		{
			packetDataWriteStream.WriteString(rankings[i].称号, 15);
			packetDataWriteStream.Write4(rankings[i].门派ID);
			packetDataWriteStream.WriteString(rankings[i].门派, 15);
			packetDataWriteStream.Write1(0);
			packetDataWriteStream.Write1(0);
			packetDataWriteStream.Write1(0);
			packetDataWriteStream.Write1(rankings[i].势力);
			packetDataWriteStream.Write1(49);
			packetDataWriteStream.Write1(rankings[i].转职);
			packetDataWriteStream.Write1(rankings[i].职业);
			packetDataWriteStream.Write1(1);
			packetDataWriteStream.Write1(222);
			packetDataWriteStream.Write1(243);
			packetDataWriteStream.Write1(1);
			packetDataWriteStream.WriteString("", 9);
			packetDataWriteStream.Write1(1);
			packetDataWriteStream.Write1(rankings[i].性别);
			packetDataWriteStream.WriteString("", 16);
			packetDataWriteStream.Write8(200303078L);
			packetDataWriteStream.Write8(200503078L);
			packetDataWriteStream.Write8(200503078L);
			packetDataWriteStream.Write8(200803078L);
			packetDataWriteStream.Write8(200200368L);
			packetDataWriteStream.WriteString("", 24);
			packetDataWriteStream.Write1(14);
			packetDataWriteStream.Write4((rankings[i].性别 == 1) ? 16903191 : 26903191);
			packetDataWriteStream.WriteString("", 41);
			packetDataWriteStream.Write1(8);
			packetDataWriteStream.Write1(25);
			packetDataWriteStream.WriteString("", 95);
			packetDataWriteStream.Write1(1);
			packetDataWriteStream.Write4(8);
			packetDataWriteStream.Write4(8);
			packetDataWriteStream.WriteString("", 70);
			packetDataWriteStream.Write4(rankings[i].排名);
			packetDataWriteStream.WriteString("", 83);
		}
		return GenerateFullPacket(packetDataWriteStream.GetPacketData(790, guid));
	}

	public static byte[] SpRankingList(int guid, List<IExtremeArenaRanking> rankings)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(rankings.Count);
		packetDataWriteStream.Write(0);
		packetDataWriteStream.Write(2);
		packetDataWriteStream.Write2(7);
		packetDataWriteStream.Write4(1);
		for (int i = 0; i < rankings.Count; i++)
		{
			packetDataWriteStream.WriteString(rankings[i].称号, 15);
			packetDataWriteStream.WriteString(rankings[i].门派, 15);
			packetDataWriteStream.Write2(rankings[i].职业);
			packetDataWriteStream.Write2(rankings[i].转职);
			packetDataWriteStream.Write2(rankings[i].势力);
			packetDataWriteStream.Write4(rankings[i].等级);
			packetDataWriteStream.Write8(rankings[i].点数);
			packetDataWriteStream.Write4(rankings[i].排名);
			packetDataWriteStream.Write4(0);
		}
		return GenerateFullPacket(packetDataWriteStream.GetPacketData(346, guid));
	}

	public static byte[] SpParticipationRecords(int guid)
	{
		using PacketDataWriteStream packetDataWriteStream = new PacketDataWriteStream();
		packetDataWriteStream.Write4(0);
		packetDataWriteStream.Write1(253);
		packetDataWriteStream.Write1(2);
		packetDataWriteStream.Write1(7);
		packetDataWriteStream.Write1(11);
		packetDataWriteStream.Write4(2);
		return GenerateFullPacket(packetDataWriteStream.GetPacketData(346, guid));
	}

	private static byte[] GenerateFullPacket(byte[] data)
	{
		List<byte> list = new List<byte>();
		list.AddRange(BitConverter.GetBytes((ushort)21930));
		list.AddRange(BitConverter.GetBytes((ushort)data.Length));
		list.AddRange(data);
		list.AddRange(BitConverter.GetBytes((ushort)43605));
		return list.ToArray();
	}
}

namespace RxjhServer;

public class 坐标Class
{
	private int _ID;

	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private int _Rxjh_Map;

	private string _Rxjh_name;

	private string _别墅名字;

	private int _是否别墅;

	private int _暴率;

	public int ID
	{
		get
		{
			return _ID;
		}
		set
		{
			_ID = value;
		}
	}

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public string Rxjh_name
	{
		get
		{
			return _Rxjh_name;
		}
		set
		{
			_Rxjh_name = value;
		}
	}

	public string 别墅名字
	{
		get
		{
			return _别墅名字;
		}
		set
		{
			_别墅名字 = value;
		}
	}

	public int 是否别墅
	{
		get
		{
			return _是否别墅;
		}
		set
		{
			_是否别墅 = value;
		}
	}

	public int 暴率
	{
		get
		{
			return _暴率;
		}
		set
		{
			_暴率 = value;
		}
	}

	public 坐标Class()
	{
	}

	public static string getmapname(int id)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.Rxjh_Map == id)
			{
				return item.Rxjh_name;
			}
		}
		return string.Empty;
	}

	public static string getmapid(int id)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.Rxjh_Map == id)
			{
				return item.别墅名字;
			}
		}
		return string.Empty;
	}

	public static int mapid(int id)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.Rxjh_Map == id)
			{
				return item.是否别墅;
			}
		}
		return 0;
	}

	public static int 地图增加爆率(int 地图)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.Rxjh_Map == 地图)
			{
				return item.暴率;
			}
		}
		return 0;
	}

	public static string 查别墅名字(string 名字)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.别墅名字 == 名字)
			{
				return item.Rxjh_name;
			}
		}
		return string.Empty;
	}

	public static string GetMapName(int id)
	{
		string value;
		return (!World.Maplist.TryGetValue(id, out value)) ? string.Empty : value;
	}

	public static 坐标Class GetMap(int mapid)
	{
		foreach (坐标Class item in World.移动)
		{
			if (item.Rxjh_Map == mapid)
			{
				return item;
			}
		}
		return null;
	}

	public 坐标Class(float Rxjh__X, float Rxjh__Y, float Rxjh__Z, int Rxjh__Map)
	{
		Rxjh_X = Rxjh__X;
		Rxjh_Y = Rxjh__Y;
		Rxjh_Z = Rxjh__Z;
		Rxjh_Map = Rxjh__Map;
	}

	public static string getname(int mapid)
	{
		switch (mapid)
		{
		case 201:
			return "三邪关";
		case 101:
			return "泫勃派";
		case 401:
			return "无天阁1层";
		case 402:
			return "无天阁2层";
		case 403:
			return "无天阁3层";
		case 301:
			return "柳正关";
		case 601:
			return "渊竹林";
		case 501:
			return "万寿阁1层";
		case 502:
			return "万寿阁2层";
		case 503:
			return "万寿阁3层";
		case 901:
			return "荤捧包";
		case 801:
			return "义斗关";
		case 41001:
			return "仙魔大战";
		case 701:
			return "竹火林";
		case 1101:
			return "柳善提督府";
		case 1001:
			return "神武门";
		case 1301:
			return "南明湖";
		case 1201:
			return "银币广场";
		case 1501:
			return "血魔洞2层";
		case 1401:
			return "血魔洞1层";
		case 1801:
			return "地灵洞2层";
		case 1701:
			return "地灵洞1层";
		case 1601:
			return "血魔洞3层";
		case 2001:
			return "南明洞";
		case 1901:
			return "地灵洞3层";
		case 2201:
			return "百武关";
		case 2101:
			return "松月关";
		case 2501:
			return "失落之地";
		case 2401:
			return "会员之家";
		case 2711:
			return "迷宫第二层";
		case 2701:
			return "迷宫第一层";
		case 2601:
			return "钥匙房";
		case 2801:
			return "三界玄门";
		case 2721:
			return "迷宫第三层";
		case 5001:
			return "北海冰宫";
		case 3201:
			return "女王宫殿";
		case 2901:
			return "修炼之地";
		case 5201:
			return "北海冰宫内城";
		default:
			return string.Empty;
		case 5601:
			return "植物大战僵尸";
		case 5501:
			return "北海冰宫幻影";
		case 5401:
			return "玄冰地宫";
		case 6001:
			return "南林";
		case 20001:
			return "伏魔洞";
		case 21001:
			return "伏魔洞1层";
		case 25100:
			return "虎峡谷";
		case 25201:
			return "地下密路安全地带";
		case 25202:
			return "九字诀阵 临";
		case 25203:
			return "九字诀阵 兵";
		case 25204:
			return "九字诀阵 斗";
		case 25205:
			return "九字诀阵 者";
		case 25206:
			return "九字诀阵 皆";
		case 25207:
			return "九字诀阵 阵";
		case 25208:
			return "九字诀阵 列";
		case 25209:
			return "九字诀阵 前";
		case 25210:
			return "九字诀阵 行";
		case 25301:
			return "内部结界14区";
		case 26000:
			return "花亭平原";
		case 26100:
			return "燕飞阁";
		case 42101:
			return "天魔神宫";
		case 32001:
			return "皮皮岛";
		case 32002:
			return "遗忘村庄";
		case 32101:
		case 32102:
		case 32103:
		case 32104:
		case 32105:
		case 32106:
		case 32107:
		case 32108:
		case 32109:
			return "世外武林";
		}
	}
}

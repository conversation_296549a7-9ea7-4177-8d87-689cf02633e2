using System.Windows.Forms;

public static class ItemDef
{
    public static class Item
    {
        public const int 金刚石 = 800000001;

        public const int 寒玉石 = 800000002;

        public const int 强化石 = 800000006;

        public const int 神秘金刚石 = 800000011;

        public const int 神秘寒玉石 = 800000012;

        public const int 热血石 = 800000013;

        public const int 混元金刚石 = 800000023;

        public const int 冰魄寒玉石 = 800000024;

        public const int 神秘金刚石_追伤 = 800000025;

        public const int 神秘金刚石_武功 = 800000026;

        public const int 属性原石 = 800000027;

        public const int 属性石 = 800000028;

        public const int 初级奇玉石 = 800000046;

        public const int 中级奇玉石 = 800000047;

        public const int 高级奇玉石 = 800000048;

        public const int 最高级奇玉石 = 800000049;

        public const int 高级强化石 = 800000060;

        public const int 乾坤金刚石 = 800000061;

        public const int 凝霜寒玉石 = 800000062;

        public const int 天魔方石 = 1000001650;

        public const int 地魔方石 = 1000001651;
    }


    public struct MyItem
	{
		public string Text;

		public int Value;

		public MyItem(string text, int value)
		{
			Text = text;
			Value = value;
		}

		public override string ToString()
		{
			return Text;
		}
	}

	public static void AddComBoxItemReside2(ComboBox cbx)
	{
		cbx.Items.Clear();
		cbx.Items.Add(new MyItem("衣服", 1));
		cbx.Items.Add(new MyItem("护手", 2));
		cbx.Items.Add(new MyItem("武器", 4));
		cbx.Items.Add(new MyItem("鞋子", 5));
		cbx.Items.Add(new MyItem("内甲", 6));
		cbx.Items.Add(new MyItem("项链", 7));
		cbx.Items.Add(new MyItem("耳环", 8));
		cbx.Items.Add(new MyItem("戒指", 10));
		cbx.Items.Add(new MyItem("披风", 12));
		cbx.Items.Add(new MyItem("武功书", 1792));
		cbx.Items.Add(new MyItem("气功书", 19));
		cbx.Items.Add(new MyItem("脚本物品", 20));
		cbx.Items.Add(new MyItem("盒子", 17));
		cbx.Items.Add(new MyItem("箭", 13));
		cbx.Items.Add(new MyItem("门甲", 14));
		cbx.Items.Add(new MyItem("灵兽", 15));
		cbx.Items.Add(new MyItem("宠物用品1", 23));
		cbx.Items.Add(new MyItem("宠物用品2", 24));
		cbx.Items.Add(new MyItem("宠物用品3", 25));
		cbx.Items.Add(new MyItem("远程商店", 1793));
		cbx.Items.Add(new MyItem("远程仓库", 1794));
		cbx.Items.Add(new MyItem("灵宠", 16));
		cbx.Items.Add(new MyItem("花珠", 26));
		cbx.Items.Add(new MyItem("宝珠武器", 32));
		cbx.Items.Add(new MyItem("宝珠衣服", 33));
		cbx.Items.Add(new MyItem("宝珠内甲", 34));
		cbx.Items.Add(new MyItem("宝珠护手", 35));
		cbx.Items.Add(new MyItem("宝珠鞋子", 36));
		cbx.Items.Add(new MyItem("其它", 0));
	}

	public static bool 灵兽装备职业是否相符(int CWJob, int RESIDE1)
	{
		if (RESIDE1 == 6)
		{
			return true;
		}
		switch (CWJob)
		{
		case 1:
			if (RESIDE1 == 7)
			{
				return true;
			}
			break;
		case 2:
			if (RESIDE1 == 8)
			{
				return true;
			}
			break;
		case 3:
			if (RESIDE1 == 9)
			{
				return true;
			}
			break;
		case 4:
			if (RESIDE1 == 10)
			{
				return true;
			}
			break;
		case 5:
			if (RESIDE1 == 15)
			{
				return true;
			}
			break;
		case 6:
			if (RESIDE1 == 20)
			{
				return true;
			}
			break;
		case 7:
			if (RESIDE1 == 21)
			{
				return true;
			}
			break;
		case 8:
			if (RESIDE1 == 22)
			{
				return true;
			}
			break;
		}
		return false;
	}
}

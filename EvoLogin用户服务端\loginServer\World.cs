using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Security.Cryptography;
using System.Windows.Forms;
using loginServer.DbClss;

namespace loginServer;

public class World
{
	public static ConcurrentDictionary<int, NetState> ConnectLst = new ConcurrentDictionary<int, NetState>();

	public static ConcurrentDictionary<IntPtr, SockClient> ServerLst = new ConcurrentDictionary<IntPtr, SockClient>();

	// 2025-0618 EVIAS 保留旧的Players集合定义，但已全部迁移到ConcurrentPlayerManager
	public static ConcurrentDictionary<string, playerS> Players = new ConcurrentDictionary<string, playerS>();

	public static ConcurrentDictionary<string, playerS> PlayersTemp = new ConcurrentDictionary<string, playerS>();

	public static ConcurrentDictionary<string, playerS> privateTeams = new ConcurrentDictionary<string, playerS>();

	public static Queue m_Disposed = Queue.Synchronized(new Queue());

	public static Queue Connect = Queue.Synchronized(new Queue());

	public static Queue 狮子吼List = Queue.Synchronized(new Queue());

	public static DateTime StartTime = DateTime.Now;

	public static int 狮子吼ID = 0;

	public static int 狮子吼最大数 = 100;

	public static ConcurrentDictionary<string, DbClass> Db = new ConcurrentDictionary<string, DbClass>();

	public static List<ServerClass> ServerList = new List<ServerClass>();

	public static List<IPAddress> BipList = new List<IPAddress>();

	public static int ServerCount = 2;

	public static int ServerIDStart = 1;

	public static string SocketState = "Stoped";

	public static ConcurrentDictionary<string, int> PVPList = new ConcurrentDictionary<string, int>();

	public static ConcurrentDictionary<string, KillClass> KillList = new ConcurrentDictionary<string, KillClass>();

	public static ConcurrentDictionary<string, RunMoreClass> RunMoreClass = new ConcurrentDictionary<string, RunMoreClass>();

	public static int 帐号验证服务器端口 = 55970;

	public static int 游戏登陆服务器端口 = 1300;

	public static int 游戏登陆端口最大连接数 = 100;

	public static int 游戏登陆端口最大连接时间数 = 1000;

	public static string 分区号 = "";

	public static bool 封IP;

	public static bool 开启快速连接;

	public static int 真实线路切换 = 0;

	public static int 同IP顶号次数 = 0;

	public static string 封地区 = "";

	public static string 封IP段 = "";

	public static string 注册网站地址 = "http://rxjh.xwwl.net";

	public static string 解地区 = "";

	public static bool 断开连接;

	public static bool 加入过滤列表;

	public static int 自动查询多开 = 0;

	public static int 多开查询操作 = 0;

	public static int 允许多开数量 = 0;

	public static byte[] bNodeServer = null;

	public static string 游戏服务器IP = "127.0.0.1";

	public static int 缓冲区倍数 = 1000;

	public static int aaaaaa = 1;

	public static int vip线路 = 1;

	public static int 显示线路数量 = 20;

	public static string 服务器名;

	public static string Key1 = "***********";

	public static string Key2 = "***********";

	public static string sql;

	public static int 快速连接限制次数 = 5;

	public static int 快速连接限制时间 = 1;

	// 服务器维护状态
	public static bool 维护中 = false;

	public static string 维护提示信息 = "";

	public void SetConfig()
	{
		帐号验证服务器端口 = ((Config.IniReadValue("LoginServer", "帐号验证服务器端口") == "") ? 55970 : int.Parse(Config.IniReadValue("LoginServer", "帐号验证服务器端口")));
		游戏登陆服务器端口 = ((Config.IniReadValue("LoginServer", "游戏登陆服务器端口") == "") ? 1300 : int.Parse(Config.IniReadValue("LoginServer", "游戏登陆服务器端口")));
		狮子吼最大数 = ((Config.IniReadValue("LoginServer", "狮子吼最大数") == "") ? 100 : int.Parse(Config.IniReadValue("LoginServer", "狮子吼最大数")));
		断开连接 = Config.IniReadValue("LoginServer", "断开连接").Trim().ToLower() == "true";
		加入过滤列表 = Config.IniReadValue("LoginServer", "加入过滤列表").Trim().ToLower() == "true";
		游戏登陆端口最大连接数 = ((Config.IniReadValue("LoginServer", "游戏登陆端口最大连接数") == "") ? 10 : int.Parse(Config.IniReadValue("LoginServer", "游戏登陆端口最大连接数")));
		游戏登陆端口最大连接时间数 = ((Config.IniReadValue("LoginServer", "游戏登陆端口最大连接时间数") == "") ? 1000 : int.Parse(Config.IniReadValue("LoginServer", "游戏登陆端口最大连接时间数")));
		服务器名 = Config.IniReadValue("LoginServer", "服务器名").Trim();
		缓冲区倍数 = ((Config.IniReadValue("LoginServer", "缓冲区倍数") == "") ? 1000 : int.Parse(Config.IniReadValue("LoginServer", "缓冲区倍数")));
		分区号 = Config.IniReadValue("LoginServer", "分区号").Trim();
		注册网站地址 = ((Config.IniReadValue("LoginServer", "注册网站地址") == "") ? 注册网站地址 : Config.IniReadValue("LoginServer", "注册网站地址"));
		允许多开数量 = ((Config.IniReadValue("LoginServer", "允许多开数量") == "") ? 允许多开数量 : int.Parse(Config.IniReadValue("LoginServer", "允许多开数量")));
		自动查询多开 = ((Config.IniReadValue("LoginServer", "自动查询多开") == "") ? 自动查询多开 : int.Parse(Config.IniReadValue("LoginServer", "自动查询多开")));
		多开查询操作 = ((Config.IniReadValue("LoginServer", "多开查询操作") == "") ? 多开查询操作 : int.Parse(Config.IniReadValue("LoginServer", "多开查询操作")));
		真实线路切换 = ((Config.IniReadValue("LoginServer", "真实线路切换") == "") ? 真实线路切换 : int.Parse(Config.IniReadValue("LoginServer", "真实线路切换")));
		同IP顶号次数 = ((Config.IniReadValue("LoginServer", "同IP顶号次数") == "") ? 同IP顶号次数 : int.Parse(Config.IniReadValue("LoginServer", "同IP顶号次数")));
		Key1 = Config.IniReadValue("LoginServer", "Key1").Trim();
		Key2 = Config.IniReadValue("LoginServer", "Key2").Trim();
		vip线路 = ((!(Config.IniReadValue("LoginServer", "vip线路") == "")) ? int.Parse(Config.IniReadValue("LoginServer", "vip线路")) : 0);
		显示线路数量 = ((Config.IniReadValue("LoginServer", "显示线路数量") == "") ? 显示线路数量 : int.Parse(Config.IniReadValue("LoginServer", "显示线路数量")));
		开启快速连接 = Config.IniReadValue("LoginServer", "开启快速连接").Trim().ToLower() == "true";
		快速连接限制次数 = ((Config.IniReadValue("LoginServer", "快速连接限制次数") == "") ? 快速连接限制次数 : int.Parse(Config.IniReadValue("LoginServer", "快速连接限制次数")));
		快速连接限制时间 = ((Config.IniReadValue("LoginServer", "快速连接限制时间") == "") ? 快速连接限制时间 : int.Parse(Config.IniReadValue("LoginServer", "快速连接限制时间")));
		游戏服务器IP = Config.IniReadValue("LoginServer", "游戏服务器IP").Trim();
		Db.Clear();
		Config.Path = Application.StartupPath + "\\config\\config.ini";
		Db.TryAdd("rxjhaccount", new DbClass
		{
			ServerDb = "rxjhaccount",
			SqlConnect = "server=" + Config.IniReadValue("LoginServer", "Server").Trim() + ";uid=" + Config.IniReadValue("LoginServer", "UserName").Trim() + ";pwd=" + Config.IniReadValue("LoginServer", "PassWord").Trim() + ";database=" + Config.IniReadValue("LoginServer", "DataName").Trim()
		});
		ServerCount = int.Parse(Config.IniReadValue("LoginServer", "serverCount"));
		ServerList.Clear();
		for (int k = 0; k < ServerCount; k++)
		{
			ServerClass serverClass = new ServerClass();
			serverClass.SERVER_NAME = Config.IniReadValue("server" + k, "servername");
			serverClass.ServerCount = int.Parse(Config.IniReadValue("server" + k, "serverCount"));
			for (int l = 0; l < serverClass.ServerCount; l++)
			{
				ServerXlClass serverXlClass = new ServerXlClass();
				string[] array2 = Config.IniReadValue("server" + k, "server" + l).Split(',');
				serverXlClass.SERVER_NAME = array2[0];
				serverXlClass.ServerId = l;
				serverXlClass.ServerZId = int.Parse(array2[4]);
				serverXlClass.SERVER_IP = array2[2];
				serverXlClass.SERVER_PORT = array2[3];
				serverXlClass.线路状态 = int.Parse(array2[5]);
				serverClass.ServerList.Add(serverXlClass);
			}
			ServerXlClass serverXlClass2 = new ServerXlClass();
			string[] array3 = Config.IniReadValue("server" + k, "serverX").Split(',');
			serverXlClass2.SERVER_NAME = array3[0];
			serverXlClass2.ServerId = 20;
			serverXlClass2.ServerZId = int.Parse(array3[4]);
			serverXlClass2.SERVER_IP = array3[2];
			serverXlClass2.SERVER_PORT = array3[3];
			serverXlClass2.线路状态 = int.Parse(array3[5]);
			serverClass.ServerList.Add(serverXlClass2);
			ServerList.Add(serverClass);
		}
		SetConfig3();
		加载多开用户();
		Form1.WriteLine(2, "加载配置文件完成");
	}

	public void SetConfig3()
	{
		封IP = Config.IniReadValue("LoginServer", "封IP").Trim().ToLower() == "true";
		封地区 = Config.IniReadValue("LoginServer", "封地区");
		解地区 = Config.IniReadValue("LoginServer", "解地区");
		封IP段 = Config.IniReadValue("LoginServer", "封IP段");
		Form1.WriteLine(2, "加载封IP完成");
	}

	public static int GetRandomSeed()
	{
		byte[] array = new byte[4];
		new RNGCryptoServiceProvider().GetBytes(array);
		return BitConverter.ToInt32(array, 0);
	}

	public void 加载多开用户()
	{
		RunMoreClass.Clear();
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT    *    FROM   限制IP多开表", "rxjhaccount");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			Form1.WriteLine(2, "加载多开用户完成----没有数据");
		}
		else
		{
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				RunMoreClass runMoreClass = new RunMoreClass();
				runMoreClass.PID = dBToDataTable.Rows[i]["FLD_PID"].ToString();
				runMoreClass.Time = (DateTime)dBToDataTable.Rows[i]["FLD_TIME"];
				runMoreClass.Number = (int)dBToDataTable.Rows[i]["FLD_NUMBER"];
				if (!RunMoreClass.ContainsKey(runMoreClass.PID))
				{
					RunMoreClass.TryAdd(runMoreClass.PID, runMoreClass);
				}
			}
			Form1.WriteLine(2, "加载多开用户完成    数量:" + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public static void OpClient(string ServerID, string WorldID, string OpCode)
	{
		if (WorldID == null || WorldID == "" || ServerID == null)
		{
			return;
		}
		foreach (SockClient value in ServerLst.Values)
		{
			if (RxjhClass.IsEquals(value.ServerId, ServerID))
			{
				value.Sendd("OpClient|" + WorldID + "|" + OpCode);
				break;
			}
		}
	}

	public static NetState 得到帐号数据(int wid)
	{
		if (!ConnectLst.TryGetValue(wid, out var value))
		{
			return null;
		}
		return value;
	}

	public static void ProcessSerQueue()
	{
		try
		{
			int num = 0;
			while (num < 200 && Connect.Count > 0)
			{
				num++;
				SevConnClass sevConnClass = (SevConnClass)Connect.Dequeue();
				try
				{
					if (sevConnClass.UserId.IndexOf(' ', sevConnClass.UserId.Length - 1) != -1)
					{
						Form1.WriteLine(1, "用户非法登陆[" + sevConnClass.UserId + "]" + sevConnClass.UserIp);
					}
					else
					{
						if (!查询踢出玩家(sevConnClass.UserId))
						{
							playerS playerS2 = 查询玩家(sevConnClass.UserId);
							if (playerS2 == null)
							{
								continue;
							}
							string smsg = string.Empty;
							int num2 = 查询多开数量(sevConnClass.UserIp, out smsg);
							Form1.WriteLine(100, "IP[" + sevConnClass.UserIp + "]  目前多开为：" + num2 + "  允许多开：" + 允许多开数量);
							if (num2 > 允许多开数量)
							{
								bool flag = false;
								if (RunMoreClass.TryGetValue(sevConnClass.绑定帐号, out var value) && DateTime.Now < value.Time && num2 <= value.Number)
								{
									flag = true;
								}
								if (!flag)
								{
									Form1.WriteLine(6, "用户非法多开[" + sevConnClass.UserId + "]" + sevConnClass.UserIp + "其他多开账号：" + smsg);
									switch (多开查询操作)
									{
									case 1:
										RxjhClass.SetUserIdONLINE(playerS2.UserId);
										服务器踢出ID(sevConnClass.ServerID, sevConnClass.WorldID);
										HelperTools.ConcurrentPlayerManager.RemovePlayer(playerS2.UserId);
										playerS2.npcyd.Dispose();
										return;
									case 2:
										var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
										foreach (playerS item in allPlayers)
										{
											if (RxjhClass.IsEquals(item.绑定账号, sevConnClass.绑定帐号) && int.Parse(item.离线挂机) == 0)
											{
												RxjhClass.SetUserIdONLINE(playerS2.UserId);
												服务器踢出ID(sevConnClass.ServerID, sevConnClass.WorldID);
												HelperTools.ConcurrentPlayerManager.RemovePlayer(item.UserId);
												HelperTools.ResourceManager.SafeDispose(item.npcyd, $"玩家{item.UserId}的npcyd");
												break;
											}
										}
										playerS2.conn = 0;
										playerS2.ServerID = sevConnClass.ServerID;
										playerS2.WorldID = sevConnClass.WorldID;
										playerS2.绑定账号 = sevConnClass.绑定帐号;
										sevConnClass.Server.Sendd("用户登陆|" + sevConnClass.UserId + "|" + sevConnClass.WorldID + "|不在线|" + playerS2.原服务器IP + "|" + playerS2.原服务器端口 + "|" + playerS2.银币广场服务器IP + "|" + playerS2.银币广场服务器端口 + "|" + playerS2.原服务器序号 + "|" + playerS2.原服务器ID);
										break;
									}
								}
								else
								{
									Form1.WriteLine(6, "白名单用户多开[" + sevConnClass.UserId + "]" + sevConnClass.绑定帐号 + "其他多开账号：" + smsg);
									playerS2.conn = 0;
									playerS2.ServerID = sevConnClass.ServerID;
									playerS2.WorldID = sevConnClass.WorldID;
									playerS2.绑定账号 = sevConnClass.绑定帐号;
									sevConnClass.Server.Sendd("用户登陆|" + sevConnClass.UserId + "|" + sevConnClass.WorldID + "|不在线|" + playerS2.原服务器IP + "|" + playerS2.原服务器端口 + "|" + playerS2.银币广场服务器IP + "|" + playerS2.银币广场服务器端口 + "|" + playerS2.原服务器序号 + "|" + playerS2.原服务器ID);
								}
							}
							else
							{
								playerS2.conn = 0;
								playerS2.ServerID = sevConnClass.ServerID;
								playerS2.WorldID = sevConnClass.WorldID;
								playerS2.绑定账号 = sevConnClass.绑定帐号;
								sevConnClass.Server.Sendd("用户登陆|" + sevConnClass.UserId + "|" + sevConnClass.WorldID + "|不在线|" + playerS2.原服务器IP + "|" + playerS2.原服务器端口 + "|" + playerS2.银币广场服务器IP + "|" + playerS2.银币广场服务器端口 + "|" + playerS2.原服务器序号 + "|" + playerS2.原服务器ID);
							}
							continue;
						}
						HelperTools.ConcurrentPlayerManager.RemovePlayer(sevConnClass.UserId);
						服务器踢出ID(sevConnClass.ServerID, sevConnClass.WorldID);
					}
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "ProcessSerQueue()出错    " + ex.Message);
				}
			}
		}
		catch
		{
		}
	}

	public static int 查询多开数量(string UserNip, out string smsg)
	{
		smsg = "";
		if (自动查询多开 == 0)
		{
			return 0;
		}

		int num = 0;
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (item != null && RxjhClass.IsEquals(item.UserIp, UserNip) && item.离线挂机 == "0")
			{
				smsg += item.UserId;
				smsg += "丨";
				num++;
			}
		}
		smsg += UserNip;
		return num;
	}

	public static void Process狮子吼Queue()
	{
		if (狮子吼List.Count <= 0)
		{
			return;
		}
		狮子吼Class 狮子吼Class2 = (狮子吼Class)狮子吼List.Dequeue();
		foreach (SockClient value in ServerLst.Values)
		{
			value.Sendd("狮子吼|OK|" + 狮子吼Class2.FLD_INDEX + "|" + 狮子吼Class2.UserName + "|" + 狮子吼Class2.TxtId + "|" + 狮子吼Class2.Txt + "|" + 狮子吼Class2.线Id + "|" + 狮子吼Class2.Map + "|" + 狮子吼Class2.样式);
		}
	}

	public static void 发送传音消息(string 人物全服ID, string name, string toname, string msg, int msgType, string stringHex)
	{
		foreach (SockClient value in ServerLst.Values)
		{
			value.Sendd("传音消息|" + 人物全服ID + "|" + name + "|" + toname + "|" + msg + "|" + msgType + "|" + stringHex);
		}
	}

	public static void 发送帮派消息(string 帮派名字, string stringHex)
	{
		foreach (SockClient value in ServerLst.Values)
		{
			value.Sendd("帮派消息|" + 帮派名字 + "|" + stringHex);
		}
	}

	public static void 发送同盟消息(string 帮派名字, string username, string msg, string 服务器组)
	{
		foreach (SockClient value in ServerLst.Values)
		{
			value.Sendd("同盟聊天|OK|" + 帮派名字 + "|" + username + "|" + msg + "|" + int.Parse(服务器组));
		}
	}

	public static void 发送活动消息(string 帮派名字, string username, string msg, string 服务器组)
	{
		foreach (SockClient value in ServerLst.Values)
		{
			value.Sendd("活动开启|势力战|" + 帮派名字 + "|" + username + "|" + msg + "|" + int.Parse(服务器组));
		}
	}

	public static int 玩家换线通知(string Userid)
	{
		int result = 0;
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (RxjhClass.IsEquals(item.UserId, Userid) && item.conn == 0)
			{
				item.换线();
				return int.Parse(item.WorldID);
			}
		}
		return result;
	}

	public static int 玩家换线完成(string Userid)
	{
		int result = 0;
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (RxjhClass.IsEquals(item.UserId, Userid) && item.conn == 0)
			{
				item.换线中 = 0;
				return int.Parse(item.WorldID);
			}
		}
		return result;
	}

	public static void ProcessDisposedQueue()
	{
		try
		{
			int num = 0;
			while (num < 200 && m_Disposed.Count > 0)
			{
				num++;
				NetState netState = (NetState)m_Disposed.Dequeue();
				try
				{
					netState.delWorldIdd();
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "ProcessDisposedQueue()出错    " + ex.Message);
				}
			}
		}
		catch
		{
		}
	}

	public static bool 登录玩家(string Userid, string ip)
	{
		try
		{
			// 2025-0618 EVIAS 使用并发安全的玩家管理器
			if (HelperTools.ConcurrentPlayerManager.GetTempPlayer(Userid) == null)
			{
				playerS playerS3 = new playerS();
				playerS3.UserId = Userid;
				playerS3.conn = 1;
				HelperTools.ConcurrentPlayerManager.AddTempPlayer(Userid, playerS3);
			}

			playerS p = HelperTools.ConcurrentPlayerManager.GetOnlinePlayer(Userid);
			playerS lxp = HelperTools.ConcurrentPlayerManager.GetOfflinePlayer(Userid);

			if (p != null && p.UserIp != ip)
			{
				return false;
			}
			if (lxp != null)
			{
				return true;
			}
			if (p == null)
			{
				playerS playerS4 = new playerS();
				playerS4.UserId = Userid;
				playerS4.conn = 1;
				playerS4.UserIp = ip; // 2025-0618 EVIAS 设置IP地址
				HelperTools.ConcurrentPlayerManager.AddPlayer(Userid, playerS4);
				return true;
			}
			p.顶号++;
			if (p.顶号 >= 同IP顶号次数 && p.UserIp == ip)
			{
				p.顶号 = 0;
				return true;
			}
			return false;
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "登录玩家", Userid);
			return false;
		}
	}

	public static void 查询封号()
	{
		RxjhClass.SetUserIdZT();
	}

	public static void 登出玩家(string Userid)
	{
		var player = HelperTools.ConcurrentPlayerManager.GetOnlinePlayer(Userid);
		if (player == null)
		{
			return;
		}
		RxjhClass.SetUserIdONLINE(Userid);
		HelperTools.ConcurrentPlayerManager.RemovePlayer(Userid);
	}

	public static playerS 查询玩家(string Userid)
	{
		return HelperTools.ConcurrentPlayerManager.GetOnlinePlayer(Userid);
	}

	public static playerS 查询离线玩家(string Userid)
	{
		return HelperTools.ConcurrentPlayerManager.GetOfflinePlayer(Userid);
	}

	public static playerS 查询账号总表(string Userid)
	{
		return HelperTools.ConcurrentPlayerManager.GetTempPlayer(Userid);
	}

	public static bool 查询踢出玩家(string Userid)
	{
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (RxjhClass.IsEquals(item.UserId, Userid) && item.conn == 0)
			{
				服务器踢出ID(item.ServerID, item.WorldID);
				if (!string.IsNullOrEmpty(Userid))
				{
					HelperTools.ConcurrentPlayerManager.RemovePlayer(Userid);
				}
				return true;
			}
		}
		return false;
	}

	public static int 在线人数(string serverid)
	{
		int num = 0;
		var allPlayers = HelperTools.ConcurrentPlayerManager.GetAllPlayers();
		foreach (playerS item in allPlayers)
		{
			if (item.ServerID == serverid)
			{
				num++;
			}
		}
		return num;
	}

	public static void 服务器断开(string ServerID)
	{
		try
		{
			var playersToRemove = HelperTools.ConcurrentPlayerManager.GetPlayersByServerId(ServerID);
			foreach (playerS item in playersToRemove)
			{
				RxjhClass.SetUserIdONLINE(item.UserId);
				HelperTools.ResourceManager.SafeDispose(item.npcyd, $"玩家{item.UserId}的npcyd");
				HelperTools.ConcurrentPlayerManager.RemovePlayer(item.UserId);
			}
		}
		catch (Exception ex)
		{
			HelperTools.ExceptionHandler.LogSecureException(ex, "服务器断开", ServerID);
		}
	}

	public static SockClient 查询服务器(string serverid)
	{
		foreach (SockClient value in ServerLst.Values)
		{
			if (RxjhClass.IsEquals(value.ServerId, serverid))
			{
				return value;
			}
		}
		return null;
	}

	public static void 踢出非本线ID(string uid)
	{
		try
		{
			foreach (SockClient value in ServerLst.Values)
			{
				value.Sendd("用户踢出ID|" + uid);
			}
		}
		catch (Exception)
		{
		}
	}

	public static void 服务器踢出ID(string serverid, string userid)
	{
		try
		{
			if (userid == null || userid == "" || serverid == null)
			{
				return;
			}
			foreach (SockClient value in ServerLst.Values)
			{
				if (RxjhClass.IsEquals(value.ServerId, serverid))
				{
					value.Sendd("用户踢出|" + userid);
					break;
				}
			}
		}
		catch (Exception)
		{
		}
	}

	public static void 加入限制用户(string serverid, string worldid, int type, int value)
	{
		try
		{
			if (worldid == null || worldid == "" || !(serverid != string.Empty))
			{
				return;
			}
			foreach (SockClient value2 in ServerLst.Values)
			{
				if (RxjhClass.IsEquals(value2.ServerId, serverid))
				{
					value2.Sendd("限制用户|" + worldid + "|" + type + "|" + value);
					break;
				}
			}
		}
		catch (Exception)
		{
		}
	}

	public static void 复查用户登陆(string ServerID, Dictionary<string, playerS> Players)
	{
		try
		{
			new Dictionary<string, playerS>();
			Dictionary<string, playerS> dictionary = new Dictionary<string, playerS>();
			new Dictionary<string, playerS>();
			List<playerS> list = new List<playerS>();
			foreach (playerS value2 in Players.Values)
			{
				loginServer.DbClss.SecureDBA.UpdateUserOnlineStatus(value2.UserId, true);

				if (HelperTools.ConcurrentPlayerManager.GetPlayer(value2.UserId) == null)
				{
					HelperTools.ConcurrentPlayerManager.AddPlayer(value2.UserId, value2);
				}
				else
				{
					playerS value3 = HelperTools.ConcurrentPlayerManager.GetPlayer(value2.UserId);
					if (value3 != null && !(value3.UserIp == "127.0.0.1") && !(value2.UserIp == "127.0.0.1") && !(value3.UserIp == Hasher.GetIP()) && !(value2.UserIp == Hasher.GetIP()) && !(value3.UserId == "") && !(value2.UserId == "") && value3.ServerID != value2.ServerID)
				{
					if (value3.UserId != "")
					{
						list.Add(value3);
					}
					if (value2.UserId != "")
					{
						list.Add(value2);
					}
					Form1.WriteLine(6, "同进在线ID：[" + value3.UserId + "]IP:[" + value3.UserIp + "]ServerID:[" + value3.ServerID + "]和[" + value2.UserId + "]IP:[" + value2.UserIp + "]ServerID:[" + value2.ServerID + "]");
					if (value3.UserId == value2.UserId)
					{
						loginServer.DbClss.SecureDBA.UpdateAccountStatus(value3.UserId, 99999, "LS同进在线", 10);
					}
				}
			}
			}
			var serverPlayers = HelperTools.ConcurrentPlayerManager.GetPlayersByServerId(ServerID);
			foreach (playerS value4 in serverPlayers)
			{
				if (!Players.ContainsKey(value4.UserId) && value4.conn == 0)
				{
					dictionary.Add(value4.UserId, value4);
				}
			}

			foreach (playerS value5 in dictionary.Values)
			{
				HelperTools.ConcurrentPlayerManager.RemovePlayer(value5.UserId);
			}
			foreach (playerS item in list)
			{
				服务器踢出ID(item.ServerID, item.WorldID);
			}
		}
		catch (Exception)
		{
		}
	}
}
